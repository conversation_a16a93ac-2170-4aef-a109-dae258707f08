const TLSSigAPIv2 = require("./TLSSigAPIv2");

/**
 * 安全获取对象属性的函数
 * 替代可选链操作符 (?.)，用于兼容低版本Node.js
 * @param {Object} obj 目标对象
 * @param {string|Array} path 属性路径，可以是字符串（如 'a.b.c'）或数组（如 ['a', 'b', 'c']）
 * @param {any} defaultValue 默认值，当属性不存在时返回
 * @returns {any} 属性值或默认值
 */
function safeGet(obj, path, defaultValue = null) {
  if (obj == null) {
    return defaultValue;
  }

  const keys = Array.isArray(path) ? path : path.split(".");
  let result = obj;

  for (let i = 0; i < keys.length; i++) {
    if (result == null || typeof result !== "object") {
      return defaultValue;
    }
    result = result[keys[i]];
  }

  return result === undefined ? defaultValue : result;
}

/**
 * 使用MD5生成用户签名（用于VOIP/IM/TRTC等服务）
 * @param {string} userId 用户ID
 */

// var TLSSigAPIv2 = require('./TLSSigAPIv2'); // 源码集成需要使用相对路径

async function generateUserSig(userId) {
  var api = new TLSSigAPIv2.Api(
    "1600089665",
    "12dd9c8728f78af2e504f45534c600c77588a6470fa5cb49b044a254c9ea1eb6"
  );
  var sig = api.genSig(userId, 86400 * 180);
  return sig;
}
function formatTime(timeInMillis) {
  const time = new Date(timeInMillis);
  return (
    time.getFullYear() +
    "年" +
    (time.getMonth() + 1) +
    "月" +
    time.getDate() +
    "日 " +
    time.getHours() +
    ":" +
    time.getMinutes()
  );
}

// 格式化北京时区的时间:date,hour,dayOfWeek
const getBeijingDateAndHour = (timestamp) => {
  console.log("timestamp:", timestamp);
  const beijingTime = new Date(timestamp + 8 * 3600 * 1000);

  const year = beijingTime.getUTCFullYear();
  const month = beijingTime.getUTCMonth();
  const date = beijingTime.getUTCDate();

  // 3. 创建北京时间 0 点的 UTC 时间对象
  const beijingZeroUTC = Date.UTC(year, month, date);
  const beijingZeroTimestamp = beijingZeroUTC - 8 * 3600 * 1000;
  const dayOfWeek = beijingTime.getDay();
  return {
    date: beijingZeroTimestamp,
    hour: beijingTime.getUTCHours(),
    dayOfWeek,
  };
};

// 格式化北京时区的时间槽
function formatBeijingTimeSlots(startTime, duration) {
  const { date: beijingDate, hour: beijingHour } =
    getBeijingDateAndHour(startTime);
  const timeSlots = [];
  for (let i = beijingHour; i < beijingHour + duration / 60; i++) {
    timeSlots.push(i);
  }

  return { beijingDate, timeSlots };
}

// 生成随机字符串, 用于微信支付的字段nonceStr
function genNonceStr() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

// 生成商户订单号
function genOutTradeNo() {
  return `PAY${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

// 生成退款单号
function genRefundNo() {
  return `REFUND${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

// 生成测试记录ID
function genTestRecordId() {
  return `TEST${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

// 生成ChatSessionID
function genSessionId(AId, BId) {
  const sortedIds = [AId, BId].sort();
  return `session-${sortedIds[0]}-${sortedIds[1]}`;
}

// 生成分销订单ID
function generateDistributionOrderId() {
  return `DIST${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

// 生成提现记录ID
function generateWithdrawId() {
  return `WD${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

module.exports = {
  safeGet,
  generateUserSig,
  genNonceStr,
  genOutTradeNo,
  genRefundNo,
  genTestRecordId,
  formatTime,
  getBeijingDateAndHour,
  formatBeijingTimeSlots,
  genSessionId,
  generateDistributionOrderId,
  generateWithdrawId,
};
