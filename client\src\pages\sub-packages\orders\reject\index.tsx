import PageLol from '@components/common/page-meta';
import ReasonForm from '@components/common/reason-form';
import { REJECT_REASON_MAP } from '@constants/text';
import { orderTherapistActions } from '@core/actions/order.therapist';
import { REJECT_REASON } from '@model/order.interface';
import { useLoadingStore } from '@stores/loading.store';
import { View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { useState } from 'react';

export default function RejectPage() {
  const router = useRouter();
  const { orderId } = router.params;
  const { transactionLoading } = useLoadingStore();
  const [reason, setReason] = useState<REJECT_REASON | null>(null);
  const [detail, setDetail] = useState<string | null>(null);

  console.log('RejectPage orderId', orderId);

  const [localError, setLocalError] = useState<string | null>(null);

  if (!orderId) {
    setLocalError('订单ID不存在');
  }

  const handleSubmit = async () => {
    try {
      await orderTherapistActions.rejectOrder(orderId!, reason!, detail!);
    } catch (error) {
      setLocalError(error.message);
    }
  };

  return (
    <PageLol
      navigationProps={{ title: '拒绝订单', showBackButton: true }}
      error={localError || null}
    >
      <View className='cancel-page'>
        <ReasonForm
          title='请选择拒绝原因'
          reasonOptions={REJECT_REASON_MAP}
          selectedReason={reason}
          detail={detail}
          onReasonChange={(value) => setReason(value as REJECT_REASON)}
          onDetailChange={(value) => setDetail(value)}
          onSubmit={handleSubmit}
          loading={transactionLoading}
          placeholder='请详细描述拒绝原因'
        />
      </View>
    </PageLol>
  );
}
