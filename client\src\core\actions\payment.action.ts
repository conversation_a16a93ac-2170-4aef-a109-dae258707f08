import { PaymentParams, WithdrawParams } from '@model/payment.interface';
import { paymentService } from '@services/payment.service';
import { PaymentResultStatus, usePaymentStore } from '@stores/payment.store';
import Taro from '@tarojs/taro';
import { orderRouter } from '@utils/router';

export const paymentActions = {
  /** 启动支付 */
  startPayment: async (orderId: string) => {
    orderRouter.pay(orderId, true);
  },

  /**
   * 微信支付
   * @param orderId 订单ID
   * @returns 支付结果
   */
  wechatPay: async (orderParams: PaymentParams) => {
    try {
      console.log('🚀🚀🚀 wechatPay orderParams', orderParams);

      // 第一步，创建支付
      const paymentData = await paymentService.createPayment({
        orderId: orderParams.orderId,
        amount: orderParams.amount,
        description: orderParams.description,
        paymentMethod: orderParams.paymentMethod,
      });

      const store = usePaymentStore.getState();
      // 第二步，调用微信支付
      await Taro.requestPayment({
        timeStamp: paymentData?.timeStamp,
        nonceStr: paymentData?.nonceStr,
        package: paymentData?.package,
        signType: paymentData?.signType as any,
        paySign: paymentData?.paySign,
        success: async () => {
          // 第一步，显示加载
          Taro.showLoading({
            title: '正在查询支付结果...',
          });
          // 第二步，查询订单状态
          const isSuccess = await paymentService.validatePaymentSuccess(
            orderParams.orderId
          );
          console.log('🚀🚀🚀 wechatPay isSuccess', isSuccess);
          Taro.hideLoading();
          if (isSuccess) {
            store.setPayStatus(PaymentResultStatus.Success);
          } else {
            store.setPayStatus(PaymentResultStatus.Failed);
          }
        },
        fail: (res: any) => {
          store.setPayStatus(PaymentResultStatus.Failed);

          throw new Error(res.errMsg || '支付失败');
        },
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  /**
   * 提交提现申请
   */
  async submitWithdrawApplication(params: WithdrawParams) {
    try {
      usePaymentStore.getState().setLoading(true);
      usePaymentStore.getState().setError(null);

      const response = await paymentService.submitWithdrawApplication(params);

      // 刷新提现记录
      await this.fetchWithdrawRecords({ forceRefresh: true });

      return response;
    } catch (error) {
      usePaymentStore.getState().setError(error as string);
      console.error('Failed to submit withdraw application:', error);
      Taro.showToast({
        title: '提交失败，请稍后重试',
        icon: 'none',
      });
      return null;
    } finally {
      usePaymentStore.getState().setLoading(false);
    }
  },

  /**
   * 取消提现申请
   */
  async cancelWithdrawApplication(id: string) {
    try {
      usePaymentStore.getState().setLoading(true);
      usePaymentStore.getState().setError(null);

      const response = await paymentService.cancelWithdrawApplication(id);

      // 刷新提现记录
      await this.fetchWithdrawRecords({ forceRefresh: true });

      return response;
    } catch (error) {
      usePaymentStore.getState().setError(error as string);
      console.error('Failed to cancel withdraw application:', error);
      return null;
    } finally {
      usePaymentStore.getState().setLoading(false);
    }
  },

  /**
   * 获取提现配置
   */
  async getWithdrawConfig() {
    const response = await paymentService.getWithdrawConfig();
    return response;
  },

  /**
   * 获取提现记录列表
   */
  async fetchWithdrawRecords(
    params: {
      page?: number;
      pageSize?: number;
      forceRefresh?: boolean;
    } = {}
  ) {
    try {
      const store = usePaymentStore.getState();
      if (store.withdrawRecords.loading) {
        return;
      }
      store.withdrawRecords.loading = true;
      store.withdrawRecords.error = null;

      const { forceRefresh } = params;

      if (!forceRefresh) {
        // 如果已有数据且不强制刷新，则直接返回
        if (store.withdrawRecords.list.length > 0) {
          store.withdrawRecords.loading = false;
          return;
        }
      }

      const result = await paymentService.getWithdrawRecords({
        page: params.page || 1,
        pageSize: params.pageSize || 10,
      });

      if (result.success) {
        store.setWithdrawRecordsWithPagination({
          list: result.data,
          pagination: result.pagination!,
        });
        return;
      }

      throw new Error('获取提现记录失败');
    } catch (error) {
      usePaymentStore.getState().withdrawRecords.error = error as Error;
      console.error('Failed to fetch withdraw records:', error);
    } finally {
      usePaymentStore.getState().withdrawRecords.loading = false;
    }
  },

  /**
   * 加载更多提现记录
   */
  async loadMoreWithdrawRecords(): Promise<void> {
    const store = usePaymentStore.getState();
    if (store.withdrawRecords.loading) {
      return;
    }
    store.withdrawRecords.loading = true;

    // 没有更多数据了
    if (
      !store.withdrawRecords.pagination ||
      !store.withdrawRecords.pagination.hasNext
    )
      return;

    try {
      // 从服务获取数据
      const result = await paymentService.getWithdrawRecords({
        page: store.withdrawRecords.pagination.page + 1,
        pageSize: store.withdrawRecords.pagination.pageSize,
      });

      if (result.success) {
        const { list: currentList } = store.withdrawRecords;

        // 将新数据添加到现有数据后面
        const combinedList = [...(currentList || []), ...result.data];

        // 更新状态
        store.setWithdrawRecordsWithPagination({
          list: combinedList,
          pagination: result.pagination!,
        });
      }
    } catch (error) {
      console.error('加载更多提现记录失败:', error);
    } finally {
      store.withdrawRecords.loading = false;
    }
  },
};
