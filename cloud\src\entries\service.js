const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const {
  COLLECTIONS,
  ORDER_STATUS,
  USER_ROLE,
} = require("../common/db.constants");
const { safeGet, generateUserSig } = require("../common/utils");

const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database();
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};
// 处理函数映射
const handlers = {
  makeVideoCall: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await makeVideoCall(params, context.openid);
        return success(result);
      }
    );
  },
};

/**
 * 发起视频通话, 校验权限, 准备参数
 * @param {Object} params 参数
 * @param {string} openid 用户openid
 * @returns {Object} 结果
 */
async function makeCall(params, openid) {
  const { orderId, callerId, calleeId } = params;

  try {
    // 校验订单和用户
    const result = await db.collection(COLLECTIONS.ORDER).doc(orderId).get();
    if (!result.data) {
      throw new Error("订单不存在");
    }
    const order = result.data[0];
    if (
      order.status !== ORDER_STATUS.PENDING_START &&
      order.status !== ORDER_STATUS.IN_PROGRESS
    ) {
      throw new Error("订单状态不正确");
    }
    if (order.serviceType !== ServiceType.VIDEO_CALL) {
      throw new Error("服务类型不正确");
    }
    if (order.therapistId !== callerId) {
      throw new Error("订单咨询师不正确");
    }
    if (order.userId !== calleeId) {
      throw new Error("订单用户不正确");
    }

    // 生成视频通话参数
    const { signature, nonceStr, timeStamp, groupId } =
      await generateVideoCallParams();
    return {
      signature,
      nonceStr,
      timeStamp,
      groupId,
    };
  } catch (error) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, error);
    throw error;
  }
}

async function generateVideoCallParams(userId) {
  const timestamp = Date.now();
  const nonceStr = Math.random().toString(36).substring(2, 15);
  const groupId = `${timestamp}-${nonceStr}`;
  const signature = await generateUserSig(userId);

  return { signature, nonceStr, timeStamp, groupId };
}
