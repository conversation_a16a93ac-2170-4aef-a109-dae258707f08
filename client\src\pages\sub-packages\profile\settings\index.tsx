import { Cell, CellGroup, Dialog, Switch, Toast } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { LOGO_DEFAULT } from '@constants/assets';

import { logout } from '@core/actions/auth.action';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { appRouter } from '@utils/router';
import React, { useState } from 'react';

const SettingsPage: React.FC = () => {
  const [isLogoutVisible, setIsLogoutVisible] = useState(false);
  const [notificationEnabled, setNotificationEnabled] = useState(true);
  const [pushEnabled, setPushEnabled] = useState(true);

  // 版本信息
  const version = 'v1.0.0';
  const buildNumber = '20231120001';

  // 处理退出登录
  const handleLogout = () => {
    setIsLogoutVisible(true);
  };

  // 确认退出
  const confirmLogout = () => {
    // 实际应用中这里需要调用API清除登录状态
    Toast.loading({
      message: '退出中...',
      forbidClick: true,
      duration: 1000,
      onClose: () => {
        logout();
      },
    });
  };

  // 切换开关状态
  const toggleSwitchNotification = (e) => setNotificationEnabled(e.detail);
  const toggleSwitchPush = (e) => setPushEnabled(e.detail);

  // 清除缓存
  const handleClearCache = () => {
    Dialog.confirm({
      title: '清除缓存',
      message: '确定要清除所有本地缓存吗？这将不会删除您的账号数据。',
    })
      .then(() => {
        Taro.clearStorage({
          complete: () => {
            Toast.loading({
              message: '清除中...',
              forbidClick: true,
              duration: 1000,
              onClose: () => {
                Toast.success('缓存已清除');
              },
            });
          },
        });
      })
      .catch(() => {
        // 取消操作
      });
  };

  return (
    <PageLol
      navigationProps={{
        title: '设置',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen bg-gray-50'>
        {/* 通知与提醒 */}
        <View className='mt-6 mb-2'>
          <Text className='text-sm text-secondary mx-4'>通知与提醒</Text>
        </View>
        <CellGroup inset>
          <Cell
            title='通知开关'
            renderRightIcon={
              <Switch
                checked={notificationEnabled}
                onChange={toggleSwitchNotification}
              />
            }
          />
          <Cell
            title='消息推送设置'
            renderRightIcon={
              <Switch
                size='20px'
                checked={pushEnabled}
                onChange={toggleSwitchPush}
              />
            }
          />
        </CellGroup>

        {/* 通用设置 */}
        <View className='mt-6 mb-2'>
          <Text className='text-sm text-secondary mx-4'>通用</Text>
        </View>
        <CellGroup inset>
          <Cell title='清除缓存' isLink onClick={handleClearCache} />
          <Cell title='关于我们' isLink onClick={appRouter.about} />
        </CellGroup>

        {/* 退出登录 */}
        <View className='p-4 mt-6'>
          <View
            className='bg-white rounded-lg py-3 flex items-center justify-center'
            onClick={handleLogout}
          >
            <Text className='text-danger'>退出登录</Text>
          </View>
        </View>

        {/* 版本信息 */}
        <View className='p-4 mt-6 mb-8'>
          <View className='flex flex-col items-center'>
            <Image src={LOGO_DEFAULT} className='w-12 h-12 rounded-lg mb-2' />
            <Text className='text-sm text-secondary'>版本号：{version}</Text>
            <Text className='text-xs text-disabled mt-1'>
              Build: {buildNumber}
            </Text>
          </View>
        </View>

        {/* 退出登录弹窗 */}
        <Dialog
          id='dialog'
          show={isLogoutVisible}
          title='退出登录'
          message='确定要退出当前账号吗？'
          cancelButtonText='取消'
          confirmButtonText='退出'
          onClose={() => setIsLogoutVisible(false)}
          onCancel={() => setIsLogoutVisible(false)}
          onConfirm={confirmLogout}
        />

        <Toast id='toast' />
      </View>
    </PageLol>
  );
};

export default SettingsPage;
