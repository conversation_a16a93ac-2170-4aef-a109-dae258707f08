export interface Notice {
  _id: string;
  to: 'user' | 'therapist';
  content: string;
  model: 'closeable' | 'link';
  url?: string;
  openType?:
    | 'navigate'
    | 'redirect'
    | 'switchTab'
    | 'reLaunch'
    | 'navigateBack'
    | 'exit';
  createdAt: number;
  active: boolean;
}

export interface Banner {
  _id: string;
  to: 'user' | 'therapist';
  image: string; // 图片URL
  title: string;
  // 跳转目标配置（联合类型）
  target: {
    type: 'miniprogram' | 'webview' | 'copy';
    path?: string; // 小程序路径 pages/product?id=123
    url?: string; // H5链接 https://m.domain.com/promo
    text?: string; // 复制文本
    buttonText?: string; // 按钮文本
  };
  displayOrder: number; // 展示顺序
  active: boolean;
}

export interface OrderToday {
  pendingConfirm: number; // 待确认订单数
  refundOrAfterSale: number; // 退款/售后订单数
  complaint: number; // 投诉订单数
  totalOrder: number; // 今日新增订单总数
  totalAmount: number; // 今日新增订单总金额
}
