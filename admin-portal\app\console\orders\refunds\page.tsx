"use client";
import { REFUND_REASON_MAP } from "@/app/constants/text";
import { useAudit } from "@/app/hooks/useAudit";
import { formatTime } from "@/app/lib/time";
import { REFUND_STATUS, RefundAuditOrder } from "@/app/model/order.interface";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DotLoading } from "@/components/ui/dot-loading";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useState } from "react";

const getRefundStatus = (order: RefundAuditOrder) => {
  const refundStatus = order.refundStatus;
  switch (refundStatus) {
    case REFUND_STATUS.AUDITING:
      return { status: "退款审核中", style: "bg-yellow-100 text-yellow-800" };
    case REFUND_STATUS.REJECTED:
      return { status: "退款拒绝", style: "bg-red-100 text-red-800" };
    case REFUND_STATUS.PROCESSING:
      return { status: "退款中", style: "bg-blue-100 text-blue-800" };
    case REFUND_STATUS.COMPLETED:
      return { status: "已退款", style: "bg-green-100 text-green-800" };
    case REFUND_STATUS.FAILED:
      return { status: "退款失败", style: "bg-red-100 text-red-800" };
    default:
      return { status: "无退款", style: "bg-gray-100 text-gray-800" };
  }
};

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  description: string;
  onConfirm: () => Promise<void>;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

function ConfirmDialog({
  isOpen,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = "确认",
  cancelText = "取消",
}: ConfirmDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onConfirm();
      onCancel(); // 成功时关闭对话框
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {cancelText}
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "处理中..." : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 错误对话框组件
interface ErrorDialogProps {
  isOpen: boolean;
  title: string;
  errorMessage: string;
  onClose: () => void;
}

function ErrorDialog({
  isOpen,
  title,
  errorMessage,
  onClose,
}: ErrorDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription className="text-red-500 mt-2">
            {errorMessage}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onClose}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function RefundsPage() {
  const {
    orders,
    pagination,
    loading,
    // refreshOrders,
    loadMoreOrders,
    approveRefund,
    rejectRefund,
  } = useAudit();

  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    onConfirm: () => Promise<void>;
  }>({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: async () => {},
  });

  // 错误对话框状态
  const [errorDialog, setErrorDialog] = useState<{
    isOpen: boolean;
    title: string;
    errorMessage: string;
  }>({
    isOpen: false,
    title: "操作失败",
    errorMessage: "",
  });

  // 打开退款通过确认对话框
  const handleApproveClick = (order: RefundAuditOrder) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认通过退款",
      description: `确定通过订单 ${order.orderId} 的退款申请吗？退款金额：¥${order.refundAmount}`,
      onConfirm: async () => {
        try {
          await approveRefund(order.orderId);
        } catch (error) {
          // 如果失败，弹出错误提示框
          const errorMessage =
            error instanceof Error ? error.message : "操作失败，请重试";
          setErrorDialog({
            isOpen: true,
            title: "退款审批失败",
            errorMessage,
          });
        }
      },
    });
  };

  // 打开退款拒绝确认对话框
  const handleRejectClick = (order: RefundAuditOrder) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认拒绝退款",
      description: `确定拒绝订单 ${order.orderId} 的退款申请吗？`,
      onConfirm: async () => {
        try {
          await rejectRefund(order.orderId);
        } catch (error) {
          // 如果失败，弹出错误提示框
          const errorMessage =
            error instanceof Error ? error.message : "操作失败，请重试";
          setErrorDialog({
            isOpen: true,
            title: "退款拒绝失败",
            errorMessage,
          });
        }
      },
    });
  };

  // 关闭确认对话框
  const closeConfirmDialog = () => {
    setConfirmDialog({ ...confirmDialog, isOpen: false });
  };

  // 关闭错误对话框
  const closeErrorDialog = () => {
    setErrorDialog({ ...errorDialog, isOpen: false });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">退款审核</h1>
        <p className="text-muted-foreground">审核用户申请的退款请求</p>
      </div>

      <Card className="p-6">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <DotLoading size={8} color="#1e40af" className="mx-auto" />
          </div>
        ) : orders && orders.length > 0 ? (
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单号</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>咨询师</TableHead>
                  <TableHead className="text-right">退款金额</TableHead>
                  <TableHead>退款原因</TableHead>
                  <TableHead>申请时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((refund) => (
                  <TableRow key={refund._id}>
                    <TableCell>{refund.orderId}</TableCell>
                    <TableCell>{refund.userName}</TableCell>
                    <TableCell>{refund.therapistName}</TableCell>
                    <TableCell className="text-right">
                      ¥{refund.refundAmount}
                    </TableCell>
                    <TableCell>{`${
                      REFUND_REASON_MAP.find(
                        (item) => item.value === refund.refundReason
                      )?.label
                    }：${refund.refundDetail}`}</TableCell>
                    <TableCell>{formatTime(refund.refundTime)}</TableCell>
                    <TableCell>
                      <Badge className={getRefundStatus(refund).style || ""}>
                        {getRefundStatus(refund).status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          详情
                        </Button>
                        {refund.refundStatus === REFUND_STATUS.AUDITING && (
                          <>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleApproveClick(refund)}
                            >
                              通过
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleRejectClick(refund)}
                            >
                              拒绝
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 分页控制 */}
            <div className="flex items-center justify-between p-4 border-t">
              <div className="text-sm text-muted-foreground">
                共 {pagination?.total || 0} 条记录，第 {pagination?.page || 1}/
                {pagination?.totalPages || 1} 页
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasPrev}
                  onClick={() => loadMoreOrders(pagination?.page - 1)}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasNext}
                  onClick={() => loadMoreOrders(pagination?.page + 1)}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="py-10 text-center text-muted-foreground">
            暂无退款申请
          </div>
        )}
      </Card>

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
        onCancel={closeConfirmDialog}
      />

      {/* 错误对话框 */}
      <ErrorDialog
        isOpen={errorDialog.isOpen}
        title={errorDialog.title}
        errorMessage={errorDialog.errorMessage}
        onClose={closeErrorDialog}
      />
    </div>
  );
}
