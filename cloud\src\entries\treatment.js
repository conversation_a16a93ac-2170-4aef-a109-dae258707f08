// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS } = require("../common/db.constants");
const { safeGet, generateId } = require("../common/utils");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

/**
 * 获取推荐的自然声音
 */
async function getFeaturedAudios() {
  try {
    const result = await db
      .collection(COLLECTIONS.TREATMENT_AUDIO)
      .where({
        type: "nature",
      })
      .limit(1)
      .get();

    return {
      success: true,
      data: result.data,
    };
  } catch (err) {
    console.error("获取推荐自然声音失败:", err);
    throw err;
  }
}

/**
 * 更新收听记录
 * @param {object} params 参数
 * @param {object} params.record 收听记录
 * @param {object} context 上下文
 */
async function updateListeningRecord(params, context) {
  try {
    const { record } = params;
    const { openid } = context;

    if (!record) {
      return {
        success: false,
        message: "收听记录不能为空",
      };
    }

    if (record.userId !== openid) {
      throw new Error("收听记录的userId与当前用户不一致");
    }

    // 查询记录是否存在
    const recordResult = await db
      .collection(COLLECTIONS.TREATMENT_LISTENING_RECORD)
      .where({
        userId: openid,
        audioId: record.audioId,
      })
      .limit(1)
      .get();

    if (recordResult.data.length === 0) {
      // 记录不存在，创建新记录
      await db.collection(COLLECTIONS.TREATMENT_LISTENING_RECORD).add({
        data: {
          ...record,
          id: generateId(),
          _openid: openid,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      });
    } else {
      // 记录存在，更新记录
      const existingRecord = recordResult.data[0];
      await db
        .collection(COLLECTIONS.TREATMENT_LISTENING_RECORD)
        .doc(existingRecord._id)
        .update({
          data: {
            progress: record.progress || existingRecord.progress,
            lastPosition: record.lastPosition || existingRecord.lastPosition,
            lastListenTime: record.lastListenTime || Date.now(),
            updatedAt: Date.now(),
          },
        });
    }

    return {
      success: true,
    };
  } catch (err) {
    console.error("更新收听记录失败:", err);
    throw err;
  }
}

/**
 * 切换收藏状态
 * @param {object} params 参数
 * @param {string} params.audioId 音频ID
 * @param {boolean} params.isFavorite 是否收藏
 * @param {object} context 上下文
 */
async function toggleFavorite(params, context) {
  try {
    const { audioId, isFavorite } = params;
    const { openid } = context;

    if (!audioId) {
      return {
        success: false,
        message: "音频ID不能为空",
      };
    }

    // 查询收藏记录是否存在
    const favoriteResult = await db
      .collection(COLLECTIONS.TREATMENT_FAVORITES)
      .where({
        userId: openid,
        audioId,
      })
      .get();

    // 如果要收藏且记录不存在，添加记录
    if (isFavorite && favoriteResult.data.length === 0) {
      await db.collection(COLLECTIONS.TREATMENT_FAVORITES).add({
        data: {
          id: generateId(),
          userId: openid,
          audioId,
          _openid: openid,
          createdAt: Date.now(),
        },
      });
      return {
        success: true,
      };
    }

    // 如果要取消收藏且记录存在，删除记录
    if (!isFavorite && favoriteResult.data.length > 0) {
      await db
        .collection(COLLECTIONS.TREATMENT_FAVORITES)
        .doc(favoriteResult.data[0]._id)
        .remove();
      return {
        success: true,
      };
    }

    return {
      success: true,
    };
  } catch (err) {
    console.error("切换收藏状态失败:", err);
    throw err;
  }
}

/**
 * 增加播放次数
 * @param {object} params 参数
 * @param {string} params.audioId 音频ID
 */
async function incrementPlayCount(params) {
  try {
    const { audioId } = params;

    if (!audioId) {
      return {
        success: false,
        message: "音频ID不能为空",
      };
    }

    // 查询音频记录
    const audioResult = await db
      .collection(COLLECTIONS.TREATMENT_AUDIO)
      .where({
        id: audioId,
      })
      .get();

    if (audioResult.data.length === 0) {
      return {
        success: false,
        message: "音频不存在",
      };
    }

    // 增加播放次数
    await db
      .collection(COLLECTIONS.TREATMENT_AUDIO)
      .doc(audioResult.data[0]._id)
      .update({
        data: {
          plays: _.inc(1),
        },
      });

    return {
      success: true,
    };
  } catch (err) {
    console.error("增加播放次数失败:", err);
    throw err;
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

// 处理函数映射
const handlers = {
  // 获取推荐自然声音
  getFeaturedAudios: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await getFeaturedAudios();
      return success(result);
    });
  },

  // 更新收听记录
  updateListeningRecord: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await updateListeningRecord(params, context);
        return success(result);
      }
    );
  },

  // 切换收藏状态
  toggleFavorite: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await toggleFavorite(params, context);
        return success(result);
      }
    );
  },

  // 增加播放次数
  incrementPlayCount: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await incrementPlayCount(params);
      return success(result);
    });
  },
};
