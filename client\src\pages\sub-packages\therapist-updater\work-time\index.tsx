import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import WorkTimeEditor from '@components/therapist/WorkTimeEditor';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { my_service } from '@model/profile.therapist';
import { useGlobalStore } from '@stores/global.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

export default function WorkTimeUpdater() {
  const workTime = useTherapistUpdaterStore(
    (state) => state.formData.serviceInfo.workTime
  );
  const updateFormField = useTherapistUpdaterStore(
    (state) => state.updateFormField
  );
  const loading = useTherapistUpdaterStore((state) => state.loading);
  const error = useTherapistUpdaterStore((state) => state.error);

  useEffect(() => {
    const loadMyServices = async () => {
      await therapistProfileActions.loadMyServices();
      const myServices = useTherapistProfileStore.getState().myServices;
      if (myServices) {
        updateFormField('serviceInfo', 'workTime', myServices.workTime || {});
      }
    };
    loadMyServices();
    return () => {
      useTherapistUpdaterStore.setState({
        loading: false,
        error: null,
      });
    };
  }, []);

  // 表单验证
  const validateForm = (): boolean => {
    if (!workTime?.workDays || workTime.workDays.length === 0) {
      Taro.showToast({ title: '请选择工作日', icon: 'none' });
      return false;
    }

    if (!workTime.start || !workTime.end) {
      Taro.showToast({ title: '请设置工作时间', icon: 'none' });
      return false;
    }

    if (workTime.start >= workTime.end) {
      Taro.showToast({ title: '开始时间必须早于结束时间', icon: 'none' });
      return false;
    }

    return true;
  };

  // 处理提交
  const handleSubmit = async () => {
    if (validateForm()) {
      const serviceInfo: Partial<my_service> = {
        id: useGlobalStore.getState().openid || '',
        workTime: workTime,
      };

      await therapistProfileActions.updateMyServices(serviceInfo);
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '工作时间设置',
        showBackButton: true,
        showSearch: false,
      }}
      error={error}
    >
      <WorkTimeEditor
        workTime={workTime}
        onFormChange={(newWorkTime) => {
          updateFormField('serviceInfo', 'workTime', newWorkTime);
        }}
      />

      {/* 提交按钮 - 仅在需要时显示 */}
      <View className='mt-8'>
        <Button
          type='primary'
          block
          round
          loading={loading}
          disabled={loading}
          onClick={handleSubmit}
        >
          保存设置
        </Button>
      </View>
    </PageLol>
  );
}
