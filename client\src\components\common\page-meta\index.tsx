import { Empty, Result } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import Taro, { useDidShow, usePullDownRefresh } from '@tarojs/taro';
import { ReactNode, useEffect, useState } from 'react';
import { TIPS } from 'src/constants/text';
import DotLoading from '../loading-dots';
import NavigationBar, { NavigationBarProps } from '../navigation-bar/index';
import './index.less';

interface PageMetaProps {
  /** 页面类名 */
  className?: string;

  /** 是否使用自定义导航栏 */
  useNav?: boolean;

  /** 导航栏配置 */
  navigationProps?: NavigationBarProps;

  /** 页面内容 */
  children?: ReactNode;

  /** 下拉刷新回调 */
  onPullDownRefresh?: () => Promise<void>;
  /** 是否正在加载 */
  loading?: boolean;
  /** 加载文案 */
  loadingText?: string | null;
  /** 是否出现错误 */
  error?: string | null;
  /** 是否显示空状态 */
  isEmpty?: boolean;
  /** 空状态图片 */
  emptyImage?: string;
  /** 空状态描述 */
  emptyDescription?: string;
  /** 重试回调 */
  onRetry?: () => void;
  /** 是否添加底部安全区域，避免被tabbar遮挡 */
  withTabBarSpace?: boolean;
}

/**
 * 基础页面容器组件
 * 集成导航栏、下拉刷新、异常处理等功能
 */
const PageLol: React.FC<PageMetaProps> = ({
  className,
  useNav = true,
  navigationProps,
  // navBackgroundColor,
  children,
  onPullDownRefresh,
  loading = false,
  loadingText = null,
  error = null,
  isEmpty = false,
  emptyImage,
  emptyDescription = TIPS.empty,
  onRetry,
  withTabBarSpace = false,
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const [navHeight, setNavHeight] = useState(0);
  const [tabBarHeight, setTabBarHeight] = useState(0);

  // 获取导航栏高度和tabbar高度
  useEffect(() => {
    // 获取状态栏高度和胶囊信息
    const sys = Taro.getSystemInfoSync();
    const statusBarHeight = sys.statusBarHeight || 0;

    if (useNav) {
      const menuButton = Taro.getMenuButtonBoundingClientRect();

      if (menuButton) {
        // 导航栏高度 = 状态栏高度 + 导航内容高度
        const navContentHeight =
          menuButton.height + (menuButton.top - statusBarHeight) * 2 + 2;
        const totalNavHeight = statusBarHeight + navContentHeight;

        setNavHeight(totalNavHeight);
      } else {
        // 默认导航栏高度
        setNavHeight(statusBarHeight + 44);
      }
    }

    // 设置tabbar高度
    if (withTabBarSpace) {
      // iOS设备底部安全区域高度
      const safeAreaBottom = sys.safeArea
        ? sys.screenHeight - sys.safeArea.bottom
        : 0;
      // tabbar高度 = 基础高度 + 安全区域高度
      const baseTabBarHeight = 50; // 基础tabbar高度
      setTabBarHeight(baseTabBarHeight + safeAreaBottom);
    }
  }, [useNav, withTabBarSpace]);

  // 处理下拉刷新
  usePullDownRefresh(async () => {
    if (onPullDownRefresh) {
      setRefreshing(true);
      try {
        await Promise.all([
          new Promise((resolve) => setTimeout(resolve, 1000)),
          onPullDownRefresh(),
        ]);
      } catch (err) {
        console.error('下拉刷新出错:', err);
      } finally {
        setRefreshing(false);
        Taro.stopPullDownRefresh();
      }
    } else {
      Taro.stopPullDownRefresh();
    }
  });

  // 确保useDidShow在nextTick中执行
  useDidShow(() => {
    Taro.nextTick(() => {
      // 页面显示时的逻辑，如需要可以在这里添加
    });
  });

  // 渲染内容区域
  const renderContent = () => {
    if (error) {
      return (
        <View className='page-meta__error'>
          <Result type='error' title={error || TIPS.networkError} />
          {onRetry && (
            <View className='page-meta__retry' onClick={onRetry}>
              重试
            </View>
          )}
        </View>
      );
    }

    if (isEmpty) {
      return (
        <View className='page-meta__empty'>
          <Empty image={emptyImage} description={emptyDescription} />
        </View>
      );
    }

    return (
      <>
        {(refreshing || loading) && (
          <View className='page-meta__loading'>
            <DotLoading />
            {loadingText && (
              <View className='page-meta__loading-text'>{loadingText}</View>
            )}
          </View>
        )}
        {children}
      </>
    );
  };
  // console.log('page-meta tabBarHeight', tabBarHeight);
  // 如果使用自定义导航栏
  if (useNav) {
    return (
      <View className={`page-meta ${className}`}>
        {/* 自定义导航栏 */}
        <NavigationBar {...navigationProps}></NavigationBar>

        {/* 内容区域 - 添加与导航栏等高的顶部内边距 */}
        <View
          className='page-meta__content'
          style={`margin-top: ${navHeight}px;`}
        >
          {renderContent()}
        </View>
        {/* 添加底部安全区域，避免被tabbar遮挡 */}
        {withTabBarSpace && (
          <View className='w-full' style={{ height: tabBarHeight }}></View>
        )}
      </View>
    );
  }

  // 使用框架自带的导航栏
  return (
    <View className='page-meta page-meta--system-nav'>
      {/* 内容区域 */}
      <View className='page-meta__content'>{renderContent()}</View>
      {/* 添加底部安全区域，避免被tabbar遮挡 */}
      {withTabBarSpace && (
        <View className='w-full' style={{ height: tabBarHeight }}></View>
      )}
    </View>
  );
};

export default PageLol;
