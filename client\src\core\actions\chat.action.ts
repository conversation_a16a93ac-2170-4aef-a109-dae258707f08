import { Toast } from '@antmjs/vantui';
import {
  MessageContentType,
  MessageStatus,
  SendMessageParams,
} from '@model/chat.interface';
import { chatService } from '@services/chat.service';
import { uploadService } from '@services/upload.service';
import { useChatStore } from '@stores/chat.store';
import { useGlobalStore } from '@stores/global.store';
import { genSessionId } from '@utils/app';

export const chatActions = {
  // 获取聊天消息
  fetchMessages: async (sessionId: string, lastMessageTimestamp?: number) => {
    console.log('fetchMessages', sessionId, lastMessageTimestamp);
    if (!sessionId || sessionId === '') return;
    const store = useChatStore.getState();

    // 如果已经在加载中，则不重复请求
    if (store.loading) {
      console.log('store.loading 已经在加载中，不重复请求');
      return;
    }

    store.setLoading(true);
    try {
      const messages = await chatService.getMessages({
        sessionId,
        lastMessageTimestamp,
        limit: 20,
      });
      console.log('chatActions fetchMessages messages', messages);
      store.addMessages(sessionId, messages);

      // if (lastMessageTimestamp && messages && messages.length > 0) {
      //   // 获取现有消息
      //   const existingMessages = store.getMessages(sessionId);
      //   // 过滤掉已存在的消息
      //   const uniqueMessages = messages.filter(
      //     (newMsg) =>
      //       !existingMessages.some(
      //         (existingMsg) => existingMsg.id === newMsg.id
      //       )
      //   );

      //   if (uniqueMessages.length > 0) {
      //     store.prependMessages(sessionId, uniqueMessages);
      //   }
      //   return uniqueMessages;
      // } else if (messages) {
      //   store.setMessages(sessionId, messages);
      //   return messages;
      // }
    } catch (error) {
      console.error('chatActions fetchMessages 获取消息失败:', error);
      store.setError('获取消息失败，请稍后重试chatActions');
    } finally {
      store.setLoading(false);
    }
  },

  // 发送消息
  sendMessage: async (params: SendMessageParams) => {
    console.log('sendMessage', params);
    const store = useChatStore.getState();
    const tempId = Math.random().toString(10).substring(2, 6);

    // 先添加一条发送中的消息
    store.addMessage(params.sessionId, {
      id: tempId,
      sessionId: params.sessionId,
      senderId: useGlobalStore.getState().openid || '',
      receiverId: params.receiverId,
      content: params.content,
      contentType: params.contentType,
      status: MessageStatus.SENDING,
      timestamp: Date.now(),
      extra: params.extra,
    });

    try {
      const message = await chatService.sendMessage(params);
      // 更新消息状态
      store.updateMessage(params.sessionId, tempId, {
        ...message,
        status: MessageStatus.SENT,
      });
      return message;
    } catch (error) {
      console.error('发送消息失败:', error);
      // 更新消息状态为发送失败
      store.updateMessage(params.sessionId, tempId, {
        status: MessageStatus.FAILED,
      });
      Toast.fail('发送失败，请重试');
      throw error;
    }
  },

  // 重发消息
  resendMessage: async (sessionId: string, messageId: string) => {
    const store = useChatStore.getState();
    const message = store.getMessageById(sessionId, messageId);

    if (!message) {
      console.error('消息不存在');
      return;
    }

    // 重新发送
    store.updateMessage(sessionId, messageId, {
      status: MessageStatus.SENDING,
    });

    try {
      const result = await chatService.sendMessage({
        sessionId: message.sessionId,
        receiverId: message.receiverId,
        content: message.content,
        contentType: message.contentType,
        extra: message.extra,
      });

      if (result) {
        store.updateMessage(sessionId, messageId, {
          ...result,
          status: MessageStatus.SENT,
        });
      }
    } catch (error) {
      console.error('重发消息失败:', error);
      store.updateMessage(sessionId, messageId, {
        status: MessageStatus.FAILED,
      });
      Toast.fail('重发失败，请重试');
    }
  },

  // 标记消息为已读
  markMessageAsRead: async (sessionId: string, messageId: string) => {
    const store = useChatStore.getState();
    try {
      const message = store.getMessageById(sessionId, messageId);
      if (message && message.isRead) {
        return;
      }

      // 调用接口更新远程状态
      await chatService.markMessageAsRead(sessionId, messageId);

      // 更新本地状态
      store.updateMessage(sessionId, messageId, {
        isRead: true,
      });
    } catch (error) {
      console.error('标记消息已读失败:', error);
    }
  },

  // 创建会话
  createSession: async (AId: string, BId: string) => {
    console.log('createSession', AId, BId);
    const store = useChatStore.getState();
    store.setLoading(true);

    try {
      const session = await chatService.createSession(AId, BId);
      if (session) {
        store.addSession(session);
        // store.setCurrentSessionId(session.therapistId);
      }
      return session;
    } catch (error) {
      console.error('创建会话失败:', error);
      Toast.fail('创建会话失败，请稍后重试');
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 获取会话
  fetchSession: async ([AId, BId]: [string, string]) => {
    console.log('fetchSession', AId, BId);

    if (!AId || !BId) {
      useChatStore.getState().setError('参数错误，请稍后重试');
      return;
    }
    const sessionId = genSessionId(AId, BId);
    const store = useChatStore.getState();
    const sessionInStore = store.getSession(sessionId);
    if (sessionInStore) return sessionInStore;
    if (store.loading) return;
    store.setLoading(true);

    try {
      const session = await chatService.getSession(sessionId);
      if (session) {
        store.addSession(session);
        return session;
      } else {
        return await chatActions.createSession(AId, BId);
      }
    } catch (error) {
      console.error('chatActions fetchSession:', error);
      store.setError('获取会话失败，请稍后重试');
      // throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 获取会话列表
  fetchSessions: async () => {
    console.log('fetchSessions');
    const store = useChatStore.getState();
    store.setLoading(true);

    try {
      const sessions = await chatService.getSessions();
      store.setSessions(sessions);
      return sessions;
    } catch (error) {
      console.error('chatActions fetchSessions:', error);
      store.setError('获取会话列表失败，请稍后重试');
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 标记会话已读
  // markSessionAsRead: async (sessionId: string) => {
  //   console.log('markSessionAsRead', sessionId);
  //   const store = useChatStore.getState();

  //   try {
  //     await chatService.markAsRead(sessionId);
  //     store.markSessionAsRead(sessionId);
  //   } catch (error) {
  //     console.error('标记已读失败:', error);
  //   }
  // },

  // 上传图片
  uploadImage: async (sessionId: string, receiverId: string, file: string) => {
    const store = useChatStore.getState();
    const tempId = Math.random().toString(10).substring(2, 6); // 生成临时ID

    // 先添加一条发送中的消息
    store.addMessage(sessionId, {
      id: tempId,
      sessionId,
      senderId: 'currentUser', // 这里应该从用户信息中获取
      receiverId,
      content: '[图片]',
      contentType: MessageContentType.IMAGE,
      status: MessageStatus.SENDING,
      timestamp: Date.now(),
      extra: {
        uploading: true,
      },
    });

    try {
      const result = await uploadService.uploadPhotos([file]);

      // 发送图片消息
      const message = await chatService.sendMessage({
        sessionId,
        receiverId,
        content: '[图片]',
        contentType: MessageContentType.IMAGE,
        extra: {
          imageUrl: result[0],
        },
      });

      // 更新消息状态
      store.updateMessage(sessionId, tempId, {
        ...message,
        status: MessageStatus.SENT,
        extra: {
          ...message?.extra,
          uploading: false,
        },
      });

      return message;
    } catch (error) {
      console.error('上传图片失败:', error);
      // 更新消息状态为发送失败
      store.updateMessage(sessionId, tempId, {
        status: MessageStatus.FAILED,
        extra: {
          uploading: false,
          error: '上传失败',
        },
      });
      Toast.fail('上传失败，请重试');
      throw error;
    }
  },
};
