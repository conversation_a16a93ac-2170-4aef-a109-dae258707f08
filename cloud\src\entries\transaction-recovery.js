// 云函数入口文件
const cloud = require("wx-server-sdk");
const { coordinator } = require("../common/transaction-coordinator");
require("../common/transaction-handlers"); // 加载回滚处理函数

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 云函数入口函数
exports.main = async (event, context) => {
  console.log("事务恢复触发:", event);
  const { businessType, olderThanMinutes = 30 } = event;

  try {
    // 查找未完成的事务
    const pendingTransactions = await coordinator.findPendingTransactions({
      businessType,
      olderThanMinutes,
    });

    console.log(`找到 ${pendingTransactions.length} 个未完成的事务`);

    // 处理每个未完成的事务
    const results = [];
    for (const tx of pendingTransactions) {
      try {
        console.log(
          `处理未完成事务: ${tx._id}, 业务ID: ${tx.businessId}, 类型: ${tx.businessType}`
        );

        // 获取事务的最后一个已完成的步骤
        const lastCompletedStep = [...tx.steps]
          .reverse()
          .find((step) => step.status === "completed");

        // 获取事务的下一个待执行步骤
        const nextPendingStep = tx.steps.find(
          (step) => step.status === "pending"
        );

        if (nextPendingStep) {
          // 如果有待执行的步骤，尝试回滚事务
          console.log(
            `事务 ${tx._id} 有待执行的步骤: ${nextPendingStep.name}，执行回滚`
          );
          await coordinator.rollbackTransaction(tx._id, "定时恢复触发的回滚");
          results.push({
            txId: tx._id,
            businessId: tx.businessId,
            action: "rollback",
            status: "success",
          });
        } else if (
          lastCompletedStep &&
          lastCompletedStep === tx.steps[tx.steps.length - 1]
        ) {
          // 如果最后一个步骤已完成，尝试提交事务
          console.log(`事务 ${tx._id} 所有步骤已完成，执行提交`);
          await coordinator.commitTransaction(tx._id);
          results.push({
            txId: tx._id,
            businessId: tx.businessId,
            action: "commit",
            status: "success",
          });
        } else {
          // 其他情况，标记为需要人工干预
          console.log(`事务 ${tx._id} 状态异常，需要人工干预`);
          results.push({
            txId: tx._id,
            businessId: tx.businessId,
            action: "manual",
            status: "pending",
          });
        }
      } catch (txError) {
        console.error(`处理事务 ${tx._id} 失败:`, txError);
        results.push({
          txId: tx._id,
          businessId: tx.businessId,
          action: "error",
          error: txError.message,
          status: "failed",
        });
      }
    }

    // 清理已完成的旧事务
    const cleanupResult = await coordinator.cleanupCompletedTransactions(7); // 7天前的已完成事务

    return {
      totalPending: pendingTransactions.length,
      processed: results,
      cleaned: cleanupResult,
    };
  } catch (error) {
    console.error("事务恢复失败:", error);
    return {
      error: error.message,
      stack: error.stack,
    };
  }
};
