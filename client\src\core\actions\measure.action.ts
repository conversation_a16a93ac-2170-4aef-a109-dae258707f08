// measure.actions.ts
import { PsychologicalTestSummary } from '@model/measure.model';
import { TestCategory } from '@model/test.model';
import { measureService } from '@services/measure.service';
import { useMeasureStore } from '@stores/measure.store';

// 缓存策略配置
const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7天缓存

export const measureActions = {
  // 获取分类数据（静态数据，带缓存策略）
  fetchCategories: async (
    forceRefresh = false
  ): Promise<{ [key in TestCategory]: PsychologicalTestSummary[] }> => {
    const store = useMeasureStore.getState();

    try {
      // 检查缓存有效性
      const lastUpdated = store.lastUpdated || 0;
      const isCacheValid =
        !forceRefresh &&
        Date.now() - lastUpdated < CACHE_TTL &&
        Object.values(store.categories).some((category) => category.length > 0);

      if (isCacheValid) {
        console.log('[Measure] Using cached data');
        return store.categories;
      }

      store.setLoading(true);
      store.setError(null);

      // 获取完整数据
      const allMeasures = await measureService.getMeasures();
      const categories = allMeasures.reduce((acc, measure) => {
        if (!acc[measure.category]) {
          acc[measure.category] = [];
        }
        acc[measure.category].push(measure);
        return acc;
      }, {} as { [key in TestCategory]: PsychologicalTestSummary[] });
      // 更新存储（带时间戳）
      store.setCategories(categories);

      return categories;
    } catch (error) {
      store.setError(error as Error);

      // 缓存回退策略
      if (
        Object.values(store.categories).some((category) => category.length > 0)
      ) {
        console.warn('[Measure] Using cached data after error');
        return store.categories;
      }

      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 增量更新推荐卡片
  fetchFeaturedCards: async () => {
    const store = useMeasureStore.getState();

    try {
      store.setLoading(true);

      // 只获取推荐卡片数据
      const featuredCards = await measureService.getFeaturedCards();

      // 合并更新
      store.setFeaturedCards(featuredCards);

      return featuredCards;
    } catch (error) {
      store.setError(error as Error);
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 获取历史记录(无缓存)
  fetchRecentHistory: async (forceRefresh = false) => {
    const store = useMeasureStore.getState();

    if (!forceRefresh && store.recentHistory.length > 0) {
      return store.recentHistory;
    }
    const recentHistory = await measureService.getRecentHistory();
    store.setRecentHistory(recentHistory);
    return recentHistory;
  },

  // 加载历史测试记录
  fetchTestRecord: async (recordId: string): Promise<void> => {
    const store = useMeasureStore.getState();

    try {
      store.setLoading(true);
      store.setError(null);

      // 从本地历史记录中加载
      const recordItem = store.recentHistory.find((r) => r.id === recordId);

      // 如果本地没有，从服务器获取
      if (!recordItem) {
        const record = await measureService.getTestRecord(recordId);
        if (record) {
          store.setRecentHistory([record, ...store.recentHistory]);
        }
      }
    } catch (error) {
      console.error('加载测试记录失败:', error);
      store.setError(error as Error);
    } finally {
      store.setLoading(false);
    }
  },
};
