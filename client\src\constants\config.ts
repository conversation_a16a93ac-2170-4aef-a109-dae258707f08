import { ORDER_STATUS, REFUND_STATUS } from '@model/order.interface';

// API地址配置
export const API_HOST =
  process.env.NODE_ENV === 'production'
    ? 'https://api.mental-ai.com' // 生产环境API地址
    : 'https://dev-api.mental-ai.com'; // 开发环境API地址

// 上传文件配置
export const UPLOAD_CONFIG = {
  MAX_SIZE: 2 * 1024 * 1024, // 2MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
};

// 通用配置
export const COMMON_CONFIG = {
  CUSTOMER_SERVICE: '400-888-8888',
  WORK_TIME: '周一至周日 9:00-22:00',
};

// 状态码
export const STATUS_CODE = {
  SUCCESS: 0,
  ERROR: -1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
};
// 定义咨询师端订单状态分类
export const ORDER_TABS_THERAPIST = [
  {
    key: 'pending_payment',
    title: '待付款',
    statuses: [ORDER_STATUS.PENDING_PAYMENT],
    refundStatuses: [REFUND_STATUS.NONE],
  },
  {
    key: 'pending_confirm',
    title: '待确认',
    statuses: [ORDER_STATUS.PENDING_CONFIRM],
    refundStatuses: [REFUND_STATUS.NONE],
  },
  {
    key: 'in_progress',
    title: '进行中',
    statuses: [ORDER_STATUS.PENDING_START, ORDER_STATUS.IN_PROGRESS],
    refundStatuses: [REFUND_STATUS.NONE],
  },
  {
    key: 'refund',
    title: '退款/售后',
    statuses: [], // 不限制主状态
    refundStatuses: [
      REFUND_STATUS.AUDITING,
      REFUND_STATUS.PROCESSING,
      REFUND_STATUS.COMPLETED,
      REFUND_STATUS.FAILED,
      REFUND_STATUS.REJECTED,
    ],
  },
  {
    key: 'complaint',
    title: '投诉',
    statuses: [], // 这里需要根据实际业务逻辑添加投诉状态
    refundStatuses: [], // 不限制退款状态
  },
];

// 定义用户端订单状态分类
export const ORDER_TABS_USER = [
  {
    key: 'pending',
    title: '进行中',
    statuses: [
      ORDER_STATUS.PENDING_START,
      ORDER_STATUS.IN_PROGRESS,
      ORDER_STATUS.PENDING_PAYMENT,
      ORDER_STATUS.PENDING_CONFIRM,
    ],
    refundStatuses: [REFUND_STATUS.NONE], // 只显示无退款的订单
  },
  {
    key: 'completed',
    title: '已完成',
    statuses: [ORDER_STATUS.COMPLETED, ORDER_STATUS.REVIEWED],
    refundStatuses: [REFUND_STATUS.NONE], // 只显示无退款的订单
  },
  {
    key: 'cancelled',
    title: '已取消',
    statuses: [ORDER_STATUS.REJECTED, ORDER_STATUS.CANCELLED], // 已拒绝或已取消
    refundStatuses: [], // 不限制退款状态
  },
  {
    key: 'refund',
    title: '退款/售后',
    statuses: [], // 不限制主状态
    refundStatuses: [
      REFUND_STATUS.AUDITING,
      REFUND_STATUS.PROCESSING,
      REFUND_STATUS.COMPLETED,
      REFUND_STATUS.FAILED,
      REFUND_STATUS.REJECTED,
    ],
  },
];

export const SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_PAID = [
  'mc2U2YPCWNkm6xydDPkthrTDnlTov9uOtJ2VXNGD56M',
  'JKf2ZSpJJDcOHDl7gi7gqUftsO-P1k3IiyasAXWD0Mo',
  'gHHGPdvTRCFowvHypEQW9Ck6lAYw429KFQytJmCnTFg',
];
export const SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_CANCEL = [
  'mc2U2YPCWNkm6xydDPkthrTDnlTov9uOtJ2VXNGD56M',
  'JKf2ZSpJJDcOHDl7gi7gqUftsO-P1k3IiyasAXWD0Mo',
  'gHHGPdvTRCFowvHypEQW9Ck6lAYw429KFQytJmCnTFg',
];
// 默认工作时间
export const DEFAULT_WORK_TIME = {
  /** 开始时间 (小时, 0-23) */
  start: 9,
  /** 结束时间 (小时, 0-23) */
  end: 18,
  /** 工作日 (周一到周日, 0-6) */
  workDays: [1, 2, 3, 4, 5],
  /** 例外日 (每月不工作的日期) */
  exceptionDays: [],
};

export const TRUSTED_DOMAINS = ['trusted-domain.com', 'campaign.example.cn'];
