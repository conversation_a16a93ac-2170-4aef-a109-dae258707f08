import { TherapistListRequest } from '@core/api';
import { therapist_summary } from '@model/therapist.interface';
import { BaseCacheService } from './base-cache.service';
import {
  CACHE_PREFIX,
  favoriteTherapistsCacheOptions,
  recommendedTherapistsCacheOptions,
  therapistExtinfoCacheOptions,
  therapistListCacheOptions,
  therapistReviewsCacheOptions,
  therapistServiceCacheOptions,
  therapistSummaryCacheOptions,
} from './therapist-cache-policy';

/**
 * 咨询师缓存管理器
 * 封装缓存操作，统一缓存键的生成和管理
 */
export class TherapistCacheManager {
  /**
   * 设置咨询师列表缓存
   * @param params 请求参数
   * @param data 咨询师列表数据
   * @param pagination 分页信息
   */
  static setTherapistList(
    params: TherapistListRequest,
    data: therapist_summary[],
    pagination: any
  ): void {
    // 生成缓存键
    const cacheKey = therapistListCacheOptions.getCacheKey(params);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      {
        data,
        pagination,
      },
      therapistListCacheOptions.ttl,
      therapistListCacheOptions.getVersion()
    );
  }

  /**
   * 获取咨询师列表缓存
   * @param params 请求参数
   * @returns 缓存数据或null
   */
  static getTherapistList(
    params: TherapistListRequest
  ): { data: therapist_summary[]; pagination: any } | null {
    // 生成缓存键
    const cacheKey = therapistListCacheOptions.getCacheKey(params);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置咨询师基本信息缓存
   * @param id 咨询师ID
   * @param data 咨询师基本信息
   */
  static setTherapistSummary(id: string, data: any): void {
    // 生成缓存键
    const cacheKey = therapistSummaryCacheOptions.getCacheKey(id);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      therapistSummaryCacheOptions.ttl,
      therapistSummaryCacheOptions.getVersion()
    );
  }

  /**
   * 获取咨询师基本信息缓存
   * @param id 咨询师ID
   * @returns 缓存数据或null
   */
  static getTherapistSummary(id: string): any {
    // 生成缓存键
    const cacheKey = therapistSummaryCacheOptions.getCacheKey(id);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置咨询师详细信息缓存
   * @param id 咨询师ID
   * @param data 咨询师详细信息
   */
  static setTherapistExtInfo(id: string, data: any): void {
    // 生成缓存键
    const cacheKey = therapistExtinfoCacheOptions.getCacheKey(id);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      therapistExtinfoCacheOptions.ttl,
      therapistExtinfoCacheOptions.getVersion()
    );
  }

  /**
   * 获取咨询师详细信息缓存
   * @param id 咨询师ID
   * @returns 缓存数据或null
   */
  static getTherapistExtInfo(id: string): any {
    // 生成缓存键
    const cacheKey = therapistExtinfoCacheOptions.getCacheKey(id);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置咨询师服务缓存
   * @param id 咨询师ID
   * @param data 咨询师服务数据
   */
  static setTherapistService(id: string, data: any): void {
    // 生成缓存键
    const cacheKey = therapistServiceCacheOptions.getCacheKey(id);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      therapistServiceCacheOptions.ttl,
      therapistServiceCacheOptions.getVersion()
    );
  }

  /**
   * 获取咨询师服务缓存
   * @param id 咨询师ID
   * @returns 缓存数据或null
   */
  static getTherapistService(id: string): any {
    // 生成缓存键
    const cacheKey = therapistServiceCacheOptions.getCacheKey(id);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置推荐咨询师缓存
   * @param direction 推荐方向
   * @param data 推荐咨询师数据
   */
  static setRecommendedTherapists(
    direction: string | undefined,
    data: any
  ): void {
    // 生成缓存键
    const cacheKey = recommendedTherapistsCacheOptions.getCacheKey(direction);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      recommendedTherapistsCacheOptions.ttl,
      recommendedTherapistsCacheOptions.getVersion()
    );
  }

  /**
   * 获取推荐咨询师缓存
   * @param direction 推荐方向
   * @returns 缓存数据或null
   */
  static getRecommendedTherapists(direction?: string): any {
    // 生成缓存键
    const cacheKey = recommendedTherapistsCacheOptions.getCacheKey(direction);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置咨询师评价缓存
   * @param id 咨询师ID
   * @param page 页码
   * @param data 评价数据
   */
  static setTherapistReviews(id: string, page: number, data: any): void {
    // 生成缓存键
    const cacheKey = therapistReviewsCacheOptions.getCacheKey(id, page);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      therapistReviewsCacheOptions.ttl,
      therapistReviewsCacheOptions.getVersion()
    );
  }

  /**
   * 获取咨询师评价缓存
   * @param id 咨询师ID
   * @param page 页码
   * @returns 缓存数据或null
   */
  static getTherapistReviews(id: string, page = 1): any {
    // 生成缓存键
    const cacheKey = therapistReviewsCacheOptions.getCacheKey(id, page);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置收藏的咨询师缓存
   * @param userId 用户ID
   * @param data 收藏的咨询师数据
   */
  static setFavoriteTherapists(userId: string, data: any): void {
    // 生成缓存键
    const cacheKey = favoriteTherapistsCacheOptions.getCacheKey(userId);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      data,
      favoriteTherapistsCacheOptions.ttl,
      favoriteTherapistsCacheOptions.getVersion()
    );
  }

  /**
   * 获取收藏的咨询师缓存
   * @param userId 用户ID
   * @returns 缓存数据或null
   */
  static getFavoriteTherapists(userId: string): any {
    // 生成缓存键
    const cacheKey = favoriteTherapistsCacheOptions.getCacheKey(userId);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 清除咨询师列表缓存
   */
  static clearTherapistList(): void {
    BaseCacheService.clearByPrefix(therapistListCacheOptions.keyPrefix);
  }

  /**
   * 清除咨询师详情缓存
   * @param id 咨询师ID
   */
  static clearTherapistDetail(id: string): void {
    // 清除基本信息缓存
    BaseCacheService.remove(therapistSummaryCacheOptions.getCacheKey(id));
    // 清除详细信息缓存
    BaseCacheService.remove(therapistExtinfoCacheOptions.getCacheKey(id));
    // 清除服务缓存
    BaseCacheService.remove(therapistServiceCacheOptions.getCacheKey(id));
  }

  /**
   * 清除所有咨询师缓存
   */
  static clearAll(): void {
    // 清除所有咨询师相关缓存
    Object.values(CACHE_PREFIX).forEach((prefix) => {
      BaseCacheService.clearByPrefix(prefix);
    });
  }
}
