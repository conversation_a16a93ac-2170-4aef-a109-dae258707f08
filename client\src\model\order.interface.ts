import { ConsultationDirection, INITATOR_SOURCE } from './common.interface';
import { ServiceType } from './service.interface';

export interface Order_summary {
  /** 订单ID */
  _id: string;
  /** 用户ID */
  userId: string;
  /** 用户头像 冗余字段*/
  userAvatar?: string;
  /** 用户名称 冗余字段*/
  userName?: string;
  /** 咨询师ID */
  therapistId: string;
  /** 咨询师头像 冗余字段*/
  therapistAvatar?: string;
  /** 咨询师名称 冗余字段*/
  therapistName?: string;
  /** 订单主状态 */
  status: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS];
  /** 退款状态 */
  refundStatus: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS];
  /** 服务类型 */
  serviceType: ServiceType;
  /** 服务价格 */
  price: number;
  /** 服务时长 */
  duration: number;
  /** 服务开始时间 */
  startTime: number; // date in milliseconds
  /** 服务结束时间 */
  endTime: number; // date in milliseconds
  /** 服务地点 */
  location: string;

  /** 用户提交的咨询信息 */
  consultationInfo: {
    /** 用户称呼 */
    name?: string;
    /** 咨询方向 */
    direction?: ConsultationDirection;
    /** 咨询描述 */
    desc?: string;
  };

  /** 是否有投诉 */
  hasComplaint: boolean;

  /** 创建时间 */
  createdAt: number; // date in milliseconds
  /** 更新时间 */
  updatedAt: number; // date in milliseconds
}
/** 主订单状态, 与退款状态独立，主状态的任何状态都可能存在退款 */
export const ORDER_STATUS = {
  // 待处理状态
  PENDING_PAYMENT: 10, // 待支付
  PENDING_CONFIRM: 11, // 待确认
  // 活跃状态
  PENDING_START: 20, // 待开始
  IN_PROGRESS: 21, // 进行中
  // 结束状态
  COMPLETED: 30, // 已完成
  REVIEWED: 31, // 已评价
  REJECTED: 40, // 已拒绝
  CANCELLED: 41, // 已取消
};

/** 退款状态 */
export const REFUND_STATUS = {
  NONE: 0, // 无退款
  AUDITING: 1, // 退款审核中
  REJECTED: 2, // 退款拒绝
  PROCESSING: 3, // 退款中
  COMPLETED: 4, // 已退款
  FAILED: 5, // 退款失败
};

// 操作类型枚举
export enum ACTION_TYPE {
  CREATE = 0, // 创建订单
  PAY = 1, // 支付
  SUBMIT_USER_INFO = 2, // 提交用户信息
  ACCEPT = 3, // 接受
  REJECT = 4, // 拒绝
  CANCEL = 5, // 取消
  START = 6, // 开始服务
  COMPLETE = 7, // 完成服务
  REVIEW = 8, // 评价
  REFUND_REQUEST = 9, // 退款申请
  REFUND_AUDIT_PASS = 10, // 退款审核通过
  REFUND_AUDIT_REJECT = 11, // 退款审核拒绝
  REFUND_REFUNDING = 12, // 退款中
  REFUND_COMPLETE = 13, // 退款完成
  REFUND_FAILED = 14, // 退款失败
  REFUND_ROLLBACK = 15, // 退款回滚
}

export interface Order_action {
  _id: string;
  orderId: string; // 关联订单ID
  action: ACTION_TYPE;
  initiator: INITATOR_SOURCE; // 发起方
  actionTime: number; // unix timestamp in milliseconds
  extraData?: any; // 扩展数据
}

/** 退款审核方式 */
export enum REFUND_AUDIT_TYPE {
  /** 自动审核 */
  AUTO = 0,
  /** 人工审核 */
  MANUAL = 1,
}

/** 退款方式 */
export enum REFUND_TYPE {
  /** 原路退回 */
  BACK_TO_ORIGINAL = 0,
  /** 退款到余额 */
  REFUND_TO_BALANCE = 1,
}

/** 取消信息 */
export interface OrderCancelInfo {
  /** 取消来源 */
  source: INITATOR_SOURCE;
  /** 取消原因 */
  reason: CANCEL_REASON;
  /** 取消详情 */
  detail?: string;
}

export enum CANCEL_REASON {
  TIME_CONFLICT = 0,
  FOUND_BETTER = 1,
  PRICE_CONCERN = 2,
  FORCE_MAJEURE = 3,
  // 请假
  LEAVE = 4,
  // 其他
  OTHER = 5,
}

export enum REJECT_REASON {
  TIME_CONFLICT = 0,
  FOUND_UNSUITABLE = 1, // 不适合的咨询问题
  PRICE_CONCERN = 2, // 价格原因
  FORCE_MAJEURE = 3, // 不可抗力
  OTHER = 4, // 其他
}

export enum REFUND_REASON {
  LEAVE = 0, // 请假
  FORCE_MAJEURE = 1, // 不可抗力
  OTHER = 2, // 其他原因
}

export interface Order_review {
  /** 评价ID */
  _id: string;
  /**评价用户id */
  userId: string;
  /** 用户头像 */
  avatar: string;
  /** 用户名称 */
  userName: string;
  /**被评价者id */
  therapistId: string;
  /** 订单id */
  orderId: string;
  /** 服务类型 */
  service: ServiceType;
  /** 评价内容 */
  content: string;

  /** 评分 */
  score: number;
  /** 标签 */
  tags: string[];

  /** 是否匿名 */
  isAnonymous?: boolean;

  /** 创建时间 */
  createdAt: number;
}

/** 退款审核订单 */
export interface RefundAuditOrder {
  _id: string;
  orderId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  therapistId: string;
  therapistName: string;
  therapistAvatar: string;
  serviceType: ServiceType;
  /** 退款状态 */
  refundStatus: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS];
  /** 退款金额 */
  refundAmount: number;
  /** 退款原因 */
  refundReason: REFUND_REASON;
  /** 退款详情 */
  refundDetail: string;
  /** 退款时间 */
  refundTime: number;
}
