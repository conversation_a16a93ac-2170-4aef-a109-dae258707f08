import Taro from '@tarojs/taro';
import { CacheData, CacheState } from './cache-types';

/**
 * 基础缓存服务
 * 提供缓存数据的存储、获取和验证功能
 */
export class BaseCacheService {
  // 内存缓存存储对象
  private static cacheStore: Record<string, CacheData<any>> = {};

  /**
   * 设置缓存数据
   * @param key 缓存键名
   * @param data 缓存数据
   * @param ttl 过期时间(毫秒)
   * @param version 缓存版本
   */
  static set(key: string, data: any, ttl = 60 * 1000, version = '1.0'): void {
    const now = Date.now();
    this.cacheStore[key] = {
      data,
      timestamp: now,
      expireAt: now + ttl,
      version,
    };

    // 异步持久化到本地存储
    this.persistToStorage(key);
  }

  /**
   * 获取缓存数据
   * @param key 缓存键名
   * @param defaultValue 默认值
   * @returns 缓存数据或默认值
   */
  static get(key: string, defaultValue: any = null): any {
    // 先尝试从内存获取
    let cacheData = this.cacheStore[key];

    // 没有从内存获取到，尝试从本地存储获取
    if (!cacheData) {
      const storedData = this.loadFromStorage(key);
      if (storedData) {
        cacheData = storedData;
        // 如果从本地存储获取到，更新内存缓存
        this.cacheStore[key] = storedData;
      }
    }

    // 检查缓存状态
    if (cacheData) {
      const cacheState = this.checkCacheState(cacheData);

      if (cacheState === CacheState.VALID) {
        return cacheData.data;
      }
    }

    return defaultValue;
  }

  /**
   * 检查缓存状态
   * @param cacheData 缓存数据
   * @returns 缓存状态
   */
  static checkCacheState(cacheData: CacheData<any>): CacheState {
    const now = Date.now();

    // 检查是否过期
    if (cacheData.expireAt < now) {
      return CacheState.EXPIRED;
    }

    return CacheState.VALID;
  }

  /**
   * 删除缓存
   * @param key 缓存键名
   */
  static remove(key: string): void {
    delete this.cacheStore[key];

    try {
      wx.removeStorageSync(`cache:${key}`);
    } catch (e) {
      console.error('删除本地存储缓存失败:', e);
    }
  }

  /**
   * 清除所有缓存
   */
  static clear(): void {
    this.cacheStore = {};

    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach((key) => {
        if (key.startsWith('cache:')) {
          wx.removeStorageSync(key);
        }
      });
    } catch (e) {
      console.error('清除本地存储缓存失败:', e);
    }
  }

  /**
   * 获取所有匹配前缀的缓存键
   * @param prefix 缓存键前缀
   * @returns 匹配的缓存键数组
   */
  static getKeys(prefix: string): string[] {
    // 从内存缓存中获取匹配的键
    const memoryKeys = Object.keys(this.cacheStore).filter((key) =>
      key.startsWith(prefix)
    );

    // 从本地存储获取匹配的键
    try {
      const storageInfo = wx.getStorageInfoSync();
      const storageKeys = storageInfo.keys
        .filter((key) => key.startsWith(`cache:${prefix}`))
        .map((key) => key.substring(6)); // 移除'cache:'前缀

      // 合并内存和存储的键，并去重
      return [...new Set([...memoryKeys, ...storageKeys])];
    } catch (e) {
      console.error('获取本地存储缓存键失败:', e);
      return memoryKeys;
    }
  }

  /**
   * 清除所有匹配前缀的缓存
   * @param prefix 缓存键前缀
   */
  static clearByPrefix(prefix: string): void {
    const keys = this.getKeys(prefix);
    keys.forEach((key) => this.remove(key));
  }

  /**
   * 持久化缓存到本地存储
   * @param key 缓存键名
   */
  private static persistToStorage(key: string): void {
    const data = this.cacheStore[key];
    if (!data) return;

    try {
      Taro.setStorageSync(`cache:${key}`, JSON.stringify(data));
    } catch (e) {
      console.error('持久化缓存失败:', e);
    }
  }

  /**
   * 从本地存储加载缓存
   * @param key 缓存键名
   * @returns 缓存数据或null
   */
  private static loadFromStorage(key: string): CacheData<any> | null {
    try {
      const data = Taro.getStorageSync(`cache:${key}`);
      if (data) {
        return JSON.parse(data);
      }
    } catch (e) {
      console.error('读取本地存储缓存失败:', e);
    }

    return null;
  }
}
