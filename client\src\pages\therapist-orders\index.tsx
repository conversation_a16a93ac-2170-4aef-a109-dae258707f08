import { Result } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { ORDER_TABS_THERAPIST } from '@constants/config';
import { useOrderList } from '@hooks/useOrderList';
import { View } from '@tarojs/components';
import {
  useDidHide,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
  useReady,
  useRouter,
  useUnload,
} from '@tarojs/taro';
import { useCallback, useEffect, useRef, useState } from 'react';
import OrderStatusTabs from '../sub-packages/orders/components/OrderStatusTabs';
import OrderTimeline from '../sub-packages/orders/components/OrderTimeline';

export default function TherapistOrdersPage() {
  const router = useRouter();
  const { tab } = router.params;
  const [activeTab, setActiveTab] = useState(
    tab ? ORDER_TABS_THERAPIST.findIndex((t) => t.key === tab) : -1
  );

  const renderCount = useRef(0);

  // 安全的增加计数
  renderCount.current += 1;

  console.log('TherapistOrdersPage 安全渲染次数:', renderCount.current);

  // 根据路由参数确定初始筛选条件
  const initialParams = useCallback(() => {
    if (!tab) return {};

    const tabIndex = ORDER_TABS_THERAPIST.findIndex((t) => t.key === tab);
    if (tabIndex === -1) return {};

    // 特殊处理投诉标签
    if (tab === 'complaint') {
      return { complaint: true };
    }

    const tabConfig = ORDER_TABS_THERAPIST[tabIndex];
    return {
      status: tabConfig.statuses,
      refundStatus: tabConfig.refundStatuses,
    };
  }, [tab]);

  const {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    onStatusChange,
    onSearch,
  } = useOrderList({
    params: initialParams(),
  });

  // 监听 tab 参数变化，手动调用 onStatusChange
  useEffect(() => {
    console.log('TherapistOrdersPage useEffect', tab);
    if (tab) {
      const tabIndex = ORDER_TABS_THERAPIST.findIndex((t) => t.key === tab);
      if (tabIndex !== -1) {
        setActiveTab(tabIndex);
        // 特殊处理投诉标签
        if (tab === 'complaint') {
          onStatusChange([], true);
        } else {
          const tabConfig = ORDER_TABS_THERAPIST[tabIndex];
          onStatusChange(tabConfig.statuses, false, tabConfig.refundStatuses);
        }
      } else {
        onStatusChange([]);
      }
    } else {
      onStatusChange([]);
    }
  }, [tab, onStatusChange]);

  // // 处理下拉刷新
  // const handlePullDownRefresh = useCallback(async () => {
  //   await refreshOrders();
  // }, [refreshOrders]);

  // // 处理重试
  // const handleRetry = useCallback(() => {
  //   refreshOrders();
  // }, [refreshOrders]);

  // 监听滚动到底部事件，实现上拉加载更多
  useReachBottom(() => {
    if (pagination && pagination.hasNext && !loading) {
      loadMoreOrders();
    }
  });

  useDidHide(() => {
    console.log('TherapistOrdersPage useDidHide');
  });
  useUnload(() => {
    console.log('TherapistOrdersPage useUnload');
  });
  usePullDownRefresh(() => {
    console.log('onPullDownRefresh');
  });
  useReady(() => {
    console.log('onReady');
  });
  useLoad(() => {
    console.log('onLoad');
  });
  // 渲染订单时间线
  const genTimelineData = useCallback(() => {
    if (orders.length === 0) {
      return {}; // 返回空对象，而不是JSX
    }

    // 按日期分组订单
    const ordersByDate = orders.reduce((acc, order) => {
      const date = new Date(order.updatedAt);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;

      if (!acc[dateStr]) {
        acc[dateStr] = [];
      }
      acc[dateStr].push(order);
      return acc;
    }, {} as Record<string, typeof orders>);

    return ordersByDate;
  }, [orders]);

  return (
    <PageLol
      useNav
      navigationProps={{
        showBackButton: false,
        showSearch: true,
        searchPlaceholder: '输入订单号或者用户名',
        onSearch: onSearch,
      }}
      onPullDownRefresh={refreshOrders}
      onRetry={refreshOrders}
      isEmpty={!loading && orders.length === 0}
    >
      {/* 订单状态筛选组 */}
      <OrderStatusTabs
        tabs={ORDER_TABS_THERAPIST}
        activeKey={ORDER_TABS_THERAPIST[activeTab]?.key || ''}
        onChange={(key) => {
          const tabIndex = ORDER_TABS_THERAPIST.findIndex((t) => t.key === key);
          if (tabIndex !== -1) {
            if (tabIndex !== activeTab) {
              setActiveTab(tabIndex);
              // 特殊处理投诉标签
              if (key === 'complaint') {
                onStatusChange([], true);
              } else {
                const tabConfig = ORDER_TABS_THERAPIST[tabIndex];
                onStatusChange(
                  tabConfig.statuses,
                  false,
                  tabConfig.refundStatuses
                );
              }
            } else {
              // 一个都不选时，切换到全部订单
              setActiveTab(-1);
              onStatusChange([]);
            }
          }
        }}
      />

      {/* 订单生命线 */}
      {orders.length > 0 && <OrderTimeline data={genTimelineData()} />}

      {/* {orders.length === 0 && !loading && (
        <Result type='info' title='暂无订单' message='暂无订单' />
      )} */}

      {/* 加载更多状态 */}
      {loading && <Result type='wait' title='加载中...' />}

      {/* 全部加载完成 */}
      {!loading && pagination && !pagination.hasNext && orders.length > 0 && (
        <View className='py-4 text-center text-secondary text-sm'>
          没有更多数据了
        </View>
      )}

      {/* 错误提示 */}
      {/* {error && <Result type='error' title='加载失败' message={error} />} */}
    </PageLol>
  );
}
