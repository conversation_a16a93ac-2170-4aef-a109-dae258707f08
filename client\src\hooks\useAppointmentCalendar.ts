import { DEFAULT_WORK_TIME } from '@constants/config';
import { schedule_item, work_time } from '@model/therapist.interface';
import { getDayInMillis, getDayInMillisFromDateInMillis } from '@utils/time';
import { useEffect, useRef, useState } from 'react';

interface UseCalendarProps {
  occupancy?: schedule_item[];
  selectedDateUnixStamp?: number;
  selectedTimeSlotInHour?: number;
  workTime?: work_time;
}

export const useCalendar = ({
  occupancy,
  selectedDateUnixStamp,
  selectedTimeSlotInHour,
  workTime,
}: UseCalendarProps) => {
  // 当前选中的日期和时间
  const [selectedDate, setSelectedDate] = useState<number | null>(
    selectedDateUnixStamp
      ? getDayInMillisFromDateInMillis(selectedDateUnixStamp)
      : null
  );
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<number | null>(
    selectedTimeSlotInHour ? selectedTimeSlotInHour : null
  );

  // 日期可用性状态
  const [datesToShow, setDatesToShow] = useState<number[]>([]);
  const [dateAvailability, setDateAvailability] = useState<
    Record<number, boolean>
  >({});

  // 日期可用性控制
  const [isAvailableDay, setIsAvailableDay] = useState<boolean>(true);

  // 使用 ref 来跟踪是否已经设置了初始日期，避免无限循环
  const hasSetInitialDate = useRef(false);

  useEffect(() => {
    console.log('useAppointmentCalendar useEffect', occupancy, workTime);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const dates: number[] = [];
    let currentDate = today;

    // 生成接下来7天
    while (currentDate <= endOfMonth || dates.length < 7) {
      dates.push(getDayInMillis(currentDate));
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000); // 添加一天
    }
    // 合并添加occupancy中的日期
    if (occupancy) {
      // 如果occupancy中的日期在dates中不存在，则添加到dates中
      occupancy.forEach((item) => {
        if (!dates.includes(item.date)) {
          dates.push(item.date);
        }
      });
    }
    // 排序
    dates.sort((a, b) => a - b);

    // 设置日期选择器显示的日期
    setDatesToShow(dates);

    // 构建日期可用性映射
    const availabilityMap: Record<number, boolean> = {};

    // 默认所有日期不可用
    dates.forEach((date) => {
      availabilityMap[date] = false;
    });

    // 根据workTime更新可用日期
    const workTimes = workTime || DEFAULT_WORK_TIME;
    dates.forEach((dateMillis) => {
      const dateObj = new Date(dateMillis);
      const weekDay = dateObj.getDay(); // 0是周日，1-6是周一到周六
      // 检查该日期的星期几是否在工作日列表中
      if (workTimes.workDays?.includes(weekDay)) {
        availabilityMap[dateMillis] = true;
      }
      // 检查是否是例外日，例外日不工作
      if (workTimes.exceptionDays?.includes(dateObj.getDate())) {
        availabilityMap[dateMillis] = false;
      }
    });

    // 根据occupancy更新不可用日期
    if (occupancy) {
      //每个item代表一天
      occupancy.forEach((item) => {
        // available存在且为true
        if (item.available !== undefined && item.available === false) {
          availabilityMap[item.date] = false;
        } else {
          availabilityMap[item.date] = true;
        }
      });
    }

    setDateAvailability(availabilityMap);

    // 找到第一个可用日期并选择它，但只在没有设置过初始日期时设置
    const firstAvailableDate = dates.find((date) => availabilityMap[date]);
    if (firstAvailableDate && !selectedDate && !hasSetInitialDate.current) {
      hasSetInitialDate.current = true;
      setSelectedDate(firstAvailableDate);
    }
  }, [occupancy, workTime]);

  // 判断日期是否可用
  const isDateAvailable = (date: number) => {
    return dateAvailability[date] ?? true;
  };

  // 获取特定日期的占用时间槽
  const getOccupiedSlots = (date: number) => {
    // 在occupancy.schedule中找到匹配的日期
    if (!occupancy) return [];

    const scheduleItem = occupancy.find((item) => item.date === date);

    return scheduleItem?.slots || [];
  };

  // 判断时间段是否可用
  const isTimeSlotAvailable = (hour: number) => {
    if (!selectedDate) return false;

    // 检查日期是否可用
    if (!isDateAvailable(selectedDate)) return false;

    // 如果是今天，检查时间是否已过
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const selectedDateObj = new Date(selectedDate);

    if (selectedDateObj.getTime() === today.getTime()) {
      // 如果是今天，只显示当前时间之后的时间段
      if (hour <= now.getHours()) {
        return false;
      }
    }

    // 获取该日期的占用时间槽
    const occupiedSlots = getOccupiedSlots(selectedDate);

    // slots数组中包含的时间戳表示已被占用
    return !occupiedSlots.includes(hour);
  };

  // 获取可用的时间段
  const getWorkTimeSlots = () => {
    if (!selectedDate) return [];
    const workTimes = workTime || DEFAULT_WORK_TIME;
    const slots: number[] = [];
    for (let hour = workTimes.start; hour <= workTimes.end; hour++) {
      slots.push(hour);
    }
    return slots;
  };

  // 处理日期选择 date: 时间戳in millis
  const handleDateSelect = (date: number) => {
    console.log('handleDateSelect', date);
    if (isDateAvailable(date)) {
      setSelectedDate(date);
      setSelectedTimeSlot(null);
      // 检查当前日期的可用状态
      const scheduleItem = occupancy?.find((item) => item.date === date);
      setIsAvailableDay(scheduleItem?.available ?? true);
    }
  };

  // 处理时间段选择
  const handleTimeSlotSelect = (hour: number) => {
    console.log('handleTimeSlotSelect', hour);
    if (selectedDate && isTimeSlotAvailable(hour)) {
      setSelectedTimeSlot(hour);
    }
  };

  // 处理日历日期选择
  const handleCalendarDateSelect = (date: number) => {
    console.log('handleCalendarDateSelect', date);
    // 第一步，检查加入到datesToShow中
    if (!datesToShow.includes(date)) {
      setDatesToShow((prev) => [...prev, date]);
    }
    // 第二步，设置selectedDate
    handleDateSelect(date);
  };

  // 切换日期可用性
  const toggleDateAvailability = (available: string) => {
    console.log('toggleDateAvailability', available);
    setIsAvailableDay(available === 'true');
    // 如果false，则将selectedTimeSlot设置为null
    if (available === 'false') {
      setSelectedTimeSlot(null);
    }
  };

  // 格式化小时为时间字符串
  const formatHour = (hour: number) => {
    return `${hour}:00`;
  };

  return {
    selectedDate,
    selectedTimeSlot,
    datesToShow,
    isDateAvailable,
    isTimeSlotAvailable,
    getWorkTimeSlots,
    handleDateSelect,
    handleTimeSlotSelect,
    handleCalendarDateSelect,
    formatHour,
    isAvailableDay,
    toggleDateAvailability,
  };
};
