import { therapistActions } from '@core/actions/therapist.action';
import { useTherapistStore } from '@stores/therapist.store';
import { useEffect } from 'react';

export function useTherapistSummary(therapistId: string) {
  const therapist = useTherapistStore((state) =>
    state.therapists?.find((it) => it.id === therapistId)
  );
  useEffect(() => {
    if (therapistId && therapistId.trim() !== '') {
      therapistActions.fetchTherapistSummary(therapistId);
    }
  }, [therapistId]);
  return therapist;
}
