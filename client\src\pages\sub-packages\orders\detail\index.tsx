// client/src/pages/sub-packages/orders/detail/index.tsx
import { Button } from '@antmjs/vantui';
import ActionBar from '@components/common/ActionBar';
import PageLol from '@components/common/page-meta';
import { PhoneButton } from '@components/common/PhoneButton';
import TherapistSimpleCard from '@components/therapist/TherapistSimpleCard';
import { ORDER_STATUS_MAP, REFUND_STATUS_MAP } from '@constants/text';
import { appointmentActions } from '@core/actions/appointment.action';
import { paymentActions } from '@core/actions/payment.action';
import { useOrderDetail } from '@hooks/useOrderDetail';
import useRenderCount from '@hooks/useRenderCount';
import { ORDER_STATUS, REFUND_STATUS } from '@model/order.interface';
import { OrderDetailShared } from '@pages/sub-packages/orders/components/OrderDetailShared';
import { useLoadingStore } from '@stores/loading.store';
import { useRouter } from '@tarojs/taro';
import { appRouter, orderRouter } from '@utils/router';

export default function OrderDetailPage() {
  const router = useRouter();
  const orderId = router.params.orderId as string;
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  // 使用自定义hook获取订单详情
  const { order, orderActions, therapist, orderReview, refresh } =
    useOrderDetail(orderId);

  // 使用自定义Hook跟踪渲染次数
  useRenderCount('OrderDetailPage');

  const orderAgainButton = () => {
    if (!order || !therapist) {
      return null;
    }
    return (
      <Button
        className='flex-1'
        plain
        hairline
        type='primary'
        loading={transactionLoading}
        disabled={transactionLoading}
        onClick={() => {
          setTransactionLoading(true);
          appointmentActions.startAppointment({
            from: 'order',
            orderId,
            therapistId: therapist!.id,
          });
        }}
      >
        再来一单
      </Button>
    );
  };
  const renderOrderDetailActions = () => {
    if (!orderId || !order || !therapist) {
      return null;
    }

    const leftButtons = [
      {
        icon: 'delete-o',
        text: '取消订单',
        onClick: () => {
          orderRouter.cancel(orderId);
        },
      },
    ];
    if (order.status === ORDER_STATUS.PENDING_PAYMENT) {
      leftButtons.push({
        icon: 'chat-o',
        text: '消息',
        onClick: () => {
          appRouter.chat(therapist.id);
        },
      });
    } else if (order.status <= ORDER_STATUS.PENDING_START) {
      leftButtons.push(
        //补充信息
        {
          icon: 'edit-o',
          text: '补充信息',
          onClick: () => {
            orderRouter.submitInfo(orderId, false, 'detail');
          },
        }
      );
    }

    switch (order.status) {
      case ORDER_STATUS.PENDING_PAYMENT:
        return (
          <ActionBar leftButtons={leftButtons}>
            {/* 去支付 */}
            <Button
              type='primary'
              round
              className='flex-1'
              loading={transactionLoading}
              disabled={transactionLoading}
              onClick={() => {
                setTransactionLoading(true);
                paymentActions.startPayment(orderId);
              }}
            >
              去支付
            </Button>
          </ActionBar>
        );
      case ORDER_STATUS.PENDING_CONFIRM:
        return (
          <ActionBar leftButtons={leftButtons}>
            {/* 拨打电话  */}
            <PhoneButton orderId={orderId} personId={order.therapistId} />
          </ActionBar>
        );
      case ORDER_STATUS.PENDING_START:
        return (
          <ActionBar leftButtons={leftButtons}>
            {/* 拨打电话  */}
            <PhoneButton orderId={orderId} personId={order.therapistId} />
          </ActionBar>
        );
      case ORDER_STATUS.COMPLETED:
        return (
          <ActionBar>
            {/* 再来一单, 开启预约流程 */}
            {orderAgainButton()}

            {/* 评价 */}
            <Button
              className='flex-1'
              round
              size='normal'
              type='primary'
              loading={transactionLoading}
              disabled={transactionLoading}
              onClick={() => orderRouter.review(orderId)}
            >
              去评价
            </Button>
          </ActionBar>
        );
      case ORDER_STATUS.REVIEWED:
        return (
          //纯文字按钮
          <ActionBar>{orderAgainButton()}</ActionBar>
        );
      default:
        return null;
    }
  };

  if (!order || !therapist) {
    return null;
  }

  // 获取显示的状态文本
  const getStatusText = () => {
    // 如果有退款状态且不是无退款，优先显示退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      return REFUND_STATUS_MAP[order.refundStatus];
    }
    // 否则显示订单主状态
    return ORDER_STATUS_MAP[order.status] || '订单详情';
  };

  return (
    <PageLol
      useNav
      navigationProps={{
        title: getStatusText(),
        showBackButton: true,
      }}
      onRetry={refresh}
      error={null}
      onPullDownRefresh={refresh}
    >
      <OrderDetailShared
        order={order}
        orderActions={orderActions}
        orderReview={orderReview}
        personCard={
          <TherapistSimpleCard therapist={therapist} className='mb-6' />
        }
        renderOrderDetailActions={renderOrderDetailActions}
      />
    </PageLol>
  );
}
