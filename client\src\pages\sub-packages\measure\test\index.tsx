import { Button, Checkbox } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { testAction } from '@core/actions/test.action';
import { useTestStore } from '@stores/test.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

/**
 * 测量表介绍页（TestIntroPage）
 * 展示测量表信息、知情同意书、同意勾选、开始测试按钮
 */
export default function TestIntroPage() {
  const router = useRouter();
  const { testId } = router.params;
  const test = useTestStore.use.testList().find((it) => it.id === testId);
  const [consentChecked, setConsentChecked] = useState(false);

  useEffect(() => {
    // 拉取测量表信息
    if (testId) {
      // 只拉取测量表信息，不初始化答题流程
      testAction.fetchTest(testId);
    }
  }, [testId]);

  const handleStart = () => {
    if (!consentChecked) {
      Taro.showToast({ title: '请先同意知情说明', icon: 'none' });
      return;
    }
    Taro.navigateTo({
      url: `/pages/sub-packages/measure/test/doing?testId=${testId}`,
    });
  };

  return (
    <PageLol
      navigationProps={{
        title: test?.shortTitle || '心理测量',
        showBackButton: true,
        onBack: () => Taro.navigateBack(),
      }}
    >
      <View className='flex flex-col h-full p-4'>
        {/* 测量表信息 */}
        <Text className='text-xl font-bold mb-2'>{test?.title}</Text>
        <Text className='text-secondary text-sm mb-4'>{test?.description}</Text>

        <Text className='text-default font-medium text-md mb-4'>
          {test?.instructions}
        </Text>
        <View className='flex flex-row justify-between'>
          <Text className='text-secondary text-sm'>
            题目数量：{test?.howmany ?? 0}
          </Text>
          {test?.duration && (
            <Text className='text-secondary text-sm ml-4'>
              预计时间：{test?.duration}分钟
            </Text>
          )}
        </View>
        {/* 知情同意书 */}
        <View className='bg-gray-100 p-4 rounded mb-4 mt-4'>
          <Text className='font-bold mb-2 block'>知情同意书</Text>
          <Text>
            本测量仅用于心理健康评估，数据将严格保密，参与完全自愿，您可随时退出。如有疑问请联系工作人员。
          </Text>
        </View>

        {/* 同意勾选 */}
        <Checkbox
          value={consentChecked}
          onChange={(e) => setConsentChecked(e.detail)}
          className='mb-4'
        >
          我已阅读并同意知情说明
        </Checkbox>

        {/* 开始测试按钮 */}
        <Button
          type='primary'
          round
          block
          disabled={!consentChecked}
          onClick={handleStart}
        >
          开始测试
        </Button>
      </View>
    </PageLol>
  );
}
