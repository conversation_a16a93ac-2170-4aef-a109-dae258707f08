import { SUCCESS_CODE } from '@core/api';
import { ConsultationDirection } from '@model/common.interface';
import { LearningCard } from '@model/course.interface';
import { COLLECTIONS } from '@model/db.model';
import { Banner, Notice, OrderToday } from '@model/home.interface';
import { therapist_summary } from '@model/therapist.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class HomeService extends BaseService<Notice> {
  constructor() {
    super(COLLECTIONS.NOTICE);
  }

  async fetchNotices(to: 'user' | 'therapist') {
    try {
      const result = await this.directRead({ active: true, to });
      return result as Notice[];
    } catch (error) {
      console.error('获取公告失败:', error);
      throw error;
    }
  }

  async fetchBanners(to: 'user' | 'therapist') {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.BANNER)
        .where({ active: true, to })
        .orderBy('displayOrder', 'asc')
        .get();
      return result.data as Banner[];
    } catch (error) {
      console.error('获取Banner失败:', error);
      throw error;
    }
  }

  /**
   * 获取推荐咨询师列表
   * @param direction 可选的分类参数
   * @param limit 返回数量限制
   */
  async readRecommendedTherapists(
    direction?: ConsultationDirection,
    limit: number = 10
  ): Promise<therapist_summary[]> {
    console.log(
      'therapistService fetchRecommendedTherapists',
      direction,
      limit
    );
    try {
      // 初始化数据库
      const db = Taro.cloud.database();

      // 构建查询条件
      let query: any = {
        status: 'active',
      };

      const _ = db.command;
      if (direction) {
        // 查询方向, 方向是数组,查找包含direction的
        query['directions'] = _.in([direction]);
      }

      // 查询推荐咨询师
      const result = await db
        .collection(COLLECTIONS.THERAPIST)
        .where(query)
        .orderBy('favoriteCount', 'desc')
        .limit(limit)
        .get();

      return result.data as therapist_summary[];
    } catch (error) {
      console.error('获取推荐咨询师列表失败:', error);
      throw error;
    }
  }

  async fetchRecommendedCourses() {
    try {
      const result = await callCloudFunction('learn', 'getFeaturedCourses');
      if (result && result.code === SUCCESS_CODE) {
        return result.data as LearningCard[];
      }
      throw new Error(result.message);
    } catch (error) {
      console.error('获取推荐课程失败:', error);
      throw error;
    }
  }

  async fetchOrderToday() {
    try {
      const result = await callCloudFunction('order', 'getOrderToday');
      console.log('fetchOrderToday result', result);
      if (result && result.code === SUCCESS_CODE) {
        return result.data as OrderToday;
      }
      throw new Error(result.message);
    } catch (error) {
      console.error('获取订单统计失败:', error);
      throw error;
    }
  }
}

export const homeService = new HomeService();
