import { userService } from '@services/user.service';
import { useUserStore } from '@stores/user.store';

/**
 * 获取平台其它用户(包含咨询师)个人资料(公开信息/敏感信息)页面相关操作
 */
export const userActions = {
  /**
   * 获取用户公开信息
   */
  fetchUserPublic: async (userId: string, forceRefresh = false) => {
    console.log('userActions fetchUserPublic', userId, forceRefresh);
    let result;
    if (forceRefresh) {
      result = await userService.getUserInfo(userId);
      if (result) {
        // 更新状态
        useUserStore.getState().updateUser(result);
      }
      return result;
    }
    const user = useUserStore.getState().getUser(userId);
    if (user) {
      return user;
    }
    result = await userService.getUserInfo(userId);
    if (result) {
      useUserStore.getState().updateUser(result);
    }
    return result;
  },
};
