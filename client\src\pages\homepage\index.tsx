/* eslint-disable react/jsx-boolean-value */
// import logo from "@/assets/logo.png"; // 请将logo图片放在src/assets/logo.png
import useRenderCount from '@hooks/useRenderCount';
import { USER_ROLE } from '@model/user.interface';
import { getCurrentRole } from '@utils/role';
import TherapistHomePage from './components/therapist';
import UserHomePage from './components/user';

export default function HomePage() {
  const currentRole = getCurrentRole();
  // 跟踪渲染次数
  useRenderCount('HomePage');

  if (currentRole === USER_ROLE.THERAPIST) {
    return <TherapistHomePage />;
  }
  return <UserHomePage />;
}
