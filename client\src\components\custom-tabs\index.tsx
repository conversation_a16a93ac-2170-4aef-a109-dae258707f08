import { Text, View } from '@tarojs/components';
// 移除 less 文件引用

interface TabItem {
  key: string | number;
  label: string;
}

interface CustomTabsProps {
  tabs: TabItem[];
  active: string | number;
  onChange?: (key: string | number) => void;
  primaryColor?: string;
  secondaryColor?: string;
}

const CustomTabs: React.FC<CustomTabsProps> = ({
  tabs,
  active,
  onChange,
  primaryColor = 'var(--color-primary)',
  secondaryColor = 'var(--color-text-secondary)',
}) => {
  const handleTabClick = (key: string | number) => {
    if (onChange) {
      onChange(key);
    }
  };

  // 创建交替排列的标签和指示器数组
  const renderItems = () => {
    const items: JSX.Element[] = [];

    tabs.forEach((tab, index) => {
      // 添加标签
      items.push(
        <View
          key={tab.key}
          className='justify-center items-center cursor-pointer transition-colors duration-200'
          onClick={() => handleTabClick(tab.key)}
        >
          <Text
            className={`transition-colors duration-200 ${
              active === tab.key
                ? 'text-base font-bold text-default'
                : 'text-md font-medium text-secondary'
            }`}
          >
            {tab.label}
          </Text>
        </View>
      );

      // 如果不是最后一个标签，添加指示器
      if (index < tabs.length - 1) {
        items.push(
          <View key={`indicator-${index}`} className='w-px h-3 bg-gray-200' />
        );
      }
    });

    return items;
  };

  return (
    <View className='w-full flex flex-row justify-start overflow-x-auto items-center gap-x-3'>
      {renderItems()}
    </View>
  );
};

export default CustomTabs;
