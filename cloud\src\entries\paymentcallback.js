// 云函数入口文件
const cloud = require("wx-server-sdk");
const { COLLECTIONS } = require("../common/db.constants");
const { onPaid } = require("../service/orderOperation");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  console.log("支付回调数据:", event);

  try {
    // 解析支付回调数据
    const { returnCode, returnMsg } = event;

    // 验证返回结果,如果通信失败，则主动查询订单
    if (returnCode !== "SUCCESS") {
      console.error(
        returnMsg || "收到支付回调，但returnCode不正确",
        event.out_trade_no
      );

      return { errcode: 1, errmsg: "收到支付回调，但通信失败" };
    }

    const { resultCode, errCode, errCodeDes, outTradeNo } = event;
    // 查询支付记录
    const paymentResult = await db
      .collection(COLLECTIONS.PAYMENT)
      .where({ outTradeNo })
      .get();
    if (!paymentResult.data || paymentResult.data.length === 0) {
      console.error("未找到支付记录:", outTradeNo);
      return { errcode: 0, errmsg: "未找到支付记录" };
    }

    // 校验
    const payment = paymentResult.data[0];
    if (payment.status === "paid") {
      console.error("支付记录状态不是待支付，不更新", outTradeNo);
      return { errcode: 0, errmsg: "支付记录状态不是待支付，不更新" };
    }

    if (resultCode !== "SUCCESS") {
      console.error(
        errCodeDes || "收到支付回调，但result_code不正确",
        errCode,
        outTradeNo
      );
      // 更新支付记录
      await db
        .collection(COLLECTIONS.PAYMENT)
        .where({ outTradeNo })
        .update({
          data: { status: "failed" },
        });
      return { errcode: 0, errmsg: "收到支付回调，但result_code不正确" };
    }

    // 校验支付金额,微信支付以分为单位
    if (Number((payment.amount * 100).toFixed(0)) !== Number(event.totalFee)) {
      console.error(
        "支付金额不正确",
        outTradeNo,
        payment.amount,
        event.totalFee
      );
      // 更新支付记录
      await db
        .collection(COLLECTIONS.PAYMENT)
        .where({ outTradeNo })
        .update({
          data: { status: "failed" },
        });
      return { errcode: 0, errmsg: "支付金额不正确" };
    }

    const orderId = payment.orderId;

    await onPaid(orderId, outTradeNo);

    // 微信支付要求返回数据，否则会重试
    return { errcode: 0, errmsg: "OK" };
  } catch (error) {
    console.error("处理支付回调失败:", error);
    return { errcode: 1, errmsg: "回调处理异常" };
  }
};
