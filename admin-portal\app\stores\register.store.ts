import { AVATAR_DEFAULT } from '@constants/assets';
import { my_extend, my_service, my_summary } from '@model/profile.therapist';
import { ServiceType } from '@model/service.interface';
import {
  TherapistCertification,
  TherapistEducation,
  TherapistTraining,
} from '@model/therapist.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

// 注册表单数据类型
export interface TherapistRegisterFormData {
  summary: Partial<my_summary>;
  extend: Partial<my_extend>;
  serviceInfo: Partial<my_service>;
}

/**
 * 咨询师注册状态
 */
interface State {
  // 当前步骤
  currentStep: number;
  // 表单状态
  formData: TherapistRegisterFormData;
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string | null;
}

/**
 * 咨询师注册状态操作
 */
interface Action {
  // 初始化表单数据
  initFormData: (userData?: { userName?: string; avatar?: string }) => void;
  // 重置表单数据
  resetFormData: (keepUserInfo?: boolean) => void;
  // 更新表单字段
  updateFormField: (
    section: keyof TherapistRegisterFormData,
    field: string,
    value: any
  ) => void;
  // 设置当前步骤
  setCurrentStep: (step: number) => void;
  // 下一步
  nextStep: () => void;
  // 上一步
  prevStep: () => void;
  // 获取表单数据
  getFormData: () => TherapistRegisterFormData;
  // 设置加载状态
  setLoading: (loading: boolean) => void;
  // 设置错误信息
  setError: (error: string | null) => void;
}

// 初始状态
const initialState: State = {
  currentStep: 0,
  loading: false,
  error: null,
  formData: {
    // 基础信息
    summary: {
      name: '', // 姓名
      avatar: '', // 头像
      gender: 'male', // 性别
      location: '', // 所在城市
      titles: [] as string[], // 职称
      favoriteCount: 0,
      service: [] as ServiceType[],
      promo: null,
      price: 0,
      available: '可约',
      tags: [],
      specialties: [] as string[], // 专长领域
      directions: [], // 咨询方向
      status: 'active', // 状态
      rating: 0,
      ratingCount: 0,
    },
    // 详细信息
    extend: {
      introduction: '', // 个人简介
      photos: [] as string[], // 个人照片
      education: {
        education: '', // 最高学历
        schools: [],
      } as TherapistEducation, // 教育背景
      certifications: [] as TherapistCertification[], // 资质证书
      trainings: [] as TherapistTraining[], // 培训经历
      socialMedia: [] as {
        platform: string;
        link: string;
      }[], // 社交媒体
    },
    // 服务信息
    serviceInfo: {
      services: [
        {
          type: ServiceType.VIDEO,
          duration: 50,
          unit: '分钟',
          price: 300,
          promo: undefined,
          finalPrice: 300,
          enabled: true,
        },
        {
          type: ServiceType.FACE_TO_FACE,
          duration: 50,
          unit: '分钟',
          price: 300,
          promo: undefined,
          finalPrice: 300,
          enabled: true,
        },
      ],
      promo: undefined,
      workTime: {
        start: 9,
        end: 18,
        workDays: [1, 2, 3, 4, 5, 6, 7],
      },
    },
  },
};

// 创建 store
const therapistRegisterStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,

        // 初始化表单数据
        initFormData: (userData) => {
          if (userData) {
            set((draft) => {
              draft.formData.summary.name = userData.userName || AVATAR_DEFAULT;
              draft.formData.summary.avatar = userData.avatar || AVATAR_DEFAULT;
            });
          }
        },

        // 重置表单数据
        resetFormData: (keepUserInfo = true) => {
          const currentState = get();
          const userName = keepUserInfo
            ? currentState.formData.summary.name
            : '';
          const avatar = keepUserInfo
            ? currentState.formData.summary.avatar
            : '';

          set((draftState) => {
            // 创建新的表单数据对象而不是修改现有对象
            draftState.formData = {
              summary: {
                ...initialState.formData.summary,
                name: userName,
                avatar: avatar,
              },
              extend: { ...initialState.formData.extend },
              serviceInfo: { ...initialState.formData.serviceInfo },
            };
          });
        },

        // 更新表单字段
        updateFormField: (section, field, value) => {
          set((draftState) => {
            // 使用类型断言确保类型兼容
            draftState.formData[section] = {
              ...(draftState.formData[section] || {}),
              [field]: value,
            } as any;
          });
        },

        // 设置当前步骤
        setCurrentStep: (step) => {
          set((draftState) => {
            draftState.currentStep = step;
          });
        },

        // 下一步
        nextStep: () => {
          set((draftState) => {
            draftState.currentStep = Math.min(3, draftState.currentStep + 1);
          });
        },

        // 上一步
        prevStep: () => {
          set((draftState) => {
            draftState.currentStep = Math.max(0, draftState.currentStep - 1);
          });
        },

        // 获取表单数据
        getFormData: () => {
          return get().formData;
        },

        // 设置加载状态
        setLoading: (loading) => {
          set((draftState) => {
            draftState.loading = loading;
          });
        },

        // 设置错误信息
        setError: (error) => {
          set((draftState) => {
            draftState.error = error;
          });
        },
      }),
      {
        name: StorageSceneKey.THERAPIST_REGISTER,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化关键状态
        partialize: (state) => ({
          currentStep: state.currentStep,
          formData: state.formData,
        }),
      }
    )
  )
);

// 导出带选择器的 store
export const useTherapistRegisterStore = createSelectors(
  therapistRegisterStore
);
