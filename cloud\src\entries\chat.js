// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS } = require("../common/db.constants");
const { genSessionId, safeGet } = require("../common/utils");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 路由处理函数
const handlers = {
  getMessages: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await getMessages(data, context.openid);
        return success(result);
      }
    );
  },

  sendMessage: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await sendMessage(data, context.openid);
        return success(result);
      }
    );
  },

  getSession: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await getSession(data.sessionId, context.openid);
        return success(result);
      }
    );
  },

  getSessions: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await getSessions(context.openid);
        return success(result);
      }
    );
  },

  createSession: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await createSession(data, context);
        return success(result);
      }
    );
  },

  markMessageAsRead: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await markMessageAsRead(
          data.sessionId,
          data.messageId,
          context.openid
        );
        return success(result);
      }
    );
  },

  uploadImage: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await uploadImage(data.fileID);
        return success(result);
      }
    );
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", [USER_ROLE.GUEST]);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error("聊天操作失败:", err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

// 获取聊天消息
async function getMessages(params, openid) {
  try {
    const { sessionId, lastMessageId, limit = 20 } = params;

    // 构建查询条件
    let query = db.collection(COLLECTIONS.CHAT_MESSAGE).where({
      sessionId,
      _openid: openid,
    });

    if (lastMessageId) {
      // 获取最后一条消息的时间，用于分页
      const lastMessage = await db
        .collection(COLLECTIONS.CHAT_MESSAGE)
        .doc(lastMessageId)
        .get();
      if (lastMessage.data) {
        query = query.where({
          timestamp: _.lt(lastMessage.data.timestamp),
        });
      }
    }

    // 获取消息
    const { data } = await query
      .orderBy("timestamp", "desc")
      .limit(limit)
      .get();

    return { messages: data.reverse() }; // 按时间正序返回
  } catch (error) {
    console.error("获取聊天消息失败:", error);
    throw error;
  }
}

// 发送消息
async function sendMessage(params, openid) {
  try {
    const { sessionId, receiverId, content, contentType, extra = {} } = params;

    // 生成消息ID
    const messageId = `msg_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 8)}`;

    // 构建消息数据
    const message = {
      _id: messageId,
      id: messageId,
      sessionId,
      senderId: openid,
      receiverId,
      content,
      contentType,
      status: "sent",
      timestamp: Date.now(),
      isRead: false, // 初始为未读
      extra,
      _openid: openid,
    };

    // 保存消息
    await db.collection(COLLECTIONS.CHAT_MESSAGE).add({
      data: message,
    });

    // 更新会话最后一条消息和未读数
    await updateSessionOnNew(sessionId, message, openid);

    return { data: message };
  } catch (error) {
    console.error("发送消息失败:", error);
    throw error;
  }
}

// 获取会话详情
async function getSession(sessionId, openid) {
  try {
    const { data } = await db
      .collection(COLLECTIONS.CHAT_SESSION)
      .where({
        _id: sessionId,
        _openid: openid,
      })
      .get();

    if (data.length === 0) {
      throw new Error("未找到会话");
    }

    return { session: data[0] };
  } catch (error) {
    console.error("获取会话详情失败:", error);
    throw error;
  }
}

// 获取会话列表
async function getSessions(openid) {
  try {
    const { data } = await db
      .collection(COLLECTIONS.CHAT_SESSION)
      .where({
        _openid: openid,
      })
      .orderBy("timestamp", "desc")
      .get();

    return { sessions: data };
  } catch (error) {
    console.error("获取会话列表失败:", error);
    throw error;
  }
}

// 创建会话
async function createSession(params, context) {
  const { AId, BId } = params;
  if (
    !AId ||
    !BId ||
    AId === BId ||
    (AId !== context.openid && BId !== context.openid)
  ) {
    throw new Error("参数错误");
  }
  try {
    const sessionId = genSessionId(AId, BId);
    // 检查是否已存在会话
    const exists = await db
      .collection(COLLECTIONS.CHAT_SESSION)
      .where({
        _id: sessionId,
      })
      .get();

    if (exists.data && exists.data.length > 0) {
      return { session: exists.data[0] };
    }

    // 获取咨询师信息
    const therapist = await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: _.in([AId, BId]) })
      .get();

    if (!therapist.data || therapist.data.length === 0) {
      throw new Error("未找到咨询师信息");
    }

    const therapistInfo = therapist.data[0];

    // 获取用户信息
    let userInfo = context.userInfo;
    if (therapistInfo.id === AId) {
      const user = await db.collection(COLLECTIONS.USER_PUBLIC).doc(BId).get();
      if (!user.data || user.data.length === 0) {
        throw new Error("未找到用户信息");
      }
      userInfo = user.data;
    }

    // 创建新会话
    const session = {
      _id: sessionId,
      id: sessionId,
      AId: userInfo.id,
      BId: therapistInfo.id,
      AName: userInfo.userName,
      BName: therapistInfo.name,
      AAvatar: userInfo.avatar,
      BAvatar: therapistInfo.avatar,
      AUnreadCount: 0,
      BUnreadCount: 0,
      lastMessage: null,
      timestamp: Date.now(),
    };

    await db.collection(COLLECTIONS.CHAT_SESSION).add({
      data: session,
    });

    return { session };
  } catch (error) {
    console.error("创建会话失败:", error);
    throw error;
  }
}

// 标记消息为已读
async function markMessageAsRead(sessionId, messageId, openid) {
  try {
    const result = await db
      .collection(COLLECTIONS.CHAT_MESSAGE)
      .doc(messageId)
      .get();
    if (!result.data) {
      throw new Error("未找到消息");
    }
    const message = result.data;
    if (message.isRead) {
      return { success: true };
    }

    // 1. 更新消息表中的消息已读状态
    await db
      .collection(COLLECTIONS.CHAT_MESSAGE)
      .doc(messageId)
      .update({
        data: {
          isRead: true,
        },
      });

    const session = await db
      .collection(COLLECTIONS.CHAT_SESSION)
      .doc(sessionId)
      .get();
    if (!session.data) {
      throw new Error("未找到会话");
    }
    // 2. 更新会话表中的未读计数
    if (message.senderId == session.data.AId) {
      await db
        .collection(COLLECTIONS.CHAT_SESSION)
        .doc(sessionId)
        .update({
          data: {
            BUnreadCount: _.inc(-1),
          },
        });
    } else {
      await db
        .collection(COLLECTIONS.CHAT_SESSION)
        .doc(sessionId)
        .update({
          data: {
            AUnreadCount: _.inc(-1),
          },
        });
    }
    // 3. 如果是最后一条消息，更新会话表中最后一条消息的已读状态
    if (message.id == session.data.lastMessage.id) {
      await db
        .collection(COLLECTIONS.CHAT_SESSION)
        .doc(sessionId)
        .update({
          data: {
            "lastMessage.isRead": true,
          },
        });
    }

    return { success: true };
  } catch (error) {
    console.error("标记已读失败:", error);
    throw error;
  }
}

// 上传图片
async function uploadImage(fileID) {
  try {
    const result = await cloud.getTempFileURL({
      fileList: [fileID],
    });

    return {
      fileID,
      tempFileURL: result.fileList[0].tempFileURL,
    };
  } catch (error) {
    console.error("获取图片链接失败:", error);
    throw error;
  }
}

// 更新会话最后一条消息和未读数
async function updateSessionOnNew(sessionId, message, openid) {
  try {
    // 更新session的状态
    const session = await db
      .collection(COLLECTIONS.CHAT_SESSION)
      .doc(sessionId)
      .get();
    if (!session.data) {
      throw new Error("未找到会话");
    }
    const sessionData = session.data;
    console.log("sessionData", sessionData);

    //如果是咨询师发送的消息，更新会话的最后一条消息和时间戳和用户未读
    if (sessionData.AId == message.senderId) {
      // 更新会话的最后一条消息和时间戳
      await db
        .collection(COLLECTIONS.CHAT_SESSION)
        .doc(sessionId)
        .update({
          data: {
            lastMessage: _.set(message),
            timestamp: message.timestamp,
            BUnreadCount: _.inc(1),
          },
        });
    } else {
      //如果是用户发送的消息，更新会话的最后一条消息和时间戳和咨询师未读
      await db
        .collection(COLLECTIONS.CHAT_SESSION)
        .doc(sessionId)
        .update({
          data: {
            lastMessage: _.set(message),
            timestamp: message.timestamp,
            AUnreadCount: _.inc(1),
          },
        });
    }
  } catch (error) {
    console.error("更新会话失败:", error);
    throw error;
  }
}
