import { AssessmentCard } from "@/components/shared/assessment-card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";

// 模拟心理测量工具数据
const assessmentTools = [
  {
    id: "tool-1",
    name: "抑郁自评量表(SDS)",
    description: "评估抑郁症状严重程度的标准化测量工具",
    icon: "/placeholder.svg",
    questionCount: 20,
    time: "约5分钟",
    category: "情绪障碍",
    usedCount: 28500,
  },
  {
    id: "tool-2",
    name: "焦虑自评量表(SAS)",
    description: "评估焦虑症状严重程度的标准化测量工具",
    icon: "/placeholder.svg",
    questionCount: 20,
    time: "约5分钟",
    category: "情绪障碍",
    usedCount: 26800,
  },
  {
    id: "tool-3",
    name: "症状自评量表(SCL-90)",
    description: "评估多种心理症状的综合自评量表",
    icon: "/placeholder.svg",
    questionCount: 90,
    time: "约15分钟",
    category: "综合评估",
    usedCount: 15600,
  },
  {
    id: "tool-4",
    name: "匹兹堡睡眠质量指数(PSQI)",
    description: "评估睡眠质量的专业量表",
    icon: "/placeholder.svg",
    questionCount: 19,
    time: "约5分钟",
    category: "睡眠障碍",
    usedCount: 12300,
  },
  {
    id: "tool-5",
    name: "人格特质测验(EPQ)",
    description: "测量个体人格特质的经典测验",
    icon: "/placeholder.svg",
    questionCount: 88,
    time: "约15分钟",
    category: "人格测验",
    usedCount: 18900,
  },
  {
    id: "tool-6",
    name: "生活质量量表(QOL)",
    description: "评估个体生活满意度和生活质量",
    icon: "/placeholder.svg",
    questionCount: 26,
    time: "约8分钟",
    category: "生活评估",
    usedCount: 9800,
  },
  {
    id: "tool-7",
    name: "艾森克人格问卷(EPI)",
    description: "测量内外向、神经质等人格维度",
    icon: "/placeholder.svg",
    questionCount: 57,
    time: "约10分钟",
    category: "人格测验",
    usedCount: 11500,
  },
  {
    id: "tool-8",
    name: "瑞文标准推理测验",
    description: "评估抽象推理能力的非语言测验",
    icon: "/placeholder.svg",
    questionCount: 60,
    time: "约30分钟",
    category: "智力测验",
    usedCount: 8700,
  },
  {
    id: "tool-9",
    name: "领悟社会支持量表(PSSS)",
    description: "测量个体感知到的社会支持程度",
    icon: "/placeholder.svg",
    questionCount: 12,
    time: "约3分钟",
    category: "社会支持",
    usedCount: 7500,
  },
  {
    id: "tool-10",
    name: "心理弹性量表(CD-RISC)",
    description: "评估个体的心理恢复力和抗压能力",
    icon: "/placeholder.svg",
    questionCount: 25,
    time: "约5分钟",
    category: "心理韧性",
    usedCount: 10200,
  },
  {
    id: "tool-11",
    name: "生活事件量表(LES)",
    description: "评估个体近期生活事件的压力程度",
    icon: "/placeholder.svg",
    questionCount: 48,
    time: "约10分钟",
    category: "压力评估",
    usedCount: 8100,
  },
  {
    id: "tool-12",
    name: "职业倦怠量表(MBI)",
    description: "评估职业倦怠程度的专业工具",
    icon: "/placeholder.svg",
    questionCount: 22,
    time: "约5分钟",
    category: "职业健康",
    usedCount: 13400,
  },
];

// 类别筛选选项
const categoryOptions = [
  { value: "all", label: "所有类别" },
  { value: "emotion", label: "情绪障碍" },
  { value: "personality", label: "人格测验" },
  { value: "comprehensive", label: "综合评估" },
  { value: "sleep", label: "睡眠障碍" },
  { value: "life", label: "生活评估" },
  { value: "intelligence", label: "智力测验" },
  { value: "social", label: "社会支持" },
  { value: "work", label: "职业健康" },
];

// 时长筛选选项
const timeOptions = [
  { value: "all", label: "所有时长" },
  { value: "0-5", label: "5分钟以内" },
  { value: "5-10", label: "5-10分钟" },
  { value: "10+", label: "10分钟以上" },
];

export default function AssessmentsPage() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">心理测量工具</h1>
        <p className="text-muted-foreground">
          标准化的心理测量工具，帮助你了解自己的心理状态
        </p>
      </div>

      {/* 筛选区域 */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input placeholder="搜索测量工具..." className="pl-10" />
          </div>
          <div className="flex flex-wrap gap-2">
            <Select defaultValue="all">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="工具类别" />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="测试时长" />
              </SelectTrigger>
              <SelectContent>
                {timeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button>筛选</Button>
          </div>
        </div>
      </div>

      {/* 测量工具列表 */}
      <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3">
        {assessmentTools.map((tool) => (
          <AssessmentCard
            key={tool.id}
            id={tool.id}
            name={tool.name}
            description={tool.description}
            category={tool.category}
            time={tool.time}
            questionCount={tool.questionCount}
            usedCount={tool.usedCount}
          />
        ))}
      </div>

      {/* 分页 */}
      <div className="mt-8 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-primary text-primary-foreground"
          >
            1
          </Button>
          <Button variant="outline" size="sm">
            2
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
}
