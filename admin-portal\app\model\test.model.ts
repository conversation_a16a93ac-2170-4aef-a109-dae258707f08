// models/test.model.ts

export const DEFAULT_LIKERT_OPTIONS = [
  '从来没有',
  '偶尔',
  '有时',
  '经常',
  '总是',
];
// 量表分类枚举
export enum TestCategory {
  ALL = 'all', // 全部
  EMOTION = 'emotion', // 情绪
  PERSONALITY = 'personality', // 人格
  STRESS = 'stress', // 压力
  SLEEP = 'sleep', // 睡眠
  RELATIONSHIP = 'relationship', // 人际关系
  CAREER = 'career', // 职业
  ADDICTION = 'addiction', // 成瘾行为
  TRAUMA = 'trauma', // 创伤
  GENERAL = 'general', // 综合
  OTHER = 'other', // 其他
}

// 量表题目类型
export enum QuestionType {
  LIKERT = 'likert', // 李克特量表（1-5分）
  MULTIPLE_CHOICE = 'multiple_choice', // 多选题
  BINARY = 'binary', // 是非题
  OPEN_ENDED = 'open_ended', // 开放题
}

// 量表题目
export interface TestQuestion {
  id: string;
  text: string;
  type: QuestionType;
  options?: string[]; // 选择题选项
  required: boolean;
  reverseScoring: boolean; // 是否反向计分
  dimension?: string; // 所属维度
}

// 量表定义, 全量定义， 与 PsychologicalTestSummary 对应
export interface PsychologicalTest {
  id: string;
  title: string;
  instructions: string;
  shortTitle?: string; // 短名称 (例如: "抑郁自评")
  description: string; // 描述
  shortDescription?: string; // 简短描述 (例如: "快速筛查抑郁症状")
  category: TestCategory; // 分类
  howmany: number; // 题目数量
  duration: number; // 预计完成时间(分钟)
  usersCompleted: number; // 完成人数
  icon: string; // 图标URL或base64
  coverImage?: string; // 封面图URL (可选)
  isFree: boolean; // 是否免费
  questions: TestQuestion[];
  scoringRules: ScoringRule[];
}

// 计分规则
export interface ScoringRule {
  dimension?: string; // 维度名称
  questionIds: string[]; // 相关题目ID
  calculation: string; // 计算规则，如 "sum" 或 "average"
  range: [number, number]; // 得分范围
  interpretation: InterpretationRule[]; // 结果解读规则
}

// 解释级别枚举
export enum InterpretationLevel {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

// 解释级别对应的显示文本
export const LEVEL_DISPLAY_TEXT: Record<InterpretationLevel, string[]> = {
  [InterpretationLevel.NONE]: ['无'],
  [InterpretationLevel.LOW]: ['轻度', '轻微'],
  [InterpretationLevel.MEDIUM]: ['中度', '中'],
  [InterpretationLevel.HIGH]: ['重度', '重'],
};

// 通过文本查找对应的级别
export function getLevelByText(text: string): InterpretationLevel | undefined {
  for (const [level, texts] of Object.entries(LEVEL_DISPLAY_TEXT)) {
    if (texts.includes(text)) {
      return level as InterpretationLevel;
    }
  }
  return undefined;
}

// 结果解读规则
export interface InterpretationRule {
  scoreRange: [number, number]; // 分数区间
  normScore: number; // 常模得分
  level: string; // 结果等级
  description: string; // 结果描述
  recommendations: string[]; // 专业建议
}

// 维度得分
export interface DimensionScore {
  dimension: string; // 维度名称
  score: number; // 得分
  range: [number, number]; // 得分范围
  interpretation?: InterpretationRule; // 结果解读
}

// 测试结果
export interface TestResult {
  score?: number; // 总分
  range?: [number, number]; // 得分范围
  interpretation?: InterpretationRule; // 结果解读

  dimensionScores?: DimensionScore[]; // 各维度得分. 总分已从里面剔除
}

// 测试报告
export interface TestReport {
  testId: string;
  testTitle: string; // 量表标题 冗余字段
  answers: UserAnswer[];
  result: TestResult;
}

// 用户测试记录
export interface UserTestRecord {
  id: string; // 记录ID
  userId: string;
  report: TestReport;
  startTime: number; // 时间戳
  endTime?: number;
  reportViewed: boolean;
}

// 用户答案
export interface UserAnswer {
  questionId: string;
  answer: number | string | number[]; // 根据题目类型
  timestamp: number;
}
