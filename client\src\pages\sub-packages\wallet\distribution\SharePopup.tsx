import { Dialog } from '@antmjs/vantui';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';

interface SharePopupProps {
  showQrCode: boolean;
  qrCodeUrl: string;
}

export default function SharePopup({ showQrCode, qrCodeUrl }: SharePopupProps) {
  const [show, setShow] = useState(showQrCode);

  // 保存二维码到相册
  const saveQrCode = () => {
    Taro.saveImageToPhotosAlbum({
      filePath: qrCodeUrl,
      success: () => {
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
        });
      },
      fail: () => {
        Taro.showToast({
          title: '保存失败',
          icon: 'error',
        });
      },
    });
  };

  return (
    <Dialog
      show={show}
      title='我的专属二维码'
      showCancelButton
      onClose={() => setShow(false)}
      onConfirm={saveQrCode}
      confirmButtonText='保存到相册'
    >
      <View className='flex flex-col items-center py-4'>
        <Image src={qrCodeUrl} className='w-48 h-48 mb-4' />
        <Text className='text-center text-sm text-secondary'>
          邀请好友扫码注册，您可获得好友订单10%的佣金
        </Text>
      </View>
    </Dialog>
  );
}
