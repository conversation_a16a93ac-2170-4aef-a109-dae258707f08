import { Empty, Loading } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import TherapistCard from '@components/therapist/TherapistCard';
import { userProfileActions } from '@core/actions/profile.user';
import { therapist_summary } from '@model/therapist.interface';
import { View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import React, { useState } from 'react';

/**
 * 收藏的咨询师页面
 */
const FavoriteTherapistsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [favoriteTherapists, setFavoriteTherapists] = useState<
    therapist_summary[]
  >([]);

  // 获取收藏的咨询师列表
  const fetchFavoriteTherapists = async () => {
    try {
      setLoading(true);
      const result = await userProfileActions.fetchFavoriteTherapists();
      setFavoriteTherapists(result || []);
    } catch (error) {
      console.error('获取收藏的咨询师失败:', error);
      Taro.showToast({
        title: '获取收藏的咨询师失败',
        icon: 'none',
      });
    } finally {
      setLoading(false);
    }
  };

  // 页面显示时重新加载数据
  useDidShow(() => {
    fetchFavoriteTherapists();
  });

  // 取消收藏
  const handleUnfavorite = async (therapistId: string) => {
    try {
      Taro.showLoading({ title: '处理中' });
      await userProfileActions.toggleFavorite(therapistId, false);
      // 刷新列表
      fetchFavoriteTherapists();
      Taro.showToast({
        title: '已取消收藏',
        icon: 'success',
      });
    } catch (error) {
      console.error('取消收藏失败:', error);
      Taro.showToast({
        title: '取消收藏失败',
        icon: 'none',
      });
    } finally {
      Taro.hideLoading();
    }
  };

  // 点击咨询师，跳转到详情页
  const handleTherapistClick = (therapistId: string | number) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/therapist/detail/index?id=${therapistId}`,
    });
  };

  return (
    <PageLol
      navigationProps={{
        title: '收藏的咨询师',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen bg-gray-50 p-4'>
        {loading ? (
          <View className='flex justify-center items-center p-8'>
            <Loading type='spinner' color='#1989fa' />
          </View>
        ) : favoriteTherapists.length > 0 ? (
          <View className='pb-4'>
            {favoriteTherapists.map((therapist) => (
              <TherapistCard
                key={therapist.id}
                therapist={therapist}
                backgroundColor='white'
                showDivider
                onFavoriteClick={() =>
                  handleUnfavorite(therapist.id.toString())
                }
                onClick={handleTherapistClick}
                isFavorite
                showFavoriteButton
              />
            ))}
          </View>
        ) : (
          <Empty description='暂无收藏的咨询师' />
        )}
      </View>
    </PageLol>
  );
};

export default FavoriteTherapistsPage;
