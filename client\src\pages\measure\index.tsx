import { Icon } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import MeasureCategoryTags from '@components/measure/MeasureCategoryTags';
import { ICONS_BOLD_WHITE, ICONS_LINEAR } from '@constants/assets';
import { TEST_CATEGORY_MAP } from '@constants/text';
import { measureActions } from '@core/actions/measure.action';
import { useRenderCount } from '@hooks/useRenderCount';
import { PsychologicalTestSummary } from '@model/measure.model';
import { TestCategory } from '@model/test.model';
import { useMeasureStore } from '@stores/measure.store';

import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';

export default function MeasurePage() {
  const categories = useMeasureStore.use.categories();
  const featuredCards = useMeasureStore.use.featuredCards();
  const error = useMeasureStore.use.error();
  const [activeCategory, setActiveCategory] = useState<TestCategory>(
    TestCategory.ALL
  );

  useEffect(() => {
    // 页面加载时获取数据（带缓存）
    measureActions.fetchCategories();
    measureActions.fetchFeaturedCards();
    measureActions.fetchRecentHistory();

    return () => {
      useMeasureStore.getState().setLoading(false);
      useMeasureStore.getState().setError(null);
    };
  }, []);
  // 安全的增加计数
  useRenderCount('MeasurePage');
  console.log('categories', categories);
  const handleRefresh = async () => {
    console.log('handleRefresh');
    // 强制刷新数据
    await Promise.all([
      measureActions.fetchCategories(true),
      measureActions.fetchFeaturedCards(),
      measureActions.fetchRecentHistory(),
    ]);
  };

  const navigateToHistory = () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/measure/history/index',
    });
  };

  const handleCategoryChange = (categoryId: TestCategory) => {
    setActiveCategory(categoryId);
  };

  const handleTestClick = (id: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/measure/test/index?testId=${id}`,
    });
  };

  // 标签数据
  const categoryTabs = [
    {
      title: '全部',
      category: TestCategory.ALL,
      count: Object.values(categories).reduce(
        (acc, arr) => acc + arr.length,
        0
      ),
    },
    ...Object.entries(categories).map(([key, arr]) => ({
      title: TEST_CATEGORY_MAP[key as TestCategory],
      category: key as TestCategory,
      count: arr.length,
    })),
  ];

  // 分类查找
  const filteredTests = useCallback(() => {
    if (activeCategory === TestCategory.ALL) {
      return Object.values(categories).reduce(
        (all, arr) => all.concat(arr),
        [] as PsychologicalTestSummary[]
      );
    }
    return categories[activeCategory] ?? [];
  }, [activeCategory, categories]);

  // 获取今日推荐测试
  const recommendedTests = featuredCards.map((card) => ({
    id: card.id,
    title: card.shortTitle,
    description: card.shortDescription,
    questions: card.howmany,
    duration: `${card.duration}分钟`,
    participants: card.usersCompleted,
    image: card.icon,
    category: card.category,
  }));

  // 分类名称和数量
  const currentCategoryTitle =
    activeCategory === TestCategory.ALL
      ? '全部'
      : TEST_CATEGORY_MAP[activeCategory];
  const currentCategoryCount =
    activeCategory === TestCategory.ALL
      ? Object.values(categories).reduce((acc, arr) => acc + arr.length, 0)
      : categories[activeCategory]?.length ?? 0;

  return (
    <PageLol
      navigationProps={{
        title: '心理测量',
        showBackButton: false,
        showSearch: false,
        buttons: [
          {
            icon: 'clock-o',
            onClick: navigateToHistory,
          },
        ],
      }}
      error={error?.message || null}
      onPullDownRefresh={handleRefresh}
      withTabBarSpace
    >
      {/* 分类标签 */}
      <View className='px-8 pt-2'>
        <MeasureCategoryTags
          categories={categoryTabs}
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
        />
      </View>

      {/* 今日推荐 */}
      <View className='px-4 mt-6'>
        <View className='text-base font-bold mb-3'>今日推荐</View>
        <ScrollView scrollX>
          <View className=' flex  gap-4'>
            {recommendedTests.map((test) => (
              <View
                key={test.id}
                className='w-[160Px] h-[180Px] bg-white rounded-xl px-4 py-3 shadow-sm active:scale-[0.98] transition-transform flex flex-col justify-between items-start'
                onClick={() => handleTestClick(test.id)}
              >
                <View className='flex justify-center'>
                  <Image
                    src={test.image || 'https://placehold.co/40x40'}
                    className='w-10 h-10'
                    mode='aspectFill'
                  />
                </View>
                {/** 标题 + 描述*/}
                <View className='flex flex-col  items-start'>
                  <Text className='block text-md font-bold'>{test.title}</Text>
                  <Text className='block text-sm text-secondary'>
                    {test.description}
                  </Text>
                </View>
                {/** 使用人数 */}
                {test.participants > 1000 && (
                  <View className='flex items-center text-xs text-secondary bg-tertiarylight rounded-full px-2 py-1'>
                    <Icon name={ICONS_LINEAR.USER24} size='12px' />
                    <Text>{test.participants}人使用过</Text>
                  </View>
                )}
                <View className='flex justify-between items-center w-full'>
                  <Text className='text-xs text-secondary'>
                    {test.questions}题
                  </Text>
                  <View
                    className='bg-primary text-white text-xs font-bold rounded-full px-2 py-1'
                    onClick={() => handleTestClick(test.id)}
                  >
                    快速测量
                  </View>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* 分类测量 */}
      <View className='px-4 mt-6 mb-6'>
        <View className='flex justify-between items-center'>
          <Text className='text-base font-bold'>
            {currentCategoryTitle}测量
            <Text className='text-sm font-bold text-secondary'>
              ({currentCategoryCount})
            </Text>
          </Text>
        </View>

        <View className='mt-3'>
          {filteredTests().map((test, index) => (
            <View
              key={index}
              className='bg-white rounded-3xl px-4 py-3 mb-4 flex items-center w-full h-[92Px]'
            >
              {/** 图标 */}
              <View
                className={`w-14 h-14 rounded-full flex items-center justify-center ${
                  index % 3 === 0
                    ? 'bg-primarylight'
                    : index % 3 === 1
                    ? 'bg-secondarylight'
                    : 'bg-tertiarylight'
                }`}
              >
                <Image
                  src={test.icon || 'https://placehold.co/30x30'}
                  className='w-8 h-8'
                  mode='aspectFill'
                />
              </View>
              {/** 标题 + 描述 */}
              <View className='flex flex-col h-full justify-between flex-1 ml-3'>
                <View className='flex flex-col '>
                  <Text className='text-md font-bold'>{test.shortTitle}</Text>
                  <View className='text-sm text-secondary whitespace-pre-line'>
                    {test.shortDescription}
                  </View>
                </View>
                {/** 题数 + 时长 */}
                <View className='flex items-center gap-2'>
                  {/** 题数 + 时长 */}
                  <View className='flex items-center'>
                    <Icon
                      name={ICONS_LINEAR.PLAY24}
                      className='text-secondary'
                      size='12px'
                    />
                    <Text className='ml-1 text-sm font-medium text-default'>
                      {test.howmany}题
                    </Text>
                  </View>
                  <View className='flex items-center'>
                    <Icon
                      name={ICONS_LINEAR.CLOCK24}
                      className='text-secondary'
                      size='12px'
                    />
                    <Text className='ml-1 text-sm font-medium text-default'>
                      {test.duration}分钟
                    </Text>
                  </View>
                </View>
              </View>
              {/** 测量按钮 */}
              <View
                className='w-8 h-8 rounded-full flex items-center justify-center bg-primary text-white'
                onClick={() => handleTestClick(test.id)}
              >
                <Icon name={ICONS_BOLD_WHITE.PLAY64} size='16px' />
              </View>
            </View>
          ))}
        </View>
      </View>
    </PageLol>
  );
}
