/*
 * 消息中心
 */

import { NotificationItem } from '@model/notification.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { StorageSceneKey, zustandStorage } from './libs/storage';

import createSelectors from './libs/selector';

interface State {
  loading: boolean;
  error: string | null;
  notifications: NotificationItem[];
  updatedAt: number | null; // 最后更新时间 timestamp
}
interface Action {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setNotifications: (notifications: NotificationItem[]) => void;
  addNotifications: (notifications: NotificationItem[]) => void;
  removeNotifications: (notificationIds: string[]) => void;
  markAsRead: (notificationId: string) => void;
  setUpdatedAt: (updatedAt: number) => void;
}

const initialState: State = {
  loading: false,
  error: null,
  notifications: [],
  updatedAt: null,
};

const notificationsStore = create<State & Action>()(
  immer(
    persist(
      (set, _get) => ({
        ...initialState,
        setLoading: (loading: boolean) => set({ loading }),
        setError: (error: string | null) => set({ error }),
        setNotifications: (notifications: NotificationItem[]) =>
          set({ notifications }),
        addNotifications: (notifications: NotificationItem[]) =>
          set((state) => ({
            notifications: [...state.notifications, ...notifications],
          })),
        removeNotifications: (notificationIds: string[]) =>
          set((state) => ({
            notifications: state.notifications.filter(
              (n) => !notificationIds.includes(n.id)
            ),
          })),
        markAsRead: (notificationId: string) =>
          set((state) => ({
            notifications: state.notifications.map((n) =>
              n.id === notificationId ? { ...n, read: true } : n
            ),
          })),
        setUpdatedAt: (updatedAt: number) => set({ updatedAt }),
      }),
      {
        name: StorageSceneKey.MESSAGES,
        storage: createJSONStorage(() => zustandStorage),
      }
    )
  )
);

export const useNotificationsStore = createSelectors(notificationsStore);

export function useNotificationsReset() {
  notificationsStore.setState(initialState);
}
