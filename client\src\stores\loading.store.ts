import { create } from 'zustand';
import createSelectors from './libs/selector';

interface State {
  transactionLoading: boolean;
}

interface Action {
  setTransactionLoading: (loading: boolean) => void;
}

const loadingStore = create<State & Action>((set) => ({
  transactionLoading: false,
  setTransactionLoading: (loading) => {
    return set({
      transactionLoading: loading,
    });
  },
}));

export const useLoadingStore = createSelectors(loadingStore);
