import { COLLECTIONS } from '@model/db.model';
import {
  IncomeDetail,
  StatSummary,
  TherapistMonthlyStat,
  Wallet,
} from '@model/income.model';
import { useGlobalStore } from '@stores/global.store';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';

class IncomeService extends BaseService<Wallet> {
  constructor() {
    super(COLLECTIONS.WALLET);
  }

  async getMyWallet() {
    try {
      const result = await this.directRead({
        id: useGlobalStore.getState().openid,
      });
      console.log('therapistProfileService getMyWallet result', result);
      return result[0];
    } catch (error) {
      console.error('therapistProfileService getMyWallet error:', error);
      throw error;
    }
  }

  async getStatSummary() {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.STAT_SUMMARY)
        .where({
          id: useGlobalStore.getState().openid,
        })
        .get();
      return result.data[0] as StatSummary;
    } catch (error) {
      console.error('获取收入统计摘要失败:', error);
      throw error;
    }
  }
  async getIncomeList(
    params: {
      page?: number;
      pageSize?: number;
      incomeType?: 1 | 2 | 3;
      status?: 'settled' | 'frozen' | 'refunded';
      query?: string;
    } = {}
  ): Promise<{
    success: boolean;
    code: number;
    data: IncomeDetail[];
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    console.log('getIncomeList params', params);
    try {
      const { page = 1, pageSize = 10, incomeType, status, query } = params;
      const skip = (page - 1) * pageSize;

      const db = Taro.cloud.database();
      const _ = db.command;

      // 构建查询条件
      let whereCondition: any = {
        id: useGlobalStore.getState().openid,
        _openid: '{openid}',
      };

      // 添加收入类型过滤
      if (incomeType) {
        whereCondition.incomeType = incomeType;
      }

      // 添加状态过滤
      if (status) {
        whereCondition.status = status;
      }

      // 添加关键词搜索
      if (query && query.trim() !== '') {
        whereCondition = _.and([
          whereCondition,
          _.or([
            {
              orderId: new RegExp(query, 'i'),
            },
            {
              userNickname: new RegExp(query, 'i'),
            },
          ]),
        ]);
      }

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.INCOME_DETAIL)
        .where(whereCondition)
        .count();

      const total = countResult.total;

      // 获取数据
      const result = await db
        .collection(COLLECTIONS.INCOME_DETAIL)
        .where(whereCondition)
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSize);

      return {
        success: true,
        code: 200,
        data: (result.data || []) as IncomeDetail[],
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取收入列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取收入图表
   * @param fromMonth 开始月份 时间升序 unix时间戳
   * @param toMonth 结束月份 时间升序 unix时间戳
   * @returns
   */
  async getIncomeChart(fromMonth: number, toMonth: number) {
    try {
      const db = Taro.cloud.database();
      const _ = db.command;
      const result = await db
        .collection(COLLECTIONS.THERAPIST_MONTHLY_STATS)
        .where({
          therapistId: useGlobalStore.getState().openid,
          startDate: _.gte(fromMonth).and(_.lte(toMonth)),
        })
        .orderBy('startDate', 'asc')
        .get();
      return result.data as TherapistMonthlyStat[];
    } catch (error) {
      console.error('获取收入图表失败:', error);
      throw error;
    }
  }
}

export const incomeService = new IncomeService();
