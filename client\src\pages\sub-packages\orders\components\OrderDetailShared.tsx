import { Cell, CellGroup, Ellipsis, Steps } from '@antmjs/vantui';
import { PurchasedServiceCard } from '@components/order-card/ServiceCard';
import TherapistReviewItem from '@components/therapist/TherapistReviewItem';
import { ACTION_TYPE_MAP, CONSULTATION_DIRECTIONS_MAP } from '@constants/text';
import {
  Order_action,
  Order_review,
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from '@model/order.interface';
import { ServiceType } from '@model/service.interface';
import { Text, View } from '@tarojs/components';
import { ReactNode } from 'react';

export interface OrderDetailProps {
  order: Order_summary;
  orderActions: Order_action[];
  orderReview: Order_review[];
  personCard: ReactNode; // 用户卡片或咨询师卡片
  renderOrderDetailActions: ({
    status,
    serviceType,
  }: {
    status: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS];
    serviceType: ServiceType;
  }) => ReactNode;
}

export const OrderDetailShared: React.FC<OrderDetailProps> = ({
  order,
  orderActions,
  orderReview,
  personCard,
  renderOrderDetailActions,
}) => {
  const refundSteps = [
    { text: '退款申请', desc: '' },
    { text: '退款审核', desc: '' },
    {
      text:
        order.refundStatus === REFUND_STATUS.REJECTED
          ? '退款拒绝'
          : order.refundStatus === REFUND_STATUS.FAILED
          ? '退款失败'
          : '退款完成',
      desc: '',
    },
  ];

  const activeStep =
    order.refundStatus === REFUND_STATUS.PROCESSING
      ? 1
      : order.refundStatus === REFUND_STATUS.COMPLETED ||
        order.refundStatus === REFUND_STATUS.FAILED
      ? 2
      : 0;

  return (
    <View className='min-h-screen pb-[120px] px-4'>
      {/* 订单状态如果是退款，显示退款状态Steps  */}
      {(order.refundStatus === REFUND_STATUS.AUDITING ||
        order.refundStatus === REFUND_STATUS.PROCESSING ||
        order.refundStatus === REFUND_STATUS.REJECTED ||
        order.refundStatus === REFUND_STATUS.COMPLETED ||
        order.refundStatus === REFUND_STATUS.FAILED) && (
        <Steps steps={refundSteps} active={activeStep} />
      )}

      {/* 订单信息卡片 */}
      {/* 用户/咨询师信息 */}
      {personCard}

      {/* 预约时间 */}
      <View className='rounded-lg  mt-6'>
        <Text className='text-base font-bold block mb-4'>预约时间</Text>
        {/* 格式化时间 */}
        <View className='flex flex-col items-start'>
          <Text className='text-md font-medium'>
            {(() => {
              const date = new Date(order.startTime);
              const month = date.getMonth() + 1;
              const day = date.getDate();
              const year = date.getFullYear();
              return `${month}月${day}号, ${year}`;
            })()}
          </Text>
          <Text className='text-md font-medium'>
            {`${new Date(order.startTime).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })} - ${new Date(
              order.startTime + order.duration * 60 * 1000
            ).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })} (${order.duration} 分钟)`}
          </Text>
        </View>
      </View>

      {/* 预约信息 */}
      {order.consultationInfo && order.consultationInfo.name && (
        <View className='rounded-lg mt-6'>
          <Text className='text-base font-bold block mb-4'>预约信息</Text>
          <View className='flex flex-row mb-2'>
            <Text className='w-16 text-secondary font-medium text-md'>
              姓名
            </Text>
            <Text className='flex-1 text-md'>
              {order.consultationInfo.name}
            </Text>
          </View>
          <View className='flex flex-row mb-2'>
            <Text className='w-16 text-secondary font-medium text-md'>
              分类
            </Text>
            <Text className='flex-1 text-md'>
              {
                CONSULTATION_DIRECTIONS_MAP.find(
                  (item) => item.key === order.consultationInfo.direction
                )?.label
              }
            </Text>
          </View>
          <View className='flex flex-row'>
            <Text className='w-16 flex-shrink-0 text-secondary font-medium text-md'>
              描述
            </Text>
            <View className='flex-1'>
              <Ellipsis
                className='text-md'
                rows={3}
                symbol={<Text>...</Text>}
                hiddenAction
              >
                {order.consultationInfo.desc ?? ''}
              </Ellipsis>
            </View>
          </View>
        </View>
      )}

      {/* 预约服务 */}
      <View className='rounded-lg mt-6 mb-6'>
        <Text className='text-base font-bold block mb-4'>预约服务</Text>
        <PurchasedServiceCard
          serviceType={order.serviceType}
          serviceDuration={order.duration}
          price={order.price}
          serviceUnit='分钟'
        />
      </View>

      {/* 订单评价 */}
      {orderReview.length > 0 && (
        <View className='rounded-lg mt-6'>
          <Text className='text-base font-bold block mb-4'>订单评价</Text>
          {orderReview.map((review) => (
            <TherapistReviewItem key={review._id} review={review} />
          ))}
        </View>
      )}

      {/* 订单操作日志 */}
      <View className=' mt-6  '>
        <Text className='text-base font-bold block mb-4'>订单详细日志</Text>
        <View className='flex flex-col bg-white rounded-lg'>
          {orderActions.map((action) => (
            <CellGroup key={action._id} inset>
              <Cell
                title={ACTION_TYPE_MAP[action.action]}
                value={new Date(action.actionTime).toLocaleString()}
              />
              {action.extraData &&
                (() => {
                  // 过滤掉以'_'开头的字段
                  const filteredData = Object.entries(action.extraData)
                    .filter(([key]) => !key.startsWith('_'))
                    .reduce((obj, [key, value]) => {
                      obj[key] = value;
                      return obj;
                    }, {});
                  const jsonString = JSON.stringify(
                    filteredData,
                    null,
                    2
                  ).replace(/^\{|\}$/g, '');

                  // 只有在过滤后还有属性时才显示
                  if (Object.keys(filteredData).length > 0) {
                    return (
                      <Cell title='操作详情'>
                        <View className='whitespace-pre-wrap break-all text-right'>
                          {jsonString}
                        </View>
                      </Cell>
                    );
                  }
                  return null;
                })()}
            </CellGroup>
          ))}
        </View>
      </View>

      {/* 底部actions区 */}
      {renderOrderDetailActions({
        status: order.status,
        serviceType: order.serviceType,
      })}
    </View>
  );
};
