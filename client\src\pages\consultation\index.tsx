/* eslint-disable react/jsx-boolean-value */
import { Button, Empty, Icon, Switch } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import TherapistCard from '@components/therapist/TherapistCard';
import { CONSULTATION_DIRECTIONS_MAP, SERVICE_TYPE_MAP } from '@constants/text';
import { useAreaData } from '@hooks/useAreaData';
import useRenderCount from '@hooks/useRenderCount';
import { useTherapistList } from '@hooks/useTherapistList';
import { ConsultationDirection } from '@model/common.interface';
import { ServiceType } from '@model/service.interface';
import { TherapistFilters } from '@model/therapist.interface';
import { useConfigStore } from '@stores/config.store';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { useReachBottom } from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { useCallback, useEffect, useState } from 'react';
import './index.less';

export default function ConsultationPage() {
  // 跟踪渲染次数
  useRenderCount('ConsultationPage');

  // 使用自定义hook处理地区数据
  const {
    activeProvince,
    activeCity,
    provinces,
    cities,
    location,
    hotCities,
    handleProvinceSelect,
    handleCitySelect,
    handleHotCitySelect,
    resetAreaSelection,
  } = useAreaData();

  // 使用自定义hook管理咨询师列表
  const {
    therapists,
    pagination,
    loading,
    // refreshing,
    loadingMore,
    error,
    refreshTherapists,
    loadMoreTherapists,
    applyFilters,
  } = useTherapistList();

  // 使用configStore的过滤选项
  const { appConfig } = useConfigStore();
  const [activeCategories, setActiveCategories] = useState<
    ConsultationDirection[]
  >([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilter, setShowFilter] = useState(false);
  const [onlyAvailable, setOnlyAvailable] = useState(false);

  // 筛选相关状态
  const [consultType, setConsultType] = useState<ServiceType[]>([]);

  const [showAreaFilter, setShowAreaFilter] = useState(false);

  // const topBarRef = useRef<any>();
  const [topBarBottom, setTopBarBottom] = useState(0);

  useEffect(() => {
    if (showFilter || showAreaFilter) {
      Taro.createSelectorQuery()
        .select('.consult-top-bar')
        .boundingClientRect()
        .exec((res) => {
          if (res && res[0]) setTopBarBottom(res[0].bottom);
        });
    }
  }, [showFilter, showAreaFilter]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    handleApplyFilters({ keyword: value });
  };

  // 处理分类筛选
  const handleCategoryChange = (category: ConsultationDirection) => {
    const newCategories = activeCategories.includes(category)
      ? activeCategories.filter((c) => c !== category)
      : [category]; // 只允许选择一个分类

    setActiveCategories(newCategories);
    handleApplyFilters({ directions: newCategories });
  };

  // 加载更多数据
  const loadMore = async () => {
    if (pagination?.hasNext && !loadingMore) {
      loadMoreTherapists();
    }
  };

  // 处理下拉刷新
  const handlePullDownRefresh = async () => {
    refreshTherapists();
    Taro.stopPullDownRefresh();
  };

  // 应用筛选条件
  const handleApplyFilters = useCallback(
    (override: Partial<TherapistFilters> = {}) => {
      const filterOnlyAvailable =
        override.todayAvailable !== undefined
          ? override.todayAvailable
          : onlyAvailable;
      const filterDirections =
        override.directions !== undefined
          ? override.directions
          : activeCategories;
      const filterLocation =
        override.location !== undefined ? override.location : location;
      const filterServiceTypes =
        override.serviceTypes !== undefined
          ? override.serviceTypes
          : consultType;
      const filterKeyword =
        override.keyword !== undefined ? override.keyword : searchQuery;

      console.log(
        'handleApplyFilters',
        filterKeyword,
        filterDirections,
        filterLocation,
        filterServiceTypes,
        filterOnlyAvailable
      );
      const newFilterOptions: TherapistFilters = {
        keyword: filterKeyword,
        directions: filterDirections.length > 0 ? filterDirections : [],
        location: filterLocation,
        serviceTypes: filterServiceTypes,
        todayAvailable: filterOnlyAvailable,
      };

      applyFilters(newFilterOptions);
      setShowFilter(false);
      setShowAreaFilter(false);
    },
    [
      searchQuery,
      activeCategories,
      location,
      consultType,
      onlyAvailable,
      applyFilters,
    ]
  );

  // 重置筛选条件
  const resetFilters = () => {
    setConsultType([]);
  };

  // 处理只看可约的开关
  const handleAvailableChange = (checked: boolean) => {
    setOnlyAvailable(checked);
    handleApplyFilters({ todayAvailable: checked });
  };

  // 在地区选择后应用筛选
  const onAreaSelected = () => {
    handleApplyFilters({ location });
  };

  // 处理重试
  const handleRetry = () => {
    handleApplyFilters();
  };

  // 监听滚动到底部事件，实现上拉加载更多
  useReachBottom(() => {
    if (pagination && pagination.hasNext && !loadingMore && !loading) {
      loadMore();
    }
  });

  return (
    <PageLol
      useNav={true}
      navigationProps={{
        showBackButton: false,
        showSearch: true,
        searchPlaceholder: '搜索心理师或者咨询方向',
        onSearch: handleSearch,
      }}
      onPullDownRefresh={
        showFilter || showAreaFilter ? undefined : handlePullDownRefresh
      }
      loading={loading && !loadingMore}
      error={error || null}
      onRetry={handleRetry}
      className={`${
        showFilter || showAreaFilter ? 'overflow-hidden h-[100vh]' : ''
      }`}
      withTabBarSpace={true}
    >
      {/* 顶部操作区 */}
      <View className='consult-top-bar px-4 py-3 flex flex-col gap-3'>
        {/* 筛选栏 */}
        <View className='px-4 flex flex-row  justify-between items-center'>
          {/* 地区选择按钮 */}
          <View
            className='flex flex-row items-center gap-1'
            onClick={() => {
              setShowAreaFilter(!showAreaFilter);
              setShowFilter(false);
            }}
          >
            <Text className='text-md font-medium'>地区</Text>
            <Icon
              name={showAreaFilter ? 'arrow-up' : 'arrow-down'}
              size='12px'
              className='text-secondary'
            />
          </View>

          {/* 筛选按钮 */}
          <View
            className='flex flex-row items-center gap-1'
            onClick={() => {
              setShowFilter(!showFilter);
              setShowAreaFilter(false);
            }}
          >
            <Text className='text-md font-medium'>筛选</Text>
            <Icon
              name={showFilter ? 'arrow-up' : 'arrow-down'}
              size='12px'
              className='text-secondary'
            />
          </View>
          {/* 只看可约 */}
          <View className='flex flex-row items-center gap-1'>
            <Switch
              checked={onlyAvailable}
              activeColor='var(--color-success)'
              onChange={(e) => handleAvailableChange(e.detail)}
            />
            <Text className='text-sm font-medium'>今天可约</Text>
          </View>
        </View>
        {/* 分类按钮组 */}
        <View className='flex flex-row  py-1 gap-2 whitespace-nowrap flex-nowrap justify-between items-center'>
          {CONSULTATION_DIRECTIONS_MAP.map((category) => (
            <View
              key={category.key}
              className={`px-2 py-1 rounded-full text-xs font-semibold cursor-pointer transition-all ${
                activeCategories.includes(category.key)
                  ? 'border border-primary bg-primarylight text-primary '
                  : 'text-secondary bg-border'
              }`}
              onClick={() => handleCategoryChange(category.key)}
            >
              <Text>{category.label}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* 心理师列表 */}
      <View className='pb-4 mt-2'>
        {therapists && therapists.length > 0 ? (
          therapists.map((therapist) => (
            <TherapistCard
              key={therapist.id}
              therapist={therapist}
              backgroundColor='transparent'
              showDivider={true}
              onClick={() => appRouter.therapistDetail(therapist.id.toString())}
              isFavorite={(therapist as any).isFavorite}
              showFavoriteButton={true}
            />
          ))
        ) : (
          <Empty description='暂无符合条件的心理师' />
        )}

        {/* 加载更多状态 */}
        {loadingMore && (
          <View className='py-4 text-center text-secondary text-sm'>
            加载中...
          </View>
        )}

        {/* 全部加载完成 */}
        {pagination &&
          !pagination.hasNext &&
          therapists &&
          therapists.length > 0 && (
            <View className='py-4 text-center text-secondary text-sm'>
              没有更多数据了
            </View>
          )}
      </View>

      {/* 筛选弹窗 */}
      {showFilter && (
        <>
          {/* 遮罩层 */}
          <View
            className='fixed left-0 right-0 z-40 bg-black bg-opacity-40'
            style={{ top: `${topBarBottom}px`, bottom: 0 }}
            onClick={() => setShowFilter(false)}
          />
          {/* 弹窗内容 */}
          <View
            className='fixed left-0 w-full z-50 bg-white shadow-lg'
            style={{ top: `${topBarBottom}px` }}
            onClick={(e) => e.stopPropagation()}
            onTouchMove={(e) => {
              console.log('onTouchMove', e);
              e.stopPropagation();
            }}
          >
            <View className='p-4 overflow-y-auto'>
              <View className='text-lg font-bold mb-4'>筛选条件</View>

              {/* 咨询方式 */}
              <View className='mb-4'>
                <View className='text-base font-medium mb-2'>咨询方式</View>
                <View className='flex flex-row flex-wrap gap-2'>
                  <Button
                    size='small'
                    type={consultType.length === 0 ? 'primary' : 'default'}
                    plain={consultType.length > 0}
                    hairline={consultType.length > 0}
                    className='rounded-full'
                    onClick={() => setConsultType([])}
                  >
                    全部
                  </Button>
                  {appConfig.serviceTypes.map((serviceType) => (
                    <Button
                      key={serviceType}
                      size='small'
                      round
                      type={
                        consultType.includes(serviceType)
                          ? 'primary'
                          : 'default'
                      }
                      plain={!consultType.includes(serviceType)}
                      hairline={!consultType.includes(serviceType)}
                      onClick={() =>
                        setConsultType(
                          consultType.includes(serviceType)
                            ? consultType.filter((t) => t !== serviceType)
                            : [...consultType, serviceType]
                        )
                      }
                    >
                      {SERVICE_TYPE_MAP[serviceType]}
                    </Button>
                  ))}
                </View>
              </View>

              {/* 操作按钮 */}
              <View className='flex flex-row gap-3 mt-6'>
                <Button
                  block
                  plain
                  type='default'
                  className='flex-1'
                  onClick={resetFilters}
                >
                  重置
                </Button>
                <Button
                  block
                  type='primary'
                  className='flex-1'
                  onClick={() => handleApplyFilters()}
                >
                  确定
                </Button>
              </View>
            </View>
          </View>
        </>
      )}

      {/* 地区选择弹窗 */}
      {showAreaFilter && (
        <>
          {/* 遮罩层 */}
          <View
            className='fixed left-0 right-0 z-40 bg-black bg-opacity-40'
            style={{ top: `${topBarBottom}px`, bottom: 0 }}
            onClick={() => setShowAreaFilter(false)}
            onTouchMove={(e) => e.stopPropagation()}
          />
          {/* 弹窗内容 */}
          <View
            className='fixed left-0 w-full z-50 bg-white shadow-lg'
            style={{ top: `${topBarBottom}px` }}
            onTouchMove={(e) => e.stopPropagation()}
          >
            <View className='p-4 overflow-y-auto h-full'>
              {/* 热门城市 */}
              <View className='mb-6'>
                <View className='text-base font-bold mb-2'>热门城市</View>
                <View className='flex flex-row flex-wrap gap-2'>
                  <Button
                    key='全部'
                    size='small'
                    type={!location ? 'primary' : 'default'}
                    plain={!!location}
                    hairline={!!location}
                    className='rounded-full'
                    onClick={() => {
                      resetAreaSelection();
                      handleApplyFilters();
                    }}
                  >
                    全部
                  </Button>
                  {hotCities?.map((city) => (
                    <Button
                      key={city}
                      size='small'
                      type={location === city ? 'primary' : 'default'}
                      plain={location !== city}
                      hairline={location !== city}
                      className='rounded-full bg-bg'
                      onClick={() => {
                        handleHotCitySelect(city);
                        onAreaSelected();
                      }}
                    >
                      {city}
                    </Button>
                  ))}
                </View>
              </View>

              {/* 省市级联选择器 */}
              <View className='flex flex-row w-full flex-1'>
                {/* 省份列表 */}
                <ScrollView
                  scrollY
                  className='border-r border-gray-100 pr-2 bg-gray-100 rounded-sm w-[30vw] h-[60vh] overflow-y-auto'
                >
                  {Object.entries(provinces).map(([code, name]) => (
                    <View
                      key={code}
                      className={`py-3 px-2 ${
                        activeProvince === code
                          ? 'text-default font-bold'
                          : 'text-secondary font-normal'
                      }`}
                      onClick={() => {
                        const shouldApply = handleProvinceSelect(code, name);
                        if (shouldApply) {
                          onAreaSelected();
                        }
                      }}
                    >
                      {name}
                    </View>
                  ))}
                </ScrollView>

                {/* 城市列表 */}
                <ScrollView
                  scrollY
                  className='flex-1 pl-2 h-[60vh] overflow-y-auto'
                  onTouchMove={(e) => {
                    console.log('onTouchMove', e);
                    e.stopPropagation();
                  }}
                >
                  {activeProvince &&
                    cities[activeProvince] &&
                    Object.entries(cities[activeProvince]).map(
                      ([code, name]) => (
                        <View
                          key={code}
                          className={`py-3 px-2 ${
                            activeCity === code
                              ? 'text-default font-medium'
                              : 'text-secondary font-normal'
                          }`}
                          onClick={() => {
                            handleCitySelect(code, name);
                            onAreaSelected();
                          }}
                        >
                          {name}
                        </View>
                      )
                    )}
                </ScrollView>
              </View>

              {/* 操作按钮 */}
              <View className='flex flex-row gap-3 mt-4'>
                <Button
                  block
                  plain
                  type='default'
                  className='flex-1'
                  onClick={resetAreaSelection}
                >
                  重置
                </Button>
                <Button
                  block
                  type='primary'
                  className='flex-1'
                  onClick={() => {
                    handleApplyFilters();
                    setShowAreaFilter(false);
                  }}
                >
                  确定
                </Button>
              </View>
            </View>
          </View>
        </>
      )}
    </PageLol>
  );
}
