export enum MessageDirection {
  SEND = 'send', // 发送
  RECEIVE = 'receive', // 接收
}

export enum MessageStatus {
  SENDING = 'sending', // 发送中
  SENT = 'sent', // 已发送
  DELIVERED = 'delivered', // 已送达
  READ = 'read', // 已读
  FAILED = 'failed', // 发送失败
}

export enum MessageContentType {
  TEXT = 'text', // 文本
  IMAGE = 'image', // 图片
  VOICE = 'voice', // 语音
  VIDEO = 'video', // 视频
  FILE = 'file', // 文件
  ORDER = 'order', // 订单
  LOCATION = 'location', // 位置
  CUSTOM = 'custom', // 自定义
}

export interface ChatMessage {
  id: string;
  sessionId: string; // 会话ID
  senderId: string; // 发送者ID
  receiverId: string; // 接收者ID
  content: string; // 消息内容
  contentType: MessageContentType; // 消息类型
  status: MessageStatus; // 消息状态
  timestamp: number; // 时间戳
  isRead?: boolean; // 消息是否已读
  extra?: Record<string, any>; // 额外信息，比如图片URL、语音时长等
}

// 会话
export interface ChatSession {
  id: string; // 会话ID
  AId: string; // 用户A的ID
  BId: string; // 用户B的ID
  AName?: string; // 用户A的名称
  AAvatar?: string; // 用户A的头像
  BName?: string; // 用户B的名称
  BAvatar?: string; // 用户B的头像
  lastMessage?: ChatMessage; // 最后一条消息
  AUnreadCount: number; // 用户A未读消息数
  BUnreadCount: number; // 用户B未读消息数
  timestamp: number; // 更新时间戳
  type?: 'chat'; // 会话类型
}

// 系统会话
export interface SystemChatSession {
  id: string; // 会话ID
  title: string; // 会话标题
  avatar?: string; // 会话头像
  lastMessage?: ChatMessage; // 最后一条消息
  unreadCount: number; // 未读消息数
  timestamp: number; // 更新时间戳
  type: 'system'; // 会话类型
}

export type AnySession = ChatSession | SystemChatSession;

export interface SendMessageParams {
  sessionId: string;
  receiverId: string;
  content: string;
  contentType: MessageContentType;
  extra?: Record<string, any>;
}

export interface GetMessagesParams {
  sessionId: string;
  lastMessageTimestamp?: number;
  limit?: number;
}
