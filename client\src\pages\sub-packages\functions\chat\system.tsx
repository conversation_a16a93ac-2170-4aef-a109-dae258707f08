import PageLol from '@components/common/page-meta';
import { AVATAR_DEFAULT, ICONS_BOLD_PRIMARY } from '@constants/assets';
import { useChat } from '@hooks/useChat';
import useRenderCount from '@hooks/useRenderCount';
import { useGlobalStore } from '@stores/global.store';
import { View } from '@tarojs/components';
import ChatList from './components/ChatList';

/**
 * 系统聊天页面
 * 用于系统与用户的消息通知，不能发送消息，只能查看
 */
export default function SystemChatPage() {
  // 获取当前用户信息
  const myUserId = useGlobalStore.use.openid();

  // 为系统会话使用固定ID
  const systemId = 'system';

  useRenderCount('SystemChatPage');

  // 使用聊天Hook
  const {
    currentMessages,
    error,
    loading,
    handleResendMessage,
    handleMarkMessageAsRead,
  } = useChat(systemId);

  return (
    <PageLol
      useNav
      navigationProps={{
        title: '系统消息',
        showBackButton: true,
      }}
      error={error || null}
    >
      <View className='flex flex-col relative'>
        {/* 消息列表 */}
        <ChatList
          messages={currentMessages}
          loading={loading}
          receiverAvatar={ICONS_BOLD_PRIMARY.NOTIFICATION || AVATAR_DEFAULT}
          onResendMessage={handleResendMessage}
          onMarkAsRead={handleMarkMessageAsRead}
        />
      </View>
    </PageLol>
  );
}
