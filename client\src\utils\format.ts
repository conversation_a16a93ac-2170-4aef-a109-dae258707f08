/**
 * 格式化时间戳为可读时间
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
export function formatTime(timestamp: number): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 今天的消息只显示时间
  if (
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  ) {
    return formatTimeOnly(date);
  }

  // 昨天的消息显示"昨天 HH:MM"
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return `昨天 ${formatTimeOnly(date)}`;
  }

  // 一周内的消息显示星期几
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return `${weekdays[date.getDay()]} ${formatTimeOnly(date)}`;
  }

  // 其他情况显示完整日期
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
    date.getDate()
  )} ${formatTimeOnly(date)}`;
}

/**
 * 只格式化时间部分 (HH:MM)
 */
function formatTimeOnly(date: Date): string {
  return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
}

/**
 * 数字补零
 */
function padZero(num: number): string {
  return num < 10 ? `0${num}` : `${num}`;
}
