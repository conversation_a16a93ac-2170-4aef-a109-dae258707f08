import { Icon } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import './index.less';

export type IconButtonType = 'filled' | 'outlined' | 'standard';
export type IconButtonSize = 'mini' | 'small' | 'normal' | 'big';

export interface IconButtonProps {
  /** 图标名称 */
  icon: string;
  /** 按钮类型 */
  type?: IconButtonType;
  /** 按钮大小 */
  size?: IconButtonSize;
  /** 按钮颜色 */
  color?: string;
  /** 按钮图标颜色 */
  iconColor?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击事件 */
  onClick?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const IconButton: React.FC<IconButtonProps> = ({
  icon,
  type = 'standard',
  size = 'normal',
  color,
  iconColor,
  disabled = false,
  onClick,
  className = '',
  style,
}) => {
  // 图标尺寸映射
  const iconSizeMap = {
    mini: '24',
    small: '32',
    normal: '48',
    big: '64',
  };

  // 构建按钮类名
  const buttonClass = `
    icon-button 
    ${size} 
    ${type} 
    ${disabled ? 'disabled' : ''} 
    ${className}
  `
    .trim()
    .replace(/\s+/g, ' ');

  // 自定义颜色样式
  const customStyle: React.CSSProperties = {};

  // 如果设置了自定义颜色且非禁用状态
  if (color && !disabled) {
    if (type === 'filled') {
      customStyle.backgroundColor = color;
    } else if (type === 'outlined') {
      customStyle.borderColor = color;
    }
  }

  // 合并自定义样式
  const buttonStyle = {
    ...customStyle,
    ...style,
  };

  // 确定图标颜色
  const getIconColor = () => {
    if (disabled) return 'var(--text-disabled)';
    if (iconColor) return iconColor;

    // 如果没有设置iconColor，但设置了color且为outlined或standard类型
    if (color && (type === 'outlined' || type === 'standard')) {
      return color;
    }

    // 默认情况下使用LESS中定义的颜色
    return undefined;
  };

  return (
    <View
      className={buttonClass}
      style={buttonStyle}
      onClick={disabled ? undefined : onClick}
    >
      <Icon
        className='icon-button-icon'
        name={icon}
        size={iconSizeMap[size]}
        color={getIconColor()}
      />
    </View>
  );
};

export default IconButton;
