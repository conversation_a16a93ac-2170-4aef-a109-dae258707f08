import { PsychologicalTest } from "@/app/model/test.model";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";

interface TestJsonEditorProps {
  initialData?: Partial<PsychologicalTest>;
  onSubmit: (data: PsychologicalTest) => void;
}

export default function TestJsonEditor({
  initialData,
  onSubmit,
}: TestJsonEditorProps) {
  const [jsonText, setJsonText] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState<boolean>(false);

  // 初始化JSON文本
  useEffect(() => {
    if (initialData) {
      try {
        const formattedJson = JSON.stringify(initialData, null, 2);
        setJsonText(formattedJson);
        setIsValid(true);
        setError(null);
      } catch (err) {
        setError("初始数据格式化失败");
        setIsValid(false);
      }
    } else {
      // 提供一个默认的模板
      const defaultTemplate = {
        id: "",
        title: "测量工具名称",
        shortTitle: "短名称",
        description: "详细描述",
        shortDescription: "简短描述",
        instructions: "使用说明",
        category: "emotion", // 情绪类别
        howmany: 0,
        duration: 5,
        usersCompleted: 0,
        icon: "/assets/icons/emotion-brain.svg",
        isFree: true,
        questions: [
          {
            id: "q1",
            text: "题目内容",
            type: "likert",
            options: ["从来没有", "偶尔", "有时", "经常", "总是"],
            required: true,
            reverseScoring: false,
            dimension: "总分",
          },
        ],
        scoringRules: [
          {
            dimension: "总分",
            questionIds: ["q1"],
            calculation: "sum",
            range: [0, 100],
            interpretation: [
              {
                scoreRange: [0, 50],
                normScore: 0,
                level: "正常",
                description: "得分在正常范围内",
                recommendations: ["保持健康的生活方式"],
              },
              {
                scoreRange: [51, 100],
                normScore: 0,
                level: "异常",
                description: "得分超出正常范围",
                recommendations: ["建议咨询专业人士"],
              },
            ],
          },
        ],
      };

      setJsonText(JSON.stringify(defaultTemplate, null, 2));
      setIsValid(true);
      setError(null);
    }
  }, [initialData]);

  // 验证JSON格式
  const validateJson = (text: string): boolean => {
    try {
      JSON.parse(text);
      setError(null);
      return true;
    } catch (err) {
      if (err instanceof Error) {
        setError(`JSON格式错误: ${err.message}`);
      } else {
        setError("JSON格式错误");
      }
      return false;
    }
  };

  // 处理JSON文本变化
  const handleJsonChange = (text: string) => {
    setJsonText(text);
    const valid = validateJson(text);
    setIsValid(valid);
  };

  // 格式化JSON
  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonText);
      const formatted = JSON.stringify(parsed, null, 2);
      setJsonText(formatted);
      setIsValid(true);
      setError(null);
    } catch (err) {
      if (err instanceof Error) {
        setError(`无法格式化JSON: ${err.message}`);
      } else {
        setError("无法格式化JSON");
      }
    }
  };

  // 提交JSON
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValid) {
      setError("请先修复JSON格式错误");
      return;
    }

    try {
      const testData = JSON.parse(jsonText) as PsychologicalTest;

      // 简单验证必要字段
      if (!testData.title) {
        setError("缺少必要字段: title");
        return;
      }

      if (!Array.isArray(testData.questions)) {
        setError("questions 必须是数组");
        return;
      }

      // 更新题目数量
      testData.howmany = testData.questions.length;

      onSubmit(testData);
    } catch (err) {
      if (err instanceof Error) {
        setError(`提交失败: ${err.message}`);
      } else {
        setError("提交失败");
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <div className="flex justify-end">
          <Button type="button" variant="outline" onClick={formatJson}>
            格式化JSON
          </Button>
        </div>

        <Textarea
          value={jsonText}
          onChange={(e) => handleJsonChange(e.target.value)}
          className="font-mono h-[500px] text-sm"
          placeholder="输入JSON格式的测量工具数据"
        />
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={!isValid}>
          保存
        </Button>
      </div>
    </form>
  );
}
