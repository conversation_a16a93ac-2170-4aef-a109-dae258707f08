import { treatmentActions } from '@core/actions/treatment.action';
import {
  AUDIO_CATEGORY_MAP,
  AudioCategory,
  CategoryTab,
  TreatmentAudio,
} from '@model/treatment.interface';
import { useTreatmentStore } from '@stores/treatment.store';
import { useCallback, useEffect, useMemo } from 'react';

// 自定义Hook - 封装页面逻辑
export const useTreatmentPage = () => {
  // 从状态存储中获取数据
  const audioListByCategory = useTreatmentStore.use.audioListByCategory();
  const activeCategory = useTreatmentStore.use.activeCategory();
  const featuredNatureAudios = useTreatmentStore.use.featuredAudios();

  const loading = useTreatmentStore.use.loading();
  const error = useTreatmentStore.use.error();

  // 初始化加载数据
  useEffect(() => {
    // 默认设置为全部分类
    if (!useTreatmentStore.getState().activeCategory) {
      useTreatmentStore.getState().setActiveCategory(AudioCategory.ALL);
    }
    const loadData = async () => {
      await Promise.all([
        // 页面加载时获取数据（带缓存）
        treatmentActions.fetchFeaturedAudios(),
        // 获取所有音频列表
        treatmentActions.fetchAudioList({}),
      ]);
    };

    loadData();

    return () => {
      // 页面卸载时清理状态
      useTreatmentStore.getState().setLoading(false);
      useTreatmentStore.getState().setError(null);
    };
  }, []);

  // 处理音频点击 - 不再直接播放，而是保存状态供跳转使用
  const handleAudioClick = useCallback((id: string) => {
    // 保存选中的音频ID，供播放页面使用
    useTreatmentStore.getState().setSelectedAudioId(id);
  }, []);

  // 处理分类点击
  const handleTabClick = useCallback((category: AudioCategory) => {
    // 设置活跃分类
    const store = useTreatmentStore.getState();
    store.setActiveCategory(category);

    if (category) {
      // 切换分类时，加载该分类下的音频
      treatmentActions.fetchAudioList({ category });
    } else {
      // 如果取消选择分类，加载所有音频
      treatmentActions.fetchAudioList({});
    }
  }, []);

  const refresh = useCallback(async () => {
    treatmentActions.fetchFeaturedAudios();
    // 获取所有音频列表
    treatmentActions.fetchAudioList({ forceRefresh: true });
    // treatmentActions.fetchListeningRecords();
  }, []);

  // 获取指定分类的音频列表
  const getAudiosByCategory = useCallback(
    (categoryId: AudioCategory): TreatmentAudio[] => {
      return useTreatmentStore.getState().getAudioListByCategory(categoryId);
    },
    []
  );

  // 获取所有分类的音频，按分类组织
  // 使用useMemo缓存结果，避免在每次渲染时重新计算
  // const getAllAudiosByCategory = useMemo(() => {
  //   const result: Record<string, TreatmentAudio[]> = {};
  //   Object.keys(audioListByCategory).forEach((categoryId) => {
  //     result[categoryId] = useTreatmentStore
  //       .getState()
  //       .getAudioListByCategory(categoryId as AudioCategory);
  //   });
  //   return result;
  // }, [audioListByCategory]);

  const categories = useMemo((): CategoryTab[] => {
    return Object.keys(audioListByCategory).map((categoryId) => {
      return {
        title: AUDIO_CATEGORY_MAP[categoryId as AudioCategory],
        count: audioListByCategory[categoryId].length,
        category: categoryId as AudioCategory,
      };
    });
  }, [audioListByCategory]);

  return {
    categories,
    activeCategory,
    featuredNatureAudios,
    loading,
    error,
    handleAudioClick,
    handleTabClick,
    refresh,
    getAudiosByCategory,
    audioListByCategory,
  };
};
