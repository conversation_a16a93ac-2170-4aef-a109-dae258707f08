import { SUCCESS_CODE } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import {
  AudioCategory,
  CategoryTab,
  TreatmentAudio,
} from '@model/treatment.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

/**
 * 疗愈模块服务
 */
class TreatmentServiceImpl extends BaseService<TreatmentAudio> {
  constructor() {
    super(COLLECTIONS.TREATMENT_CATEGORY);
  }

  /**
   * 获取分类标签
   */
  async getCategories(): Promise<CategoryTab[]> {
    try {
      // 从云函数获取分类和数量
      const result = await this.directRead();
      return result as unknown as CategoryTab[];
    } catch (error) {
      console.error('获取分类失败:', error);
      throw error;
    }
  }

  /**
   * 获取推荐自然声音
   */
  async getFeaturedAudios(): Promise<TreatmentAudio[]> {
    try {
      const result = await callCloudFunction(
        'treatment',
        'getFeaturedAudios',
        {}
      );

      if (result.success && result.code === SUCCESS_CODE) {
        return result.data || [];
      }

      throw new Error(result.message || '获取推荐自然声音失败');
    } catch (error) {
      console.error('获取推荐自然声音失败:', error);
      throw error;
    }
  }

  /**
   * 获取音频列表
   * @param category 分类
   * @param limit 限制数量
   * @param skip 跳过数量
   */
  async getAudios(
    category?: AudioCategory,
    limit = 10,
    skip = 0
  ): Promise<TreatmentAudio[]> {
    try {
      const db = Taro.cloud.database();
      const query = db.collection(COLLECTIONS.TREATMENT_AUDIO);

      // 如果有分类，添加筛选条件
      if (category) {
        query.where({
          category,
        });
      }

      const result = await query
        .skip(skip)
        .limit(limit)
        .orderBy('plays', 'desc')
        .get();

      console.log('TreatmentServiceImpl.getAudios', result);

      return result.data.map((item) => item as TreatmentAudio);
    } catch (error) {
      console.error('获取音频列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取音频详情
   * @param audioId 音频ID
   */
  async getAudioDetail(audioId: string): Promise<TreatmentAudio | null> {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.TREATMENT_AUDIO)
        .where({ id: audioId })
        .limit(1)
        .get();

      if (result.data.length > 0) {
        return result.data[0] as TreatmentAudio;
      }

      return null;
    } catch (error) {
      console.error('获取音频详情失败:', error);
      throw error;
    }
  }

  /**
   * 增加播放次数
   * @param audioId 音频ID
   */
  async incrementPlayCount(audioId: string): Promise<boolean> {
    try {
      const result = await callCloudFunction(
        'treatment',
        'incrementPlayCount',
        {
          audioId,
        }
      );

      return result.success && result.code === SUCCESS_CODE;
    } catch (error) {
      console.error('增加播放次数失败:', error);
      return false;
    }
  }
}

export const treatmentService = new TreatmentServiceImpl();
