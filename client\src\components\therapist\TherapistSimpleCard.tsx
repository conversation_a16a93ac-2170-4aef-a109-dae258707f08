import { AVATAR_DEFAULT } from '@constants/assets';
import { therapist_summary } from '@model/therapist.interface';
import { Image, Text, View } from '@tarojs/components';

interface TherapistSimpleCardProps {
  therapist?: therapist_summary;
  className?: string;
}

export default function TherapistSimpleCard({
  therapist,
  className = '',
}: TherapistSimpleCardProps) {
  if (!therapist) return null;

  return (
    <View className={`bg-white rounded-xl p-4 flex items-center ${className}`}>
      <Image
        src={therapist.avatar ?? AVATAR_DEFAULT}
        className='w-12 h-12 rounded-full mr-3'
      />
      <View className='flex-1'>
        <Text className='text-base font-bold block'>{therapist.name}</Text>
        <Text className='text-sm text-secondary block'>
          {therapist.titles && therapist.titles.join(' | ')}
        </Text>
      </View>
    </View>
  );
}
