// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS } = require("../common/db.constants");
const { submitWithdraw } = require("../service/withdrawOperation");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = userInfo?.role || USER_ROLE.GUEST;
    const userId = userInfo?._id;

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

/**
 * 取消提现申请
 * @param {string} withdrawId 提现记录ID
 * @param {string} openid 用户openid
 */
async function cancelWithdraw(withdrawId, openid) {
  try {
    // 查询提现记录
    const withdrawResult = await db
      .collection(COLLECTIONS.WITHDRAW)
      .where({ id: withdrawId, userId: openid })
      .get();

    if (!withdrawResult.data || withdrawResult.data.length === 0) {
      throw new Error("提现记录不存在");
    }

    const withdraw = withdrawResult.data[0];

    // 验证提现状态
    if (withdraw.status !== "pending") {
      throw new Error("只能取消待处理的提现申请");
    }

    // 更新提现记录状态
    await db
      .collection(COLLECTIONS.WITHDRAW)
      .where({ id: withdrawId })
      .update({
        data: {
          status: "cancelled",
          updatedAt: Date.now(),
        },
      });

    // 恢复账户余额
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: openid })
      .update({
        data: {
          balance: _.inc(withdraw.amount),
          frozenAmount: _.inc(-withdraw.amount),
          updatedAt: Date.now(),
        },
      });

    return { success: true };
  } catch (error) {
    console.error("取消提现申请失败:", error);
    throw error;
  }
}

// 路由处理函数
const handlers = {
  // 提交提现申请
  submitWithdraw: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_AND_THERAPIST_SELF,
      context,
      async () => {
        const result = await submitWithdraw(data, context.openid);
        return success(result);
      }
    );
  },

  // 取消提现申请
  cancelWithdraw: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_AND_THERAPIST_SELF,
      context,
      async () => {
        const result = await cancelWithdraw(data.id, context.openid);
        return success(result);
      }
    );
  },
};
