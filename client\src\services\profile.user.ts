/**
 * 用户个人profile服务实现类
 * 从用户角度提供个人资料管理和业务功能
 * 遵循架构原则：
 * 1. 公开数据直接从数据库读取
 * 2. 敏感数据通过云函数获取
 * 3. 所有写操作通过云函数
 */

import { CloudFunctionResult } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import { user_public } from '@model/user.interface';
import { useGlobalStore } from '@stores/global.store';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class ProfileServiceImpl extends BaseService<user_public> {
  constructor() {
    super(COLLECTIONS.USER_PUBLIC);
  }

  async fetchMyPublicProfile() {
    const result = await this.directRead({
      id: useGlobalStore.getState().openid,
    });
    console.log('ProfileServiceImpl fetchMyPublicProfile result', result);
    return result[0];
  }

  async updateAvatarAndNickname(
    avatar: string,
    nickname: string
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('user', 'updateInfo', {
        avatar,
        userName: nickname,
      });
      return result;
    } catch (error) {
      console.error('userProfileService updateAvatarAndNickname error:', error);
      throw error;
    }
  }

  /**
   * 获取收藏的咨询师列表
   * 用户自己的数据，直接读取
   */
  async readFavoriteTherapists(): Promise<string[]> {
    try {
      // 查询用户收藏的咨询师ID列表
      const db = Taro.cloud.database();

      // 查询用户收藏的咨询师ID列表
      const userFavoritesResult = await db
        .collection(COLLECTIONS.USER_FAVORITES)
        .where({
          userId: '{openid}',
        })
        .field({
          therapistId: true,
        })
        .get();

      if (!userFavoritesResult.data || userFavoritesResult.data.length === 0) {
        return [];
      }

      // 提取咨询师ID列表
      const therapistIds = userFavoritesResult.data.map(
        (item: any) => item.therapistId
      );

      return therapistIds;
    } catch (error) {
      console.error('获取收藏的咨询师列表失败:', error);
      return [];
    }
  }

  /**
   * 收藏/取消收藏咨询师
   * 写操作，通过云函数
   */
  async favorite(
    id: string | number,
    favorite: boolean = true
  ): Promise<boolean> {
    try {
      const result = await callCloudFunction('therapist', 'favorite', {
        id,
        favorite,
      });
      return result.success;
    } catch (error) {
      console.error('收藏咨询师操作失败:', error);
      throw error;
    }
  }
}

export const UserProfileService = new ProfileServiceImpl();
