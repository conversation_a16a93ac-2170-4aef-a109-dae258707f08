import { chatActions } from '@core/actions/chat.action';
import { ChatSession } from '@model/chat.interface';
import { useChatStore } from '@stores/chat.store';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useShallow } from 'zustand/react/shallow';

export function useMessages(session: ChatSession | null) {
  const loadedSessionRef = useRef<string | null>(null);

  // 使用useMemo缓存sessionId，避免不必要的依赖变化
  const sessionId = useMemo(() => {
    return session?.id || '';
  }, [session]);

  // 使用useShallow减少不必要的重新渲染
  const messages = useChatStore(
    useShallow((state) => state.getMessages(sessionId))
  );

  // 使用useCallback缓存fetchMessages函数
  const fetchMessages = useCallback(async () => {
    if (
      session &&
      sessionId &&
      sessionId.trim() !== '' &&
      loadedSessionRef.current !== sessionId
    ) {
      // 只在会话ID变化或首次加载时获取消息
      loadedSessionRef.current = sessionId;
      await chatActions.fetchMessages(
        sessionId,
        session.lastMessage?.timestamp
      );
    }
  }, [session, sessionId]);

  useEffect(() => {
    fetchMessages();
  }, [fetchMessages]);

  return messages;
}
