// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS } = require("../common/db.constants");
const { safeGet, genTestRecordId } = require("../common/utils");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

/**
 * 获取推荐课程
 * @param {string} context context
 */
async function getFeaturedCourses(context) {
  try {
    console.log("getFeaturedCourses", context);

    const result = await db
      .collection(COLLECTIONS.COURSES)
      .field({
        id: true,
        shortTitle: true,
        shortDescription: true,
        category: true,
        howmany: true,
        duration: true,
        usersCompleted: true,
        icon: true,
        coverImage: true,
        isFree: true,
      })
      .limit(2)
      .get();
    return {
      success: true,
      data: result.data,
    };
  } catch (err) {
    console.error("getFeaturedCards 失败:", err);
    throw err;
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};
// 处理函数映射
const handlers = {
  getFeaturedCourses: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await getFeaturedCourses(context);
      return success(result);
    });
  },
};
