import { ScrollView, Text, View } from '@tarojs/components';
import React, { useCallback } from 'react';

interface DateItem {
  date: string; // 2024-06-19
  day: string; // 19
  week: string; // 六
}

interface DateSwiperProps {
  dates: DateItem[];
  selected: string; // 选中的date
  onSelect: (date: string) => void;
}

const DateSwiper: React.FC<DateSwiperProps> = ({
  dates,
  selected,
  onSelect,
}) => {
  const handleDateSelect = useCallback(
    (date: string) => {
      onSelect(date);
    },
    [onSelect]
  );

  return (
    <ScrollView
      className='w-full'
      scrollX
      enableFlex
      style={{ whiteSpace: 'nowrap' }}
    >
      <View className='flex flex-row px-2 py-3 space-x-3'>
        {dates.map((item) => (
          <View
            key={item.date}
            className={`flex flex-col items-center justify-center w-12 h-16 rounded-xl cursor-pointer transition-all ${
              selected === item.date
                ? 'bg-primary text-white shadow font-bold'
                : 'bg-gray-100 text-gray-700'
            }`}
            onClick={() => handleDateSelect(item.date)}
          >
            <Text className='text-lg mb-1'>{item.day}</Text>
            <Text className='text-xs'>{item.week}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

export default DateSwiper;
