import { TRUSTED_DOMAINS } from '@constants/config';
import { WebView } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

export default function Webview() {
  const { url } = useRouter().params;
  const [safeUrl, setSafeUrl] = useState('');

  useEffect(() => {
    if (!url) return;
    const safeUrlDecoded = decodeURIComponent(url);
    // 域名白名单校验
    if (isSafeDomain(safeUrlDecoded)) {
      setSafeUrl(safeUrlDecoded);
    } else {
      Taro.showModal({
        title: '安全提示',
        content: '即将打开外部链接',
        success: (res) => {
          if (res.confirm) setSafeUrl(safeUrlDecoded);
        },
      });
    }
  }, [url]);

  const isSafeDomain = (urlString: string) => {
    return TRUSTED_DOMAINS.some((domain) => urlString.includes(domain));
  };

  return <WebView src={safeUrl} />;
}
