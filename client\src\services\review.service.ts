import { CloudFunctionResult, TherapistReviewRequest } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import { Order_review } from '@model/order.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class ReviewServiceImpl extends BaseService<Order_review> {
  constructor() {
    super(COLLECTIONS.ORDER_REVIEWS);
  }

  async fetchOrderReviews(orderId: string) {
    try {
      const reviews = await this.directRead({
        orderId,
      });
      return reviews;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async reviewOrder(
    orderId: string,
    score: number,
    comment: string,
    tags: string[]
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('review', 'comment', {
        orderId,
        score,
        comment,
        tags,
      });
      console.log('reviewOrder result', result);
      return result;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async readFewReviews(
    therapistId: string,
    limit = 5
  ): Promise<Order_review[]> {
    console.log('reviewService readFewReviews', therapistId);
    try {
      const db = Taro.cloud.database();
      const _ = db.command;
      //查询有内容且按评分降序排序的5条评价
      const query = _.and([
        {
          therapistId: therapistId,
          content: db.command.neq(''),
        },
        _.or([{ userId: '{openid}' }, { therapistId: '{openid}' }]),
      ]);

      console.log('reviewService readFewReviewsq query', query);
      const result = await db
        .collection(COLLECTIONS.ORDER_REVIEWS)
        .where(query)
        .orderBy('score', 'desc')
        .limit(limit)
        .get();
      console.log('reviewService readFewReviewsq result', result);
      return result.data as Order_review[];
    } catch (error) {
      console.error('获取咨询师评价失败readFewReviews:', error);
      throw error;
    }
  }

  /**
   * 获取咨询师评价
   * @param params 评价查询参数
   */
  async readReviews(params: TherapistReviewRequest) {
    console.log('therapistService readReviews', params);
    try {
      const {
        therapistId,
        page = 1,
        pageSize = 10,
        minRating,
        hasContent,
      } = params;

      // 初始化数据库
      const db = Taro.cloud.database();
      const _ = db.command;

      // 构建查询条件
      let query: any = {
        therapistId: therapistId.toString(),
      };
      query = _.and([
        query,
        _.or([{ userId: '{openid}' }, { therapistId: '{openid}' }]),
      ]);

      if (minRating && minRating > 0) {
        query.score = _.gte(minRating);
      }

      if (hasContent) {
        query.content = _.neq('');
      }

      // 计算分页偏移
      const skip = (page - 1) * pageSize;

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.ORDER_REVIEWS)
        .where(query)
        .count();

      const total = countResult.total;

      // 查询评价数据
      const result = await db
        .collection(COLLECTIONS.ORDER_REVIEWS)
        .where(query)
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 使用云函数获取平均评分
      const { result: avgScoreResult } = await Taro.cloud.callFunction({
        name: 'therapistStats',
        data: {
          action: 'getAvgRating',
          therapistId: therapistId.toString(),
        },
      });

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      return {
        score: (avgScoreResult as any).avgScore || 0,
        count: total,
        list: result.data,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取咨询师评价失败:', error);
      throw error;
    }
  }
}

export const reviewService = new ReviewServiceImpl();
