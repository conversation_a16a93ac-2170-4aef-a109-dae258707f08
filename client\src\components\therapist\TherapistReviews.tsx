import { Order_review } from '@model/order.interface';
import { Button, Text, View } from '@tarojs/components';
import TherapistReviewItem from './TherapistReviewItem';

interface TherapistPreferRatingCardProps {
  therapistId: string;
  score: number;
  count: number;
  reviews: Order_review[];
  maxCount?: number;
}

const TherapistPreferRatingCard: React.FC<TherapistPreferRatingCardProps> = ({
  therapistId,
  score,
  count,
  reviews,
  maxCount,
}) => {
  const displayReviews = maxCount ? reviews.slice(0, maxCount) : reviews;

  return (
    <View className='flex flex-col'>
      <View className='flex flex-row items-center justify-between mb-4 w-full'>
        <View className='flex flex-row items-center flex-1'>
          <Text className='text-base font-bold'>用户评价</Text>
          <Text className='text-primary ml-2'>{score}分</Text>
          <Text className='text-xs text-secondary ml-2'>({count}条评价)</Text>
        </View>
        <Button
          className='text-sm text-primary bg-transparent border-none p-0'
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/sub-packages/therapist/reviews/index?id=${therapistId}`,
            });
          }}
        >
          查看全部
        </Button>
      </View>
      {/* 评价列表 */}
      <View className='flex flex-col'>
        {displayReviews.length > 0 ? (
          displayReviews.map((review, idx) => (
            <TherapistReviewItem key={idx} review={review} />
          ))
        ) : (
          <Text className='text-gray-400 text-center py-4'>暂无评价</Text>
        )}
      </View>
    </View>
  );
};

export default TherapistPreferRatingCard;
