const cloud = require("wx-server-sdk");
const { COLLECTIONS } = require("./db.constants");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

/**
 * 订单状态回滚处理函数
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackOrderStatus(data, tx) {
  const { orderId, originalStatus } = data;

  console.log(
    `[rollbackOrderStatus] 回滚订单状态: ${orderId}, 原状态: ${originalStatus}`
  );

  try {
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .update({
        data: {
          status: originalStatus,
          updatedAt: Date.now(),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
            reason: tx.error || "事务回滚",
          },
        },
      });

    console.log(`[rollbackOrderStatus] 订单状态回滚成功: ${orderId}`);
  } catch (error) {
    console.error(`[rollbackOrderStatus] 订单状态回滚失败: ${orderId}`, error);
    throw error;
  }
}

/**
 * 排期信息回滚处理函数 - 添加排期
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackScheduleRemove(data, tx) {
  const { therapistId, startTime, duration, beijingDate, timeSlots } = data;

  console.log(
    `[rollbackScheduleRemove] 回滚排期删除: ${therapistId}, 日期: ${beijingDate}`
  );

  try {
    await db
      .collection(COLLECTIONS.THERAPIST_SCHEDULES)
      .where({
        id: therapistId,
        date: beijingDate,
      })
      .update({
        data: {
          slots: db.command.addToSet({
            $each: timeSlots,
          }),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      })
      .then((res) => {
        // 如果未匹配到文档，执行插入
        if (res.stats.updated === 0) {
          return db.collection(COLLECTIONS.THERAPIST_SCHEDULES).add({
            data: {
              id: therapistId,
              date: beijingDate,
              slots: timeSlots,
              _tx_rollback: {
                txId: tx._id,
                timestamp: Date.now(),
              },
            },
          });
        }
        return res;
      });

    console.log(
      `[rollbackScheduleRemove] 排期删除回滚成功: ${therapistId}, 日期: ${beijingDate}`
    );
  } catch (error) {
    console.error(
      `[rollbackScheduleRemove] 排期删除回滚失败: ${therapistId}, 日期: ${beijingDate}`,
      error
    );
    throw error;
  }
}

/**
 * 排期信息回滚处理函数 - 删除排期
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackScheduleAdd(data, tx) {
  const { therapistId, beijingDate, timeSlots } = data;

  console.log(
    `[rollbackScheduleAdd] 回滚排期添加: ${therapistId}, 日期: ${beijingDate}`
  );

  try {
    await db
      .collection(COLLECTIONS.THERAPIST_SCHEDULES)
      .where({
        id: therapistId,
        date: beijingDate,
      })
      .update({
        data: {
          slots: db.command.pullAll(timeSlots),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      });

    console.log(
      `[rollbackScheduleAdd] 排期添加回滚成功: ${therapistId}, 日期: ${beijingDate}`
    );
  } catch (error) {
    console.error(
      `[rollbackScheduleAdd] 排期添加回滚失败: ${therapistId}, 日期: ${beijingDate}`,
      error
    );
    throw error;
  }
}

/**
 * 退款记录回滚处理函数
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackRefundRecord(data, tx) {
  const { orderId, outRefundNo } = data;

  console.log(
    `[rollbackRefundRecord] 回滚退款记录: ${orderId}, 退款单号: ${outRefundNo}`
  );

  try {
    // 标记退款记录为无效
    await db
      .collection(COLLECTIONS.REFUND_RECORD)
      .where({ orderId, outRefundNo })
      .update({
        data: {
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
            isInvalid: true,
          },
        },
      });

    console.log(
      `[rollbackRefundRecord] 退款记录回滚成功: ${orderId}, 退款单号: ${outRefundNo}`
    );
  } catch (error) {
    console.error(
      `[rollbackRefundRecord] 退款记录回滚失败: ${orderId}, 退款单号: ${outRefundNo}`,
      error
    );
    throw error;
  }
}

/**
 * 咨询师收入回滚处理函数
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackTherapistIncome(data, tx) {
  const { therapistId, orderId, income } = data;

  console.log(
    `[rollbackTherapistIncome] 回滚咨询师收入: ${therapistId}, 订单: ${orderId}, 金额: ${income}`
  );

  try {
    // 更新收入明细
    await db
      .collection(COLLECTIONS.THERAPIST_INCOME_DETAIL)
      .where({ id: therapistId, orderId })
      .update({
        data: {
          amount: db.command.inc(income),
          status: "settled",
          updatedAt: Date.now(),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      });

    // 更新咨询师钱包
    await db
      .collection(COLLECTIONS.THERAPIST_WALLET)
      .where({ id: therapistId })
      .update({
        data: {
          balance: db.command.inc(income),
          totalIncome: db.command.inc(income),
          updatedAt: Date.now(),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      });

    console.log(
      `[rollbackTherapistIncome] 咨询师收入回滚成功: ${therapistId}, 订单: ${orderId}`
    );
  } catch (error) {
    console.error(
      `[rollbackTherapistIncome] 咨询师收入回滚失败: ${therapistId}, 订单: ${orderId}`,
      error
    );
    throw error;
  }
}

/**
 * 分销订单回滚处理函数
 * @param {object} data 步骤数据
 * @param {object} tx 事务对象
 */
async function rollbackDistributionOrder(data, tx) {
  const { orderId, referrerId, commission } = data;

  console.log(
    `[rollbackDistributionOrder] 回滚分销订单: ${orderId}, 推荐人: ${referrerId}, 佣金: ${commission}`
  );

  try {
    // 更新分销订单状态
    await db
      .collection(COLLECTIONS.DISTRIBUTION_ORDER)
      .where({ orderId })
      .update({
        data: {
          status: "settled",
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      });

    // 更新分销账户余额
    await db
      .collection(COLLECTIONS.DISTRIBUTION_OVERVIEW)
      .where({ userId: referrerId })
      .update({
        data: {
          totalIncome: db.command.inc(commission),
          balance: db.command.inc(commission),
          updatedAt: Date.now(),
          _tx_rollback: {
            txId: tx._id,
            timestamp: Date.now(),
          },
        },
      });

    console.log(
      `[rollbackDistributionOrder] 分销订单回滚成功: ${orderId}, 推荐人: ${referrerId}`
    );
  } catch (error) {
    console.error(
      `[rollbackDistributionOrder] 分销订单回滚失败: ${orderId}, 推荐人: ${referrerId}`,
      error
    );
    throw error;
  }
}

// 注册回滚处理函数到全局对象
global.rollbackOrderStatus = rollbackOrderStatus;
global.rollbackScheduleRemove = rollbackScheduleRemove;
global.rollbackScheduleAdd = rollbackScheduleAdd;
global.rollbackRefundRecord = rollbackRefundRecord;
global.rollbackTherapistIncome = rollbackTherapistIncome;
global.rollbackDistributionOrder = rollbackDistributionOrder;

module.exports = {
  rollbackOrderStatus,
  rollbackScheduleRemove,
  rollbackScheduleAdd,
  rollbackRefundRecord,
  rollbackTherapistIncome,
  rollbackDistributionOrder,
};
