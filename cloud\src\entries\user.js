// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS, ORDER_STATUS } = require("../common/db.constants");
const { safeGet, generateUserSig } = require("../common/utils");
const {
  addReferralRelation,
  initDistributionAccount,
} = require("../service/distribution");
const { AVATAR_DEFAULT } = require("../common/config");
const { initWallet, initStatSummary } = require("../service/incomeOperation");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

/**
 * 登录处理
 * @param {string} code 微信登录凭证
 * @param {string} phoneCode 手机号cloudID
 * @param {string} openid 用户openid
 */
async function handleLogin(code, phoneCode, referralInfo, context) {
  try {
    console.log("handleLogin", { code, phoneCode, referralInfo, context });

    const { openid, userInfo } = context;
    // 获取或创建用户
    let user = userInfo;
    let isNewUser = false;
    console.log("handleLogin user", user);
    if (!user) {
      console.log(`用户不存在，创建新用户: ${openid}`);
      user = await createUser(openid);
      isNewUser = true;

      // 处理邀请信息
      if (referralInfo) {
        const { referralId, referralType } = referralInfo;
        if (referralId) {
          await addReferralRelation(referralId, referralType ?? "", userInfo);
        }
      }
    }

    // 获取手机号
    let phone = null;
    if (phoneCode) {
      const phoneResult = await cloud.getOpenData({
        list: [phoneCode],
      });

      if (phoneResult.list?.[0]?.data?.phoneNumber) {
        phone = phoneResult.list[0].data.phoneNumber;
      }
    }

    // 如果获取到手机号，更新用户手机号
    if (phone) {
      const userSensitiveCollection = db.collection(COLLECTIONS.USER_SENSITIVE);

      await userSensitiveCollection
        .where({ id: openid })
        .update({
          data: {
            phone,
            updatedAt: Date.now(),
          },
        })
        .then(async (res) => {
          if (res.stats.updated === 0) {
            await userSensitiveCollection.add({
              data: {
                _id: openid,
                id: openid,
                phone,
                createdAt: Date.now(),
                updatedAt: Date.now(),
              },
            });
          }
        });
    }

    // 生成用户签名和令牌
    const userSig = await generateUserSig(user.id);

    // 返回登录结果
    return {
      success: true,
      userInfo: {
        id: user.id,
        nickName: user.userName,
        avatarUrl: user.avatar,
        role: user.role,
      },
      userSig,
      openid,
      isNewUser,
    };
  } catch (err) {
    console.error("登录处理失败:", err);
    throw err;
  }
}

/**
 * 获取用户敏感信息
 * @param {string} openid 用户openid
 * 如果提供userId和orderId，则查询userId的敏感信息，需要验证userId是否为openid在orderId订单的userId，
 * 且订单状态为PENDING_CONFIRM，PENDING，IN_PROGRESS
 */
async function getUserPhoneNumber(openid, userId, orderId) {
  try {
    if (userId && orderId) {
      const orderResult = await db
        .collection(COLLECTIONS.ORDER)
        .where({
          _id: orderId,
          userId: openid,
          therapistId: userId,
          status: {
            $in: [
              ORDER_STATUS.PENDING_CONFIRM,
              ORDER_STATUS.PENDING_START,
              ORDER_STATUS.IN_PROGRESS,
            ],
          },
        })
        .get();
      if (orderResult.data.length === 0) {
        throw new Error("订单不存在或状态不正确");
      }
      // 返回咨询师的敏感信息
      const therapistResult = await db
        .collection(COLLECTIONS.USER_SENSITIVE)
        .where({ id: userId })
        .get();
      return {
        phone: therapistResult.data[0]?.phone || null,
      };
    } else {
      // 查询用户敏感信息
      const userResult = await db
        .collection(COLLECTIONS.USER_SENSITIVE)
        .where({ id: openid })
        .get();

      return {
        phone: userResult.data[0]?.phone || null,
      };
    }
  } catch (err) {
    console.error("获取用户信息失败:", err);
    throw err;
  }
}

/**
 * 创建新用户
 * @param {string} openid 用户openid
 * @param {object} userInfo 微信用户信息
 */
async function createUser(openid, userInfo = {}) {
  try {
    console.log("createUser", openid, userInfo);
    // 创建用户公开信息
    const userData = {
      _id: openid,
      _openid: openid,
      id: openid,
      userName: "用户", // 默认昵称，等待用户自行设置
      avatar: AVATAR_DEFAULT, // 默认为空，等待用户自行设置
      role: [USER_ROLE.USER],
      status: "active",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const result = await db.collection(COLLECTIONS.USER_PUBLIC).add({
      data: userData,
    });

    // 初始化分销账户
    await initDistributionAccount(openid);

    // 初始化钱包
    await initWallet(openid);

    // 初始化汇总统计
    await initStatSummary(openid);

    return {
      ...userData,
      _id: result._id,
    };
  } catch (err) {
    console.error("创建用户失败:", err);
    throw err;
  }
}

/**
 * 更新用户信息
 * @param {Object} data 更新数据
 * @param {string} openid 用户openid
 */
async function updateUserInfo(data, context) {
  const { openid, userInfo } = context;

  if (!userInfo) {
    throw new Error("用户不存在");
  }

  // 提取需要更新的字段
  const updateData = {};

  // 更新用户名
  if (data.userName !== undefined) {
    updateData.userName = data.userName;
  }

  // 更新头像
  if (data.avatar !== undefined) {
    updateData.avatar = data.avatar;
  }

  // 如果有其他字段需要更新，也可以添加到updateData中
  if (Object.keys(data).some((key) => !["userName", "avatar"].includes(key))) {
    // 过滤掉userName和avatar，其他字段直接添加到updateData
    Object.keys(data).forEach((key) => {
      if (!["userName", "avatar"].includes(key)) {
        updateData[key] = data[key];
      }
    });
  }

  // 添加更新时间
  updateData.updatedAt = Date.now();

  // 更新用户信息
  await db.collection(COLLECTIONS.USER_PUBLIC).doc(userInfo._id).update({
    data: updateData,
  });

  return { success: true };
}

/**
 * 检查用户是否有咨询师角色
 * @param {string} openid 用户openid
 */
async function checkTherapistRole(context) {
  const { openid, userInfo } = context;
  try {
    // 查询用户信息
    if (!userInfo) {
      return {
        isTherapist: false,
      };
    }

    // 检查用户是否有咨询师角色
    const isTherapist = userInfo.role.includes(USER_ROLE.THERAPIST);

    return {
      isTherapist,
    };
  } catch (err) {
    console.error("检查咨询师角色失败:", err);
    throw err;
  }
}

/**
 * 刷新用户签名
 * @param {string} openid 用户openid
 * @returns {object} 包含新签名的对象
 */
async function refreshUserSig(openid) {
  try {
    console.log("refreshUserSig", openid);
    if (!openid) {
      throw new Error("用户openid不存在");
    }

    // 生成新的userSig
    const userSig = await generateUserSig(openid);

    return { userSig };
  } catch (err) {
    console.error("刷新用户签名失败:", err);
    throw err;
  }
}

/**
 * 添加用户角色
 * @param {string} role 要添加的角色
 * @param {string} openid 用户openid
 */
async function addUserRole(role, openid) {
  try {
    // 验证角色是否有效
    if (
      ![USER_ROLE.USER, USER_ROLE.THERAPIST, USER_ROLE.ADMIN].includes(role)
    ) {
      return {
        success: false,
        message: "无效的角色",
      };
    }

    // 如果添加咨询师角色，先检查用户是否有咨询师资格
    if (role === USER_ROLE.THERAPIST) {
      const therapistResult = await db
        .collection(COLLECTIONS.THERAPIST)
        .where({ id: openid })
        .get();

      if (therapistResult.data.length === 0) {
        return {
          success: false,
          message: "您还不是咨询师，请先完成咨询师注册",
        };
      }
    }

    // 更新用户角色
    const userCollection = db.collection(COLLECTIONS.USER_PUBLIC);
    const userResult = await userCollection.where({ _openid: openid }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: "用户不存在",
      };
    }

    // 获取用户当前角色
    const currentUser = userResult.data[0];
    let currentRoles = Array.isArray(currentUser.role)
      ? currentUser.role
      : [currentUser.role];

    // 如果用户已经有这个角色，不需要添加
    if (currentRoles.includes(role)) {
      return {
        success: true,
        message: "用户已拥有该角色",
      };
    }

    // 添加新角色
    currentRoles.push(role);

    // 更新用户角色
    await userCollection.doc(currentUser._id).update({
      data: {
        role: currentRoles,
        updatedAt: Date.now(),
      },
    });

    return {
      success: true,
      message: "角色添加成功",
      roles: currentRoles,
    };
  } catch (err) {
    console.error("添加角色失败:", err);
    return {
      success: false,
      message: "添加角色时发生错误",
    };
  }
}

/**
 * 移除用户角色
 * @param {string} role 要移除的角色
 * @param {string} openid 用户openid
 */
async function removeUserRole(role, openid) {
  try {
    // 验证角色是否有效
    if (
      ![USER_ROLE.USER, USER_ROLE.THERAPIST, USER_ROLE.ADMIN].includes(role)
    ) {
      return {
        success: false,
        message: "无效的角色",
      };
    }

    // 更新用户角色
    const userCollection = db.collection(COLLECTIONS.USER_PUBLIC);
    const userResult = await userCollection.where({ _openid: openid }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: "用户不存在",
      };
    }

    // 获取用户当前角色
    const currentUser = userResult.data[0];
    let currentRoles = Array.isArray(currentUser.role)
      ? currentUser.role
      : [currentUser.role];

    // 如果用户没有这个角色，不需要移除
    if (!currentRoles.includes(role)) {
      return {
        success: true,
        message: "用户没有该角色",
      };
    }

    // 用户至少要保留一个角色
    if (currentRoles.length <= 1) {
      return {
        success: false,
        message: "用户必须至少保留一个角色",
      };
    }

    // 移除角色
    currentRoles = currentRoles.filter((r) => r !== role);

    // 更新用户角色
    await userCollection.doc(currentUser._id).update({
      data: {
        role: currentRoles,
        updatedAt: Date.now(),
      },
    });

    return {
      success: true,
      message: "角色移除成功",
      roles: currentRoles,
    };
  } catch (err) {
    console.error("移除角色失败:", err);
    return {
      success: false,
      message: "移除角色时发生错误",
    };
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};
// 处理函数映射
const handlers = {
  // 登录相关
  login: async (params, context) => {
    const result = await handleLogin(
      params.code,
      params.phoneCode,
      params.referralInfo,
      context
    );
    return success(result);
  },

  // 更新用户信息
  updateInfo: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await updateUserInfo(params, context);
        return success(result);
      }
    );
  },

  // 检查咨询师角色
  checkTherapistRole: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await checkTherapistRole(context);
        return success(result);
      }
    );
  },

  // 添加用户角色
  addUserRole: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await addUserRole(
          params.role,
          params.targetOpenid || context.openid
        );
        return success(result);
      }
    );
  },

  // 移除用户角色
  removeUserRole: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await removeUserRole(
          params.role,
          params.targetOpenid || context.openid
        );
        return success(result);
      }
    );
  },

  // 获取用户敏感信息
  getUserPhoneNumber: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await getUserPhoneNumber(
          context.openid,
          params.userId,
          params.orderId
        );
        return success(result);
      }
    );
  },

  // 刷新用户签名
  refreshUserSig: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await refreshUserSig(context.openid);
        return success(result);
      }
    );
  },
};
