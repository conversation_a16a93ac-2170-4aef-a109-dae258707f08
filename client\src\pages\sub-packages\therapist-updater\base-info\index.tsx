import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import BaseInfoEditer from '@components/therapist/BaseInfoEditer';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

export default function BaseInfoUpdater() {
  const summary = useTherapistUpdaterStore((state) => state.formData.summary);

  const updateFormField = useTherapistUpdaterStore(
    (state) => state.updateFormField
  );
  const loading = useTherapistUpdaterStore((state) => state.loading);
  const error = useTherapistUpdaterStore((state) => state.error);

  useEffect(() => {
    const loadMyProfile = async () => {
      await therapistProfileActions.loadMyProfile();
      const myProfile = useTherapistProfileStore.getState().myProfile;
      console.log('loadMyProfile myProfile', myProfile);
      if (myProfile) {
        useTherapistUpdaterStore.getState().setFormData({
          ...useTherapistUpdaterStore.getState().formData,
          summary: myProfile,
        });
      }
    };
    loadMyProfile();
    return () => {
      useTherapistUpdaterStore.setState({
        loading: false,
        error: null,
      });
    };
  }, []);
  console.log('BaseInfoUpdater summary', summary);
  // 表单验证
  const validateForm = (): boolean => {
    if (!summary.name) {
      Taro.showToast({ title: '请输入姓名', icon: 'none' });
      return false;
    }

    if (!summary.location) {
      Taro.showToast({ title: '请选择所在城市', icon: 'none' });
      return false;
    }
    if (summary.titles?.length === 0) {
      Taro.showToast({ title: '请选择职称', icon: 'none' });
      return false;
    }
    if (summary.specialties?.length === 0) {
      Taro.showToast({ title: '请选择专长领域', icon: 'none' });
      return false;
    }
    if (summary.directions?.length === 0) {
      Taro.showToast({ title: '请选择咨询方向', icon: 'none' });
      return false;
    }
    return true;
  };

  // 处理下一步按钮点击
  const handleSubmit = async () => {
    if (validateForm()) {
      console.log('submit', summary);
      await therapistProfileActions.updateMyBaseInfo(summary);
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '基本信息编辑',
        showBackButton: true,
        showSearch: false,
      }}
      error={error}
    >
      {/* <Text className='text-lg font-bold mb-4 block'>基本信息</Text> */}

      <BaseInfoEditer summary={summary} onFormChange={updateFormField} />
      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='primary'
          block
          round
          loading={loading}
          disabled={loading}
          className='flex-1 ml-2'
          onClick={handleSubmit}
        >
          提交
        </Button>
      </View>
    </PageLol>
  );
}
