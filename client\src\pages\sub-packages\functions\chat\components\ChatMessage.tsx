import { Icon, Image, Loading } from '@antmjs/vantui';
import WaveAnimation from '@components/common/WaveAnimation';
import OrderCard from '@components/order-card/OrderCard';
import OrderCardForTherapist from '@components/order-card/OrderCardForTherapist';
import { AVATAR_DEFAULT } from '@constants/assets';
import {
  ChatMessage as ChatMessageType,
  MessageContentType,
  MessageStatus,
} from '@model/chat.interface';
import { USER_ROLE } from '@model/user.interface';
import { useGlobalStore } from '@stores/global.store';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatTimeForToday } from '@utils/time';
import { useEffect, useState } from 'react';

interface ChatMessageProps {
  message: ChatMessageType;
  receiverAvatar?: string;
  onResend?: (messageId: string) => void;
  onRead?: (messageId: string) => void; // 标记消息为已读的回调
}

/**
 * 聊天消息组件
 * 用于显示单条消息气泡
 */
export default function ChatMessage({
  message,
  receiverAvatar = AVATAR_DEFAULT,
  onResend,
  onRead,
}: ChatMessageProps) {
  const isSend = message.senderId === useGlobalStore.getState().openid;
  const isRead = message.isRead === true;
  const [isPlaying, setIsPlaying] = useState(false);

  // 如果消息不是语音，且不是发送的消息，收到即标记为已读
  useEffect(() => {
    if (message.contentType !== MessageContentType.VOICE && !isSend) {
      onRead?.(message.id);
    }
  }, [isSend, message.contentType, message.id, onRead]);

  // 播放语音
  const handlePlayAudio = (audioUrl: string) => {
    // 设置播放状态
    setIsPlaying(true);

    const innerAudioContext = Taro.createInnerAudioContext();
    innerAudioContext.autoplay = true;
    innerAudioContext.src = audioUrl;

    innerAudioContext.onPlay(() => {
      console.log('开始播放');
      // 如果是接收的消息且未读，标记为已读
      if (!isSend && !isRead && onRead) {
        onRead(message.id);
      }
    });

    innerAudioContext.onEnded(() => {
      console.log('播放结束');
      setIsPlaying(false);
    });

    innerAudioContext.onError((res) => {
      console.log('播放错误', res);
      setIsPlaying(false);
      Taro.showToast({
        title: '播放失败',
        icon: 'none',
      });
    });
  };

  // 处理查看订单详情
  const handleViewOrder = (orderId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/detail/index?id=${orderId}`,
    });
  };

  return (
    <View
      className={`flex mb-4 items-end ${
        isSend ? 'justify-end' : 'justify-start'
      }`}
    >
      {!isSend && (
        <View className='mx-2'>
          <Image
            className='w-10 h-10 rounded-full'
            src={receiverAvatar}
            round
          />
        </View>
      )}

      <View
        className={`${
          message.contentType === MessageContentType.ORDER
            ? 'max-w-[80vw]'
            : 'max-w-[60vw]'
        }`}
      >
        <View
          className={`${
            message.contentType === MessageContentType.TEXT
              ? `flex px-4 py-3 rounded-[40px] break-words ${
                  isSend
                    ? 'bg-[#2775b6] text-white rounded-br-sm'
                    : 'bg-white text-default rounded-bl-sm'
                }`
              : 'flex items-center'
          }`}
        >
          {message.contentType === MessageContentType.TEXT ? (
            <Text className='text-base leading-relaxed'>{message.content}</Text>
          ) : message.contentType === MessageContentType.IMAGE ? (
            <View className='relative min-w-[120px] min-h-[120px]'>
              {message.extra?.uploading ? (
                <View className='flex items-center justify-center w-[120px] h-[120px] bg-black/10 rounded'>
                  <Loading type='spinner' size='24px' />
                </View>
              ) : (
                <Image
                  className='max-w-[300px] rounded-lg'
                  src={message.extra?.imageUrl || ''}
                  onClick={() => {
                    Taro.previewImage({
                      current: message.extra?.imageUrl || '',
                      urls: [message.extra?.imageUrl || ''],
                    });
                  }}
                />
              )}
            </View>
          ) : message.contentType === MessageContentType.VOICE ? (
            <>
              <View
                className={`flex items-center border border-border rounded-lg ${
                  isSend ? 'bg-[#2775b6]' : 'bg-white'
                } px-2 py-1`}
                onClick={() => handlePlayAudio(message.extra?.audioUrl || '')}
              >
                <View className='flex items-center'>
                  {isPlaying ? (
                    <WaveAnimation
                      isPlaying={isPlaying}
                      frameClassName={isSend ? 'bg-white' : 'bg-primary'}
                    />
                  ) : (
                    <Icon
                      name='volume-o'
                      size='16px'
                      color={isSend ? '#fff' : 'var(--color-primary)'}
                    />
                  )}
                </View>
                <Text
                  className={`ml-2 ${isSend ? 'text-white' : 'text-default'}`}
                >
                  {message.extra?.duration || '0'}″
                </Text>
              </View>
              {!isRead && !isSend && (
                <View className='ml-1 flex items-center justify-center w-2 h-2 bg-danger rounded-full mt-1'></View>
              )}
            </>
          ) : message.contentType === MessageContentType.ORDER ? (
            <View className='w-full'>
              {useGlobalStore.getState().currentRole === USER_ROLE.THERAPIST ? (
                <OrderCardForTherapist
                  orderId={message.content}
                  showDivider={false}
                  onCardClick={() => handleViewOrder(message.content)}
                />
              ) : (
                <OrderCard orderId={message.content} showActions={false} />
              )}
            </View>
          ) : null}
        </View>

        <View
          className={`flex items-center text-xs text-secondary mt-1 ${
            isSend ? 'justify-end' : ''
          }`}
        >
          {formatTimeForToday(message.timestamp)}
          {isSend && (
            <View className='ml-1'>
              {message.status === MessageStatus.SENDING ? (
                <Loading type='spinner' size='12px' />
              ) : message.status === MessageStatus.SENT ? (
                isRead ? (
                  <Text className='text-xs text-secondary ml-1'>已读</Text>
                ) : null
              ) : message.status === MessageStatus.FAILED ? (
                <Icon
                  name='warning-o'
                  size='12px'
                  color='#f44336'
                  onClick={() => onResend && onResend(message.id)}
                />
              ) : null}
            </View>
          )}
        </View>
      </View>
    </View>
  );
}
