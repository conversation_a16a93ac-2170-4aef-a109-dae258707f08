import { Payment } from "@/app/model/payment.interface";
import { financeCloudFunctions } from "@/lib/cloud-functions";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  Pagination,
  TransactionListParams,
  TransactionListRequest,
} from "../model/api";

/**
 * 交易列表管理Hook
 * 提供交易列表的获取、筛选和状态监听功能
 */
export function useTransactionList(
  initialParams: { params?: TransactionListParams } = {}
) {
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<Payment[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0,
  });
  const [filters, setFilters] = useState<
    TransactionListParams & { dateRange?: [number, number] }
  >({
    query: "",
    type: "",
    status: "",
    dateRange: undefined,
  });

  const requestParamsRef = useRef({
    filters: initialParams.params,
  });

  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchTransactions = useCallback(
    async (params: TransactionListRequest) => {
      if (loading) return;
      setLoading(true);
      try {
        const res = await financeCloudFunctions.getTransactions(params);
        setTransactions(res.data as Payment[]);
        setPagination(res.pagination as Pagination);
        if (res.summary) {
          setSummary(
            res.summary as {
              totalIncome: number;
              totalExpense: number;
              balance: number;
            }
          );
        }
      } catch (error) {
        console.error("获取交易记录失败:", error);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 刷新列表
  const refreshTransactions = useCallback(async () => {
    try {
      await fetchTransactions({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
      });
    } catch (error) {
      console.error("刷新交易记录失败:", error);
    }
  }, [fetchTransactions, pagination?.pageSize]);

  // 加载更多/翻页
  const loadMoreTransactions = useCallback(
    async (page: number = pagination?.page + 1) => {
      try {
        await fetchTransactions({
          page: page,
          pageSize: pagination?.pageSize || 10,
          params: requestParamsRef.current.filters,
        });
      } catch (error) {
        console.error("加载更多交易记录失败:", error);
      }
    },
    [pagination?.page, pagination?.pageSize, fetchTransactions]
  );

  // 搜索交易
  const onSearch = useCallback(
    async (searchWord: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        query: searchWord,
      };

      setFilters(newFilters);

      try {
        await fetchTransactions({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("搜索交易记录失败:", error);
      }
    },
    [fetchTransactions, pagination?.pageSize]
  );

  // 筛选交易类型
  const onTypeChange = useCallback(
    async (type: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        type,
      };

      setFilters(newFilters);

      try {
        await fetchTransactions({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("筛选交易类型失败:", error);
      }
    },
    [fetchTransactions, pagination?.pageSize]
  );

  // 筛选交易状态
  const onStatusChange = useCallback(
    async (status: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        status,
      };

      setFilters(newFilters);

      try {
        await fetchTransactions({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("筛选交易状态失败:", error);
      }
    },
    [fetchTransactions, pagination?.pageSize]
  );

  // 设置日期范围
  const onDateRangeChange = useCallback(
    async (dateRange: [number, number] | undefined) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        dateRange,
      };

      setFilters(newFilters);

      try {
        await fetchTransactions({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按日期筛选交易失败:", error);
      }
    },
    [fetchTransactions, pagination?.pageSize]
  );

  // 初始加载
  useEffect(() => {
    refreshTransactions();
  }, [refreshTransactions]); // 只在组件挂载时执行一次

  return {
    transactions,
    pagination,
    summary,
    loading,
    refreshTransactions,
    loadMoreTransactions,
    onTypeChange,
    onStatusChange,
    onSearch,
    onDateRangeChange,
    filters,
  };
}
