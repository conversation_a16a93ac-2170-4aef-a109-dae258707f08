import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { FileEdit, Play, PlusCircle, Trash2 } from "lucide-react";

// 模拟疗愈音乐数据
const healingMusic = [
  {
    id: "MUSIC-001",
    title: "冥想放松音乐一",
    category: "冥想",
    duration: "15:20",
    status: "已上线",
    createdAt: "2023-04-15",
  },
  {
    id: "MUSIC-002",
    title: "自然雨声",
    category: "自然声音",
    duration: "30:00",
    status: "已上线",
    createdAt: "2023-05-10",
  },
  {
    id: "MUSIC-003",
    title: "深度睡眠音乐",
    category: "睡眠",
    duration: "45:30",
    status: "已上线",
    createdAt: "2023-05-22",
  },
  {
    id: "MUSIC-004",
    title: "森林晨曦",
    category: "自然声音",
    duration: "20:15",
    status: "已上线",
    createdAt: "2023-06-01",
  },
  {
    id: "MUSIC-005",
    title: "海浪声",
    category: "自然声音",
    duration: "60:00",
    status: "未上线",
    createdAt: "2023-06-05",
  },
];

// 状态对应的样式
const statusStyles = {
  已上线: "bg-green-100 text-green-800",
  未上线: "bg-gray-100 text-gray-800",
};

export default function HealingMusicPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">疗愈音乐管理</h1>
          <p className="text-muted-foreground">管理平台提供的疗愈音乐资源</p>
        </div>
        <Button className="flex items-center gap-1">
          <PlusCircle size={18} />
          <span>添加音乐</span>
        </Button>
      </div>

      <Card className="p-6">
        <div className="flex mb-6">
          <Input placeholder="搜索音乐标题..." className="max-w-sm" />
          <Button className="ml-2">搜索</Button>
        </div>

        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>音乐标题</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>时长</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {healingMusic.map((music) => (
                <TableRow key={music.id}>
                  <TableCell className="font-medium">{music.id}</TableCell>
                  <TableCell>{music.title}</TableCell>
                  <TableCell>{music.category}</TableCell>
                  <TableCell>{music.duration}</TableCell>
                  <TableCell>
                    <Badge
                      className={
                        statusStyles[
                          music.status as keyof typeof statusStyles
                        ] || ""
                      }
                    >
                      {music.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{music.createdAt}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Play size={14} />
                        试听
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <FileEdit size={14} />
                        编辑
                      </Button>
                      <Button
                        variant={
                          music.status === "已上线" ? "default" : "outline"
                        }
                        size="sm"
                      >
                        {music.status === "已上线" ? "下线" : "上线"}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Trash2 size={14} />
                        删除
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-end space-x-2 mt-4">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </Card>
    </div>
  );
}
