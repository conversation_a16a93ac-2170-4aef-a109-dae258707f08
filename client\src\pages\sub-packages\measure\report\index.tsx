import {
  But<PERSON>,
  Circle,
  Collapse,
  CollapseItem,
  Icon,
  Tag,
} from '@antmjs/vantui';
import DotLoading from '@components/common/loading-dots';
import PageLol from '@components/common/page-meta';
import { measureActions } from '@core/actions/measure.action';
import { useRenderCount } from '@hooks/useRenderCount';
import { getLevelByText, InterpretationLevel } from '@model/test.model';
import { useMeasureStore } from '@stores/measure.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

/**
 * 测试结果页面
 * 展示测试结果和报告
 */
export default function TestReportPage() {
  const router = useRouter();
  const { recordId } = router.params;

  const testRecord = useMeasureStore.use
    .recentHistory()
    .find((it) => it.id === recordId);

  const [expandedDimensions, setExpandedDimensions] = useState<string[]>([]);
  const [showHistoryChart, setShowHistoryChart] = useState(false);

  // 计数测试
  useRenderCount('TestReportPage');
  console.log('TestReportPage recordId', recordId);

  // 加载测试记录
  useEffect(() => {
    if (!recordId) {
      Taro.showToast({
        title: '记录ID不能为空',
        icon: 'error',
      });
      return;
    }

    measureActions.fetchTestRecord(recordId);
  }, [recordId]);

  // 返回首页
  const handleBackToHome = () => {
    Taro.switchTab({
      url: '/pages/measure/index',
    });
  };

  // 重新测试
  const handleRetakeTest = () => {
    if (!testRecord) return;

    Taro.navigateTo({
      url: `/pages/sub-packages/measure/test/doing?testId=${testRecord.report.testId}`,
    });
  };

  // 获取颜色渐变梯度
  const getScoreColor = (level: string): [string, string] => {
    const levelEnum = getLevelByText(level);

    // 返回线性渐变色组 - 使用更柔和的渐变色降低用户压力
    // 健康状态：从浅绿到深绿的渐变
    if (levelEnum === InterpretationLevel.LOW) {
      // 需关注：从浅蓝到深蓝的渐变
      return ['#BFDBFE', '#3B82F6'];
    } else if (levelEnum === InterpretationLevel.MEDIUM) {
      // 中度关注：从浅黄到橙色的渐变
      return ['#FEF3C7', '#F59E0B'];
    } else if (levelEnum === InterpretationLevel.HIGH) {
      return ['#FEE2E2', '#EF4444'];
      // 需要干预：从浅红到深红的渐变，但使用较柔和的色调
    } else {
      return ['#A7F3D0', '#10B981'];
    }
  };

  // 获取结果标签颜色
  const getLevelColor = (level: string) => {
    const levelEnum = getLevelByText(level);
    switch (levelEnum) {
      case InterpretationLevel.LOW:
        return 'success';
      case InterpretationLevel.MEDIUM:
        return 'warning';
      case InterpretationLevel.HIGH:
        return 'danger';
      default:
        return 'primary';
    }
  };

  // 渲染报告概览区
  const renderReportOverview = () => {
    if (!testRecord) return null;

    const {
      score,
      interpretation,
      range = [0, 100],
    } = testRecord.report.result;
    const scoreRange = range[1] - range[0];
    const scorePercentage = score ? (score / scoreRange) * 100 : 0;
    return (
      <View className='bg-white rounded-lg p-6 mb-6'>
        {/* 测试标题和完成日期 */}
        <View className='mb-6'>
          <Text className='text-lg font-bold text-default'>
            {testRecord.report.testTitle}
          </Text>
          <Text className='text-secondary text-sm  mt-2 block'>
            完成时间: {new Date(testRecord.endTime || 0).toLocaleString()}
          </Text>
        </View>

        {/* 总分展示 */}
        {score && (
          <View className='mb-6'>
            {/* 结果标签 */}
            {interpretation?.level && (
              <View className='mb-4'>
                <Tag type={getLevelColor(interpretation.level)} size='medium'>
                  {interpretation.level}
                </Tag>
              </View>
            )}

            {/* 可视化图表：环形进度条 */}
            <View className='flex justify-center mb-4'>
              <Circle
                value={scorePercentage}
                lineCap='round'
                text={`${score}/${scoreRange}`}
                size={120}
                strokeWidth={8}
                layerColor='#F3F4F6'
                color={
                  // 从渐变色中提取第二个颜色值作为环形进度条的颜色
                  getScoreColor(interpretation?.level || '')[1]
                }
              />
            </View>

            {/* 结果描述 */}
            {interpretation?.description && (
              <Text className='text-secondary text-sm leading-relaxed text-center block'>
                {interpretation.description}
              </Text>
            )}
          </View>
        )}

        {/* 关键结论 */}
        {interpretation?.recommendations &&
          interpretation.recommendations.length > 0 && (
            <View className='border-t pt-4'>
              {interpretation.recommendations.map((rec, index) => (
                <View key={index} className='flex items-start mb-2'>
                  <View
                    className={`w-2 h-2 rounded-full bg-primary mt-2 mr-3 flex-shrink-0 ${
                      index === 0 ? 'bg-primary' : 'bg-tertiary'
                    }`}
                  />
                  <Text className='text-default text-sm leading-relaxed'>
                    {rec}
                  </Text>
                </View>
              ))}
            </View>
          )}
      </View>
    );
  };

  // 渲染维度分解
  const renderDimensionAnalysis = () => {
    if (!testRecord) return null;

    const { dimensionScores } = testRecord.report.result;
    if (!dimensionScores || dimensionScores.length === 0) return null;

    return (
      <View className='bg-white rounded-lg p-4 mb-6'>
        <Text className='text-base font-bold mb-4 block'>维度分解</Text>

        <Collapse
          value={expandedDimensions}
          border={false}
          onChange={(event) => setExpandedDimensions(event.detail)}
        >
          {dimensionScores.map((dimensionScore) => {
            const { dimension, score, interpretation, range } = dimensionScore;
            const normScore = interpretation?.normScore || 3.0;
            const scoreRange = range[1] - range[0];
            return (
              <CollapseItem
                key={dimension}
                title={`${dimension} - ${score.toFixed(1)}`}
                border
                name={dimension}
              >
                <View className='pt-4'>
                  {/* 柱状图对比 */}
                  <View className='mb-4'>
                    <View className='flex justify-between mb-2'>
                      <Text className='text-sm text-gray-600'>
                        用户得分 vs 常模
                      </Text>
                      <Text className='text-sm text-gray-600'>
                        安全阈值: 4.0
                      </Text>
                    </View>

                    <View className='space-y-3'>
                      {/* 用户得分 */}
                      <View>
                        <View className='flex justify-between mb-1'>
                          <Text className='text-sm'>您的得分</Text>
                          <Text className='text-sm font-medium'>
                            {score.toFixed(1)}
                          </Text>
                        </View>
                        <View className='h-3 bg-gray-200 rounded-full overflow-hidden'>
                          <View
                            className='h-full rounded-full'
                            style={{
                              width: `${(score / scoreRange) * 100}%`,
                              background: `linear-gradient(to right, ${
                                getScoreColor(interpretation?.level || '')[0]
                              }, ${
                                getScoreColor(interpretation?.level || '')[1]
                              })`,
                            }}
                          />
                        </View>
                      </View>

                      {/* 常模得分 */}
                      <View>
                        <View className='flex justify-between mb-1'>
                          <Text className='text-sm'>常模平均</Text>
                          <Text className='text-sm font-medium'>
                            {normScore.toFixed(1)}
                          </Text>
                        </View>
                        <View className='h-3 bg-gray-200 rounded-full overflow-hidden'>
                          <View
                            className='h-full bg-gray-400 rounded-full'
                            style={{
                              width: `${(normScore / scoreRange) * 100}%`,
                            }}
                          />
                        </View>
                      </View>

                      {/* 安全阈值线 */}
                      <View className='relative h-3'>
                        <View className='absolute top-0 left-0 w-full h-3 bg-gray-200 rounded-full' />
                        <View
                          className='absolute top-0 h-3 w-0.5 bg-red-500'
                          style={{ left: '80%' }}
                        />
                        <Text className='absolute top-4 left-4/5 text-xs text-red-500'>
                          阈值
                        </Text>
                      </View>
                    </View>
                  </View>

                  {/* 详细描述 */}
                  {interpretation && (
                    <View className=' pt-4'>
                      <Text className='text-sm font-bold mb-1 block text-default'>
                        详细分析
                      </Text>
                      <Text className='text-sm text-secondary leading-relaxed mb-3'>
                        {interpretation.description}
                      </Text>

                      {interpretation.recommendations &&
                        interpretation.recommendations.length > 0 && (
                          <View className='pt-4'>
                            <Text className='text-sm font-bold mb-1 block text-default'>
                              针对性建议
                            </Text>
                            {interpretation.recommendations.map(
                              (rec, index) => (
                                <View
                                  key={index}
                                  className='flex items-start mb-1'
                                >
                                  <View className='w-1.5 h-1.5 rounded-full bg-primary mt-1.5 mr-2 flex-shrink-0' />
                                  <Text className='text-sm text-default'>
                                    {rec}
                                  </Text>
                                </View>
                              )
                            )}
                          </View>
                        )}
                    </View>
                  )}
                </View>
              </CollapseItem>
            );
          })}
        </Collapse>
      </View>
    );
  };

  // 渲染行动建议
  const renderActionRecommendations = () => {
    if (!testRecord) return null;

    const { score, interpretation } = testRecord.report.result;
    const scoreLevel = score
      ? score > 60
        ? 'high'
        : score > 40
        ? 'medium'
        : 'low'
      : 'low';

    return (
      <View className='bg-white rounded-lg p-6 mb-6'>
        <Text className='text-lg font-medium mb-4 block'>个性化行动建议</Text>

        {/* 即时自助 */}
        <View className='mb-6'>
          <View className='flex items-center mb-3'>
            <Icon name='clock-o' className='text-blue-500 mr-2' />
            <Text className='font-medium'>即时自助</Text>
          </View>
          <View className='bg-blue-50 rounded-lg p-4'>
            <Text className='text-sm text-gray-700 mb-3'>5分钟呼吸练习</Text>
            <Button
              size='small'
              type='primary'
              className='w-full'
              onClick={() =>
                Taro.navigateTo({ url: '/pages/treatment/breathing' })
              }
            >
              开始练习
            </Button>
          </View>
        </View>

        {/* 中长期改善 */}
        <View className='mb-6'>
          <View className='flex items-center mb-3'>
            <Icon name='bookmark-o' className='text-green-500 mr-2' />
            <Text className='font-medium'>中长期改善</Text>
          </View>
          <View className='bg-green-50 rounded-lg p-4'>
            <Text className='text-sm font-medium mb-1'>情绪管理21天训练营</Text>
            <Text className='text-sm text-gray-600 mb-3'>
              系统学习情绪调节技巧
            </Text>
            <Button
              size='small'
              type='primary'
              className='w-full'
              onClick={() =>
                Taro.navigateTo({ url: '/pages/course/detail?id=emotion-21' })
              }
            >
              查看课程
            </Button>
          </View>
        </View>

        {/* 咨询引导 */}
        {scoreLevel === 'high' && (
          <View className='mb-6'>
            <View className='flex items-center mb-3'>
              <Icon name='service-o' className='text-red-500 mr-2' />
              <Text className='font-medium'>专业咨询</Text>
            </View>
            <View className='bg-red-50 rounded-lg p-4 border border-red-200'>
              <Text className='text-sm font-medium mb-1 text-red-700'>
                建议预约专业咨询师
              </Text>
              <Text className='text-sm text-red-600 mb-3'>
                您的得分表明可能需要专业帮助，我们建议您考虑咨询专业心理师
              </Text>
              <Button
                size='small'
                type='danger'
                className='w-full'
                onClick={() =>
                  Taro.navigateTo({ url: '/pages/consultation/index' })
                }
              >
                预约咨询
              </Button>
            </View>
          </View>
        )}
      </View>
    );
  };

  // 渲染历史追踪
  const renderHistoryTracking = () => {
    const recentHistory = useMeasureStore.use.recentHistory();
    const currentTestId = testRecord?.report.testId;

    // 筛选同类型测试的历史记录
    const testHistory = recentHistory
      .filter((record) => record.report.testId === currentTestId)
      .sort((a, b) => (b.endTime || 0) - (a.endTime || 0))
      .slice(0, 5);

    if (testHistory.length <= 1) return null;

    return (
      <View className='bg-white rounded-lg p-6 mb-6'>
        <View className='flex justify-between items-center mb-4'>
          <Text className='text-base font-bold'>历史追踪</Text>
          {/* 如果存在总分，则显示图表，否则不显示 */}
          {/* {testRecord?.report.result.score && (
            <Button
              size='small'
              type='primary'
              plain
              onClick={() => setShowHistoryChart(!showHistoryChart)}
            >
              {showHistoryChart ? '隐藏图表' : '显示图表'}
            </Button>
          )} */}
        </View>

        {/* {showHistoryChart && (
          <View className='mb-4'>
            <View className='h-32 bg-gray-50 rounded-lg p-4 relative'>
              <View className='flex justify-between items-end h-full'>
                {testHistory.map((record, index) => {
                  const score = record.report.result.score || 0;
                  const height = (score / 100) * 100;
                  return (
                    <View
                      key={record.id}
                      className='flex flex-col items-center'
                    >
                      <View
                        className='w-4 bg-blue-500 rounded-t'
                        style={{ height: `${height}%` }}
                      />
                      <Text className='text-xs text-gray-500 mt-1'>
                        {new Date(record.endTime || 0).toLocaleDateString()}
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          </View>
        )} */}

        {/* 时间轴 */}
        <View className='space-y-3'>
          {testHistory.map((record, index) => (
            <View key={record.id} className='flex items-start'>
              <View className='w-2 h-2 rounded-full bg-primary mt-2 mr-3 flex-shrink-0' />
              <View className='flex-1'>
                <View className='flex justify-between items-center mb-2'>
                  <Text className='text-sm font-medium'>
                    {new Date(record.endTime || 0).toLocaleDateString()}
                  </Text>
                  {/* 得分 如果没有总分，则汇总显示各维度得分*/}
                  <View className='text-sm text-primary'>
                    {record.report.result.score ||
                      record.report.result.dimensionScores
                        ?.map(
                          (dimension) =>
                            `${dimension.dimension}: ${dimension.score}`
                        )
                        .join(',') ||
                      0}
                  </View>
                </View>
                <Text
                  className='text-xs text-primary'
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/pages/sub-packages/measure/report/index?recordId=${record.id}`,
                    });
                  }}
                >
                  {record.report.result.interpretation?.level || '详情'}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // 渲染隐私保护与免责声明
  const renderPrivacyAndDisclaimer = () => {
    return (
      <View className='bg-white rounded-lg p-6 mb-6'>
        {/* 数据加密标识 */}
        <View className='flex items-center justify-center mb-4'>
          <Icon name='shield-o' className='text-green-500 mr-2' size='20px' />
          <Text className='text-sm text-green-600 font-medium'>
            银行级加密保护
          </Text>
        </View>

        {/* 免责声明 */}
        <View className='bg-gray-50 rounded-lg p-4'>
          <Text className='text-xs text-gray-600 leading-relaxed text-center'>
            本测试结果仅供参考，不能作为临床诊断依据。如有心理健康问题，请及时咨询专业心理医生或精神科医生。
          </Text>
        </View>
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{
        title: '测试结果',
        showBackButton: true,
        onBack: handleBackToHome,
      }}
    >
      {!testRecord ? (
        <View className='flex justify-center items-center h-full'>
          <DotLoading />
        </View>
      ) : (
        <View className='p-4 bg-gray-50 min-h-screen'>
          {/* 报告概览区 */}
          {renderReportOverview()}

          {/* 维度分解 */}
          {renderDimensionAnalysis()}

          {/* 行动建议 */}
          {/* {renderActionRecommendations()} */}

          {/* 历史追踪 */}
          {renderHistoryTracking()}

          {/* 隐私保护与免责声明 */}
          {renderPrivacyAndDisclaimer()}

          {/* 操作按钮 */}
          <View className='flex space-x-4 mb-6'>
            <Button
              className='flex-1 bg-blue-500 text-white'
              onClick={handleRetakeTest}
            >
              重新测试
            </Button>

            <Button
              className='flex-1 bg-gray-200 text-gray-700'
              onClick={handleBackToHome}
            >
              返回首页
            </Button>
          </View>
        </View>
      )}
    </PageLol>
  );
}
