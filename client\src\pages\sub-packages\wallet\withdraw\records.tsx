import { Button, Empty, Loading, Tag } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { paymentActions } from '@core/actions/payment.action';
import { WithdrawRecord } from '@model/payment.interface';
import { usePaymentStore } from '@stores/payment.store';
import { Text, View } from '@tarojs/components';
import { useDidShow, useReachBottom } from '@tarojs/taro';
import { useCallback } from 'react';

/**
 * 提现记录页面
 */
export default function WithdrawRecordsPage() {
  const withdrawRecords = usePaymentStore.use.withdrawRecords();
  const loading = usePaymentStore.use.loading();

  // 页面显示时加载提现记录
  useDidShow(() => {
    refreshWithdrawRecords();
  });

  // 刷新提现记录
  const refreshWithdrawRecords = useCallback(async () => {
    await paymentActions.fetchWithdrawRecords({ forceRefresh: true });
  }, []);

  // 加载更多提现记录
  const loadMoreWithdrawRecords = useCallback(async () => {
    if (withdrawRecords.pagination?.hasNext && !withdrawRecords.loading) {
      await paymentActions.loadMoreWithdrawRecords();
    }
  }, [withdrawRecords.pagination?.hasNext, withdrawRecords.loading]);

  // 监听滚动到底部事件
  useReachBottom(() => {
    loadMoreWithdrawRecords();
  });

  // 获取状态标签
  const getStatusTag = (status: WithdrawRecord['status']) => {
    switch (status) {
      case 'pending':
        return (
          <Tag plain type='primary'>
            审核中
          </Tag>
        );
      case 'processing':
        return (
          <Tag plain type='warning'>
            处理中
          </Tag>
        );
      case 'completed':
        return (
          <Tag plain type='success'>
            已完成
          </Tag>
        );
      case 'failed':
        return (
          <Tag plain type='danger'>
            已失败
          </Tag>
        );
      default:
        return <Tag plain>未知</Tag>;
    }
  };

  // 格式化时间
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
      2,
      '0'
    )}-${String(date.getDate()).padStart(2, '0')} ${String(
      date.getHours()
    ).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 取消提现
  const cancelWithdraw = async (id: string) => {
    const result = await paymentActions.cancelWithdrawApplication(id);
    if (result) {
      refreshWithdrawRecords();
    }
  };

  // 下拉刷新
  const onPullDownRefresh = async () => {
    await refreshWithdrawRecords();
  };

  return (
    <PageLol
      navigationProps={{
        title: '提现记录',
        showBackButton: true,
      }}
      onPullDownRefresh={onPullDownRefresh}
    >
      <View className='min-h-screen pb-16 bg-gray-1'>
        {/* 提现记录列表 */}
        {loading && withdrawRecords.list.length === 0 ? (
          <View className='py-10 flex justify-center'>
            <Loading type='spinner' />
          </View>
        ) : withdrawRecords.list.length === 0 ? (
          <Empty description='暂无提现记录' />
        ) : (
          <View className='bg-bg mx-4 mt-4 rounded-xl mb-6'>
            {withdrawRecords.list.map((record) => (
              <View
                key={record.id}
                className='px-4 py-3 border-b border-gray-100 last:border-0'
              >
                <View className='flex justify-between mb-2'>
                  <Text className='font-medium'>
                    ¥{record.amount.toFixed(2)}
                  </Text>
                  {getStatusTag(record.status)}
                </View>

                {/* 详细信息 */}
                <View className='text-sm text-secondary'>
                  <View className='flex justify-between mb-1'>
                    <Text>提现方式：</Text>
                    <Text>{record.bankName || '微信'}</Text>
                  </View>
                  <View className='flex justify-between mb-1'>
                    <Text>申请时间：</Text>
                    <Text>{formatDate(record.createdAt)}</Text>
                  </View>
                  {record.status === 'completed' && record.completedAt && (
                    <View className='flex justify-between mb-1'>
                      <Text>到账时间：</Text>
                      <Text>{formatDate(record.completedAt)}</Text>
                    </View>
                  )}
                  {record.status === 'failed' && record.remark && (
                    <View className='mb-1'>
                      <Text>失败原因：{record.remark}</Text>
                    </View>
                  )}
                </View>

                {/* 操作按钮 */}
                {record.status === 'pending' && (
                  <View className='mt-2 flex justify-end'>
                    <Button
                      size='small'
                      plain
                      hairline
                      onClick={() => cancelWithdraw(record.id)}
                    >
                      取消申请
                    </Button>
                  </View>
                )}
              </View>
            ))}

            {/* 加载更多 */}
            <View className='py-3 text-center text-sm text-secondary'>
              {withdrawRecords.loading ? (
                <Loading type='spinner' size='24px' />
              ) : withdrawRecords.pagination?.hasNext ? (
                <Text className='px-4 py-2' onClick={loadMoreWithdrawRecords}>
                  加载更多
                </Text>
              ) : (
                <Text>没有更多了</Text>
              )}
            </View>
          </View>
        )}
      </View>
    </PageLol>
  );
}
