/**
 * 格式化unix时间戳为可读字符串
 * @param timestamp unix时间戳（毫秒）
 * @returns 格式化后的时间字符串，格式：YYYY-MM-DD HH:mm:ss
 */
export function formatTime(timestamp: number): string {
  if (!timestamp) return "-";

  const date = new Date(timestamp);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化unix时间戳为日期字符串
 * @param timestamp unix时间戳（毫秒）
 * @returns 格式化后的日期字符串，格式：YYYY-MM-DD
 */
export function formatDate(timestamp: number): string {
  if (!timestamp) return "-";

  const date = new Date(timestamp);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

/**
 * 格式化相对时间
 * @param timestamp unix时间戳（毫秒）
 * @returns 相对时间字符串，如：刚刚、5分钟前、2小时前、昨天、3天前、2周前、1个月前
 */
export function formatRelativeTime(timestamp: number): string {
  if (!timestamp) return "-";

  const now = Date.now();
  const diffSeconds = Math.floor((now - timestamp) / 1000);

  if (diffSeconds < 60) return "刚刚";
  if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}分钟前`;
  if (diffSeconds < 86400) return `${Math.floor(diffSeconds / 3600)}小时前`;
  if (diffSeconds < 172800) return "昨天";
  if (diffSeconds < 604800) return `${Math.floor(diffSeconds / 86400)}天前`;
  if (diffSeconds < 2592000) return `${Math.floor(diffSeconds / 604800)}周前`;
  if (diffSeconds < 31536000)
    return `${Math.floor(diffSeconds / 2592000)}个月前`;

  return `${Math.floor(diffSeconds / 31536000)}年前`;
}
