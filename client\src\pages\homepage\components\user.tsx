/* eslint-disable react/jsx-boolean-value */
// import logo from "@/assets/logo.png"; // 请将logo图片放在src/assets/logo.png
import { Button, Image, NoticeBar, Search } from '@antmjs/vantui';
import illustration1 from '@assets/images/bubles.png';
import illustration2 from '@assets/images/Intersect.png';
import logo from '@assets/logo.png';
import DotLoading from '@components/common/loading-dots';
import PageLol from '@components/common/page-meta';
import HomeBanners from '@components/home-swiper';
import TherapistCard from '@components/therapist/TherapistCard';
import { HOME_TABS } from '@constants/text';
import { COLORS } from '@constants/theme';
import { homeActions } from '@core/actions/home.action';
import { ConsultationDirection } from '@model/common.interface';
import { useHomeStore } from '@stores/home.store';
import { Text, View } from '@tarojs/components';
import { appRouter } from '@utils/router';
import { useEffect, useState } from 'react';
import CustomTabs from 'src/components/custom-tabs';

// 导航栏自定义内容
// 使用slot显示logo和标题
const NavTitleWithLogo = () => {
  return (
    <View className='flex flex-row items-center gap-2'>
      {/* Logo图标 */}
      <Image src={logo} round width={56} height={56} />

      {/* 心之安标题 */}
      <Text className='text-lg font-bold' style={` color: ${COLORS.primary} `}>
        心之安
      </Text>
    </View>
  );
};

export default function UserHomePage() {
  const [tab, setTab] = useState(ConsultationDirection.ANXIETY);
  const { notices, banners, recommendedTherapists, loading } = useHomeStore();

  // 获取推荐咨询师
  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          homeActions.fetchRecommendedTherapists(tab, false, 4),
          homeActions.fetchNotices('user'),
          homeActions.fetchBanners('user'),
        ]);
      } catch (error) {
        console.error('获取推荐咨询师失败:', error);
      }
    };

    loadData();
  }, [tab]);

  // 切换分类标签
  const handleTabChange = (key: string | number) => {
    setTab(key as ConsultationDirection);
  };

  return (
    <PageLol
      navigationProps={{
        children: <NavTitleWithLogo />,
      }}
      withTabBarSpace={true}
    >
      <View className='homepage-content'>
        {/* noticeBar */}
        {notices && notices.length > 0 && (
          <NoticeBar
            className='rounded-lg mx-4 mt-3'
            leftIcon='volume-o'
            text={notices[0].content}
            mode={notices[0].model}
            url={notices[0].url}
            openType={notices[0].openType}
          />
        )}

        {/* 搜索框 */}
        <Search
          placeholder='查询医生或者健康问题'
          shape='round'
          className='mt-3'
          background={COLORS.pageBack}
        />
        {banners && banners.length > 0 && <HomeBanners banners={banners} />}

        {/* 导航区 1+2卡片 */}
        <View className='flex flex-row gap-2 px-4 mb-4 mt-3'>
          {/* 心理咨询卡片 */}
          <View
            className='flex-1 bg-gradient-to-br from-[#E0EAFC] to-[#CFDEF3] rounded-xl p-4 flex flex-col items-start justify-between h-[200Px] relative'
            onClick={appRouter.consultation}
          >
            <View className='flex flex-row items-center mb-2 z-10'>
              <Text className='text-base font-semibold mr-2'>心理咨询</Text>
            </View>
            <View className='flex flex-col items-start justify-center z-10'>
              <Text className='text-primary text-xl font-bold'>300+</Text>
              <Text className='text-md font-medium'>全国优质咨询师</Text>
            </View>
            <View className='flex flex-row items-center justify-start z-10'>
              <Button
                type='primary'
                size='small'
                round={true}
                className='self-start'
              >
                去预约
              </Button>
            </View>
            <Image
              src={illustration1}
              className='w-[132Px] h-[132Px] absolute bottom-0 right-0 z-0'
            />
          </View>
          <View className='flex-1 flex flex-col gap-2'>
            <View
              className='bg-gradient-to-br from-[#C9D6FF] to-[#E2E2E2] rounded-xl p-4 flex-1 flex flex-col justify-center gap-1'
              onClick={appRouter.measurement}
            >
              <Text className='text-base font-bold'>免费心理测量</Text>
              <Text className='text-md text-secondary mt-1'>测量工具</Text>
            </View>
            <View
              className='bg-gradient-to-br from-[#D3CCE3] to-[#E9E4F0] rounded-xl p-4 flex-1 flex flex-col justify-center gap-1 relative'
              onClick={appRouter.treatment}
            >
              <Text className='text-base font-bold'>正念·冥想</Text>
              <Text className='text-md text-secondary mt-1'>艺术疗愈</Text>
              <Image
                src={illustration2}
                className='w-[50Px] h-[92Px] absolute bottom-0 right-0 z-0'
              />
            </View>
          </View>
        </View>

        {/* 推荐心理师tabs和卡片列表 */}
        <View className='flex-1 bg-white rounded-t-2xl mt-4 mx-4'>
          <View className='px-4 pt-4'>
            <CustomTabs
              tabs={HOME_TABS}
              active={tab}
              onChange={handleTabChange}
            />
          </View>

          <View className='w-full'>
            {!loading &&
              recommendedTherapists &&
              recommendedTherapists.length > 0 &&
              (recommendedTherapists.length > 0 ? (
                recommendedTherapists.map((item, index) => (
                  <TherapistCard
                    key={item.id}
                    therapist={item}
                    backgroundColor='transparent'
                    showDivider={index !== recommendedTherapists.length - 1}
                    onClick={() =>
                      appRouter.therapistDetail(item.id.toString())
                    }
                  />
                ))
              ) : (
                <View className='p-4 text-center'>
                  <Text className='text-secondary'>暂无推荐咨询师</Text>
                </View>
              ))}
          </View>

          {loading && (
            <View className='px-4 py-2 flex justify-center'>
              <DotLoading />
            </View>
          )}
        </View>
      </View>
    </PageLol>
  );
}
