import { Pagination } from '@core/api';
import { Order_review } from '@model/order.interface';
import {
  therapist_extend,
  therapist_schedule,
  therapist_service,
  therapist_summary,
  TherapistFilters,
} from '@model/therapist.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

/**
 * 咨询师状态管理 store
 * 存储咨询师列表、详情和状态
 */
interface State {
  // 咨询师列表数据
  therapists: therapist_summary[] | null;
  // 推荐咨询师id列表
  recommendTherapists: string[] | null;
  // 当前查看的咨询师详情
  currentTherapistExtinfo: therapist_extend | null;
  // 当前查看的咨询师排期信息
  currentTherapistOccuppied: therapist_schedule | null;
  // 当前查看的咨询师优选评价
  currentTherapistPreferRating: Order_review[] | null;
  // 当前查看的咨询师服务
  currentTherapistService: therapist_service | null;
  // 列表加载状态
  loading: boolean;
  // 详情加载状态
  loadingDetail: boolean;
  // 错误信息
  error: string | null;
  // 详情错误信息
  detailError: string | null;
  // 分页信息
  pagination: Pagination | null;
  // 筛选条件
  filters: TherapistFilters | null;
}

interface Action {
  setTherapists: (therapists: therapist_summary[]) => void;
  setCurrentTherapistExtinfo: (extinfo: therapist_extend) => void;
  setCurrentTherapistOccuppied: (occupied: therapist_schedule) => void;
  setCurrentTherapistPreferRating: (rating: Order_review[]) => void;
  setCurrentTherapistService: (service: therapist_service) => void;
  updateSummary: (summary: therapist_summary) => void;
  reset: () => void;
}

const initialState: State = {
  therapists: null,
  recommendTherapists: null,
  currentTherapistExtinfo: null,
  currentTherapistOccuppied: null,
  currentTherapistService: null,
  currentTherapistPreferRating: null,
  loading: false,
  loadingDetail: false,
  error: null,
  detailError: null,
  pagination: null,
  filters: null,
};

/**
 * 咨询师状态管理 store
 */
const therapistStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        // 初始状态
        ...initialState,
        setTherapists: (therapists) => set({ therapists }),
        setCurrentTherapistExtinfo: (extinfo) =>
          set({ currentTherapistExtinfo: extinfo }),
        setCurrentTherapistOccuppied: (occupied) =>
          set({ currentTherapistOccuppied: occupied }),
        setCurrentTherapistPreferRating: (rating) =>
          set({ currentTherapistPreferRating: rating }),
        setCurrentTherapistService: (service) =>
          set({ currentTherapistService: service }),
        // 存在则更新，不存在则添加
        updateSummary: (summary) =>
          set((state) => {
            const index = state.therapists?.findIndex(
              (therapist) => therapist.id === summary.id
            );
            if (index !== undefined && index !== -1) {
              const newTherapists = [...state.therapists!];
              newTherapists[index] = summary;
              return { therapists: newTherapists };
            } else {
              if (state.therapists) {
                return { therapists: [...state.therapists, summary] };
              } else {
                return { therapists: [summary] };
              }
            }
          }),
        reset: () => set(initialState),
      }),
      {
        //! 注意这里的name是当前这个Zustand模块进行缓存时的唯一key, 每个需要缓存的Zustand模块都必须分配一个唯一key
        name: StorageSceneKey.THERAPIST,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 过滤选项
        partialize: (state) => ({
          filters: state.filters,
        }),
      }
    )
  )
);

export const useTherapistStore = createSelectors(therapistStore);
