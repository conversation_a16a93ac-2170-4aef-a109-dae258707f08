import { Checkbox, Stepper } from '@antmjs/vantui';
import { SERVICE_TYPE_MAP, SERVICE_TYPE_MAP_SHORT } from '@constants/text';
import { Service, ServiceType } from '@model/service.interface';
import { Text, View } from '@tarojs/components';
import { useEffect, useState } from 'react';

// 所有可用的服务类型
const ALL_SERVICES = [
  {
    type: ServiceType.VIDEO,
    name: SERVICE_TYPE_MAP[ServiceType.VIDEO],
    shortName: SERVICE_TYPE_MAP_SHORT[ServiceType.VIDEO],
    defaultPrice: 200,
    defaultDuration: 60,
    enabled: true,
  },
  {
    type: ServiceType.FACE_TO_FACE,
    name: SERVICE_TYPE_MAP[ServiceType.FACE_TO_FACE],
    shortName: SERVICE_TYPE_MAP_SHORT[ServiceType.FACE_TO_FACE],
    defaultPrice: 300,
    defaultDuration: 60,
    enabled: true,
  },
];

interface ServiceConfigEditorProps {
  services?: Service[];
  onServicesChange: (services: Service[]) => void;
  showTitle?: boolean;
  showDescription?: boolean;
}

export default function ServiceConfigEditor({
  services = [],
  onServicesChange,
  showTitle = true,
  showDescription = true,
}: ServiceConfigEditorProps) {
  // 服务配置状态
  const [serviceConfigs, setServiceConfigs] = useState<Service[]>([]);

  // 初始化服务配置
  useEffect(() => {
    const configs = ALL_SERVICES.map((service) => {
      const existingService = services.find((s) => s.type === service.type);
      return {
        type: service.type,
        enabled: !!existingService && existingService.enabled !== false,
        price: existingService?.price || service.defaultPrice,
        duration: existingService?.duration || service.defaultDuration,
        finalPrice: existingService?.finalPrice || service.defaultPrice,
      };
    });
    setServiceConfigs(configs);
  }, [services]);

  // 当服务配置变化时，同步更新父组件数据（仅在用户操作时触发）
  const handleServiceConfigChange = (newConfigs: Service[]) => {
    setServiceConfigs(newConfigs);
    const enabledServices = newConfigs.filter((config) => config.enabled);
    onServicesChange(enabledServices);
  };

  // 处理服务启用/禁用
  const handleServiceToggle = (serviceType: ServiceType, enabled: boolean) => {
    const updatedConfigs = serviceConfigs.map((config) =>
      config.type === serviceType ? { ...config, enabled } : config
    );
    handleServiceConfigChange(updatedConfigs);
  };

  // 处理价格变化
  const handlePriceChange = (serviceType: ServiceType, price: number) => {
    const updatedConfigs = serviceConfigs.map((config) =>
      config.type === serviceType ? { ...config, price } : config
    );
    handleServiceConfigChange(updatedConfigs);
  };

  // 处理时长变化
  const handleDurationChange = (serviceType: ServiceType, duration: number) => {
    const updatedConfigs = serviceConfigs.map((config) =>
      config.type === serviceType ? { ...config, duration } : config
    );
    handleServiceConfigChange(updatedConfigs);
  };

  // 处理最终价格变化
  const handleFinalPriceChange = (
    serviceType: ServiceType,
    finalPrice: number
  ) => {
    const updatedConfigs = serviceConfigs.map((config) =>
      config.type === serviceType ? { ...config, finalPrice } : config
    );
    handleServiceConfigChange(updatedConfigs);
  };

  return (
    <View>
      {showTitle && (
        <Text className='text-lg font-bold mb-3 block'>服务列表</Text>
      )}
      {showDescription && (
        <Text className='text-sm text-gray-500 mb-4 block'>
          勾选要提供的服务类型，并设置相应的价格、时长和最终价格
        </Text>
      )}

      <View className='space-y-4'>
        {serviceConfigs.map((config) => {
          const serviceData = ALL_SERVICES.find((s) => s.type === config.type);
          if (!serviceData) return null;

          return (
            <View
              key={config.type}
              className='bg-white border border-border rounded-lg p-4'
            >
              {/* 服务标题和启用开关 */}
              <View className='flex justify-between items-center mb-3'>
                <View className='flex items-center'>
                  <Checkbox
                    value={config.enabled}
                    onChange={(e) => handleServiceToggle(config.type, e.detail)}
                    shape='round'
                    iconSize='20px'
                  >
                    <Text className='ml-2 font-medium'>{serviceData.name}</Text>
                  </Checkbox>
                </View>
              </View>

              {/* 服务配置（仅在启用时显示） */}
              {config.enabled && (
                <View className='space-y-3'>
                  {/* 价格设置 */}
                  <View className='flex justify-between items-center'>
                    <Text className='text-sm text-gray-600'>咨询价格</Text>
                    <View className='flex items-center'>
                      <Text className='mr-2 text-sm'>¥</Text>
                      <Stepper
                        value={config.price}
                        min={10}
                        max={1000}
                        step={10}
                        onChange={(e) =>
                          handlePriceChange(config.type, Number(e.detail))
                        }
                      />
                      <Text className='ml-2 text-sm'>元/次</Text>
                    </View>
                  </View>

                  {/* 时长设置 */}
                  <View className='flex justify-between items-center'>
                    <Text className='text-sm text-gray-600'>咨询时长</Text>
                    <View className='flex items-center'>
                      <Stepper
                        value={config.duration}
                        min={15}
                        max={180}
                        step={5}
                        onChange={(e) =>
                          handleDurationChange(config.type, Number(e.detail))
                        }
                      />
                      <Text className='ml-2 text-sm'>分钟/次</Text>
                    </View>
                  </View>

                  {/* 最终价格设置 */}
                  <View className='flex justify-between items-center'>
                    <Text className='text-sm text-gray-600'>最终价格</Text>
                    <View className='flex items-center'>
                      <Text className='mr-2 text-sm'>¥</Text>
                      <Stepper
                        value={config.finalPrice}
                        min={10}
                        max={1000}
                        step={10}
                        onChange={(e) =>
                          handleFinalPriceChange(config.type, Number(e.detail))
                        }
                      />
                      <Text className='ml-2 text-sm'>元/次</Text>
                    </View>
                  </View>

                  {/* 服务预览 */}
                  <View className='bg-gray-50 p-3 rounded'>
                    <Text className='text-sm text-gray-600'>
                      {serviceData.shortName}咨询：¥{config.finalPrice}元/
                      {config.duration}分钟
                    </Text>
                  </View>
                </View>
              )}
            </View>
          );
        })}
      </View>
    </View>
  );
}
