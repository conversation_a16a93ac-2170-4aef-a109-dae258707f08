import { PlaybackState, TreatmentAudio } from '@model/treatment.interface';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';

interface State {
  // 音频
  audio: TreatmentAudio | null;
  // 播放状态
  playbackState: PlaybackState;
  // 加载状态
  loading: boolean;
}

interface Action {
  // 设置音频
  setAudio: (audio: TreatmentAudio | null) => void;
  // 播放控制
  setPlaybackState: (state: Partial<PlaybackState>) => void;
  startPlaying: () => void;
  pausePlaying: () => void;
  updateProgress: (currentTime: number, duration: number) => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
}

const initialState: State = {
  audio: null,
  playbackState: {
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    progress: 0,
  },
  loading: false,
};

const audioStore = create<State & Action>()(
  immer((set) => ({
    ...initialState,
    setAudio: (audio) => set({ audio }),
    setPlaybackState: (playbackState) =>
      set({
        playbackState: { ...initialState.playbackState, ...playbackState },
      }),
    startPlaying: () =>
      set((state) => {
        state.playbackState.isPlaying = true;
      }),
    pausePlaying: () =>
      set((state) => {
        state.playbackState.isPlaying = false;
      }),
    updateProgress: (currentTime, duration) =>
      set((state) => {
        state.playbackState.currentTime = currentTime;
        state.playbackState.duration = duration;
        state.playbackState.progress = (currentTime / duration) * 100;
      }),
    setLoading: (loading) => set({ loading }),
    reset: () => set(initialState),
  }))
);

export const useAudioStore = createSelectors(audioStore);
