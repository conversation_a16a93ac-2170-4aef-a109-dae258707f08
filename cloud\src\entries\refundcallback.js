// 云函数入口文件
const cloud = require("wx-server-sdk");
const {
  onRefundSuccess,
  onRefundFailed,
  invokePaymentRefund,
} = require("../service/orderOperation");
const { COLLECTIONS, REFUND_STATUS } = require("../common/db.constants");
const { coordinator } = require("../common/transaction-coordinator");
require("../common/transaction-handlers"); // 加载回滚处理函数

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 最大重试次数
const MAX_RETRY_COUNT = 3;

// 云函数入口函数
exports.main = async (event, context) => {
  console.log("退款回调数据:", event);

  try {
    // 解析回调数据
    const { returnCode, returnMsg } = event;

    // 验证返回结果,如果通信失败，则主动查询订单
    if (returnCode !== "SUCCESS") {
      console.error(
        returnMsg || "收到退款回调，但returnCode不正确",
        event.out_trade_no
      );

      return { errcode: 1, errmsg: "收到退款回调，但通信失败" };
    }

    const {
      resultCode,
      refundStatus,
      errCode,
      errCodeDes,
      outTradeNo,
      outRefundNo,
    } = event;
    // 查询退款记录
    const refundRecordResult = await db
      .collection(COLLECTIONS.REFUND_RECORD)
      .where({ outTradeNo, outRefundNo })
      .get();
    if (!refundRecordResult.data || refundRecordResult.data.length === 0) {
      console.error("未找到退款记录:", outTradeNo);
      return { errcode: 0, errmsg: "未找到退款记录" };
    }
    const refundRecord = refundRecordResult.data[0];

    // 获取退款重试次数
    const retryCount = refundRecord.retryCount || 0;

    // 如果已经处理完成，直接返回
    if (refundRecord.status === REFUND_STATUS.COMPLETED) {
      console.error("退款记录已退款:", refundRecord.orderId);
      return { errcode: 0, errmsg: "退款记录已退款" };
    }

    // 获取关联的事务ID（如果有）
    const txId = refundRecord._txId;
    let refundTxId = null;

    if (resultCode !== "SUCCESS" && refundStatus !== "SUCCESS") {
      // 开始一个新的退款失败处理事务
      refundTxId = await coordinator.startTransaction(
        refundRecord.orderId,
        "refund_failed",
        {
          outTradeNo,
          outRefundNo,
          errCode,
          errCodeDes,
          originalTxId: txId,
        }
      );

      // 处理各种退款失败情况
      if (errCode === "SYSTEMERROR" || errCode === "BIZERR_NEED_RETRY") {
        // 系统错误，需要重试
        if (retryCount < MAX_RETRY_COUNT) {
          // 添加事务步骤
          await coordinator.addStep(refundTxId, {
            name: "update_refund_retry",
            data: {
              refundRecordId: refundRecord._id,
              orderId: refundRecord.orderId,
              outRefundNo,
              retryCount: retryCount + 1,
            },
          });

          // 更新重试次数
          await db
            .collection(COLLECTIONS.REFUND_RECORD)
            .doc(refundRecord._id)
            .update({
              data: {
                retryCount: retryCount + 1,
                lastRetryTime: Date.now(),
                lastError: errCodeDes || errCode,
                updatedAt: Date.now(),
                _txId: refundTxId,
              },
            });

          await coordinator.completeStep(refundTxId, "update_refund_retry");

          // 添加事务步骤
          await coordinator.addStep(refundTxId, {
            name: "retry_refund",
            data: {
              refundRecordId: refundRecord._id,
              orderId: refundRecord.orderId,
              outRefundNo,
            },
          });

          // 重试退款
          await invokePaymentRefund(refundRecord);

          await coordinator.completeStep(refundTxId, "retry_refund");
          await coordinator.commitTransaction(refundTxId);

          return { errcode: 0, errmsg: "退款失败，重试中" };
        } else {
          // 超过最大重试次数，标记为失败
          console.error(
            `退款失败，已达到最大重试次数(${MAX_RETRY_COUNT})`,
            refundRecord.orderId
          );

          // 添加事务步骤
          await coordinator.addStep(refundTxId, {
            name: "mark_refund_failed",
            data: {
              refundRecordId: refundRecord._id,
              orderId: refundRecord.orderId,
              outRefundNo,
              reason: `退款失败，已达到最大重试次数(${MAX_RETRY_COUNT}): ${
                errCodeDes || errCode
              }`,
            },
          });

          await onRefundFailed(
            refundRecord,
            `退款失败，已达到最大重试次数(${MAX_RETRY_COUNT}): ${
              errCodeDes || errCode
            }`
          );

          await coordinator.completeStep(refundTxId, "mark_refund_failed");
          await coordinator.commitTransaction(refundTxId);

          return { errcode: 0, errmsg: "退款失败，已达到最大重试次数" };
        }
      } else {
        // 其他错误情况，标记为失败
        let failReason = "退款失败";

        if (errCode === "REFUND_FEE_NOT_ENOUGH") {
          failReason = "退款金额不足";
        } else if (errCode === "TRADE_OVERDUE") {
          failReason = "订单已经超过退款期限";
        } else if (errCode === "REFUND_NOT_ALLOWED") {
          failReason = "退款不允许";
        } else {
          failReason = errCodeDes || "退款失败";
        }

        console.error(
          `${failReason}:`,
          refundRecord.orderId,
          errCode,
          errCodeDes
        );

        // 添加事务步骤
        await coordinator.addStep(refundTxId, {
          name: "mark_refund_failed",
          data: {
            refundRecordId: refundRecord._id,
            orderId: refundRecord.orderId,
            outRefundNo,
            reason: failReason,
          },
        });

        await onRefundFailed(refundRecord, failReason);

        await coordinator.completeStep(refundTxId, "mark_refund_failed");
        await coordinator.commitTransaction(refundTxId);

        return { errcode: 0, errmsg: failReason };
      }
    }

    // 退款成功
    // 开始一个新的退款成功处理事务，或继续使用原有事务
    const successTxId =
      txId ||
      (await coordinator.startTransaction(
        refundRecord.orderId,
        "refund_success",
        {
          outTradeNo,
          outRefundNo,
        }
      ));

    try {
      // 添加事务步骤
      if (!txId) {
        await coordinator.addStep(successTxId, {
          name: "process_refund_success",
          data: {
            refundRecordId: refundRecord._id,
            orderId: refundRecord.orderId,
            outRefundNo,
          },
        });
      }

      await onRefundSuccess(refundRecord);

      if (!txId) {
        await coordinator.completeStep(successTxId, "process_refund_success");
        await coordinator.commitTransaction(successTxId);
      }

      return { errcode: 0, errmsg: "退款成功" };
    } catch (error) {
      console.error("处理退款成功回调失败:", error);

      // 记录错误但仍然返回成功，避免微信重复回调
      // 这种情况需要人工干预，检查数据一致性
      await db
        .collection(COLLECTIONS.REFUND_RECORD)
        .doc(refundRecord._id)
        .update({
          data: {
            status: REFUND_STATUS.COMPLETED,
            processingError: error.message,
            needManualCheck: true,
            updatedAt: Date.now(),
          },
        });

      if (!txId) {
        // 回滚事务
        try {
          await coordinator.rollbackTransaction(successTxId, error.message);
        } catch (rollbackError) {
          console.error("事务回滚失败:", rollbackError);
        }
      }

      return {
        errcode: 0,
        errmsg: "退款标记为成功，但处理过程出错，需要人工检查",
        error: error.message,
      };
    }
  } catch (error) {
    console.error("退款回调处理失败:", error);
    // 返回成功以避免微信重复回调，但记录错误
    return {
      errcode: 0,
      errmsg: "退款回调处理失败，但返回成功避免重复回调",
      error: error.message,
    };
  }
};
