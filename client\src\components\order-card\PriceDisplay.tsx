import { Text } from '@tarojs/components';

interface PriceDisplayProps {
  price: number;
  size?: 'sm' | 'md' | 'lg';
  showUnit?: boolean;
  color?: 'primary' | 'default' | 'danger';
}

export default function PriceDisplay({
  price,
  size = 'lg',
  showUnit = true,
  color = 'primary',
}: PriceDisplayProps) {
  const textSizeMap = {
    sm: 'text-sm',
    md: 'text-md',
    lg: 'text-lg',
  };

  return (
    <Text className={`text-${color} ${textSizeMap[size]} font-bold`}>
      {showUnit && (
        <Text className={`text-${color} ${textSizeMap.md}`}>￥</Text>
      )}
      {price}
    </Text>
  );
}
