import { therapistActions } from '@core/actions/therapist.action';
import { TherapistListRequest } from '@core/api';
import { TherapistFilters } from '@model/therapist.interface';
import { useTherapistStore } from '@stores/therapist.store';
import { useCallback, useEffect, useState } from 'react';

/**
 * 咨询师列表管理Hook
 * 提供咨询师列表的获取、筛选和收藏功能
 */
export function useTherapistList(initialParams?: TherapistListRequest) {
  // 从store中获取状态
  const {
    therapists,
    pagination,
    loading,
    error: storeError,
    filters,
  } = useTherapistStore();

  // 本地状态
  const [params, setParams] = useState<TherapistListRequest>(
    initialParams ?? {
      page: 1,
      pageSize: 20,
    }
  );
  const [refreshing, setRefreshing] = useState(false); // 下拉刷新状态
  const [loadingMore, setLoadingMore] = useState(false); // 加载更多状态

  /**
   * 加载咨询师列表
   */
  const fetchTherapists = useCallback(
    async (searchParams: TherapistListRequest = params) => {
      console.log('fetchTherapists', searchParams);
      try {
        const result = await therapistActions.fetchTherapists(searchParams);
        console.log('fetchTherapists result', result);
        return true;
      } catch (err) {
        console.error('获取咨询师列表失败:', err);
        return false;
      }
    },
    [params]
  );

  /**
   * 刷新列表
   */
  const refreshTherapists = useCallback(async () => {
    console.log('refreshTherapists');
    setRefreshing(true);
    try {
      // 重置为第一页，保持其他参数
      const refreshParams = {
        ...params,
        page: 1,
        forceRefresh: true,
      };
      await fetchTherapists(refreshParams);
    } finally {
      setRefreshing(false);
    }
  }, [fetchTherapists, params]);

  /**
   * 加载更多
   */
  const loadMoreTherapists = useCallback(async () => {
    console.log('loadMoreTherapists');
    // 检查是否有更多数据可加载
    if (!pagination?.hasNext || loadingMore) return;

    setLoadingMore(true);
    try {
      await therapistActions.loadMore();
    } finally {
      setLoadingMore(false);
    }
  }, [pagination, loadingMore]);

  /**
   * 应用筛选条件
   */
  const applyFilters = useCallback(
    async (newFilters: TherapistFilters) => {
      console.log('applyFilters', newFilters);
      const filterParams: TherapistListRequest = {
        ...params,
        page: 1, // 重置为第一页
        filters: newFilters,
      };

      setParams(filterParams);
      await therapistActions.applyFilters(newFilters);
    },
    [params]
  );

  /**
   * 切换收藏状态
   */
  //   const toggleFavorite = useCallback(
  //     async (therapistId: string, isFavorite: boolean) => {
  //       try {
  //         // 直接调用服务层API
  //         const result = await therapistService.favorite(
  //           therapistId,
  //           !isFavorite
  //         );

  //         // 更新列表中对应的咨询师
  //         if (therapists) {
  //           const updatedTherapists = therapists.map((t) =>
  //             t.id.toString() === therapistId.toString()
  //               ? { ...t, isFavorite: !isFavorite }
  //               : t
  //           );
  //           useTherapistStore.setState({ therapists: updatedTherapists });
  //         }

  //         return true;
  //       } catch (err) {
  //         console.error('收藏操作失败:', err);
  //         Taro.showToast({
  //           title: '操作失败',
  //           icon: 'none',
  //         });
  //         return false;
  //       }
  //     },
  //     [therapists]
  //   );

  // 初始加载
  useEffect(() => {
    if (!therapists) {
      console.log('useTherapistList useEffect', therapists);
      fetchTherapists();
    }
  }, [fetchTherapists, therapists]);

  return {
    therapists,
    pagination,
    loading,
    error: storeError,
    filters,
    refreshing,
    loadingMore,
    fetchTherapists,
    refreshTherapists,
    loadMoreTherapists,
    applyFilters,
  };
}
