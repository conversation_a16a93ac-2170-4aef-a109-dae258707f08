import { Image } from '@antmjs/vantui';
import DefaultImage from '@components/common/DefaultImage';
import DotLoading from '@components/common/loading-dots';
import PageLol from '@components/common/page-meta';
import useRenderCount from '@hooks/useRenderCount';
import {
  AUDIO_CATEGORY_MAP,
  AudioCategory,
  CategoryTab,
  TreatmentAudio,
} from '@model/treatment.interface';
import { useTreatmentStore } from '@stores/treatment.store';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatTime5 } from '@utils/time';
import { FC, memo, useCallback } from 'react';
import { useTreatmentPage } from './useTreatmentPage';

// 定义组件接口
interface CategoryTabsProps {
  categories: CategoryTab[];
  activeCategory: AudioCategory | string;
  handleTabClick: (category: AudioCategory) => void;
}

interface FeaturedAudioProps {
  audio: TreatmentAudio;
  handleAudioClick: (id: string) => void;
}

interface FeaturedAudiosProps {
  featuredNatureAudios: TreatmentAudio[];
  handleAudioClick: (id: string) => void;
}

interface AudioCardProps {
  music: TreatmentAudio;
  handleAudioClick: (id: string) => void;
}

interface CategoryAudioSectionProps {
  categoryTitle: string;
  audios: TreatmentAudio[];
  handleAudioClick: (id: string) => void;
}

interface AudioListProps {
  activeCategory: AudioCategory | string;
  categories: CategoryTab[];
  handleAudioClick: (id: string) => void;
  getAudiosByCategory: (categoryId: AudioCategory) => TreatmentAudio[];
  audioListByCategory: Record<string, TreatmentAudio[]>;
}

// 分类标签组件
const CategoryTabs: FC<CategoryTabsProps> = memo(
  ({ categories, activeCategory, handleTabClick }) => {
    // 跟踪渲染次数
    useRenderCount('CategoryTabs');

    return (
      <View className='px-4 pt-3 pb-2'>
        <ScrollView
          className='whitespace-nowrap'
          scrollX
          enhanced
          showScrollbar={false}
        >
          <View className='inline-flex space-x-3'>
            {categories.map((tab: CategoryTab, index: number) => (
              <View
                key={tab.category}
                className={`px-2 py-1 rounded-full transition-colors duration-200 ${
                  activeCategory === tab.category
                    ? 'bg-primary text-white'
                    : ` text-secondary ${
                        index % 3 === 0
                          ? 'bg-blue-50'
                          : index % 3 === 1
                          ? 'bg-green-50'
                          : 'bg-pink-50'
                      }`
                }`}
                onClick={() => handleTabClick(tab.category)}
              >
                <Text className='text-sm font-medium'>{tab.title}</Text>
                {tab.count ? (
                  <Text className='text-xs ml-1 opacity-80'>({tab.count})</Text>
                ) : null}
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  }
);

// 推荐音频组件 - 简化版
const FeaturedAudio: FC<FeaturedAudioProps> = memo(
  ({ audio, handleAudioClick }) => {
    useRenderCount('FeaturedAudio');

    return (
      <View
        className='relative rounded-3xl h-52 overflow-hidden shadow-md'
        onClick={() => handleAudioClick(audio.id)}
      >
        <Image
          src={audio.coverImage}
          width='100%'
          height='100%'
          fit='cover'
          renderLoading={<DotLoading />}
          renderError={<DefaultImage size={100} />}
        />
        <View className='absolute px-6 py-3 w-full h-full inset-0 bg-black bg-opacity-30 flex flex-col items-start justify-between'>
          {/* 标题 */}
          <View className='text-white'>
            <Text className='text-2xl font-bold block'>{audio.title}</Text>
            <Text className='text-sm block mt-1'>{audio.description}</Text>
          </View>
          {/* 时长 */}
          <View className='w-full flex justify-end'>
            <Text className='text-white text-sm font-medium'>
              {formatTime5(audio.duration)}
            </Text>
          </View>
        </View>
      </View>
    );
  }
);

// 推荐音频列表组件
const FeaturedAudios: FC<FeaturedAudiosProps> = memo(
  ({ featuredNatureAudios, handleAudioClick }) => {
    useRenderCount('FeaturedAudios');

    return (
      <View className='px-4 py-4'>
        <View className='flex justify-between items-center mb-3'>
          <Text className='text-base font-bold'>今日推荐</Text>
          <View className='w-6 h-6 flex items-center justify-center'>
            <Text className='text-xl'>⋮</Text>
          </View>
        </View>

        {/* 推荐卡片 */}
        {featuredNatureAudios.map((audio) => (
          <FeaturedAudio
            key={audio.id}
            audio={audio}
            handleAudioClick={handleAudioClick}
          />
        ))}
      </View>
    );
  }
);

// 音频卡片组件 - 新设计
const AudioCard: FC<AudioCardProps> = memo(({ music, handleAudioClick }) => {
  useRenderCount('AudioCard');

  return (
    <View className='mr-3 w-24' onClick={() => handleAudioClick(music.id)}>
      <Image
        src={music.thumbnail || music.coverImage}
        width='96px'
        height='120px'
        fit='cover'
        radius='8px'
        lazyLoad
        renderLoading={<DotLoading />}
        renderError={<DefaultImage size={96} />}
      />
      <View className='text-md font-medium truncate  mt-1'>{music.title}</View>
      <Text className='text-sm font-bold text-primary'>
        {formatTime5(music.duration ?? 0)}
      </Text>
    </View>
  );
});

// 分类音频列表
const CategoryAudioSection: FC<CategoryAudioSectionProps> = memo(
  ({ categoryTitle, audios, handleAudioClick }) => {
    useRenderCount('CategoryAudioSection');

    // 将音频列表分为两行
    const rows: Array<typeof audios> = [[], []];

    // 将音频平均分配到两行
    audios.forEach((audio, index) => {
      rows[index % 2].push(audio);
    });

    return (
      <View className='mb-6'>
        <Text className='text-base font-bold'>{categoryTitle}</Text>

        <ScrollView
          className='whitespace-nowrap mt-3'
          scrollX
          enhanced
          showScrollbar={false}
        >
          <View className='inline-block'>
            {/* 第一行 */}
            <View className='flex mb-3'>
              {rows[0].map((audio) => (
                <AudioCard
                  key={audio.id}
                  music={audio}
                  handleAudioClick={handleAudioClick}
                />
              ))}
            </View>

            {/* 第二行 */}
            <View className='flex'>
              {rows[1].map((audio) => (
                <AudioCard
                  key={audio.id}
                  music={audio}
                  handleAudioClick={handleAudioClick}
                />
              ))}
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }
);

// 音频列表组件 - 重构版
const AudioList: FC<AudioListProps> = memo(
  ({
    activeCategory,
    categories,
    handleAudioClick,
    getAudiosByCategory,
    audioListByCategory,
  }) => {
    useRenderCount('AudioList');

    // 如果是"全部"分类，按分类分组显示
    if (activeCategory === AudioCategory.ALL) {
      return (
        <View className='px-4 pb-4'>
          {categories.map(
            (category) =>
              audioListByCategory[category.category]?.length > 0 && (
                <CategoryAudioSection
                  key={category.category}
                  categoryTitle={category.title}
                  audios={audioListByCategory[category.category]}
                  handleAudioClick={handleAudioClick}
                />
              )
          )}
        </View>
      );
    }

    // 如果是特定分类，只显示该分类
    // 使用 getAudiosByCategory 获取特定分类的音频
    const audios = getAudiosByCategory(activeCategory as AudioCategory);

    return (
      <View className='px-4 pb-4'>
        <CategoryAudioSection
          categoryTitle={
            categories.find((c) => c.category === activeCategory)?.title ||
            '音乐'
          }
          audios={audios}
          handleAudioClick={handleAudioClick}
        />
      </View>
    );
  }
);

// 主页面组件
export default function TreatmentPage() {
  // 跟踪渲染次数
  useRenderCount('TreatmentPage');

  const {
    categories,
    activeCategory,
    featuredNatureAudios,
    loading,
    error,
    handleTabClick,
    refresh,
    getAudiosByCategory,
    audioListByCategory,
  } = useTreatmentPage();

  // 使用useCallback优化事件处理函数，防止不必要的重渲染
  const navigateToAudioPlayer = useCallback((audioId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/treatment/player/index?id=${audioId}`,
      // url: `/pages/sub-packages/treatment/player/circleTest`, //only test
    });
  }, []);

  // 包装音频点击处理函数
  const handleAudioItemClick = useCallback(
    (audioId: string) => {
      navigateToAudioPlayer(audioId);
    },
    [navigateToAudioPlayer]
  );

  return (
    <PageLol
      navigationProps={{
        title: '疗愈音乐',
        showBackButton: false,
      }}
      loading={loading}
      error={error?.message}
      onPullDownRefresh={refresh}
    >
      <View className='flex flex-col min-h-screen bg-gray-100'>
        {/* 分类标签 */}
        <CategoryTabs
          categories={[
            {
              title: AUDIO_CATEGORY_MAP[AudioCategory.ALL],
              count: useTreatmentStore.getState().getAllAudioList().length,
              category: AudioCategory.ALL,
            },
            ...categories,
          ]}
          activeCategory={activeCategory || AudioCategory.ALL}
          handleTabClick={handleTabClick}
        />

        {/* 今日推荐 */}
        <FeaturedAudios
          featuredNatureAudios={featuredNatureAudios}
          handleAudioClick={handleAudioItemClick}
        />

        {/* 音乐列表 - 重构版 */}
        <AudioList
          activeCategory={activeCategory || AudioCategory.ALL}
          categories={categories}
          handleAudioClick={handleAudioItemClick}
          getAudiosByCategory={getAudiosByCategory}
          audioListByCategory={audioListByCategory}
        />
      </View>
    </PageLol>
  );
}
