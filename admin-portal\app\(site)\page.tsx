"use client";
import { AssessmentCard } from "@/components/shared/assessment-card";
import { QRCodeDialog } from "@/components/shared/qrcode-dialog";
import { SiteFooter } from "@/components/shared/site-footer";
import { SiteHeader } from "@/components/shared/site-header";
import { TherapistCard } from "@/components/shared/therapist-card";
import { But<PERSON> } from "@/components/ui/button";
import {
  getHomeTherapistList,
  getPsychologicalTestSummaryList,
} from "@/lib/cloud-data";
import { Award, FileCheck, Star, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { PsychologicalTestSummary } from "../model/measure.model";
import { therapist_summary } from "../model/therapist.interface";
// 模拟咨询师数据
// const therapists = [
//   {
//     id: "therapist-1",
//     name: "王医生",
//     avatar: "/placeholder.svg",
//     title: "心理咨询师",
//     specialties: ["抑郁症", "焦虑障碍"],
//     price: 299,
//     rating: 4.9,
//     reviewCount: 125,
//     experience: "10年+",
//   },
//   {
//     id: "therapist-2",
//     name: "李医生",
//     avatar: "/placeholder.svg",
//     title: "临床心理学家",
//     specialties: ["婚姻家庭", "人际关系"],
//     price: 399,
//     rating: 4.8,
//     reviewCount: 98,
//     experience: "8年",
//   },
//   {
//     id: "therapist-3",
//     name: "张医生",
//     avatar: "/placeholder.svg",
//     title: "心理咨询师",
//     specialties: ["青少年心理", "学业压力"],
//     price: 359,
//     rating: 4.7,
//     reviewCount: 86,
//     experience: "5年",
//   },
//   {
//     id: "therapist-4",
//     name: "刘医生",
//     avatar: "/placeholder.svg",
//     title: "精神科医师",
//     specialties: ["强迫症", "情绪管理"],
//     price: 459,
//     rating: 4.9,
//     reviewCount: 103,
//     experience: "12年+",
//   },
//   {
//     id: "therapist-5",
//     name: "陈医生",
//     avatar: "/placeholder.svg",
//     title: "心理咨询师",
//     specialties: ["创伤治疗", "性心理"],
//     price: 499,
//     rating: 4.8,
//     reviewCount: 78,
//     experience: "7年",
//   },
//   {
//     id: "therapist-6",
//     name: "赵医生",
//     avatar: "/placeholder.svg",
//     title: "心理咨询师",
//     specialties: ["职场压力", "情感问题"],
//     price: 329,
//     rating: 4.7,
//     reviewCount: 92,
//     experience: "6年",
//   },
// ];

// // 模拟心理测量工具数据
// const assessmentTools = [
//   {
//     id: "tool-1",
//     name: "抑郁自评量表(SDS)",
//     description: "评估抑郁症状严重程度的标准化测量工具",
//     icon: "/placeholder.svg",
//     questionCount: 20,
//     time: "约5分钟",
//   },
//   {
//     id: "tool-2",
//     name: "焦虑自评量表(SAS)",
//     description: "评估焦虑症状严重程度的标准化测量工具",
//     icon: "/placeholder.svg",
//     questionCount: 20,
//     time: "约5分钟",
//   },
//   {
//     id: "tool-3",
//     name: "症状自评量表(SCL-90)",
//     description: "评估多种心理症状的综合自评量表",
//     icon: "/placeholder.svg",
//     questionCount: 90,
//     time: "约15分钟",
//   },
//   {
//     id: "tool-4",
//     name: "匹兹堡睡眠质量指数(PSQI)",
//     description: "评估睡眠质量的专业量表",
//     icon: "/placeholder.svg",
//     questionCount: 19,
//     time: "约5分钟",
//   },
//   {
//     id: "tool-5",
//     name: "人格特质测验(EPQ)",
//     description: "测量个体人格特质的经典测验",
//     icon: "/placeholder.svg",
//     questionCount: 88,
//     time: "约15分钟",
//   },
//   {
//     id: "tool-6",
//     name: "生活质量量表(QOL)",
//     description: "评估个体生活满意度和生活质量",
//     icon: "/placeholder.svg",
//     questionCount: 26,
//     time: "约8分钟",
//   },
// ];

// Banner数据
const banners = [
  {
    id: 1,
    title: "专业心理咨询",
    description: "一对一在线视频咨询，帮助你解决心理困扰",
    image: "/placeholder.svg",
  },
  {
    id: 2,
    title: "心理测量工具",
    description: "标准化心理测量，了解自己的心理状态",
    image: "/placeholder.svg",
  },
];

export default function HomePage() {
  const [therapists, setTherapists] = useState<therapist_summary[]>([]);
  const [assessmentTools, setAssessmentTools] = useState<
    PsychologicalTestSummary[]
  >([]);

  useEffect(() => {
    getHomeTherapistList().then((data) => {
      console.log("getHomeTherapistList data", data);
      setTherapists(data as therapist_summary[]);
    });
    getPsychologicalTestSummaryList().then((data) => {
      console.log("getPsychologicalTestSummaryList data", data);
      setAssessmentTools(data as PsychologicalTestSummary[]);
    });
  }, []);

  return (
    <>
      <SiteHeader />
      {/* Banner 轮播区 */}
      <section className="relative">
        <div className="container py-6 md:py-10">
          <div className="grid gap-4 md:grid-cols-2">
            {banners.map((banner) => (
              <div
                key={banner.id}
                className="relative overflow-hidden rounded-xl bg-slate-50"
              >
                <div className="h-56 relative">
                  <div className="absolute inset-0 z-10 bg-gradient-to-r from-background/90 to-background/30" />
                  <div className="absolute inset-0 flex flex-col justify-center p-6 z-20">
                    <h2 className="text-2xl font-bold">{banner.title}</h2>
                    <p className="text-muted-foreground mt-2 max-w-md">
                      {banner.description}
                    </p>
                    <div className="mt-4">
                      <QRCodeDialog title={`扫码体验${banner.title}`}>
                        <Button>立即体验</Button>
                      </QRCodeDialog>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-slate-200 flex items-center justify-center text-muted-foreground text-sm">
                    Banner图片占位
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 数据和认证区 */}
      <section className="bg-slate-50 py-12">
        <div className="container">
          <div className="grid gap-8 md:grid-cols-4">
            <div className="flex flex-col items-center text-center">
              <Users className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold">100万+</h3>
              <p className="text-muted-foreground">用户信赖</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <Award className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold">300+</h3>
              <p className="text-muted-foreground">认证咨询师</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <FileCheck className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold">98%</h3>
              <p className="text-muted-foreground">满意度</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <Star className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold">5年+</h3>
              <p className="text-muted-foreground">专业服务</p>
            </div>
          </div>
        </div>
      </section>

      {/* 咨询师推荐区 */}
      <section className="py-12">
        <div className="container">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">推荐咨询师</h2>
              <p className="text-muted-foreground">
                经过严格筛选和认证的专业心理咨询师
              </p>
            </div>
            <QRCodeDialog title={`扫码进行咨询`}>
              <Button variant="outline">开始咨询</Button>
            </QRCodeDialog>
          </div>

          <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3">
            {therapists.slice(0, 6).map((therapist) => (
              <TherapistCard
                key={therapist.id}
                id={therapist.id}
                name={therapist.name}
                avatar={therapist.avatar}
                title={therapist.titles[0]}
                specialties={therapist.specialties}
                price={therapist.price}
                rating={therapist.rating ?? 0}
                reviewCount={therapist.ratingCount ?? 0}
                experience={therapist.consultTime?.toString() ?? "0"}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 心理测量工具区 */}
      <section className="py-12 ">
        <div className="container">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">心理测量</h2>
              <p className="text-muted-foreground">
                专业标准化的心理测量工具，了解自己的心理状态
              </p>
            </div>
            <QRCodeDialog title={`扫码进行测试`}>
              <Button variant="outline">开始测试</Button>
            </QRCodeDialog>
          </div>

          <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3">
            {assessmentTools.slice(0, 6).map((tool) => (
              <AssessmentCard
                key={tool.id}
                id={tool.id}
                name={tool.title}
                description={tool.description}
                time={tool.duration.toString()}
                questionCount={tool.howmany}
              />
            ))}
          </div>
        </div>
      </section>
      <SiteFooter />
    </>
  );
}
