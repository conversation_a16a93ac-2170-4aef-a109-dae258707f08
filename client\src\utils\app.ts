export const isValidOrderId = (orderId: string) =>
  //前缀4位，后缀12位
  /^[0-9a-zA-Z]{4}-[0-9a-zA-Z]{12}$/.test(orderId);
export const isValidTherapistId = (therapistId: string) =>
  //前缀4位，后缀12位
  /^[0-9a-zA-Z]{4}-[0-9a-zA-Z]{12}$/.test(therapistId);

//生成订单ID 前缀4位('ODID')，后缀12位(日期+随机数): 202502291234
export const generateOrderId = () => {
  const date = new Date();
  //4位
  const year = date.getFullYear();
  //2位
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  //2位
  const day = date.getDate().toString().padStart(2, '0');
  //4位
  const random = Math.random().toString(10).substring(2, 6);
  return `ODID-${year}${month}${day}${random}`;
};

export const genSessionId = (AId: string, BId: string) => {
  const sortedIds = [AId, BId].sort();
  return `session-${sortedIds[0]}-${sortedIds[1]}`;
};
