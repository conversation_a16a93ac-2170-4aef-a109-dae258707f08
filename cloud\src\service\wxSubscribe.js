const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const { COLLECTIONS, ServiceType } = require("../common/db.constants");
const { formatTime } = require("../common/utils");

// 发送订单确认通知
async function sendOrderConfirmNotification(order) {
  try {
    const result = await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: order.therapistId })
      .field({
        name: true,
      })
      .get();
    const therapist = result.data[0] || {};
    const therapistName = therapist.name;

    /**
     * 预约时间
{{time5.DATA}}

预约专家
{{thing8.DATA}}

预约类型
{{thing4.DATA}}

订单编号
{{character_string1.DATA}}
     */

    await cloud.openapi.subscribeMessage.send({
      touser: order.userId,
      template_id: "mc2U2YPCWNkm6xydDPkthrTDnlTov9uOtJ2VXNGD56M",
      data: {
        time5: {
          value: formatTime(order.startTime), // 预约时间, 格式为2025-06-20 10:00
        },
        thing8: {
          value: therapistName,
        },
        thing4: {
          value:
            order.serviceType === ServiceType.VIDEO
              ? "视频咨询"
              : order.serviceType === ServiceType.VOICE
              ? "语音咨询"
              : order.serviceType === ServiceType.TEXT
              ? "文字咨询"
              : "面对面咨询",
        },
        character_string1: {
          value: order._id,
        },
      },
      miniprogram_state: "develop",
      miniprogram_path:
        "/pages/sub-packages/order/detail/index?orderId=" + order._id,
    });
  } catch (error) {
    console.error("发送订单确认通知失败:", error);
  }
}

// 发送订单拒绝通知
async function sendOrderRejectNotification(order, reason) {
  try {
    /**

取消原因
{{thing1.DATA}}

预约时间
{{time4.DATA}}

预约主题
{{thing3.DATA}}

订单号
{{character_string5.DATA}}
    *
     */
    await cloud.openapi.subscribeMessage.send({
      touser: order.userId,
      template_id: "JKf2ZSpJJDcOHDl7gi7gqUftsO-P1k3IiyasAXWD0Mo",
      data: {
        thing1: {
          value: reason,
        },
        time4: {
          value: formatTime(order.startTime),
        },
        thing3: {
          value:
            order.serviceType === ServiceType.VIDEO
              ? "视频咨询"
              : order.serviceType === ServiceType.VOICE
              ? "语音咨询"
              : order.serviceType === ServiceType.TEXT
              ? "文字咨询"
              : "面对面咨询",
        },
        character_string5: {
          value: order._id,
        },
      },
      miniprogram_state: "develop",
      miniprogram_path:
        "/pages/sub-packages/order/detail/index?orderId=" + order._id,
    });
  } catch (error) {
    console.error("发送订单拒绝通知失败:", error);
  }
}

// 发送退款完成通知
async function sendRefundCompleteNotification(order) {
  try {
    /**
     *退款金额
{{amount1.DATA}}

温馨提醒
{{thing2.DATA}}

申请单号
{{character_string3.DATA}}
     */
    await cloud.openapi.subscribeMessage.send({
      touser: order.userId,
      template_id: "gHHGPdvTRCFowvHypEQW9Ck6lAYw429KFQytJmCnTFg",
      data: {
        amount1: {
          value: order.refundAmount,
        },
        thing2: {
          value: "退款完成",
        },
        character_string3: {
          value: order._id,
        },
      },
      miniprogram_state: "develop",
      miniprogram_path:
        "/pages/sub-packages/order/detail/index?orderId=" + order._id,
    });
  } catch (error) {
    console.error("发送退款完成通知失败:", error);
  }
}

// todo: 退款失败通知模板未配置
// 发送退款失败通知
async function sendRefundFailedNotification(order) {
  try {
    /**
     *退款金额
{{amount1.DATA}}

温馨提醒
{{thing2.DATA}}

申请单号
{{character_string3.DATA}}
     */
    await cloud.openapi.subscribeMessage.send({
      touser: order.userId,
      template_id: "gHHGPdvTRCFowvHypEQW9Ck6lAYw429KFQytJmCnTFg",
      data: {
        amount1: {
          value: order.refundAmount,
        },
        thing2: {
          value: "退款完成",
        },
        character_string3: {
          value: order._id,
        },
      },
      miniprogram_state: "develop",
      miniprogram_path:
        "/pages/sub-packages/order/detail/index?orderId=" + order._id,
    });
  } catch (error) {
    console.error("发送退款失败通知失败:", error);
  }
}
// 测试订阅消息
async function testSubscribeMessage(data, openid) {
  try {
    const result = await db.collection(COLLECTIONS.ORDER).limit(1).get();
    if (!result.data || result.data.length === 0) {
      throw new Error("订单不存在");
    }
    const order = result.data[0];
    if (data.event === "orderConfirm") {
      await sendOrderConfirmNotification(order);
    } else if (data.event === "orderReject") {
      await _sendOrderRejectNotification(order, "测试");
    } else if (data.event === "refundComplete") {
      await _sendRefundCompleteNotification(order);
    }
  } catch (error) {
    console.error("测试订阅消息失败:", error);
    throw error;
  }
}

module.exports = {
  sendOrderConfirmNotification,
  sendOrderRejectNotification,
  sendRefundCompleteNotification,
  sendRefundFailedNotification,
  testSubscribeMessage,
};
