// 云函数入口文件
const cloud = require("wx-server-sdk");
const { getUserInfoByUnionid } = require("../common/auth");
const { success, error, CODES } = require("../common/response");
const {
  COLLECTIONS,
  ACTION_TYPE,
  REFUND_STATUS,
} = require("../common/db.constants");
const { OP_SECRET } = require("../common/config");
const {
  refundAuditPass,
  refundAuditNotPass,
} = require("../service/orderOperation");
const { completeWithdraw } = require("../service/withdrawOperation");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 路由处理函数
const handlers = {
  getRecommendTherapistList: async (params, context) => {
    const result = await getRecommendTherapistList(context);
    return success(result);
  },
  getRecommendPsychologicalTestSummaryList: async (params, context) => {
    const result = await getRecommendPsychologicalTestSummaryList(context);
    return success(result);
  },
  getOrders: async (params, context) => {
    const result = await getOrders(params);
    return success(result);
  },
  getRefundAuditOrders: async (params, context) => {
    const result = await getRefundAuditOrders(params);
    return success(result);
  },
  approveRefund: async (params, context) => {
    const result = await approveRefund(params);
    return success(result);
  },
  rejectRefund: async (params, context) => {
    const result = await rejectRefund(params);
    return success(result);
  },
  getTransactions: async (params, context) => {
    const result = await getTransactions(params);
    return success(result);
  },
  getWithdrawals: async (params, context) => {
    const result = await getWithdrawals(params);
    return success(result);
  },
  completeWithdrawal: async (params, context) => {
    const result = await completeWithdraw(params.id);
    return success(result);
  },
  getAssessmentTools: async (params, context) => {
    const result = await getAssessmentTools(params);
    return success(result);
  },
  createAssessmentTool: async (params, context) => {
    const result = await createAssessmentTool(params);
    return success(result);
  },
  updateAssessmentTool: async (params, context) => {
    const result = await updateAssessmentTool(params);
    return success(result);
  },
  deleteAssessmentTool: async (params, context) => {
    const result = await deleteAssessmentTool(params);
    return success(result);
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { opToken, unionid, action, params = {} } = event;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    opToken,
    action,
    unionid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfoByUnionid(unionid);

    // 验证用户是否是管理员
    // if (!userInfo?.role?.includes(USER_ROLE.ADMIN) || !userInfo) {
    //   console.log("userInfo", userInfo);
    //   return error("您没有权限访问该页面", CODES.FORBIDDEN);
    // }

    //  验证操作令牌
    if (opToken !== OP_SECRET) {
      return error("非法操作", CODES.FORBIDDEN);
    }

    // 构建上下文
    const actionContext = {
      unionid,
      requestId,
    };

    console.log("action", action);
    console.log("params", params);
    console.log("actionContext", actionContext);

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

async function getRecommendTherapistList(context) {
  const result = await db
    .collection(COLLECTIONS.THERAPIST)
    .where({ status: "active" })
    .orderBy("rating", "desc")
    .limit(10)
    .get();
  return { data: result.data };
}
async function getRecommendPsychologicalTestSummaryList(context) {
  const result = await db
    .collection(COLLECTIONS.MEASURES_TEST)
    .field({
      id: true,
      title: true,
      description: true,
      shortTitle: true,
      shortDescription: true,
      category: true,
      howmany: true,
      duration: true,
      usersCompleted: true,
      icon: true,
      coverImage: true,
      isFree: true,
    })
    .limit(10)
    .orderBy("usersCompleted", "desc")
    .get();
  return { data: result.data };
}

async function getOrders(request) {
  try {
    console.log("admin-portal getOrders", request);
    const { page = 1, pageSize = 100, params = {} } = request;
    const {
      status = [],
      refundStatus = [],
      complaint = false,
      dateRange = [],
      query: queryWord = "",
    } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    let query = {};
    // 添加主状态过滤
    if (status && status.length > 0) {
      query.status = _.in(status);
    }

    // 添加退款状态过滤
    if (refundStatus && refundStatus.length > 0) {
      query.refundStatus = _.in(refundStatus);
    }

    // 添加投诉状态过滤
    if (complaint) {
      query.complaint = true;
    }

    // 添加查询条件,模糊查询,订单号,用户名,手机号
    // 关键词搜索(姓名或专长)
    if (queryWord && queryWord.trim() !== "") {
      query = _.and([
        query,
        _.or([
          {
            _id: new RegExp(queryWord, "i"),
          },
          {
            therapistName: new RegExp(queryWord, "i"),
          },
          {
            "consultationInfo.name": new RegExp(queryWord, "i"),
          },
          {
            "consultationInfo.username": new RegExp(queryWord, "i"),
          },
        ]),
      ]);
    }

    // 添加日期范围过滤
    if (dateRange && dateRange.length > 0) {
      query.createdAt = _.gte(dateRange[0]).and(_.lte(dateRange[1]));
    }

    // 获取总数
    const total_result = await db
      .collection(COLLECTIONS.ORDER)
      .where(query)
      .count();
    const total = total_result.total;

    // 获取数据
    const orders_result = await db
      .collection(COLLECTIONS.ORDER)
      .where(query)
      .skip(skip)
      .limit(pageSize)
      .orderBy("createdAt", "desc")
      .get();

    console.log("OrderService getOrders orders", orders_result);

    // 计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    return {
      success: true,
      code: 200,
      data: orders_result.data,
      pagination: {
        page,
        pageSize,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        totalPages,
      },
    };
  } catch (error) {
    console.error("获取订单列表失败:", error);
    throw error;
  }
}

async function getRefundAuditOrders(request) {
  try {
    console.log("admin-portal getRefundAuditOrders", request);
    const { page = 1, pageSize = 100 } = request;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    let query = { refundStatus: REFUND_STATUS.AUDITING };

    // 获取总数
    const total_result = await db
      .collection(COLLECTIONS.ORDER)
      .where(query)
      .count();
    const total = total_result.total;

    const $ = db.command.aggregate;
    // 获取数据 与ORDER_ACTION联表查询出提交原因
    const orders_result = await db
      .collection(COLLECTIONS.ORDER)
      .aggregate()
      .match(query)
      .skip(skip)
      .limit(pageSize)
      .sort({ createdAt: -1 })
      .lookup({
        from: COLLECTIONS.ORDER_ACTION,
        let: {
          orderId: "$_id",
        },
        pipeline: $.pipeline()
          .match({
            action: ACTION_TYPE.REFUND_REQUEST,
            $expr: {
              $eq: ["$orderId", "$$orderId"],
            },
          })
          .project({
            _id: 0,
            refundReason: "$extraData.reason",
            refundDetail: "$extraData.detail",
            refundTime: "$actionTime",
          })
          .done(),
        as: "refund_request",
      })
      .replaceRoot({
        newRoot: $.mergeObjects([
          $.arrayElemAt(["$refund_request", 0]),
          {
            orderId: "$$ROOT._id",
            userName: "$$ROOT.userName",
            userAvatar: "$$ROOT.userAvatar",
            therapistName: "$$ROOT.therapistName",
            therapistAvatar: "$$ROOT.therapistAvatar",
            serviceType: "$$ROOT.serviceType",
            refundStatus: "$$ROOT.refundStatus",
            refundAmount: "$$ROOT.price",
          },
        ]),
      })
      .end();

    console.log(
      "admin-portal getRefundAuditOrders orders_result",
      orders_result
    );

    const totalPages = Math.ceil(total / pageSize);
    return {
      data: orders_result.list,
      pagination: {
        page,
        pageSize,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        totalPages,
      },
    };
  } catch (error) {
    console.error("获取退款审核订单列表失败:", error);
    throw error;
  }
}

async function approveRefund(request) {
  console.log("admin-portal approveRefund", request);
  const { orderId } = request;
  try {
    const result = await refundAuditPass(orderId);
    if (result.warning) {
      throw new Error(result.warning || result.message);
    }
    return {
      success: true,
      message: "退款审核通过,退款中",
    };
  } catch (error) {
    console.error("退款审核通过失败:", error);
    throw error;
  }
}

async function rejectRefund(request) {
  console.log("admin-portal rejectRefund", request);
  const { orderId } = request;
  try {
    await refundAuditNotPass(orderId);
    return {
      success: true,
      message: "退款审核拒绝",
    };
  } catch (error) {
    console.error("退款审核拒绝失败:", error);
    throw error;
  }
}

async function getTransactions(request) {
  try {
    console.log("admin-portal getTransactions", request);
    const { page = 1, pageSize = 100, params = {} } = request;
    const { status = "", dateRange = [], query: queryWord = "" } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    let query = {};
    // 添加主状态过滤
    if (status && status.trim() !== "") {
      query.status = status;
    }

    // 添加查询条件,模糊查询,订单号,交易号,用户名
    if (queryWord && queryWord.trim() !== "") {
      query = _.and([
        query,
        _.or([
          {
            orderId: new RegExp(queryWord, "i"),
          },
          {
            outTradeNo: new RegExp(queryWord, "i"),
          },
          {
            userId: new RegExp(queryWord, "i"),
          },
          {
            name: new RegExp(queryWord, "i"),
          },
        ]),
      ]);
    }

    // 添加日期范围过滤
    if (dateRange && dateRange.length > 0) {
      query.createdAt = _.gte(dateRange[0]).and(_.lte(dateRange[1]));
    }

    // 获取总数
    const total_result = await db
      .collection(COLLECTIONS.PAYMENT)
      .where(query)
      .count();
    const total = total_result.total;

    // 获取数据
    const orders_result = await db
      .collection(COLLECTIONS.PAYMENT)
      .where(query)
      .skip(skip)
      .limit(pageSize)
      .orderBy("createdAt", "desc")
      .get();

    console.log("admin-portal getTransactions orders", orders_result);

    // 计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    return {
      data: orders_result.data,
      pagination: {
        page,
        pageSize,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        totalPages,
      },
    };
  } catch (error) {
    console.error("获取订单列表失败:", error);
    throw error;
  }
}

async function getWithdrawals(request) {
  console.log("admin-portal getWithdrawals", request);
  try {
    const { page = 1, pageSize = 100, params = {} } = request;
    const { status = "", dateRange = [], query: queryWord = "" } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    let query = {};
    // 添加主状态过滤
    if (status && status.trim() !== "") {
      query.status = status;
    }

    // 添加查询条件,模糊查询,订单号,交易号,用户名
    if (queryWord && queryWord.trim() !== "") {
      query = _.and([
        query,
        _.or([
          {
            orderId: new RegExp(queryWord, "i"),
          },
          {
            outTradeNo: new RegExp(queryWord, "i"),
          },
          {
            userId: new RegExp(queryWord, "i"),
          },
          {
            name: new RegExp(queryWord, "i"),
          },
        ]),
      ]);
    }

    // 添加日期范围过滤
    if (dateRange && dateRange.length > 0) {
      query.createdAt = _.gte(dateRange[0]).and(_.lte(dateRange[1]));
    }

    // 获取总数
    const total_result = await db
      .collection(COLLECTIONS.WITHDRAW)
      .where(query)
      .count();
    const total = total_result.total;

    // 获取数据
    const orders_result = await db
      .collection(COLLECTIONS.WITHDRAW)
      .where(query)
      .skip(skip)
      .limit(pageSize)
      .orderBy("createdAt", "desc")
      .get();

    console.log("admin-portal getWithdrawals orders", orders_result);

    // 计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    return {
      data: orders_result.data,
      pagination: {
        page,
        pageSize,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        totalPages,
      },
    };
  } catch (error) {
    console.error("获取订单列表失败:", error);
    throw error;
  }
}

async function getAssessmentTools(request) {
  console.log("admin-portal getAssessmentTools", request);
  try {
    const { page = 1, pageSize = 100, params = {} } = request;
    const { query: queryWord = "", category = "" } = params;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    let query = {};

    // 添加查询条件,模糊查询,工具名称,工具描述
    if (queryWord && queryWord.trim() !== "") {
      query = _.and([
        query,
        _.or([
          {
            title: new RegExp(queryWord, "i"),
          },
          {
            description: new RegExp(queryWord, "i"),
          },
        ]),
      ]);
    }

    // 添加分类过滤
    if (category && category.trim() !== "") {
      query.category = category;
    }

    // 获取总数
    const total_result = await db
      .collection(COLLECTIONS.MEASURES_TEST)
      .where(query)
      .count();
    const total = total_result.total;

    // 获取数据
    const tools_result = await db
      .collection(COLLECTIONS.MEASURES_TEST)
      .where(query)
      .skip(skip)
      .limit(pageSize)
      .orderBy("createdAt", "desc")
      .get();

    const totalPages = Math.ceil(total / pageSize);
    return {
      data: tools_result.data,
      pagination: {
        page,
        pageSize,
        total,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        totalPages,
      },
    };
  } catch (error) {
    console.error("获取测量工具列表失败:", error);
    throw error;
  }
}

async function createAssessmentTool(request) {
  console.log("admin-portal createAssessmentTool", request);
  try {
    const {
      title,
      description,
      shortTitle,
      shortDescription,
      category,
      howmany,
      duration,
      usersCompleted,
      icon,
      coverImage,
      isFree,
      instructions,
      questions,
      scoringRules,
    } = request;
    const tool = {
      title,
      description,
      shortTitle,
      shortDescription,
      category,
      howmany,
      duration,
      usersCompleted,
      icon,
      coverImage,
      isFree,
      instructions,
      questions,
      scoringRules,
    };
    const result = await db.collection(COLLECTIONS.MEASURES_TEST).add(tool);
    return {
      success: true,
      message: "创建测量工具成功",
      data: result,
    };
  } catch (error) {
    console.error("创建测量工具失败:", error);
    throw error;
  }
}

async function updateAssessmentTool(request) {
  console.log("admin-portal updateAssessmentTool", request);
  try {
    const { id, ...rest } = request;
    const result = await db
      .collection(COLLECTIONS.MEASURES_TEST)
      .doc(id)
      .update(rest);
    if (result.stats.updated) {
      return {
        success: true,
        message: "更新测量工具成功",
        data: result,
      };
    } else {
      return {
        success: false,
        message: "更新测量工具失败",
      };
    }
  } catch (error) {
    console.error("更新测量工具失败:", error);
    throw error;
  }
}

async function deleteAssessmentTool(request) {
  console.log("admin-portal deleteAssessmentTool", request);
  try {
    const { id } = request;
    const result = await db
      .collection(COLLECTIONS.MEASURES_TEST)
      .doc(id)
      .remove();
    return {
      success: true,
      message: "删除测量工具成功",
      data: result,
    };
  } catch (error) {
    console.error("删除测量工具失败:", error);
    throw error;
  }
}
