import { SUCCESS_CODE } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import {
  PsychologicalTestSummary,
  RecommendationCard,
} from '@model/measure.model';
import { UserTestRecord } from '@model/test.model';
import { useGlobalStore } from '@stores/global.store';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class MeasureServiceImpl extends BaseService<PsychologicalTestSummary> {
  constructor() {
    super(COLLECTIONS.MEASURES_TEST);
  }

  async getMeasures(): Promise<PsychologicalTestSummary[]> {
    console.log('MeasureServiceImpl getMeasures');
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.MEASURES_TEST)
        .field({
          id: true,
          shortTitle: true,
          shortDescription: true,
          category: true,
          howmany: true,
          duration: true,
          usersCompleted: true,
          icon: true,
          coverImage: true,
          isFree: true,
        })
        .get()
        .then((res) =>
          res.data.map((item) => item as PsychologicalTestSummary)
        );
      console.log('🚀🚀🚀 MeasureServiceImpl getMeasures result', result);
      return result;
    } catch (error) {
      console.error('获取量表失败:', error);
      throw error;
    }
  }
  async getRecentHistory(): Promise<UserTestRecord[]> {
    console.log('MeasureServiceImpl getRecentHistory');
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.MEASURES_TEST_RECORD)
        .where({
          userId: useGlobalStore.getState().openid,
        })
        .get()
        .then((res) => res.data.map((item) => item as UserTestRecord));
      console.log('🚀🚀🚀 getRecentHistory result', result);
      return result;
    } catch (error) {
      console.error('获取量表历史记录失败:', error);
      throw error;
    }
  }

  async getFeaturedCards(): Promise<RecommendationCard[]> {
    console.log('MeasureServiceImpl getFeaturedCards');
    try {
      const result = await callCloudFunction('measure', 'getFeaturedCards', {});
      console.log('🚀🚀🚀 getFeaturedCards result', result);
      if (result.success && result.code === SUCCESS_CODE) {
        return result.data as RecommendationCard[];
      } else {
        throw new Error(result.message || '获取推荐卡片失败');
      }
    } catch (error) {
      console.error('获取推荐卡片失败:', error);
      throw error;
    }
  }
  async getTestRecord(recordId: string): Promise<UserTestRecord | null> {
    console.log('MeasureServiceImpl getTestRecord', recordId);
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.MEASURES_TEST_RECORD)
        .where({
          userId: useGlobalStore.getState().openid,
          id: recordId,
        })
        .get();
      if (result.data && result.data.length > 0) {
        return result.data[0] as UserTestRecord;
      }
      return null;
    } catch (error) {
      console.error('获取量表历史记录失败:', error);
      throw error;
    }
  }
}

export const measureService = new MeasureServiceImpl();
