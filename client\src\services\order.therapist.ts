import {
  CloudFunctionResult,
  OrderListParams,
  OrderListRequest,
  OrderListResponse,
} from '@core/api';
import { INITATOR_SOURCE } from '@model/common.interface';
import { COLLECTIONS } from '@model/db.model';
import {
  Order_action,
  Order_summary,
  REFUND_REASON,
  REJECT_REASON,
} from '@model/order.interface';
import { BaseService } from '@services/base.service';
import { callCloudFunction } from '@services/cloud';
import { useLoadingStore } from '@stores/loading.store';
import Taro from '@tarojs/taro';

/**
 * 咨询师订单服务类,咨询师端使用
 * 处理咨询师端的订单相关业务逻辑
 */
class TherapistOrderServiceImpl extends BaseService<Order_summary> {
  constructor() {
    super(COLLECTIONS.ORDER);
  }

  /**
   * 获取咨询师关联的订单列表
   */
  async getOrders(params: OrderListRequest = {}): Promise<OrderListResponse> {
    try {
      console.log('TherapistOrderServiceImpl getOrders', params);
      const { page = 1, pageSize = 10, params: queryParams } = params;

      // 构建查询条件
      let query: any = {
        // therapistId: 'oLFkE7lzKUPbOcFPLcDo4M_GSjyU',
        therapistId: '{openid}',
      };

      // 初始化数据库
      const db = Taro.cloud.database();
      // 查询命令
      const _ = db.command;

      if (queryParams) {
        const {
          status,
          refundStatus,
          query: queryWord,
          complaint,
        } = queryParams;
        // 添加主状态过滤
        if (status && status.length > 0) {
          query.status = _.in(status);
        }
        // 添加退款状态过滤
        if (refundStatus && refundStatus.length > 0) {
          query.refundStatus = _.in(refundStatus);
        }
        // 添加查询条件,模糊查询,订单号,用户名,手机号
        // 关键词搜索(姓名或专长)
        if (queryWord && queryWord.trim() !== '') {
          query = _.and([
            query,
            _.or([
              {
                _id: new RegExp(queryWord, 'i'),
              },
              {
                'consultationInfo.name': new RegExp(queryWord, 'i'),
              },
              {
                'consultationInfo.phone': new RegExp(queryWord, 'i'),
              },
            ]),
          ]);
        }
        // 添加投诉过滤
        if (complaint) {
          query.complaint = true;
        }
      }
      console.log('TherapistOrderServiceImpl getOrders query', query);

      const orders = await this.directRead(query, {
        orderField: 'createdAt',
        orderDirection: 'desc',
        skip: (page - 1) * pageSize,
        limit: pageSize,
      });

      console.log('TherapistOrderServiceImpl getOrders orders', orders);

      // 计算分页信息
      const hasNext = orders.length === pageSize;
      const hasPrev = page > 1;

      return {
        success: true,
        code: 200,
        data: orders,
        pagination: {
          page,
          pageSize,
          hasNext,
          hasPrev,
        },
      };
    } catch (error) {
      console.error('获取咨询师订单列表失败:', error);
      return {
        success: false,
        code: 500,
        data: [],
      };
    }
  }

  /**
   * 获取单个订单详情
   */
  async getOrderById(orderId: string): Promise<Order_summary | null> {
    try {
      const orders = await this.directRead({
        _id: orderId,
        therapistId: '{openid}',
      });
      if (orders.length > 0) {
        return orders[0] as Order_summary;
      }
      return null;
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return null;
    }
  }

  async getOrderActions(orderId: string): Promise<Order_action[]> {
    try {
      const db = Taro.cloud.database();
      const actions = await db
        .collection(COLLECTIONS.ORDER_ACTION)
        .where({
          orderId,
        })
        .get()
        .then((res) => res.data.map((item) => item as Order_action));
      return actions;
    } catch (error) {
      console.error('获取订单操作失败:', error);
      throw error;
    }
  }
  /**
   * 确认订单
   * 写操作，直接调用云函数
   */
  async confirmOrder(orderId: string): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('order', 'confirmOrder', {
        _id: orderId,
      });

      if (result.success) {
        return result;
      }
      throw new Error(result.message || '确认订单失败');
    } catch (error) {
      console.error('确认订单失败:', error);
      throw error;
    }
  }

  /**
   * 拒绝订单
   */
  async rejectOrder(params: {
    _id: string;
    cancel_info: {
      reason: REJECT_REASON;
      detail: string;
    };
  }): Promise<{
    success: boolean;
    message: string;
    warning?: string;
    error?: string;
  }> {
    try {
      useLoadingStore.getState().setTransactionLoading(true);
      const result = await callCloudFunction('order', 'rejectOrder', {
        ...params,
        source: INITATOR_SOURCE.THERAPIST,
      });

      return result;
    } catch (error) {
      console.error('拒绝订单失败:', error);

      throw error;
    }
  }

  /**
   * 咨询师为用户申请退款
   */
  async refundRequest(
    orderId: string,
    reason: REFUND_REASON,
    detail?: string
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('order', 'refundRequest', {
        _id: orderId,
        reason,
        detail,
      });

      if (result.success) {
        return result;
      }
      throw new Error(result.message || '退款申请失败');
    } catch (error) {
      console.error('退款申请失败:', error);
      throw error;
    }
  }

  /**
   * 开始服务
   */
  async startService(orderId: string): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('order', 'startService', {
        _id: orderId,
      });

      if (result.success) {
        return result;
      }
      throw new Error(result.message || '开始服务失败');
    } catch (error) {
      console.error('开始服务失败:', error);
      throw error;
    }
  }

  /**
   * 完成服务
   */
  async completeService(orderId: string): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('order', 'completeService', {
        _id: orderId,
      });

      if (result.success) {
        return result;
      }
      throw new Error(result.message || '完成服务失败');
    } catch (error) {
      console.error('完成服务失败:', error);
      throw error;
    }
  }

  /**
   * 监听咨询师订单列表
   */
  watchTherapistOrders(
    params: OrderListParams,
    onChange: (snapshot: any) => void
  ): any {
    console.log('therapistOrderService watchTherapistOrders params', params);
    // 构建查询条件
    const query: any = {
      therapistId: '{openid}',
    };

    // 如果指定了状态，则添加状态筛选
    if (params.status && params.status.length > 0) {
      query.status = Taro.cloud.database().command.in(params.status);
    }
    if (params.complaint) {
      query.complaint = true;
    }

    // 监听订单列表
    return this.watchData(query, onChange);
  }

  /**
   * 监听单个订单
   */
  watchOrderById(orderId: string, onChange: (snapshot: any) => void): any {
    return this.watchData(
      {
        _id: orderId,
        therapistId: '{openid}',
      },
      onChange
    );
  }
}
// 导出服务实例
export const therapistOrderService = new TherapistOrderServiceImpl();
