import { Button, Image, Tag } from '@antmjs/vantui';
import {
  AVATAR_DEFAULT,
  DEFAULT_USER_NAME,
  SERVICE_TYPE_ICON_PRIMARY,
} from '@constants/assets';
import { ORDER_STATUS_MAP, SERVICE_TYPE_MAP } from '@constants/text';
import { ORDER_STATUS } from '@model/order.interface';
import { useTherapistOrderStoreSelector } from '@stores/order.store';
import { Text, View } from '@tarojs/components';

export interface OrderCardForTherapistProps {
  orderId: string;
  cta?: () => void;
  showDivider?: boolean;
  showActions?: boolean;
  onCardClick?: () => void;
  actions?: {
    text: string;
    onClick: () => void;
    type?: 'primary' | 'default' | 'danger';
  }[];
}

export default function OrderCardForTherapist({
  orderId,
  showDivider = true,
  showActions = false,
  actions = [],
  cta,
  onCardClick,
}: OrderCardForTherapistProps) {
  // 使用store
  const order = useTherapistOrderStoreSelector((state) =>
    state.getOrderById(orderId || '')
  );

  // // 格式化时间
  const displayTime = new Date(order?.startTime || 0).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });

  // 从缓存的信息中解构出需要的数据
  const { status, serviceType } = order || {};
  console.log('OrderCardForTherapist order', order);
  console.log('OrderCardForTherapist serviceType', serviceType);
  console.log('OrderCardForTherapist status', status);
  if (!order || serviceType === undefined || status === undefined) {
    console.log('!!! OrderCardForTherapist null check failed');
    return null;
  }

  return (
    <View
      className='bg-white rounded-2xl p-4 flex flex-col mt-4'
      onClick={onCardClick}
    >
      {/* 第一部分：信息行 */}
      <View className='flex items-center'>
        <Image
          src={order?.userAvatar || AVATAR_DEFAULT}
          radius={24}
          width={220}
          height={220}
          fit='cover'
        />
        <View className='flex-1 flex flex-col ml-3'>
          {/* 名字和头衔行 */}
          <View className='flex flex-row items-center mb-4'>
            <Text className='font-bold text-base mr-2'>
              {order?.consultationInfo?.name ||
                order?.userName ||
                DEFAULT_USER_NAME}
            </Text>
          </View>
          {/* 服务行 */}
          <View className='flex flex-row items-center mb-4'>
            <Text className='text-md mr-2'>
              {SERVICE_TYPE_MAP[serviceType]}
            </Text>
            <Tag
              plain
              size='small'
              type={
                status === ORDER_STATUS.PENDING_START
                  ? 'primary'
                  : status === ORDER_STATUS.IN_PROGRESS
                  ? 'warning'
                  : status === ORDER_STATUS.COMPLETED
                  ? 'success'
                  : 'danger'
              }
            >
              {ORDER_STATUS_MAP[status]}
            </Tag>
          </View>
          {/* 日期时间行 */}
          <Text className='text-sm text-secondary'>{displayTime}</Text>
        </View>
        {/* cta */}
        {cta && serviceType && (
          <View
            className='w-10 h-10 flex items-center justify-center rounded-full ml-4 bg-primarylight'
            onClick={(e) => {
              e.stopPropagation(); // 阻止冒泡，避免触发卡片点击
              cta();
            }}
          >
            <Image
              src={SERVICE_TYPE_ICON_PRIMARY[serviceType]}
              className='w-6 h-6'
            />
          </View>
        )}
      </View>

      {/* 第二部分：分割线（可选） */}
      {showDivider && <View className='h-px bg-gray-200 my-4' />}

      {/* 第三部分：操作按钮（可选） */}
      {showActions && actions && (
        <View className='flex flex-row gap-4 justify-end '>
          {actions.map((action, index) => (
            <Button
              key={index}
              type='primary'
              onClick={(e) => {
                e.stopPropagation(); // 阻止冒泡，避免触发卡片点击
                action.onClick();
              }}
              size='small'
              plain={action.type !== 'primary'}
              hairline={action.type !== 'primary'}
              round
              className='w-full'
            >
              {action.text}
            </Button>
          ))}
        </View>
      )}
    </View>
  );
}
