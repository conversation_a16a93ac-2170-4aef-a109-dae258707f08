import { Service } from '@model/service.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { zustandStorage } from './libs/storage';

interface AppointmentState {
  // 选择的咨询师ID
  selectedTherapistId: string | null;
  // 选择的时间段（小时）
  selectedTimeSlot: number | null;
  // 选择的日期（unix时间戳，精确到天）
  selectedDate: number | null;
  // // 是否来自咨询师详情页
  fromTherapistDetail: boolean;

  // 选择的预约类型
  selectedService: Service | null;
  // 选择的预约时长倍数
  selectedDurationMultiple: number;
  // 预约价格
  price: number;

  // 预约流程的当前步骤 0: 选择时间, 1: 选择服务, 2: 确认订单
  currentStep: number;

  loading: boolean;
  error: string | null;
}

interface AppointmentActions {
  // 设置选择的日期（unix时间戳，精确到天）
  setSelectedDate: (date: number | null) => void;
  // 设置选择的时间段（小时）
  setSelectedTimeSlot: (timeSlot: number | null) => void;
  // 设置咨询师ID
  setCurrentTherapistId: (id: string | null) => void;
  // 设置来源页面
  // setFromTherapistDetail: (fromDetail: boolean) => void;
  // 设置当前步骤
  setCurrentStep: (step: number) => void;
  // 清除所有预约状态
  clearAppointment: () => void;
  // 重置到初始状态
  resetAppointment: () => void;
  // 设置选择的预约类型
  setSelectedService: (service: Service | null) => void;
  // 设置选择的预约时长倍数
  setSelectedDurationMultiple: (durationMultiple: number | null) => void;
  // 设置预约价格
  setPrice: (price: number) => void;
  // 设置加载状态
  setLoading: (loading: boolean) => void;
  // 设置错误信息
  setError: (error: string | null) => void;
  // 设置是否来自咨询师详情页
  setFromTherapistDetail: (fromTherapistDetail: boolean) => void;
}

const initialState: AppointmentState = {
  selectedTherapistId: null,
  selectedTimeSlot: null,
  selectedDate: null,
  fromTherapistDetail: false,
  currentStep: 0,
  selectedService: null,
  selectedDurationMultiple: 1,
  price: 0,
  loading: false,
  error: null,
};

const appointmentStore = create<AppointmentState & AppointmentActions>()(
  immer(
    persist(
      (set) => ({
        ...initialState,

        setSelectedDate: (date) =>
          set((state) => {
            state.selectedDate = date;
          }),
        setSelectedTimeSlot: (timeSlot) =>
          set((state) => {
            state.selectedTimeSlot = timeSlot;
          }),

        setCurrentTherapistId: (id) =>
          set((state) => {
            state.selectedTherapistId = id;
          }),

        setCurrentStep: (step) =>
          set((state) => {
            state.currentStep = step;
          }),

        clearAppointment: () =>
          set((state) => {
            state.selectedTimeSlot = null;
            state.selectedDate = null;
            state.currentStep = 0;
          }),

        resetAppointment: () => set(initialState),

        setSelectedService: (service) =>
          set((state) => {
            console.log('setSelectedService', service);
            state.selectedService = service;
            console.log('state.selectedService', state.selectedService);
          }),

        setSelectedDurationMultiple: (durationMultiple: number) =>
          set((state) => {
            state.selectedDurationMultiple = durationMultiple;
          }),

        setPrice: (price) =>
          set((state) => {
            state.price = price;
          }),

        setLoading: (loading) =>
          set((state) => {
            state.loading = loading;
          }),

        setError: (error) =>
          set((state) => {
            state.error = error;
          }),

        setFromTherapistDetail: (fromTherapistDetail) =>
          set((state) => {
            state.fromTherapistDetail = fromTherapistDetail;
          }),
      }),
      {
        name: 'appointment-storage', // 持久化存储的key
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化关键状态，不持久化临时状态
        partialize: (state) => ({
          selectedTherapist: state.selectedTherapistId,
          selectedTimeSlot: state.selectedTimeSlot,
          selectedDate: state.selectedDate,
          // fromTherapistDetail: state.fromTherapistDetail,
        }),
      }
    )
  )
);

export const useAppointmentStore = createSelectors(appointmentStore);

export function useAppointmentReset() {
  appointmentStore.setState(initialState);
}
