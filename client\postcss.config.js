module.exports = {
  plugins: {
    tailwindcss: {},
    // autoprefixer: {},
    // //https://weapp-tw.icebreaker.top/docs/quick-start/rem2rpx
    // 'postcss-rem-to-responsive-pixel': {
    //   rootValue: 32, // 1rem = 32rpx
    //   propList: ['*'], // 默认所有属性都转化
    //   transformUnit: process.env.TARO_ENV === 'h5' ? 'px' : 'rpx', // 转化的单位,可以变成 px / rpx
    // },

    //！！！ 实测：根本不可用，问题同上，根本走不到分支里面去。
    //https://weapp-tw.icebreaker.top/docs/quick-start/css-unit-transform
    // 下方为 px 转 rpx 区域
    // 'postcss-pxtransform': {
    //   platform: 'weapp',
    //   // 根据你的设计稿宽度进行配置
    //   // 可以传入一个 function
    //   designWidth (input) {
    //     const filePath = input.file.split('\\').join('/');
    //     if (filePath.includes('@antmjs/vantui')) {
    //       console.warn('vantui 750')
    //       return 750
    //     }
    //     return 375
    //   },
    //   // designWidth: 375, // 可以设置为 375 等等来应用下方的规则,
    //   deviceRatio: {
    //     640: 2.34 / 2,
    //     // 此时应用到的规则，代表 1px = 1rpx
    //     750: 1,
    //     828: 1.81 / 2,
    //     // 假如你把 designWidth 设置成 375 则使用这条规则 1px = 2rpx
    //     375: 2 / 1,
    //   },
    // },
  }
}
