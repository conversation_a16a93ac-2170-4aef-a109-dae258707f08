import { request } from '@utils/request';

export const notificationService = {
  getNotifications: async (lastMessageId?: string) => {
    const response = await request.get('/api/messages', {
      params: {
        lastMessageId,
      },
    });
    return response.data;
  },
  removeNotifications: async (notificationIds: string[]) => {
    const response = await request.post('/api/notifications/remove', {
      data: {
        notificationIds,
      },
    });
    return response.data;
  },
  markAsRead: async (messageId: string) => {
    // const response = await request.post('/api/messages/markAsRead', {
    //   messageId,
    // });
    // return response.data;
    //2秒后返回
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('markAsRead', messageId);
        resolve(true);
      }, 2000);
    });
  },
  markAllAsRead: async () => {
    const response = await request.post('/api/messages/markAllAsRead');
    return response.data;
  },
};
