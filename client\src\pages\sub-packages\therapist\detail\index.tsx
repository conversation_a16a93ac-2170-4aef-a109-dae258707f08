import { But<PERSON>, Steps, Swiper, SwiperItem } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import AppointmentCalendar from '@components/therapist/AppointmentCalendar';
import TherapistFavoriteButton from '@components/therapist/TherapistFavoriteButton';
import TherapistIntroduceCard from '@components/therapist/TherapistIntroduceCard';
import TherapistPreferRatingCard from '@components/therapist/TherapistReviews';
import { AVATAR_DEFAULT, ICONS_BOLD_PRIMARY } from '@constants/assets';
import { appointmentActions } from '@core/actions/appointment.action';
import { therapistActions } from '@core/actions/therapist.action';
import useRenderCount from '@hooks/useRenderCount';
import { TherapistCertificationType } from '@model/therapist.interface';
import { useAppointmentStore } from '@stores/appointment.store';
import { useLoadingStore } from '@stores/loading.store';
import { useTherapistStore } from '@stores/therapist.store';
import { Image, Text, View } from '@tarojs/components';
import Taro, { usePageScroll, useRouter } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';

export default function TherapistDetail() {
  // 跟踪渲染次数
  useRenderCount('TherapistDetail');

  const [tab, setTab] = useState('introduction');
  const { id } = useRouter().params;
  const isScrolling = useRef(false);
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  // 使用 appointment store 管理选择的时间
  const {
    selectedDate,
    selectedTimeSlot,
    setSelectedDate,
    setSelectedTimeSlot,
    setCurrentTherapistId,
    setFromTherapistDetail,
  } = useAppointmentStore();

  const { loading, error } = useTherapistStore();

  const currentTherapist = useTherapistStore((state) =>
    state.therapists?.find((i) => i.id === id)
  );

  const {
    currentTherapistExtinfo,
    currentTherapistOccuppied,
    currentTherapistPreferRating,
    currentTherapistService,
  } = useTherapistStore();

  useEffect(() => {
    if (!id) {
      return;
    }
    // 使用 actions 层获取咨询师详情(包含排期)
    if (!currentTherapistExtinfo) {
      therapistActions.fetchTherapistFullInfo(id);
    }
    // 设置当前咨询师ID到预约store
    setCurrentTherapistId(id);
    // 标记来自咨询师详情页
    setFromTherapistDetail(true);

    const unsubscribe = therapistActions.watchTherapistOccupancy(id);

    return () => {
      if (unsubscribe) {
        unsubscribe.close();
      }
    };
  }, [id]);

  // 点击标签滚动到对应区域
  const scrollToSection = (key: string) => {
    if (isScrolling.current) return;
    isScrolling.current = true;

    // 查询目标元素位置
    const query = Taro.createSelectorQuery();
    query.select(`#section-${key}`).boundingClientRect();
    query.selectViewport().boundingClientRect();
    query.exec((res) => {
      if (res && res[0] && res[1]) {
        const targetTop = res[0].top;
        const viewportTop = res[1].top;
        const scrollTop = targetTop - viewportTop - 50; // 减去 tabs 高度和一些额外空间

        // 使用 pageScrollTo API 滚动到目标位置
        Taro.pageScrollTo({
          scrollTop: scrollTop,
          duration: 300,
          success: () => {
            setTab(key);
            setTimeout(() => {
              isScrolling.current = false;
            }, 350);
          },
          fail: () => {
            isScrolling.current = false;
          },
        });
      } else {
        isScrolling.current = false;
      }
    });
  };

  // 监听页面滚动，更新当前激活的标签
  usePageScroll(({ scrollTop }) => {
    if (isScrolling.current) return;

    // 查询所有区域的位置
    const query = Taro.createSelectorQuery();

    tabs.forEach((tabItem) => {
      query.select(`#section-${tabItem.key}`).boundingClientRect();
    });

    query.exec((rects) => {
      if (!rects || !rects.length) return;

      // 筛选出在视口附近的区域
      const visibleSections = rects
        .filter((rect) => rect && rect.top <= 100)
        .map((rect, index) => ({
          key: tabs[index].key,
          top: rect.top,
        }));

      if (visibleSections.length > 0) {
        // 找到最接近顶部的区域
        const closestSection = visibleSections.reduce((prev, curr) => {
          return curr.top > prev.top ? curr : prev;
        });

        if (closestSection && closestSection.key !== tab) {
          setTab(closestSection.key);
        }
      }
    });
  });

  const tabs = [
    { key: 'introduction', label: '基本介绍' },
    { key: 'education', label: '教育培训' },
    { key: 'schedule', label: '可约日期' },
  ];
  if (currentTherapistPreferRating && currentTherapistPreferRating.length > 0) {
    tabs.push({ key: 'reviews', label: '用户评价' });
  }

  // 处理日期和时间选择
  const handleSelectDateTime = (date: number, timeSlot: number) => {
    setSelectedDate(date);
    setSelectedTimeSlot(timeSlot);
  };

  console.log('currentTherapist', currentTherapist);
  console.log('currentTherapistExtinfo', currentTherapistExtinfo);
  console.log('currentTherapistOccuppied', currentTherapistOccuppied);
  console.log('currentTherapistPreferRating', currentTherapistPreferRating);

  // 个人图片轮播及认证标签
  const renderProfileCarousel = () => {
    return (
      <View className='bg-white rounded-xl flex flex-col items-center mt-2 w-full'>
        <View className='relative w-full'>
          {/* 头像 carousel*/}
          {currentTherapistExtinfo?.photos &&
            currentTherapistExtinfo?.photos.length > 0 && (
              <Swiper autoPlay={0} height='356Px'>
                {currentTherapistExtinfo?.photos.map((p) => (
                  <SwiperItem key={p}>
                    <Image
                      src={p}
                      className='w-full h-[356Px]'
                      mode='aspectFill'
                    />
                  </SwiperItem>
                ))}
              </Swiper>
            )}
          {/* 认证信息 覆盖在头像上*/}
          <View className='absolute bottom-3 left-0 right-0 flex flex-row gap-2 p-2 justify-center'>
            {currentTherapistExtinfo?.certifications.map((c) => (
              <Button
                key={c.title}
                type='default'
                className='bg-secondary rounded-full px-2 py-1'
                icon={
                  c.type === TherapistCertificationType.PRACTICE_CERTIFICATION
                    ? ICONS_BOLD_PRIMARY.PRACTICE_CERTIFICATION
                    : c.type === TherapistCertificationType.PHOTO_CERTIFICATION
                    ? ICONS_BOLD_PRIMARY.PHOTO_CERTIFICATION
                    : ICONS_BOLD_PRIMARY.IDENTITY_CERTIFICATION
                }
              >
                <Text className='text-sm font-bold'>{c.title}</Text>
              </Button>
            ))}
          </View>
        </View>
      </View>
    );
  };

  //渲染个人基础信息卡片
  const renderBasicInfo = () => {
    return (
      <View className='p-4 flex flex-col gap-2'>
        {/* 个人信息 */}
        <View className='flex flex-row items-center w-full'>
          <Image
            src={currentTherapist?.avatar || AVATAR_DEFAULT}
            className='w-14 h-14 rounded-full'
          />
          <View className='flex-1 flex flex-col ml-4'>
            <View className='flex flex-row items-center mb-1'>
              <Text className='text-lg font-semibold mr-2'>
                {currentTherapist?.name}
              </Text>
            </View>
            <Text className='text-sm text-secondary'>
              {currentTherapist?.titles && currentTherapist?.titles.join(' | ')}
            </Text>
          </View>
          <TherapistFavoriteButton therapistId={id ?? ''} />
        </View>
        {/* 标签列表*/}
        <View className='flex flex-row items-center justify-between mt-4'>
          {currentTherapist?.tags.map((t) => {
            return (
              <View key={t.title} className='flex flex-col items-center'>
                <View className='flex flex-row items-baseline'>
                  <Text className='text-lg font-bold'>{t.value}</Text>
                  {t.unit && <Text className='text-xs '>{t.unit}</Text>}
                </View>
                <Text className='text-sm text-secondary'>{t.title}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // 渲染tabs
  const renderTabs = () => {
    return (
      <View className='sticky top-0 z-50 flex flex-row items-center gap-4 mt-4  px-4 w-full'>
        {tabs.map((t) => (
          <Text
            key={t.key}
            className={`py-2 ${
              tab === t.key
                ? 'text-default font-bold text-base'
                : 'text-secondary text-md'
            }`}
            onClick={() => {
              setTab(t.key);
              scrollToSection(t.key);
            }}
          >
            {t.label}
          </Text>
        ))}
      </View>
    );
  };

  //基本介绍
  const renderBasicIntroduction = () => {
    return (
      <View
        id='section-introduction'
        className='bg-white rounded-xl mt-2 p-4 min-h-[500px]'
      >
        {/* title */}
        {/* <Text className='text-base font-bold'>基本介绍</Text> */}
        <TherapistIntroduceCard
          directions={currentTherapist?.directions || []}
          specialties={currentTherapist?.specialties || []}
          introduction={currentTherapistExtinfo?.introduction || ''}
        />
      </View>
    );
  };

  // 教育培训
  const renderEducation = () => {
    return (
      <View id='section-education' className='bg-white rounded-xl mt-2 p-4 '>
        {/* title */}
        <Text className='text-base font-bold '>教育培训</Text>

        <View className='flex flex-col  mt-4'>
          {/* 教育部分 */}
          <View className='flex flex-col gap-2'>
            {/* titles  */}
            <View className='flex flex-row items-center gap-2'>
              <View className='px-2 py-1 rounded-lg bg-blue-100'>
                <Text className='text-xs text-blue-600'>资质</Text>
              </View>
              {currentTherapist?.titles && (
                <Text className='text-md'>
                  {currentTherapist?.titles.join(' , ')}
                </Text>
              )}
            </View>
            {/* 学历 */}
            <View className='flex flex-row items-center gap-2'>
              <View className='px-2 py-1 rounded-lg bg-orange-100'>
                <Text className='text-xs text-orange-600'>学历</Text>
              </View>
              <Text className='text-md '>
                {currentTherapistExtinfo?.education.education}
              </Text>
            </View>

            {/* 学校 */}
            {currentTherapistExtinfo?.education.schools &&
              currentTherapistExtinfo?.education.schools.length > 0 && (
                <View className='flex flex-row items-center gap-2'>
                  <View className='px-2 py-1 rounded-lg bg-indigo-100'>
                    <Text className='text-xs text-indigo-600'>学校</Text>
                  </View>
                  <Text className='text-md'>
                    {currentTherapistExtinfo?.education.schools
                      .map((s) => s.name)
                      .join(' , ')}
                  </Text>
                </View>
              )}
          </View>

          {/* 培训经历 */}
          {currentTherapistExtinfo?.trainings &&
            currentTherapistExtinfo?.trainings.length > 0 && (
              <View className='flex flex-col mt-6 '>
                <Text className='text-base font-bold'>培训经历</Text>
                {currentTherapistExtinfo?.trainings.map((training, idx) => (
                  <View key={idx} className='flex flex-col  mt-3'>
                    <View className='flex flex-row items-center gap-2'>
                      <Text className='text-sm font-bold'>{training.name}</Text>
                      <Text className='text-sm '>
                        {training.start} - {training.end}
                      </Text>
                      <Text className='text-sm text-gray-500'>
                        {training.duration}
                      </Text>
                    </View>

                    <Steps
                      direction='vertical'
                      active={-1}
                      steps={training.items.map((it) => ({
                        text: it.title,
                        desc: `${it.start} - ${it.end}`,
                      }))}
                    />
                  </View>
                ))}
              </View>
            )}
        </View>
      </View>
    );
  };

  // 渲染用户优选评价
  const renderReviews = () => {
    return (
      currentTherapistPreferRating && (
        <View
          id='section-reviews'
          className='bg-white rounded-xl mt-2 p-4 min-h-[300px]'
        >
          <TherapistPreferRatingCard
            therapistId={id ?? ''}
            score={currentTherapist?.rating || 0}
            count={currentTherapist?.ratingCount || 0}
            reviews={currentTherapistPreferRating}
            maxCount={3}
          />
        </View>
      )
    );
  };

  // 渲染排期信息
  const renderSchedule = () => {
    return (
      <View id='section-schedule' className='bg-white rounded-xl mt-4 p-4 mb-4'>
        {/* 使用AppointmentCalendar组件 */}
        <AppointmentCalendar
          occupancy={currentTherapistOccuppied?.schedule || null}
          selectedDateUnixStamp={selectedDate || undefined}
          selectedTimeSlotInHour={selectedTimeSlot || undefined}
          onSelectDateTime={handleSelectDateTime}
          workTime={currentTherapistService?.workTime}
        />
      </View>
    );
  };

  // 底部预约栏
  const renderBottomAppointmentBar = () => {
    //始终固定在底部, 不随页面滚动
    return (
      <View className='fixed bottom-0 left-0 w-full bg-white flex flex-row items-center justify-between px-4 py-2 z-50 border-t border-gray-200'>
        {/* <Text className='text-primary font-bold text-lg'>心之安</Text> */}
        <Button
          type='primary'
          block
          round
          loading={transactionLoading}
          disabled={transactionLoading}
          onClick={() => {
            //启动下单流程
            setTransactionLoading(true);
            appointmentActions.startAppointment({
              from: 'therapist',
              therapistId: id,
            });
          }}
        >
          立即预约
        </Button>
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{
        title: '咨询师详情',
        showBackButton: true,
      }}
      loading={loading}
      error={error || null}
      isEmpty={!loading && !id && !currentTherapistExtinfo}
    >
      {/* {currentTherapistExtinfo && currentTherapist && ( */}
      <View className='pb-20'>
        {/* 顶部信息 */}
        {renderProfileCarousel()}

        {renderBasicInfo()}

        {/* Tab切换 */}
        {renderTabs()}

        {/* 内容区域 */}
        <View className='px-4'>
          {/* 基本介绍 */}
          {renderBasicIntroduction()}
          {/* 教育培训 */}
          {renderEducation()}

          {/* 用户评价 */}
          {renderReviews()}

          {/* 可约时间 */}

          {renderSchedule()}
        </View>

        {/* 底部预约栏 */}
        {renderBottomAppointmentBar()}
      </View>
      {/* )} */}
    </PageLol>
  );
}
