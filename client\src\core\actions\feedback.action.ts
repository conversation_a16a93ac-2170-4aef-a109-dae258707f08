import { Feedback, FeedbackRequest } from '@model/feedback.interface';
import { feedbackService } from '@services/feedback.service';
import { uploadService } from '@services/upload.service';
import { useFeedbackSelectors } from '@stores/feedback.store';
import Taro from '@tarojs/taro';

export const feedbackActions = {
  /**
   * 获取反馈列表
   */
  fetchFeedbackList: async () => {
    const { setLoading, setFeedbackList, setError } =
      useFeedbackSelectors.getState();

    try {
      setLoading(true);

      const response = await feedbackService.getFeedbackList();

      setFeedbackList(response);
      return { success: true };
    } catch (error) {
      setError(true, '获取反馈列表失败，请稍后再试');
      return { success: false, message: '获取反馈列表失败，请稍后再试' };
    } finally {
      setLoading(false);
    }
  },

  /**
   * 获取反馈详情
   * @param id 反馈ID
   */
  fetchFeedbackDetail: async (id: string) => {
    const { setLoading, setCurrentFeedback, setError } =
      useFeedbackSelectors.getState();

    try {
      setLoading(true);

      const response = await feedbackService.getFeedbackDetail(id);

      setCurrentFeedback(response);
      return { success: true };
    } catch (error) {
      setError(true, '获取反馈详情失败');
      return { success: false, message: '获取反馈详情失败' };
    }
  },

  /**
   * 提交反馈
   * @param data 反馈内容，不包括图片
   * @param localImages 本地图片路径数组
   */
  submitFeedback: async (
    data: Omit<FeedbackRequest, 'images'>,
    localImages: string[] = []
  ) => {
    const { setSubmitting, addFeedback } = useFeedbackSelectors.getState();

    try {
      setSubmitting(true);

      // 上传图片
      const uploadedImages: string[] = [];

      if (localImages.length > 0) {
        for (const imagePath of localImages) {
          const result = await uploadService.uploadPhotos([imagePath]);
          uploadedImages.push(result[0]);
        }
      }

      // 提交反馈
      const requestData: FeedbackRequest = {
        ...data,
        images: uploadedImages,
      };

      const response = await feedbackService.submitFeedback(requestData);
      setSubmitting(false);

      if (response.code === 0) {
        const newFeedback = response.data as Feedback;
        addFeedback(newFeedback);

        return {
          success: true,
          message: '提交成功',
        };
      } else {
        return {
          success: false,
          message: response.message,
        };
      }
    } catch (error) {
      setSubmitting(false);

      return {
        success: false,
        message: '提交反馈失败，请稍后重试',
      };
    }
  },

  /**
   * 验证文件大小
   * @param filePath 本地文件路径
   * @param maxSize 最大文件大小（字节）
   */
  validateFileSize: async (filePath: string, maxSize: number) => {
    try {
      const fileInfo = await Taro.getFileInfo({ filePath });

      // 使用类型断言确保fileInfo.size存在
      const fileSize = (fileInfo as any).size;

      if (!fileSize || fileSize > maxSize) {
        return {
          valid: false,
          message: `文件大小不能超过${maxSize / 1024 / 1024}MB`,
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        message: '文件验证失败',
      };
    }
  },
};
