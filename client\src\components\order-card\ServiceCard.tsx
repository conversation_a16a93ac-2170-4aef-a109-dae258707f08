import { Image, Radio } from '@antmjs/vantui';
import { SERVICE_TYPE_ICON_DIVERSED } from '@constants/assets';
import { SERVICE_TYPE_MAP, SERVICE_TYPE_MAP_SHORT } from '@constants/text';
import { Service, ServiceType } from '@model/service.interface';
import { Text, View } from '@tarojs/components';
import PriceDisplay from './PriceDisplay';

interface ServiceCardProps {
  service: Service;
  checked?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

export default function ServiceCard({ service, onClick }: ServiceCardProps) {
  return (
    <View
      className='flex flex-row items-center p-4 bg-bg rounded-3xl'
      onClick={onClick}
    >
      <View className='w-14 h-14 bg-primarylight rounded-full flex items-center justify-center mr-4'>
        <Image
          src={SERVICE_TYPE_ICON_DIVERSED[service.type]}
          className='w-6 h-6'
        />
      </View>
      <View className='flex-1 flex flex-col items-start'>
        <Text className='text-base font-bold'>
          {SERVICE_TYPE_MAP[service.type]}
        </Text>
        <Text className='block text-secondary text-sm font-normal mt-1'>
          一对一{SERVICE_TYPE_MAP_SHORT[service.type]}通话
        </Text>
      </View>
      <View className='flex flex-col items-end py-3'>
        <PriceDisplay price={service.finalPrice || service.price} />
        <Text className='text-secondary text-sm ml-1 mt-1'>
          {service.duration}/{service.unit}
        </Text>
      </View>
      <Radio name={service.type} className='ml-4' />
    </View>
  );
}

export function PurchasedServiceCard({
  serviceType,
  serviceDuration,
  price,
  serviceUnit,
}: {
  serviceType: ServiceType;
  serviceDuration: number;
  price: number;
  serviceUnit: string;
}) {
  return (
    <View className='flex flex-row items-center p-4 bg-bg rounded-3xl'>
      <View className='w-14 h-14 bg-primarylight rounded-full flex items-center justify-center mr-4'>
        <Image
          src={SERVICE_TYPE_ICON_DIVERSED[serviceType]}
          className='w-6 h-6'
        />
      </View>
      <View className='flex-1 flex flex-col items-start'>
        <Text className='text-base font-bold'>
          {SERVICE_TYPE_MAP[serviceType]}
        </Text>
        <Text className='block text-secondary text-sm font-normal mt-1'>
          一对一{SERVICE_TYPE_MAP_SHORT[serviceType]}通话
        </Text>
      </View>
      <View className='flex flex-col items-end py-3'>
        <PriceDisplay price={price} />
        <Text className='text-secondary text-sm ml-1 mt-1'>
          /{serviceDuration}
          {serviceUnit}
        </Text>
      </View>
    </View>
  );
}
