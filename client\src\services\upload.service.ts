import { ResourceType } from '@model/common.interface';
import { uploadFile } from '@services/cloud';
import Taro from '@tarojs/taro';

/**
 * 上传结果接口
 */
export interface UploadResult {
  success: boolean;
  url: string;
  message?: string;
}

/**
 * 文件上传服务
 * 统一管理文件上传功能
 */
class UploadService {
  /**
   * 上传单个文件
   * @param file 文件项
   * @param fileType 文件类型
   * @param customData 额外数据
   * @returns 上传结果
   */
  async uploadFile(
    filePath: string,
    fileType: ResourceType = ResourceType.OTHER
  ): Promise<string> {
    try {
      // 显示加载状态
      Taro.showLoading({ title: '上传中...' });

      console.log('uploadFile filePath', filePath);
      console.log('uploadFile fileType', fileType);

      // 获取文件名
      const fileName = filePath.split('/').pop();
      console.log('uploadFile fileName', fileName);

      // 上传文件
      const fileId = await uploadFile(filePath, fileType + '/' + fileName);
      console.log('uploadFile result', fileId);
      return fileId;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    } finally {
      Taro.hideLoading();
    }
  }

  /**
   * 上传多个文件
   * @param files 文件列表
   * @param fileType 文件类型
   * @param customData 额外数据
   * @returns 上传结果列表
   */
  async uploadMultipleFiles(
    files: string[],
    fileType: ResourceType = ResourceType.OTHER
  ): Promise<string[]> {
    try {
      // 显示加载状态
      Taro.showLoading({ title: '上传中...' });

      // 上传多个文件
      const uploadPromises = files.map((file) =>
        this.uploadFile(file, fileType)
      );

      const results = await Promise.all(uploadPromises);

      // 隐藏加载状态
      Taro.hideLoading();

      // 过滤成功的结果并返回URL列表
      return results;
    } catch (error) {
      console.error('多文件上传失败:', error);
      Taro.hideLoading();
      throw error;
    }
  }

  /**
   * 上传头像
   * @param file 头像文件
   * @returns 上传结果
   */
  async uploadAvatar(file: string): Promise<string> {
    return this.uploadFile(file, ResourceType.AVATAR);
  }

  /**
   * 上传证书文件
   * @param file 证书文件
   * @returns 上传结果
   */
  async uploadCertification(file: string): Promise<string> {
    return this.uploadFile(file, ResourceType.CERTIFICATE);
  }

  /**
   * 上传音频文件
   * @param file 音频文件
   * @returns 上传结果
   */
  async uploadAudio(file: string): Promise<string> {
    return this.uploadFile(file, ResourceType.AUDIO);
  }

  /**
   * 上传照片
   * @param files 照片文件列表
   * @returns 照片URL列表
   */
  async uploadPhotos(files: string[]): Promise<string[]> {
    return this.uploadMultipleFiles(files, ResourceType.PHOTO);
  }
}

// 导出单例
export const uploadService = new UploadService();
