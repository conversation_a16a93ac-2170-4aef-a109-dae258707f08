import { Rate } from '@antmjs/vantui';
import { SERVICE_TYPE_MAP } from '@constants/text';
import { Order_review } from '@model/order.interface';
import { Image, Text, View } from '@tarojs/components';

export interface ReviewItemProps {
  review: Order_review;
}

const TherapistReviewItem: React.FC<ReviewItemProps> = ({ review }) => {
  return (
    <View className='flex flex-row gap-2 mb-4'>
      <Image src={review.avatar} className='w-9 h-9 rounded-full' />
      <View className='flex-1 flex flex-col gap-3'>
        <View className='flex flex-row items-center justify-between'>
          <Text className='text-sm font-bold'>{review.userName}</Text>
          <Rate
            value={review.score}
            size='12px'
            color='var(--color-star)'
            voidColor='var(--color-text-disabled)'
            readonly
            className='flex flex-row items-center'
          />
        </View>
        {/* 显示一排用户打的标签 */}
        <View className='flex flex-row flex-wrap gap-4 justify-start pl-4 w-full'>
          {review.tags.map((tag) => (
            // 标签背景色
            <View key={tag} className='bg-gray-100 rounded-md px-2 py-1'>
              <Text className='text-xs font-bold '>{tag}</Text>
            </View>
          ))}
        </View>
        <Text className='block text-sm mt-1'>{review.content}</Text>
        <View className='flex flex-row items-center justify-between'>
          <Text className='text-xs text-secondary'>
            {SERVICE_TYPE_MAP[review.service]}
          </Text>
          <Text className='text-xs text-secondary ml-2'>{review.date}</Text>
        </View>
      </View>
    </View>
  );
};

export default TherapistReviewItem;
