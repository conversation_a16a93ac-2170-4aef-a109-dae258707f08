import { OrderListRequest } from '@core/api';
import { Order_summary } from '@model/order.interface';
import { BaseCacheService } from './base-cache.service';
import {
  CACHE_ROLE,
  ORDER_CACHE_PREFIX,
  orderDetailCacheOptions,
  orderListCacheOptions,
} from './order-cache-policy';

/**
 * 订单缓存管理器
 * 封装缓存操作，统一缓存键的生成和管理
 */
export class OrderCacheManager {
  /**
   * 设置订单列表缓存
   * @param params 请求参数
   * @param data 订单列表数据
   * @param pagination 分页信息
   * @param role 角色
   */
  static setOrderList(
    params: OrderListRequest,
    data: Order_summary[],
    pagination: any,
    role: CACHE_ROLE = CACHE_ROLE.USER
  ): void {
    // 设置角色
    params.role = role;

    // 生成缓存键
    const cacheKey = orderListCacheOptions.getCacheKey(params);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      {
        data,
        pagination,
      },
      orderListCacheOptions.ttl,
      orderListCacheOptions.getVersion()
    );
  }

  /**
   * 获取订单列表缓存
   * @param params 请求参数
   * @param role 角色
   * @returns 缓存数据或null
   */
  static getOrderList(
    params: OrderListRequest,
    role: CACHE_ROLE = CACHE_ROLE.USER
  ): { data: Order_summary[]; pagination: any } | null {
    // 设置角色
    params.role = role;

    // 生成缓存键
    const cacheKey = orderListCacheOptions.getCacheKey(params);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 设置订单详情缓存
   * @param orderId 订单ID
   * @param order 订单数据
   * @param role 角色
   */
  static setOrderSummary(
    orderId: string,
    order: Order_summary,
    role: CACHE_ROLE = CACHE_ROLE.USER
  ): void {
    // 生成缓存键
    const cacheKey = orderDetailCacheOptions.getCacheKey(orderId, role);

    // 设置缓存
    BaseCacheService.set(
      cacheKey,
      order,
      orderDetailCacheOptions.ttl,
      orderDetailCacheOptions.getVersion()
    );
  }

  /**
   * 获取订单详情缓存
   * @param orderId 订单ID
   * @param role 角色
   * @returns 缓存数据或null
   */
  static getOrderSummary(
    orderId: string,
    role: CACHE_ROLE = CACHE_ROLE.USER
  ): Order_summary | null {
    // 生成缓存键
    const cacheKey = orderDetailCacheOptions.getCacheKey(orderId, role);

    // 获取缓存
    return BaseCacheService.get(cacheKey, null);
  }

  /**
   * 清除订单列表缓存
   * @param role 角色
   */
  static clearOrderList(role: CACHE_ROLE = CACHE_ROLE.USER): void {
    BaseCacheService.clearByPrefix(`${role}_${ORDER_CACHE_PREFIX.ORDER_LIST}`);
  }

  /**
   * 清除订单详情缓存
   * @param orderId 订单ID
   * @param role 角色
   */
  static clearOrderSummary(
    orderId: string,
    role: CACHE_ROLE = CACHE_ROLE.USER
  ): void {
    const cacheKey = orderDetailCacheOptions.getCacheKey(orderId, role);
    BaseCacheService.remove(cacheKey);
  }

  /**
   * 清除所有订单缓存
   * @param role 角色，不指定则清除所有角色的缓存
   */
  static clearAll(role?: CACHE_ROLE): void {
    if (role) {
      // 清除指定角色的所有缓存
      BaseCacheService.clearByPrefix(`${role}_`);
    } else {
      // 清除所有角色的缓存
      Object.values(CACHE_ROLE).forEach((r) => {
        BaseCacheService.clearByPrefix(`${r}_`);
      });
    }
  }
}
