const { PERMISSION_LEVEL, USER_ROLE } = require("./permissions");
const { COLLECTIONS } = require("./db.constants");

const cloud = require("wx-server-sdk");
/**
 * 获取用户信息
 * @param {string} openid 用户openid
 */
async function getUserInfo(openid) {
  if (!openid) return null;

  const db = cloud.database();
  const userResult = await db
    .collection(COLLECTIONS.USER_PUBLIC)
    .where({ _openid: openid })
    .get();
  return userResult.data && userResult.data.length > 0
    ? userResult.data[0]
    : null;
}

/**
 * 获取用户信息
 * @param {string} openid 用户openid
 */
async function getUserInfoByUnionid(unionid) {
  if (!unionid) return null;

  const db = cloud.database();
  const userResult = await db
    .collection(COLLECTIONS.USER_PUBLIC)
    .where({ unionid })
    .get();
  return userResult.data && userResult.data.length > 0
    ? userResult.data[0]
    : null;
}

/**
 * 权限检查函数
 * @param {number} requiredLevel 所需权限等级
 * @param {Object} context 上下文，包含 openid, userRole 等信息
 * @param {string} resourceId 资源ID（如咨询师ID）
 */
async function checkPermission(requiredLevel, context) {
  const { openid, userRole = USER_ROLE.GUEST } = context;
  let hasPermission = false;

  // 如果userRole是数组，则检查是否包含所需角色
  const userRoles = Array.isArray(userRole) ? userRole : [userRole];

  console.log("userRoles", userRoles, requiredLevel);
  switch (requiredLevel) {
    case PERMISSION_LEVEL.PUBLIC:
      hasPermission = true;
      break;

    case PERMISSION_LEVEL.USER_ONLY:
      hasPermission = userRoles.some((role) =>
        [USER_ROLE.USER, USER_ROLE.ADMIN].includes(role)
      );
      break;

    case PERMISSION_LEVEL.THERAPIST_ONLY:
      hasPermission = userRoles.some((role) =>
        [USER_ROLE.THERAPIST, USER_ROLE.ADMIN].includes(role)
      );
      break;

    // 咨询师和用户本人。业务需要鉴权
    case PERMISSION_LEVEL.THERAPIST_AND_USER_SELF:
      hasPermission = userRoles.some((role) =>
        [USER_ROLE.THERAPIST, USER_ROLE.USER, USER_ROLE.ADMIN].includes(role)
      );
      break;
    case PERMISSION_LEVEL.ADMIN_ONLY:
      hasPermission = userRoles.includes(USER_ROLE.ADMIN);
      break;
  }

  return hasPermission;
}

/**
 * 权限检查装饰器
 */
async function withPermission(requiredLevel, context, callback) {
  const hasPermission = await checkPermission(requiredLevel, context);

  if (hasPermission) {
    return await callback();
  } else {
    const error = new Error("权限不足");
    error.type = "permission";
    error.code = 403;
    throw error;
  }
}

module.exports = {
  getUserInfo,
  getUserInfoByUnionid,
  checkPermission,
  withPermission,
};
