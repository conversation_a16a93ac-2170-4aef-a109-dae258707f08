import { Result } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { distributionActions } from '@core/actions/distribution.action';
import { incomeActions } from '@core/actions/income.action';
import { useIncomeList } from '@hooks/useIncomeList';
import IncomeItem from '@pages/sub-packages/wallet/components/IncomeItem';
import { Text, View } from '@tarojs/components';
import { useReachBottom } from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';

/**
 * 咨询师收益列表页面
 */
export default function IncomeListPage() {
  const [activeTab, setActiveTab] = useState(0);

  // 使用收入列表hook
  const {
    incomeList,
    pagination,
    loading,
    refreshIncomeList,
    loadMoreIncome,
    onFilterChange,
  } = useIncomeList();

  // // 根据activeTab筛选收入
  // const getFilterParams = useCallback(() => {
  //   switch (activeTab) {
  //     case 1: // 收入
  //       return { status: 'settled' as const };
  //     case 2: // 退款
  //       return { status: 'refunded' as const };
  //     default: // 全部
  //       return {};
  //   }
  // }, [activeTab]);

  // 处理标签变化
  const handleTabChange = useCallback(
    (tabIndex: number) => {
      setActiveTab(tabIndex);
      const filterParams =
        tabIndex === 1
          ? { incomeType: 1 as const }
          : tabIndex === 2
          ? { incomeType: 2 as const }
          : tabIndex === 3
          ? { incomeType: 3 as const }
          : {};
      onFilterChange(filterParams);
    },
    [onFilterChange]
  );

  // 监听滚动到底部事件
  useReachBottom(() => {
    if (pagination?.hasNext && !loading) {
      loadMoreIncome();
    }
  });

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        incomeActions.loadMyWallet(),
        distributionActions.fetchDistributionOverview(),
      ]);
    };
    loadData();
  }, []);

  // 渲染收入列表
  const renderIncomeList = useCallback(() => {
    if (!loading && incomeList.length === 0) {
      return (
        <View className='p-4'>
          <Result type='info' title='暂无收入记录' message='暂无收入记录' />
        </View>
      );
    }

    return (
      <View className='p-4'>
        {incomeList.map((item, index) => (
          <IncomeItem
            income={item}
            key={`${item.id}-${item.createdAt}-${index}`}
          />
        ))}

        {loading && (
          <View className='py-4 text-center text-secondary text-sm'>
            加载中...
          </View>
        )}

        {!loading &&
          pagination &&
          !pagination.hasNext &&
          incomeList.length > 0 && (
            <View className='py-4 text-center text-secondary text-sm'>
              没有更多数据了
            </View>
          )}
      </View>
    );
  }, [incomeList, loading, pagination]);

  return (
    <PageLol
      navigationProps={{
        title: '收入列表',
        showBackButton: true,
        showSearch: false,
      }}
      onPullDownRefresh={refreshIncomeList}
      onRetry={refreshIncomeList}
      isEmpty={!loading && incomeList.length === 0}
    >
      <View className='min-h-screen pb-16'>
        {/* 标签组 */}
        <View className='flex flex-row items-center justify-between p-4'>
          <View
            className={`px-3 py-2 rounded-md ${
              activeTab === 0 ? 'bg-primary text-white' : 'bg-gray-100'
            }`}
            onClick={() => handleTabChange(0)}
          >
            <Text
              className={`text-sm font-bold ${
                activeTab === 0 ? 'text-white' : 'text-gray-600'
              }`}
            >
              全部
            </Text>
          </View>
          <View
            className={`px-3 py-2 rounded-md ${
              activeTab === 1 ? 'bg-primary text-white' : 'bg-gray-100'
            }`}
            onClick={() => handleTabChange(1)}
          >
            <Text
              className={`text-sm font-bold ${
                activeTab === 1 ? 'text-white' : 'text-gray-600'
              }`}
            >
              咨询
            </Text>
          </View>
          <View
            className={`px-3 py-2 rounded-md ${
              activeTab === 2 ? 'bg-primary text-white' : 'bg-gray-100'
            }`}
            onClick={() => handleTabChange(2)}
          >
            <Text
              className={`text-sm font-bold ${
                activeTab === 2 ? 'text-white' : 'text-gray-600'
              }`}
            >
              分享
            </Text>
          </View>
          <View
            className={`px-3 py-2 rounded-md ${
              activeTab === 3 ? 'bg-primary text-white' : 'bg-gray-100'
            }`}
            onClick={() => handleTabChange(3)}
          >
            <Text
              className={`text-sm font-bold ${
                activeTab === 3 ? 'text-white' : 'text-gray-600'
              }`}
            >
              退款
            </Text>
          </View>
        </View>

        {/* 收入明细列表 */}
        {renderIncomeList()}
      </View>
    </PageLol>
  );
}
