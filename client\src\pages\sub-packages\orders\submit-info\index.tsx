import {
  Button,
  Cell,
  CellGroup,
  Field,
  Radio,
  RadioGroup,
} from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import ResultPopup from '@components/common/result-popup';
import { ILLUSTRATION_MAP } from '@constants/assets';
import { CONSULTATION_DIRECTIONS_MAP } from '@constants/text';
import { orderUserActions } from '@core/actions/order.user';
import { ConsultationDirection } from '@model/common.interface';
import { useLoadingStore } from '@stores/loading.store';
import { Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { orderRouter } from '@utils/router';
import { useState } from 'react';

export default function SubmitInfo() {
  const router = useRouter();
  const { orderId, source } = router.params;
  const [name, setName] = useState('');
  const [category, setCategory] = useState<ConsultationDirection | undefined>(
    undefined
  );
  const [desc, setDesc] = useState('');
  const [showResultPopup, setShowResultPopup] = useState(false);
  const [resultType, setResultType] = useState<'success' | 'error'>('success');
  const [resultMsg, setResultMsg] = useState('');
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  console.log('orderId', orderId, 'type', typeof orderId);
  const handleSubmit = async () => {
    try {
      setTransactionLoading(true);
      await orderUserActions.submitUserInfo(orderId!, name, category, desc);
      setResultType('success');
      // 根据来源显示不同的成功信息
      if (source === 'payment') {
        setResultMsg('预约成功！您的咨询信息已提交');
      } else {
        setResultMsg('您的咨询信息已成功提交');
      }
    } catch (error) {
      setResultType('error');
      setResultMsg('提交失败，请稍后重试');
    } finally {
      setShowResultPopup(true);
      setTransactionLoading(false);
    }
  };

  // 处理确认按钮点击
  const handleConfirm = () => {
    orderRouter.detail(orderId!, true);
  };

  return (
    <PageLol
      navigationProps={{
        title: '填写咨询信息',
        showBackButton: true,
        onBack() {
          orderRouter.detail(orderId!, true);
        },
      }}
    >
      <View className=' p-4 mb-4'>
        <Text className='text-base font-bold mb-4 block'>您的称呼</Text>

        <View className='rounded-xl bg-white'>
          <Field
            placeholder='请输入'
            value={name}
            onChange={(e) => setName(e.detail)}
            required={false}
            border={false}
          />
        </View>

        <Text className='text-base font-bold mb-4 mt-6 block'>
          您要咨询哪类问题呢
        </Text>

        <RadioGroup value={category}>
          <CellGroup className='rounded-xl bg-white'>
            {CONSULTATION_DIRECTIONS_MAP.map((item) => (
              <Cell
                key={item.key}
                title={item.label}
                border={false}
                renderRightIcon={<Radio name={item.key} />}
                onClick={() => setCategory(item.key)}
              />
            ))}
          </CellGroup>
        </RadioGroup>

        <Text className='text-base font-bold mb-6 mt-6  block'>
          您的补充信息
        </Text>
        <View className='rounded-xl bg-white mb-8'>
          <Field
            placeholder='请描述您的详细问题'
            value={desc}
            onChange={(e) => setDesc(e.detail)}
            type='textarea'
            required={false}
            border={false}
          />
        </View>

        {/* 提交按钮 */}
        <Button
          type='primary'
          round
          block
          loading={transactionLoading}
          disabled={(!name && !category && !desc) || transactionLoading}
          onClick={handleSubmit}
        >
          提交
        </Button>
      </View>

      {/* 提交结果弹窗 */}
      <ResultPopup
        show={showResultPopup}
        type={resultType}
        title={
          resultType === 'success'
            ? source === 'payment'
              ? '预约成功'
              : '提交成功'
            : '提交失败'
        }
        illustration={
          resultType === 'success'
            ? source === 'payment'
              ? ILLUSTRATION_MAP.APPOINTMENT_SUCCESS
              : ILLUSTRATION_MAP.COMMON_COMPLETE
            : ILLUSTRATION_MAP.COMMON_FAILED
        }
        content={resultMsg}
        buttonText='确定'
        onButtonClick={handleConfirm}
        closeOnClickOverlay={false}
      />
    </PageLol>
  );
}
