import { Feedback } from '@model/feedback.interface';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';

interface State {
  // 状态
  feedbackList: Feedback[];
  currentFeedback: Feedback | null;
  isLoading: boolean;
  isSubmitting: boolean;
  hasError: boolean;
  errorMessage: string;
}
interface Actions {
  setFeedbackList: (feedbacks: Feedback[]) => void;
  addFeedback: (feedback: Feedback) => void;
  setCurrentFeedback: (feedback: Feedback | null) => void;
  setLoading: (loading: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  setError: (hasError: boolean, message?: string) => void;
  reset: () => void;
}
const initialState: State = {
  feedbackList: [],
  currentFeedback: null,
  isLoading: false,
  isSubmitting: false,
  hasError: false,
  errorMessage: '',
};

const feedbackStore = create<State & Actions>()(
  immer((set) => ({
    ...initialState,

    // 设置反馈列表
    setFeedbackList: (feedbacks) =>
      set((state) => {
        state.feedbackList = feedbacks;
        state.isLoading = false;
      }),

    // 添加反馈
    addFeedback: (feedback) =>
      set((state) => {
        state.feedbackList = [feedback, ...state.feedbackList];
      }),

    // 设置当前反馈详情
    setCurrentFeedback: (feedback) =>
      set((state) => {
        state.currentFeedback = feedback;
        state.isLoading = false;
      }),

    // 设置加载状态
    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
        if (loading) {
          state.hasError = false;
          state.errorMessage = '';
        }
      }),

    // 设置提交状态
    setSubmitting: (submitting) =>
      set((state) => {
        state.isSubmitting = submitting;
      }),

    // 设置错误
    setError: (hasError, message = '') =>
      set((state) => {
        state.hasError = hasError;
        state.errorMessage = message;
        state.isLoading = false;
        state.isSubmitting = false;
      }),

    // 重置
    reset: () =>
      set((state) => {
        state.feedbackList = [];
        state.currentFeedback = null;
        state.isLoading = false;
        state.isSubmitting = false;
        state.hasError = false;
        state.errorMessage = '';
      }),
  }))
);

export const useFeedbackSelectors = createSelectors(feedbackStore);
