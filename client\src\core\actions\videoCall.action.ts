import { useLoadingStore } from '@stores/loading.store';
import { useTherapistOrderStoreSelector } from '@stores/order.store';
import { useUserProfileStore } from '@stores/profile.user';
import { useVideoCallStore } from '@stores/videoCall.store';
import Taro from '@tarojs/taro';

// 请求相机和麦克风权限
const requestCameraPermission = () => {
  Taro.authorize({
    scope: 'scope.camera',
    success: () => {
      console.log('相机授权成功');
    },
    fail: () => {
      console.log('相机授权失败');
      Taro.showToast({
        title: '相机授权失败，无法进行视频通话',
        icon: 'none',
      });
    },
  });

  Taro.authorize({
    scope: 'scope.record',
    success: () => {
      console.log('麦克风授权成功');
    },
    fail: () => {
      console.log('麦克风授权失败');
      Taro.showToast({
        title: '麦克风授权失败，无法进行语音通话',
        icon: 'none',
      });
    },
  });
};

// 初始化视频通话
const registerListener = () => {
  Taro.onVoIPChatStateChanged((res) => {
    console.log('通话状态变化', res);
    handleVOIPStateChanged(res);
  });

  // 监听通话中断事件

  Taro.onVoIPChatInterrupted((res) => {
    console.log('通话中断', res);
    handleVOIPInterrupted(res);
  });

  // 监听通话成员变化

  Taro.onVoIPChatMembersChanged((res) => {
    console.log('通话成员变化', res);
    handleVOIPMembersChanged(res);
  });
};

// 处理通话状态变化
const handleVOIPStateChanged = (res: any) => {
  console.log('通话状态变化', res);
  const { state, errCode, errMsg } = res;
};

// 处理通话中断
const handleVOIPInterrupted = (res: any) => {
  console.log('通话中断', res);
  const { errCode, errMsg } = res;
  useVideoCallStore.getState().setError(errMsg || '通话中断');
};

// 处理通话成员变化
const handleVOIPMembersChanged = (res: any) => {
  console.log('通话成员变化', res);
  // 当没有成员时，结束通话
  if (res.openIdList && res.openIdList.length === 0) {
  }
};

// 统一错误处理
const handleError = (method: string, error: any) => {
  const errMsg = error?.errMsg || error?.message || '未知错误';
  useVideoCallStore.getState().setError(`视频通话操作失败: ${errMsg}`);
  console.error(`视频通话 ${method} 失败:`, error);
  throw new Error(`视频通话操作失败: ${errMsg}`);
};

// 发起通话
const call = async (orderId: string) => {
  console.log('发起视频通话', orderId);
  try {
    const order = useTherapistOrderStoreSelector
      .getState()
      .getOrderById(orderId);
    if (!order) {
      throw new Error('订单不存在');
    }
    const myInfo = useUserProfileStore.getState().profile;
    if (!myInfo) {
      throw new Error('用户信息不存在');
    }
    const caller = {
      nickname: myInfo.userName,
      openid: order.therapistId,
      image: myInfo.avatar,
    };
    const listener = {
      nickname: order.consultationInfo.name ?? order.consultationInfo.username,
      openid: order.userId,
      image: order.consultationInfo.avatar,
    };

    // 注册监听
    registerListener();

    Taro.setEnable1v1Chat({
      enable: true,
      success: () => {
        console.log('开启双人通话成功');
        // 发起VOIP通话 - 实际项目中可能需要后端支持生成签名等
        Taro.join1v1Chat({
          roomType: 'voice', // 房间类型，'voice': 音频房间，'video': 视频房间
          caller,
          // 必须添加listener参数，否则会报错
          listener,
          success: (res) => {
            console.log('正在发起视频通话...', res);
          },
          fail: (err) => {
            console.error('发起通话失败', err);
            Taro.showToast({
              title: '发起通话失败',
              icon: 'none',
            });
          },
          complete: () => {
            useLoadingStore.getState().setTransactionLoading(false);
          },
        });
      },
      fail: (err) => {
        console.error('开启双人通话失败，用户可能没有授权', err);
      },
    });
  } catch (error) {
    console.error('发起视频通话异常', error);
    handleError('call', error);
    return false;
  }
};

const enableVoIP = () => {
  Taro.setEnable1v1Chat({
    enable: true,
  });
};

export const videoCallActions = {
  requestCameraPermission,
  call,
  enableVoIP,
};
