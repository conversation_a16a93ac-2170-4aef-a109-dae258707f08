async function initWallet(openid) {
  console.log("🚀🚀🚀 initWallet openid", openid);
  try {
    const wallet = await db
      .collection(COLLECTIONS.WALLET)
      .where({ openid })
      .get();
    if (wallet.data && wallet.data.length > 0) {
      console.log("🚀🚀🚀 钱包已存在", wallet.data);
      return;
    }
    await db.collection(COLLECTIONS.WALLET).add({
      data: {
        _id: openid,
        _openid: openid,
        id: openid,
        balance: 0,
        totalIncome: 0,
        frozenAmount: 0,
        lastSettlement: 0,
        withdrawPassword: "",
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    });
  } catch (error) {
    console.error("初始化钱包失败:", error);
    throw error;
  }
}

async function initStatSummary(openid) {
  console.log("🚀🚀🚀 initStatSummary openid", openid);
  try {
    const statSummary = await db
      .collection(COLLECTIONS.STAT_SUMMARY)
      .where({ id: openid })
      .get();
    if (statSummary.data && statSummary.data.length > 0) {
      console.log("🚀🚀🚀 汇总统计已存在", statSummary.data);
      return;
    }
    await db.collection(COLLECTIONS.STAT_SUMMARY).add({
      data: {
        _openid: openid,
        id: openid,
        income: {
          total: 0,
          completed: 0,
          refunded: 0,
          distribution: 0,
          refundedDistribution: 0,
        },
        orders: {
          completed: 0,
          refunded: 0,
        },
        distribution: {
          total: 0,
          refunded: 0,
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    });
  } catch (error) {
    console.error("初始化汇总统计失败:", error);
    throw error;
  }
}

module.exports = {
  initWallet,
  initStatSummary,
};
