import {
  getStorageSync,
  removeStorageSync,
  setStorageSync,
} from '@tarojs/taro';
import { StateStorage } from 'zustand/middleware';

enum StorageSceneKey {
  PROFILE = 'storage-profile',
  USER = 'storage-user',
  MESSAGES = 'storage-messages',
  CHAT = 'storage-chat',
  THERAPIST = 'storage-therapist',
  THERAPIST_MANAGEMENT = 'storage-therapist-management',
  THERAPIST_ORDER = 'storage-therapist-order',
  USER_ORDER = 'storage-user-order',
  ADMIN_ORDER = 'storage-admin-order',
  DISTRIBUTION = 'storage-distribution',
  ORDER_REVIEWS = 'storage-order-reviews',
  GLOBLE = 'storage-globle',
  THERAPIST_UPDATER = 'storage-therapist-updater',
  THERAPIST_REGISTER = 'storage-therapist-register',
  MEASURE = 'storage-measure',
  TEST = 'storage-test',
  TREATMENT = 'storage-treatment',
  AUDIO = 'storage-audio',
  INCOME_LIST = 'storage-income-list',
}
function getItem<T = any>(key: string): T | null {
  // console.log('getItem', key);
  const value = getStorageSync(key);
  return value ? JSON.parse(value) ?? null : null;
}

function setItem(key: string, value: unknown): void {
  try {
    setStorageSync(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Could not save to localStorage: ${error}`);
  }
}

function removeItem(key: string): void {
  removeStorageSync(key);
}

export { getItem, removeItem, setItem, StorageSceneKey };

export const zustandStorage: StateStorage = {
  getItem: (name) => {
    const result = getItem(name);
    // console.log('getItem', name, result);
    return JSON.stringify(result);
  },
  setItem: (name, value) => {
    setItem(name, JSON.parse(value));
    // console.log('setItem', name, JSON.parse(value));
  },
  removeItem: (name) => {
    removeItem(name);
    // console.log('removeItem', name);
  },
};
