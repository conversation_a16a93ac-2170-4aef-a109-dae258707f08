import { Ellipsis, Icon, SwipeCell } from '@antmjs/vantui';
import PriceDisplay from '@components/order-card/PriceDisplay';
import {
  AVATAR_DEFAULT,
  ICONS_BOLD_WHITE,
  SERVICE_TYPE_ICON_BOLD,
} from '@constants/assets';
import {
  CONSULTATION_DIRECTIONS_MAP,
  ORDER_STATUS_MAP,
  REFUND_STATUS_MAP,
  SERVICE_TYPE_MAP,
} from '@constants/text';
import { orderTherapistActions } from '@core/actions/order.therapist';
import { videoCallActions } from '@core/actions/videoCall.action';
import {
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from '@model/order.interface';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatTime3 } from '@utils/time';

export default function OrderSwipeCard({ order }: { order: Order_summary }) {
  const renderActionButton = () => {
    // 如果有退款状态且不是无退款，不显示操作按钮
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      return <></>;
    }

    if (order.status <= ORDER_STATUS.PENDING_START) {
      return (
        <View className='ml-3'>
          <View
            className='flex flex-row w-10 h-10 items-center justify-center bg-primary rounded-full'
            onClick={() => {
              // 拨打电话
              orderTherapistActions.callUser(order.userId, order._id);
            }}
          >
            <Image
              src={ICONS_BOLD_WHITE.CALL}
              className='w-6 h-6 rounded-full'
            />
          </View>
        </View>
      );
    } else if (order.status === ORDER_STATUS.IN_PROGRESS) {
      return (
        <View className='ml-3'>
          <View
            className='flex flex-row w-10 h-10 items-center justify-center bg-primary rounded-full'
            onClick={() => {
              videoCallActions.call(order._id);
            }}
          >
            <Image
              src={ICONS_BOLD_WHITE.CAMERA_ON}
              className='w-6 h-6 rounded-full'
            />
          </View>
        </View>
      );
    } else {
      return <></>;
    }
  };

  // 订单操作处理函数
  const handleConfirmOrder = (orderId: string) => {
    Taro.showModal({
      title: '确认接单',
      content: '确认接单后，将无法拒绝订单',
      showCancel: true,
      success: async (res) => {
        if (res.confirm) {
          // 接单
          console.log('接单');
          Taro.showLoading({
            title: '接单中...',
          });
          try {
            await orderTherapistActions.confirmOrder(orderId);

            Taro.hideLoading();
          } catch (error) {
            Taro.hideLoading();
            Taro.showModal({
              title: '接单异常',
              content: error.message || '接单失败',
              showCancel: false,
              confirmText: '确定',
            });
          }
        }
      },
    });
  };

  const handleRejectOrder = (orderId: string) => {
    Taro.showLoading({
      title: '页面跳转中...',
    });

    // 跳转到拒绝订单页面
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/reject/index?orderId=${orderId}`,
      complete: () => {
        Taro.hideLoading();
      },
    });
  };

  const handleStartService = async () => {
    const startTime = order.startTime;
    if (startTime > Date.now()) {
      Taro.showModal({
        title: '开始服务',
        content: '还未到预约开始时间，是否继续？',
        showCancel: true,
        success: async (res) => {
          if (res.confirm) {
            try {
              await orderTherapistActions.startService(
                order._id,
                order.serviceType
              );
            } catch (error) {
              Taro.showModal({
                title: '开始服务异常',
                content: error.message || '开始服务失败',
                showCancel: false,
                confirmText: '确定',
              });
            }
          }
        },
      });
    } else {
      try {
        await orderTherapistActions.startService(order._id, order.serviceType);
      } catch (error) {
        Taro.showModal({
          title: '开始服务异常',
          content: error.message || '开始服务失败',
          showCancel: false,
          confirmText: '确定',
        });
      }
    }
  };

  const handleCompleteService = async () => {
    const endTime = order.startTime + order.duration * 60 * 1000;

    Taro.showModal({
      title: '完成服务',
      content:
        endTime > Date.now()
          ? '还未到预约结束时间，是否继续？'
          : '此操作将完成订单，是否继续？',
      showCancel: true,
      success: async (res) => {
        if (res.confirm) {
          // 完成服务
          console.log('完成服务');
          Taro.showLoading({
            title: '完成服务中...',
          });
          try {
            await orderTherapistActions.completeService(order._id);
            Taro.hideLoading();
            Taro.showToast({
              title: '完成服务成功',
              icon: 'success',
            });
          } catch (error) {
            Taro.hideLoading();
            Taro.showToast({
              title: '完成服务失败',
              icon: 'none',
            });
          }
        }
      },
    });
  };

  // 定义OrderCard的actions类型
  interface OrderAction {
    text: string;
    onClick: () => void;
    type: 'primary' | 'default' | 'danger';
  }

  // 获取订单操作按钮
  const genOrderActions = (): OrderAction[] => {
    const actions: OrderAction[] = [];

    // 如果有退款状态且不是无退款，不显示操作按钮
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      return actions;
    }

    switch (order.status) {
      case ORDER_STATUS.PENDING_PAYMENT:
        // 待付款订单无需咨询师操作
        // 临时测试
        actions.push(
          {
            text: '接单',
            onClick: () => handleConfirmOrder(order._id),
            type: 'primary',
          },
          {
            text: '拒绝',
            onClick: () => handleRejectOrder(order._id),
            type: 'danger',
          }
        );
        break;
      case ORDER_STATUS.PENDING_CONFIRM:
        actions.push(
          {
            text: '拒绝',
            onClick: () => handleRejectOrder(order._id),
            type: 'danger',
          },
          {
            text: '确认',
            onClick: () => handleConfirmOrder(order._id),
            type: 'primary',
          }
        );
        break;
      case ORDER_STATUS.PENDING_START:
        actions.push({
          text: '开始',
          onClick: () => handleStartService(),
          type: 'primary',
        });
        break;
      case ORDER_STATUS.IN_PROGRESS:
        actions.push({
          text: '完成',
          onClick: () => handleCompleteService(),
          type: 'primary',
        });
        break;
      default:
        break;
    }

    return actions;
  };
  const renderOrderActions = () => {
    return (
      <View className='flex flex-col items-center justify-center gap-4 h-full'>
        {genOrderActions().map((action) => (
          <View
            key={action.text}
            onClick={(e) => {
              e.stopPropagation();
              action.onClick();
            }}
            className={`w-10 h-10 rounded-full flex items-center justify-center ${
              action.type === 'primary'
                ? 'bg-primary text-white'
                : action.type === 'danger'
                ? 'bg-danger text-white'
                : 'border border-primary text-primary'
            }`}
          >
            <Text className='text-sm font-medium'>{action.text}</Text>
          </View>
        ))}
      </View>
    );
  };
  return (
    <SwipeCell renderRight={renderOrderActions()} rightWidth={100}>
      <View
        className='bg-white rounded-xl shadow p-3 w-full'
        onClick={() => {
          Taro.navigateTo({
            url: `/pages/sub-packages/orders/detail-therapist/index?orderId=${order._id}`,
          });
        }}
      >
        {/* 头部 */}
        <View className='flex flex-row items-center mb-2'>
          <Image
            src={order.userAvatar || AVATAR_DEFAULT}
            className='mr-2 w-10 h-10 rounded-full'
          />
          <View className='flex-1'>
            <View className='flex flex-col items-start'>
              <Text className='font-semibold text-base mr-1'>
                {order.consultationInfo?.name || order.userName}
              </Text>
              <View className='flex flex-row items-center gap-2'>
                {order.consultationInfo.name && (
                  <Text className='text-md text-secondary'>
                    {order.consultationInfo.name}
                  </Text>
                )}
                <View className='px-1.5 py-0.5 border border-border text-sm rounded-md mr-1 text-secondary'>
                  {CONSULTATION_DIRECTIONS_MAP.find(
                    (item) => item.key === order.consultationInfo.direction
                  )?.label || ''}
                </View>
              </View>
            </View>
          </View>
          <View
            className='px-2 py-0.5 rounded text-xs font-medium ml-2'
            style={{
              background:
                order.status === ORDER_STATUS.PENDING_START
                  ? '#F5F5F5'
                  : '#F5F5F5',
              color: '#FF9900',
            }}
          >
            {/* 显示订单状态，优先显示退款状态 */}
            {order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE
              ? REFUND_STATUS_MAP[order.refundStatus]
              : ORDER_STATUS_MAP[order.status]}
          </View>
        </View>
        {/* 分割线 */}
        <View className='h-px bg-gray-100 my-2' />
        {/* 内容区 */}
        <View className='flex flex-row items-center'>
          {/* 左侧信息 */}
          <View className='flex-1 min-w-0 flex flex-col items-start'>
            {/* 服务信息 */}
            <View className='flex flex-row items-center mb-1'>
              <Image
                src={SERVICE_TYPE_ICON_BOLD[order.serviceType]}
                className='w-3 h-3 mr-1'
              />
              <Text className='text-md  mr-2'>
                {SERVICE_TYPE_MAP[order.serviceType]}
              </Text>
              <View className='px-1.5 py-0.5 border border-border text-sm rounded-md mr-1 text-secondary'>
                {order.duration}分钟
              </View>
              <View className='px-1.5 py-0.5 bg-secondarylight rounded-md'>
                <PriceDisplay price={order.price} size='sm' />
              </View>
            </View>
            {/* 时间信息 */}
            <View className='flex flex-row items-center mb-1'>
              <Icon
                name='clock'
                size='12px'
                color='var(--color-text-secondary)'
                className='mr-1'
              />
              <Text className='text-md '>
                {formatTime3(
                  order.startTime,
                  order.startTime + order.duration * 60 * 1000
                )}
              </Text>
            </View>
            {/* 问诊信息 */}
            {order.consultationInfo.desc && (
              <View className='user_info_desc flex flex-row items-center w-full'>
                <Ellipsis rows={2} rectWrapper='.user_info_desc'>
                  {order.consultationInfo.desc}
                </Ellipsis>
              </View>
            )}
          </View>
          {/* 右侧CTA按钮 */}
          {renderActionButton()}
        </View>
      </View>
    </SwipeCell>
  );
}
