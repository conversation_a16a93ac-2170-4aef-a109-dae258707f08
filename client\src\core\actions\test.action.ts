import {
  DimensionScore,
  PsychologicalTest,
  TestReport,
  TestResult,
  UserAnswer,
  UserTestRecord,
} from '@model/test.model';
import { testService } from '@services/test.service';
import { useLoadingStore } from '@stores/loading.store';
import { useMeasureStore } from '@stores/measure.store';
import { useTestStore } from '@stores/test.store';
import Taro from '@tarojs/taro';

/**
 * 测试相关操作
 * 封装所有与测试相关的业务逻辑
 */
export const testAction = {
  // 获取测试详情
  fetchTest: async (testId: string): Promise<PsychologicalTest | null> => {
    const store = useTestStore.getState();

    // 从store中获取
    const test = store.getTest(testId);
    if (test) {
      return test;
    }

    try {
      store.setLoading(true);
      store.setError(null);

      const testData = await testService.getTest(testId);
      store.setTestList([...store.testList, testData]);
      return testData;
    } catch (error) {
      console.error('获取测试详情失败:', error);
      store.setError(error as Error);
      return null;
    } finally {
      store.setLoading(false);
    }
  },

  // 开始测试
  startTest: async (testId: string): Promise<void> => {
    const store = useTestStore.getState();
    try {
      // 获取测试详情
      const testData = await testAction.fetchTest(testId);
      if (!testData) {
        store.setError(new Error('获取测试详情失败'));
        return;
      }

      // 初始化测试
      store.startTest(testData);
    } catch (error) {
      console.error('开始测试失败:', error);
      store.setError(error as Error);
      return;
    } finally {
      store.setLoading(false);
    }
  },

  // 回答问题
  answerQuestion: (
    questionId: string,
    answer: number | string | number[]
  ): void => {
    const store = useTestStore.getState();
    const { currentTest } = store;

    if (!currentTest) {
      console.error('没有正在进行的测试');
      return;
    }

    const question = currentTest.questions.find((q) => q.id === questionId);
    if (!question) {
      console.error('问题不存在:', questionId);
      return;
    }

    // 创建答案对象
    const userAnswer: UserAnswer = {
      questionId,
      answer,
      timestamp: Date.now(),
    };

    // 保存答案
    store.answerQuestion(userAnswer);
  },

  // 跳转到指定题目
  goToQuestion: (index: number): void => {
    const store = useTestStore.getState();
    store.goToQuestion(index);
  },

  // 跳转到下一题
  nextQuestion: (): boolean => {
    const store = useTestStore.getState();
    const { currentTest, currentQuestionIndex } = store;

    if (!currentTest) return false;

    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex < currentTest.questions.length) {
      store.goToQuestion(nextIndex);
      return true;
    }

    return false;
  },

  // 跳转到上一题
  prevQuestion: (): boolean => {
    const store = useTestStore.getState();
    const { currentQuestionIndex } = store;

    if (currentQuestionIndex > 0) {
      store.goToQuestion(currentQuestionIndex - 1);
      return true;
    }

    return false;
  },

  // 提交测试
  submitTest: async (): Promise<void> => {
    const store = useTestStore.getState();
    const { currentTest, activeRecord } = store;

    if (!currentTest || !activeRecord) {
      console.error('没有正在进行的测试');
      useTestStore.getState().setError(new Error('没有正在进行的测试'));
      return;
    }

    try {
      Taro.showLoading({
        title: '提交中...',
      });
      useLoadingStore.getState().setTransactionLoading(true);
      store.setError(null);

      // 计算得分
      const report = testAction.generateReport(
        currentTest,
        activeRecord.report.answers
      );
      const record: UserTestRecord = {
        ...activeRecord,
        endTime: Date.now(),
        report,
      };
      store.setActiveRecord(record);

      console.log('🚀🚀🚀 准备提交测试记录', record);
      // 提交测试记录到服务器,返回recordId
      const recordId = await testService.submitTest(record);

      // 更新measureStore, 历史记录由measureStore管理
      useMeasureStore
        .getState()
        .setRecentHistory([
          { ...record, id: recordId },
          ...useMeasureStore.getState().recentHistory,
        ]);

      // 跳转到结果页
      Taro.redirectTo({
        url: `/pages/sub-packages/measure/report/index?recordId=${recordId}`,
      });
    } catch (error) {
      console.error('提交测试失败:', error);
      store.setError(error as Error);
      return;
    } finally {
      Taro.hideLoading();
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },

  // 清除当前测试
  clearCurrentTest: (): void => {
    const store = useTestStore.getState();
    store.clearCurrentTest();
  },

  // 辅助函数：生成结果报告
  generateReport: (
    test: PsychologicalTest,
    answers: UserAnswer[]
  ): TestReport => {
    // 按维度计算得分
    const dimensionScores: DimensionScore[] = [];

    test.scoringRules.forEach((rule) => {
      let dimensionScore = 0;
      let answeredCount = 0;

      rule.questionIds.forEach((qId) => {
        const answer = answers.find((a) => a.questionId === qId);
        if (answer && typeof answer.answer === 'number') {
          let score = answer.answer as number;

          // 处理反向计分
          const question = test.questions.find((q) => q.id === qId);
          if (question?.reverseScoring) {
            // 假设是5点量表
            score = 6 - score;
          }

          dimensionScore += score;
          answeredCount++;
        }
      });

      // 计算维度得分
      if (rule.calculation === 'average' && answeredCount > 0) {
        dimensionScore = dimensionScore / answeredCount;
      }
      //保存维度interpretation
      const interpretation = rule.interpretation.find(
        (i) =>
          dimensionScore >= i.scoreRange[0] && dimensionScore <= i.scoreRange[1]
      );
      if (interpretation) {
        dimensionScores.push({
          dimension: rule.dimension!,
          score: dimensionScore,
          range: rule.range,
          interpretation: interpretation,
        });
        // 都应该配置interpretation，这里是兜底异常
      } else {
        dimensionScores.push({
          dimension: rule.dimension!,
          score: dimensionScore,
          range: rule.range,
        });
      }
    });

    const result: TestResult = {
      dimensionScores,
    };
    // 从dimensionScores中剔除总分
    const totalScoreIndex = dimensionScores.findIndex(
      (d) => d.dimension === '总分'
    );

    if (totalScoreIndex !== -1) {
      const totalScoreDimension = dimensionScores[totalScoreIndex];

      dimensionScores.splice(totalScoreIndex, 1);
      result.score = totalScoreDimension.score;
      result.range = totalScoreDimension.range;
      result.interpretation = totalScoreDimension.interpretation;
      result.dimensionScores = dimensionScores as DimensionScore[];
    }

    return {
      testId: test.id,
      testTitle: test.title,
      answers: answers,
      result,
    };
  },
};
