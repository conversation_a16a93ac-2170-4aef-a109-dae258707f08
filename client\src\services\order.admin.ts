import { COLLECTIONS } from '@model/db.model';
import { Order_action, Order_summary } from '@model/order.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';

/**
 * 咨询师订单服务类,咨询师端使用
 * 处理咨询师端的订单相关业务逻辑
 */
class AdminOrderServiceImpl extends BaseService<Order_summary> {
  constructor() {
    super(COLLECTIONS.ORDER);
  }

  async getOrderActions(orderId: string): Promise<Order_action[]> {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.ORDER_ACTION)
        .where({
          orderId,
        })
        .get()
        .then((res) => res.data.map((item) => item as Order_action));
      return result;
    } catch (error) {
      console.error('获取订单操作失败:', error);
      throw error;
    }
  }
}

export const adminOrderService = new AdminOrderServiceImpl();
