import { Icon } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import { ReactNode } from 'react';

interface ActionButton {
  text: string;
  onClick: () => void;
  icon: string;
}

interface ActionBarProps {
  leftButtons?: ActionButton[];
  children?: ReactNode;
}

export default function ActionBar({
  leftButtons = [],
  children,
}: ActionBarProps) {
  return (
    <View className='fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4 flex items-center'>
      {/* 左侧按钮组 */}
      {leftButtons.length > 0 && (
        <View className='flex items-center space-x-4 mr-4'>
          {leftButtons.map((btn, index) => (
            <View
              key={index}
              className='flex flex-col items-center'
              onClick={btn.onClick}
            >
              <Icon name={btn.icon} size='18px' />
              <Text className='text-xs mt-1'>{btn.text}</Text>
            </View>
          ))}
        </View>
      )}

      {/* 右侧内容区域 */}
      <View className='flex-1 flex justify-end space-x-2'>{children}</View>
    </View>
  );
}
