import { Icon, Image } from '@antmjs/vantui';
import DefaultImage from '@components/common/DefaultImage';
import DotLoading from '@components/common/loading-dots';
import PageLol from '@components/common/page-meta';
import {
  ICONS_BOLD,
  ICONS_BOLD_PRIMARY,
  ICONS_BOLD_WHITE,
} from '@constants/assets';
import { audioActions } from '@core/actions/audio.action';
import { TreatmentAudio } from '@model/treatment.interface';
import { useAudioStore } from '@stores/audio.store';
import { useTreatmentStore } from '@stores/treatment.store';
import { Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { formatTime5 } from '@utils/time';
import { useEffect, useMemo } from 'react';

// 播放器页面
export default function PlayerPage() {
  const router = useRouter();
  const { id } = router.params;
  const audio = useTreatmentStore((state) => state.getAudio(id as string));

  // useRenderCount('PlayerPage');
  // 从状态存储中获取播放状态
  const playbackState = useAudioStore.use.playbackState();
  const loading = useAudioStore.use.loading();
  // console.log('PlayerPage playbackState', playbackState, loading);

  // 计算播放进度百分比
  const calculateProgress = useMemo(() => {
    if (!playbackState.duration) return 0;
    // 无论是否正在播放，都使用当前时间计算进度
    return (playbackState.currentTime / playbackState.duration) * 100;
  }, [playbackState.currentTime, playbackState.duration]);

  // 暂停/播放
  const togglePlay = () => {
    if (playbackState.isPlaying) {
      audioActions.pauseAudio();
    } else {
      audioActions.play();
    }
  };

  // 前进15秒
  const forward15s = () => {
    const newTime = Math.min(
      playbackState.currentTime + 15,
      playbackState.duration || 0
    );
    audioActions.seekAudio(newTime);
  };

  // 后退15秒
  const backward15s = () => {
    const newTime = Math.max(playbackState.currentTime - 15, 0);
    audioActions.seekAudio(newTime);
  };

  // 初始化加载数据
  useEffect(() => {
    console.log('PlayerPage useEffect audio', audio);
    audioActions.initialize(audio as TreatmentAudio);
    if (audio) {
      audioActions.play();
    }

    return () => {
      // 页面卸载时停止播放
      audioActions.stopAudio();
    };
  }, [audio]);

  return (
    <PageLol
      navigationProps={{
        title: '正在播放',
        showBackButton: true,
      }}
      // loading={loading}
    >
      {audio && (
        <View className='flex flex-col items-center justify-between min-h-screen bg-gray-100 p-4'>
          {/* 封面区域 */}
          <View className='flex-1 w-full flex flex-col items-center justify-center'>
            <Image
              src={audio.coverImage}
              width='256px'
              height='256px'
              fit='cover'
              radius='12px'
              renderLoading={<DotLoading />}
              renderError={<DefaultImage size={256} />}
            />

            <View className='mt-8 text-center'>
              <Text className='text-2xl font-bold block'>{audio.title}</Text>
              <Text className='text-sm text-secondary mt-2 block'>
                {audio.description}
              </Text>
            </View>
          </View>

          {/* 进度条 */}
          <View className='w-full mt-8 px-4'>
            <View className='w-full flex justify-between mb-2'>
              <Text className='text-sm text-secondary'>
                {formatTime5(playbackState.currentTime)}
              </Text>
              <Text className='text-sm text-secondary'>
                {formatTime5(
                  playbackState.duration > 0
                    ? playbackState.duration
                    : audio?.duration || 0
                )}
              </Text>
            </View>
            <View className='w-full h-1 bg-gray-200 rounded-full'>
              <View
                className='h-full bg-primary rounded-full'
                style={{
                  width: `${calculateProgress}%`,
                }}
              />
            </View>
          </View>

          {/* 控制按钮 */}
          <View className='w-full flex items-center justify-between mt-8 mb-12 px-8'>
            {/* 后退15秒 */}
            <View
              className='w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center'
              onClick={backward15s}
            >
              <Icon name={ICONS_BOLD.BACKWARD48} size='24px' />
            </View>

            {/* 播放/暂停按钮 */}
            <View className='relative'>
              {loading ? (
                <View className='w-16 h-16 rounded-full bg-white flex items-center justify-center shadow-lg'>
                  <DotLoading />
                </View>
              ) : playbackState.isPlaying ? (
                <View
                  className='w-16 h-16 bg-white rounded-full border-[4px] border-primary flex items-center justify-center shadow-lg'
                  onClick={togglePlay}
                >
                  <Icon name={ICONS_BOLD_PRIMARY.PAUSE64} size='32px' />
                </View>
              ) : (
                <View
                  className='w-16 h-16 bg-primary rounded-full  flex items-center justify-center shadow-lg'
                  onClick={togglePlay}
                >
                  <Icon name={ICONS_BOLD_WHITE.PLAY64} size='32px' />
                </View>
              )}
            </View>

            {/* 前进15秒 */}
            <View
              className='w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center'
              onClick={forward15s}
            >
              <Icon name={ICONS_BOLD.FORWARD48} size='24px' />
            </View>
          </View>
        </View>
      )}
    </PageLol>
  );
}
