import { AVATAR_DEFAULT } from '@constants/assets';
import { therapistActions } from '@core/actions/therapist.action';
import { useOrderStoreSelector } from '@stores/order.store';
import { useTherapistStore } from '@stores/therapist.store';
import { getOrderActions } from '@utils/role';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';

/**
 * 自定义Hook，用于获取订单对应的咨询师信息和订单信息.供用户端使用
 * @param orderId 订单ID
 * @param forceRefresh 是否强制刷新数据
 */
export function useOrderTherapist(
  orderId: string | null | undefined,
  forceRefresh = false
) {
  console.log('useOrderTherapist', orderId, forceRefresh);
  // 使用store
  const order = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId || ''))
  );
  const therapist = useTherapistStore(
    useShallow((state) =>
      state.therapists?.find((item) => item.id === order?.therapistId)
    )
  );

  useEffect(() => {
    // 如果没有订单ID，则不执行任何操作
    if (!orderId) {
      return;
    }

    const fetchTherapist = async () => {
      try {
        // 1. 获取订单信息
        const orderData = await getOrderActions().fetchOrderById(
          orderId,
          forceRefresh
        );

        // 2. 如果订单不存在或没有咨询师ID，则返回
        if (!orderData || !orderData.therapistId) {
          return;
        }

        // 3. 获取咨询师信息
        await therapistActions.fetchTherapistSummary(
          orderData.therapistId,
          forceRefresh
        );
      } catch (err) {
        console.error(err);
      }
    };

    fetchTherapist();
  }, [orderId, forceRefresh]);

  return {
    therapist,
    therapistAvatar: therapist?.avatar || AVATAR_DEFAULT,
    therapistName: therapist?.name || AVATAR_DEFAULT,
    order,
  };
}
