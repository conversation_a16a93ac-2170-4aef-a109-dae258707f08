import {
  Button,
  Cell,
  CellGroup,
  Field,
  Radio,
  RadioGroup,
} from '@antmjs/vantui';
import { View } from '@tarojs/components';
import { ReactNode } from 'react';

export interface ReasonOption {
  label: string;
  value: string | number;
}

interface ReasonFormProps {
  title: string;
  reasonOptions: ReasonOption[];
  selectedReason: string | number | null;
  detail: string | null;
  onReasonChange: (reason: string | number) => void;
  onDetailChange: (detail: string) => void;
  onSubmit: () => Promise<void>;
  loading: boolean;
  buttonText?: string;
  placeholder?: string;
  headerContent?: ReactNode;
}

export default function ReasonForm({
  title,
  reasonOptions,
  selectedReason,
  detail,
  onReasonChange,
  onDetailChange,
  onSubmit,
  loading,
  buttonText = '提交',
  placeholder = '请详细描述原因',
  headerContent,
}: ReasonFormProps) {
  return (
    <View className='reason-form'>
      {/* 可选的头部内容 */}
      {headerContent && headerContent}

      {/* 原因选择 */}
      <RadioGroup value={selectedReason} className='mt-4'>
        <CellGroup
          title={title}
          inset
          className='[&_.van-cell-group__title]:text-base [&_.van-cell-group__title]:font-bold'
        >
          {reasonOptions.map((item) => (
            <Cell
              key={item.value}
              title={item.label}
              clickable
              onClick={() => onReasonChange(item.value)}
              renderRightIcon={<Radio name={item.value} />}
            ></Cell>
          ))}
        </CellGroup>
      </RadioGroup>

      {/* 详细情况输入 */}
      <View className='p-4'>
        <Field
          placeholder={placeholder}
          value={detail ?? ''}
          border={false}
          type='textarea'
          autosize={{ minHeight: '168px' }}
          onChange={(e) => onDetailChange(e.detail)}
          style={`
          background-color: var(--color-primary-bg) !important;
          border-radius: 16px !important;
        `}
        />
      </View>

      {/* 提交按钮 full-width */}
      <View className='p-4'>
        <Button
          type='primary'
          block
          className='mt-8'
          disabled={selectedReason === null || loading}
          onClick={onSubmit}
          round
          loading={loading}
        >
          {buttonText}
        </Button>
      </View>
    </View>
  );
}
