import { QRCodeDialog } from "@/components/shared/qrcode-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Clock, Users } from "lucide-react";

export interface AssessmentCardProps {
  id: string;
  name: string;
  description: string;
  category?: string;
  time: string;
  questionCount?: number;
  usedCount?: number;
}

export function AssessmentCard({
  id,
  name,
  description,
  category,
  time,
  questionCount,
  usedCount,
}: AssessmentCardProps) {
  return (
    <Card key={id}>
      <CardContent className="p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <div className="text-xs text-muted-foreground">图标</div>
          </div>
          <div>
            <h3 className="font-semibold">{name}</h3>
            {category ? (
              <div className="flex items-center gap-2">
                <span className="text-xs bg-slate-100 px-2 py-1 rounded text-slate-600">
                  {category}
                </span>
              </div>
            ) : (
              <div className="text-xs text-muted-foreground">
                {questionCount}题 · {time}分钟
              </div>
            )}
          </div>
        </div>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
          {description}
        </p>
        {usedCount && (
          <div className="flex justify-between items-center mb-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{time}分钟</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{usedCount}人测试</span>
            </div>
          </div>
        )}
        <QRCodeDialog title={`扫码进行${name}`}>
          <Button variant="outline" className="w-full">
            开始测试
          </Button>
        </QRCodeDialog>
      </CardContent>
    </Card>
  );
}
