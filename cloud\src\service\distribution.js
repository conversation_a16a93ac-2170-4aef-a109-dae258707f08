// 云函数入口文件
const cloud = require("wx-server-sdk");
const { COLLECTIONS, ORDER_STATUS } = require("../common/db.constants");
const { REFERRER_RATE } = require("../common/config");
const { generateDistributionOrderId } = require("../common/utils");
const {
  _settleDistributionIncome,
  _refundDistributionIncome,
} = require("./orderOperation");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

async function _generateQrCodeUrl(openid) {
  // 生成分销二维码
  const qrResult = await cloud.openapi.wxacode.getUnlimited({
    scene: `ref=${openid}`,
    page: "pages/homepage/index",
    width: 430,
    checkPath: false,
    envVersion: "develop",
  });

  if (qrResult.errCode) {
    throw new Error(`生成二维码失败: ${qrResult.errMsg}`);
  }

  // 上传二维码到云存储
  const uploadResult = await cloud.uploadFile({
    cloudPath: `qrcode/${openid}.png`,
    fileContent: qrResult.buffer,
    config: {
      permission: "READONLY_ALL_USERS",
    },
  });

  return uploadResult.fileID;
}

/**
 * 初始化分销账户
 * @param {string} openid 用户openid
 */
async function initDistributionAccount(openid) {
  console.log("🚀🚀🚀 initDistributionAccount", openid);
  try {
    // 检查是否已存在
    const accountResult = await db
      .collection(COLLECTIONS.DISTRIBUTION_OVERVIEW)
      .where({ referrerId: openid })
      .get();

    if (accountResult.data && accountResult.data.length > 0) {
      return accountResult.data[0];
    }

    // 生成qrcode
    const qrCodeUrl = await _generateQrCodeUrl(openid);

    // 创建分销账户
    const newAccount = {
      referrerId: openid,
      qrCodeUrl,
      totalIncome: 0,
      invitedCount: 0,
      totalOrderCount: 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      _openid: openid,
    };

    const result = await db.collection(COLLECTIONS.DISTRIBUTION_OVERVIEW).add({
      data: newAccount,
    });

    newAccount._id = result._id;

    return newAccount;
  } catch (error) {
    console.error("初始化分销账户失败:", error);
    throw error;
  }
}

/**
 * 创建分销订单
 * @param {string} orderId 订单ID
 * @param {string} inviteeId 被邀请人ID
 * @param {string} referrerId 推荐人ID
 * @param {number} orderAmount 订单金额
 * @param {number} commission 佣金
 */
async function createDistributionOrder(order) {
  console.log("🚀🚀🚀 createDistributionOrder order", order);
  try {
    const { _id: orderId, userId, price, status } = order;
    // 只有服务完成时，才创建订单
    if (status !== ORDER_STATUS.COMPLETED) {
      console.error("订单状态不是已完成，不创建分销订单", orderId);
      return;
    }

    // 第二步，校验是否存在推荐人
    const referrerResult = await db
      .collection(COLLECTIONS.DISTRIBUTION_INVITED_USER)
      .where({ inviteeId: userId })
      .field({ referrerId: true })
      .get();
    const referrerId = referrerResult.data[0]?.referrerId;
    if (!referrerId) {
      // 没有推荐人，不处理
      console.info("没有推荐人，不创建分销订单", orderId);
      return;
    }

    // 第三步，计算分成比例
    const referrerRate = REFERRER_RATE; // 推荐人10%

    // 计算佣金
    const commission = Math.round(price * referrerRate * 100) / 100;

    const distributionOrderId = generateDistributionOrderId();
    // 创建分销订单记录
    const distributionOrder = {
      _id: distributionOrderId,
      id: distributionOrderId,
      referrerId,
      inviteeId: userId,
      inviteeAvatar: order.userAvatar,
      inviteeName: order.userName,
      orderId,
      orderAmount: price,
      commission,
      status: "settled", // 初始状态为已结算
      createdAt: Date.now(),
    };

    await db.collection(COLLECTIONS.DISTRIBUTION_ORDER).add({
      data: distributionOrder,
    });

    // 更新分销账户订单数量
    await db
      .collection(COLLECTIONS.DISTRIBUTION_OVERVIEW)
      .where({ referrerId })
      .update({
        data: {
          totalOrderCount: _.inc(1),
          totalIncome: _.inc(commission),
          updatedAt: Date.now(),
        },
      });

    await _settleDistributionIncome(distributionOrder);

    return distributionOrder;
  } catch (error) {
    console.error("创建分销订单失败:", error);
    throw error;
  }
}

/**
 * 更新分销订单状态
 * @param {string} orderId 原订单ID
 */
async function refundDistributionOrder(orderId) {
  console.log("🚀🚀🚀 refundDistributionOrder orderId", orderId);
  try {
    const orderResult = await db
      .collection(COLLECTIONS.DISTRIBUTION_ORDER)
      .where({ orderId })
      .get();
    if (!orderResult.data || orderResult.data.length === 0) {
      console.error("未找到分销订单", orderId);
      return;
    }
    const distributionOrder = orderResult.data[0];
    const { status } = distributionOrder;

    if (status !== "settled") {
      console.error("分销订单状态不是已结算，不退款", orderId);
      return;
    }

    // 更新分销订单状态
    await db
      .collection(COLLECTIONS.DISTRIBUTION_ORDER)
      .doc(distributionOrder._id)
      .update({
        data: {
          status: "refunded",
          refundedAt: Date.now(),
        },
      });

    // 更新分销账户余额
    await db
      .collection(COLLECTIONS.DISTRIBUTION_OVERVIEW)
      .where({ referrerId: distributionOrder.referrerId })
      .update({
        data: {
          totalIncome: _.inc(-distributionOrder.commission),
          updatedAt: Date.now(),
        },
      });

    await _refundDistributionIncome(distributionOrder);
  } catch (error) {
    console.error("退款分销订单失败:", error);
    throw error;
  }
}

/**
 * 处理邀请信息
 * @param {string} referralId 邀请人ID
 * @param {string} openid 被邀请人openid
 */
async function addReferralRelation(referralId, referralType, userInfo) {
  console.log("🚀🚀🚀 addReferralRelation", {
    referralId,
    referralType,
    userInfo,
  });
  try {
    // 如果邀请ID和用户openid相同，不处理
    if (referralId === userInfo.id) {
      return;
    }
    // 查询邀请信息
    const referralResult = await db
      .collection(COLLECTIONS.DISTRIBUTION_INVITED_USER)
      .where({ inviteeId: userInfo.id })
      .get();
    if (referralResult.data && referralResult.data.length > 0) {
      console.log("邀请信息已存在");
      return;
    }
    // 更新邀请信息
    await db.collection(COLLECTIONS.DISTRIBUTION_INVITED_USER).add({
      data: {
        referrerId: referralId,
        inviteeId: userInfo.id,
        inviteeAvatar: userInfo.avatar,
        inviteeName: userInfo.name,
        invitedTime: Date.now(),
      },
    });
    // 更新用户信息
    await db
      .collection(COLLECTIONS.DISTRIBUTION_OVERVIEW)
      .where({ referrerId: referralId })
      .update({
        data: {
          invitedCount: _.inc(1),
          updatedAt: Date.now(),
        },
      });
  } catch (err) {
    console.error("处理邀请信息失败:", err);
    throw err;
  }
}

module.exports = {
  addReferralRelation,
  initDistributionAccount,
  createDistributionOrder,
  refundDistributionOrder,
};
