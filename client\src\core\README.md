### action 负责执行流程。简单的流程包含：调用 service 与服务端交换数据，更新本地缓存。

### 流程包含：

1. 调用 service 与服务端交换数据
2. 更新本地缓存
3. 返回结果

!!! 注意，根据 AI 的建议，流程中应该负责处理 store 中的 loading 和 error 状态，由页面组件自行订阅并处理。根据需要可以增加 summitting 状态。

##### 视频通话

1. 用户进入房间时，如果对端咨询师已经启动通话，则显示咨询师视频通话界面。如果咨询师没有启动通话，则显示空闲界面（显示咨询师头像，并全屏有渐变灰蒙版。提示用户等待咨询师上线
2. 咨询师上线后并启动通话（拨号），用户端显示拨号界面(包含咨询师头像、咨询师昵称，正在拨号提示)，用户可以接听、拒绝
3. 用户接听后，显示通话界面，开启用户推流，并显示咨询师视频画面和自己画面。如果拒绝，显示空闲界面。
4. 在视频通话界面中，用户可以切换前后摄像头、开启关闭麦克风、开启关闭扬声器、开启关闭视频推流、挂断电话
5. 咨询师挂断后，用户自动挂断，用户端显示通话已结束界面（提示问诊结束，显示好评按钮）

对于用户端，共有 4 个界面：空闲界面、拨号界面、通话界面、通话结束界面。
