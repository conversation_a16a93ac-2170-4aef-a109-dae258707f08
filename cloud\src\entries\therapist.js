// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS } = require("../common/db.constants");
const { AVATAR_DEFAULT } = require("../common/config");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 路由处理函数
const handlers = {
  favorite: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await favoriteTherapist(
          params.id,
          params.favorite,
          context.openid,
          context.userId
        );
        return success(result);
      }
    );
  },

  updateSchedule: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await updateTherapistSchedule(
          params.schedule,
          context.openid
        );
        return success(result);
      }
    );
  },

  updateProfile: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await updateTherapistProfile(
          params.profile,
          context.openid
        );
        return success(result);
      }
    );
  },

  updateExtendInfo: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await updateTherapistExtendInfo(
          params.extend,
          context.openid
        );
        return success(result);
      }
    );
  },

  updateServices: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await updateTherapistServices(
          params.services,
          context.openid
        );
        return success(result);
      }
    );
  },

  register: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await registerTherapist(params, context.openid);
      return success(result);
    });
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = userInfo?.role || USER_ROLE.GUEST;
    const userId = userInfo?._id;

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    console.log("action", action);
    console.log("params", params);
    console.log("actionContext", actionContext);

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

/**
 * 收藏/取消收藏咨询师
 * @param {string} therapistId 咨询师ID
 * @param {boolean} favorite 是否收藏
 * @param {string} openid 用户openid
 * @param {string} userId 用户ID
 */
async function favoriteTherapist(therapistId, favorite = true, openid, userId) {
  try {
    const userFavoritesCollection = db.collection(COLLECTIONS.USER_FAVORITES);

    // 检查是否已经收藏
    const existingResult = await userFavoritesCollection
      .where({
        id: userId,
        therapistId,
      })
      .get();

    const alreadyFavorited =
      existingResult.data && existingResult.data.length > 0;

    // 根据操作决定是收藏还是取消收藏
    if (favorite && !alreadyFavorited) {
      // 新增收藏记录
      await userFavoritesCollection.add({
        data: {
          id: userId,
          userId,
          therapistId,
          createdAt: Date.now(),
          _openid: openid,
        },
      });

      // 增加咨询师收藏数
      await db
        .collection(COLLECTIONS.THERAPIST)
        .where({ id: therapistId })
        .update({
          data: {
            favoriteCount: _.inc(1),
          },
        });
    } else if (!favorite && alreadyFavorited) {
      // 删除收藏记录
      await userFavoritesCollection
        .where({
          id: userId,
          therapistId,
        })
        .remove();

      // 减少咨询师收藏数
      await db
        .collection(COLLECTIONS.THERAPIST)
        .where({ id: therapistId })
        .update({
          data: {
            favoriteCount: _.inc(-1),
          },
        });
    }

    // 获取更新后的收藏数
    const updatedTherapist = await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: therapistId })
      .get();

    const favoriteCount = updatedTherapist.data[0]?.favoriteCount || 0;

    return {
      favoriteCount,
      isFavorite: favorite,
    };
  } catch (error) {
    console.error("收藏咨询师操作失败:", error);
    throw error;
  }
}

/**
 * 更新咨询师排期
 * @param {string} therapistId 咨询师ID
 * @param {Array} scheduleData 排期数据
 * @param {string} openid 用户openid
 */
async function updateTherapistSchedule(scheduleData, openid) {
  console.log("updateTherapistSchedule", scheduleData, openid);
  try {
    // 验证数据
    if (!scheduleData || typeof scheduleData !== "object") {
      throw new Error("排期数据格式错误");
    }
    if (scheduleData.id !== openid) {
      throw new Error("无权限更新他人排期");
    }

    // 批量更新排期
    const batch = [];

    for (const schedule of scheduleData.schedule) {
      const { date, available, slots, note } = schedule;

      // 检查必要字段
      if (!date) {
        continue;
      }

      // 查找是否已存在该日期的排期
      const existingSchedule = await db
        .collection(COLLECTIONS.THERAPIST_SCHEDULES)
        .where({
          id: openid,
          date,
        })
        .get();

      if (existingSchedule.data && existingSchedule.data.length > 0) {
        // 更新现有排期
        batch.push(
          db
            .collection(COLLECTIONS.THERAPIST_SCHEDULES)
            .doc(existingSchedule.data[0]._id)
            .update({
              data: {
                available: available !== undefined ? available : true,
                slots: _.addToSet({
                  $each: slots || [],
                }),
                note: note || "",
                updatedAt: Date.now(),
              },
            })
        );
      } else {
        // 创建新排期
        batch.push(
          db.collection(COLLECTIONS.THERAPIST_SCHEDULES).add({
            data: {
              id: openid,
              date,
              available: available !== undefined ? available : true,
              slots: slots || [],
              note: note || "",
              createdAt: Date.now(),
              updatedAt: Date.now(),
            },
          })
        );
      }
    }

    // 执行批量操作
    await Promise.all(batch);

    return {
      message: "排期更新成功",
      updatedCount: batch.length,
    };
  } catch (error) {
    console.error("更新咨询师排期失败:", error);
    throw error;
  }
}

/**
 * 更新咨询师基本信息
 * @param {string} id 咨询师ID
 * @param {Object} profileData 个人信息数据
 * @param {string} openid 用户openid
 */
async function updateTherapistProfile(profile, openid) {
  try {
    console.log("更新咨询师基本信息", profile, openid);
    // 验证数据
    if (!profile || typeof profile !== "object") {
      throw new Error("个人信息数据格式错误");
    }
    // 只能更新自己的信息
    if (profile.id !== openid) {
      throw new Error("无权限更新他人信息");
    }

    // 提取基本信息
    const {
      id,
      name,
      avatar,
      gender,
      age,
      titles,
      location,
      tags,
      directions,
      specialties,
      favoriteCount,
      service,
      promo,
      price,
      available,
      rating,
      ratingCount,
      status,
    } = profile;

    // 更新基本信息
    const basicInfo = {};
    if (name) basicInfo.name = name;
    if (avatar) basicInfo.avatar = avatar;
    if (gender) basicInfo.gender = gender;
    if (age) basicInfo.age = age;
    if (titles) basicInfo.titles = titles;
    if (location) basicInfo.location = location;
    if (tags) basicInfo.tags = tags;
    if (directions) basicInfo.directions = directions;
    if (specialties) basicInfo.specialties = specialties;
    if (favoriteCount) basicInfo.favoriteCount = favoriteCount;
    if (service) basicInfo.service = service;
    if (promo) basicInfo.promo = promo;
    if (price) basicInfo.price = price;
    if (available) basicInfo.available = available;
    if (rating) basicInfo.rating = rating;
    if (ratingCount) basicInfo.ratingCount = ratingCount;
    if (status) basicInfo.status = status;

    if (Object.keys(basicInfo).length > 0) {
      basicInfo.updatedAt = Date.now();
      await db.collection(COLLECTIONS.THERAPIST).where({ id }).update({
        data: basicInfo,
      });
    }

    return {
      message: "个人信息更新成功",
      updatedBasic: Object.keys(basicInfo).length > 0,
    };
  } catch (error) {
    console.error("更新咨询师个人信息失败:", error);
    throw error;
  }
}

/**
 * 更新咨询师扩展信息
 * @param {Object} extend 扩展信息数据
 * @param {string} openid 用户openid
 */
async function updateTherapistExtendInfo(extend, openid) {
  try {
    console.log("更新咨询师扩展信息", extend, openid);
    // 验证数据
    if (!extend || typeof extend !== "object") {
      throw new Error("扩展信息数据格式错误");
    }

    // 只能更新自己的信息
    if (extend.id !== openid) {
      throw new Error("无权限更新他人信息");
    }

    // 提取扩展信息
    const {
      id,
      photos,
      introduction,
      education,
      certifications,
      trainings,
      socialMedia,
    } = extend;

    // 更新扩展信息
    const extendInfo = {};
    extendInfo.updatedAt = Date.now();
    if (photos) extendInfo.photos = photos;
    if (introduction) extendInfo.introduction = introduction;
    if (education) extendInfo.education = education;
    if (certifications) extendInfo.certifications = certifications;
    if (trainings) extendInfo.trainings = trainings;
    if (socialMedia) extendInfo.socialMedia = socialMedia;

    if (Object.keys(extendInfo).length > 0) {
      // 检查是否存在扩展信息
      const existingExtInfo = await db
        .collection(COLLECTIONS.THERAPIST_EXTINFO)
        .where({ id })
        .get();

      if (existingExtInfo.data && existingExtInfo.data.length > 0) {
        // 更新现有扩展信息
        await db
          .collection(COLLECTIONS.THERAPIST_EXTINFO)
          .where({ id })
          .update({
            data: extendInfo,
          });
      } else {
        // 创建新扩展信息
        await db.collection(COLLECTIONS.THERAPIST_EXTINFO).add({
          data: {
            ...extendInfo,
            id: openid,
            createdAt: Date.now(),
            _openid: openid,
          },
        });
      }
    }

    return {
      message: "扩展信息更新成功",
      updatedExtend: Object.keys(extendInfo).length > 0,
    };
  } catch (error) {
    console.error("更新咨询师扩展信息失败:", error);
    throw error;
  }
}
/**
 * 更新咨询师服务信息
 * @param {string} id 咨询师ID
 * @param {Object} servicesData 服务信息数据
 * @param {string} openid 用户openid
 */
async function updateTherapistServices(servicesData, openid) {
  try {
    // 验证数据
    if (!servicesData || typeof servicesData !== "object") {
      throw new Error("服务信息数据格式错误");
    }

    // 提取服务信息
    const { id, services, promo, workTime } = servicesData;
    // 只能更新自己的信息
    if (id !== openid) {
      throw new Error("无权限更新他人信息");
    }

    const updateData = {};
    if (services) updateData.services = services;
    if (promo) updateData.promo = promo;
    if (workTime) updateData.workTime = workTime;

    // 检查是否存在服务信息
    const existingServices = await db
      .collection(COLLECTIONS.THERAPIST_SERVICE)
      .where({ id })
      .get();

    if (existingServices.data && existingServices.data.length > 0) {
      // 更新现有服务信息
      await db
        .collection(COLLECTIONS.THERAPIST_SERVICE)
        .where({ id })
        .update({
          data: { ...updateData, updatedAt: Date.now() },
        });
    } else {
      // 创建新服务信息
      await db.collection(COLLECTIONS.THERAPIST_SERVICE).add({
        data: {
          ...updateData,
          updatedAt: Date.now(),
          createdAt: Date.now(),
          _openid: openid,
          id,
        },
      });
    }

    // 更新咨询师基本信息中的服务类型和价格
    if (services && services.length > 0) {
      const enabledServices = services.filter((service) => service.enabled);
      // 提取服务类型
      const serviceTypes = enabledServices.map((service) => service.type);

      // 找出最低价格
      const prices = enabledServices.map(
        (service) => service.finalPrice || service.price
      );
      const minPrice = Math.min(...prices);

      // 更新咨询师基本信息
      await db
        .collection(COLLECTIONS.THERAPIST)
        .where({ id })
        .update({
          data: {
            service: serviceTypes,
            price: minPrice,
            updatedAt: Date.now(),
          },
        });
    }

    return {
      message: "服务信息更新成功",
    };
  } catch (error) {
    console.error("更新咨询师服务信息失败:", error);
    throw error;
  }
}

/**
 * 注册成为咨询师
 * @param {Object} data 注册数据
 * @param {string} openid 用户openid
 * @param {string} userId 用户ID
 */
async function registerTherapist(data, openid) {
  try {
    console.log("注册咨询师", openid);
    // 验证数据
    if (!data || typeof data !== "object") {
      throw new Error("注册数据格式错误");
    }

    const { summary, sensitive, detailInfo, serviceInfo } = data;

    if (!summary || !sensitive || !detailInfo || !serviceInfo) {
      throw new Error("注册数据不完整");
    }

    // 检查用户是否已经是咨询师
    const existingTherapist = await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: openid })
      .get();

    if (existingTherapist.data && existingTherapist.data.length > 0) {
      throw new Error("您已经注册为咨询师");
    }

    // 检查用户是否已经注册
    const existingUser = await db
      .collection(COLLECTIONS.USER_PUBLIC)
      .where({ id: openid })
      .get();

    // 如果没注册， 则注册
    if (!existingUser.data || existingUser.data.length === 0) {
      await db.collection(COLLECTIONS.USER_PUBLIC).add({
        data: {
          _openid: openid,
          _id: openid,
          id: openid,
          userName: summary.name,
          avatar: summary.avatar || AVATAR_DEFAULT,
          role: [USER_ROLE.THERAPIST, USER_ROLE.USER],
          status: "active",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      });
    } else {
      // 如果已注册，则更新用户信息
      await db
        .collection(COLLECTIONS.USER_PUBLIC)
        .where({ id: openid })
        .update({
          data: {
            role: _.addToSet(USER_ROLE.THERAPIST),
            updatedAt: Date.now(),
          },
        });
    }

    // 创建咨询师基本信息
    //计算优惠信息
    const promo =
      serviceInfo.promo?.desc ??
      // 查找是否存在优惠信息, 有则取第一个
      serviceInfo.services.find((service) => service.promo)?.promo?.desc;

    await db.collection(COLLECTIONS.THERAPIST).add({
      data: {
        _id: openid,
        id: openid,
        name: summary.name,
        avatar: summary.avatar || AVATAR_DEFAULT,
        titles: summary.titles || [],
        location: summary.location || "",
        favoriteCount: 0,
        service: serviceInfo.services.map((service) => service.type) || [],
        tags: summary.tags || [],
        directions: summary.directions || [],
        specialties: summary.specialties || [],
        // 最低价格
        price: serviceInfo.services.reduce((min, service) => {
          return Math.min(min, service.finalPrice || service.price);
        }, Infinity),
        // 优惠信息, 取第一个
        promo,
        available: "可预约",
        rating: 0,
        ratingCount: 0,
        status: "active",
        createdAt: Date.now(),
        updatedAt: Date.now(),
        _openid: openid,
      },
    });

    // 检查是否存在用户私密信息，不存在则创建，存在则更新
    const existingSensitive = await db
      .collection(COLLECTIONS.USER_SENSITIVE)
      .where({ id: openid })
      .get();

    if (!existingSensitive.data || existingSensitive.data.length === 0) {
      await db.collection(COLLECTIONS.USER_SENSITIVE).add({
        data: {
          id: openid,
          realName: sensitive.realName || "",
          phone: sensitive.phone,
          email: sensitive.email,
          identityVerification: sensitive.identityVerification,
          bankAccount: sensitive.bankAccount,
          emergencyContact: sensitive.emergencyContact,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          _openid: openid,
        },
      });
    } else {
      await db
        .collection(COLLECTIONS.USER_SENSITIVE)
        .where({ id: openid })
        .update({
          data: {
            realName: sensitive.realName || "",
            phone: sensitive.phone,
            email: sensitive.email,
            identityVerification: sensitive.identityVerification,
            bankAccount: sensitive.bankAccount,
            emergencyContact: sensitive.emergencyContact,
            updatedAt: Date.now(),
          },
        });
    }

    // 创建咨询师扩展信息
    await db.collection(COLLECTIONS.THERAPIST_EXTINFO).add({
      data: {
        id: openid,
        photos: detailInfo.photos,
        introduction: detailInfo.introduction,
        education: detailInfo.education,
        certifications: detailInfo.certifications,
        trainings: detailInfo.trainings,
        socialMedia: detailInfo.socialMedia,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        _openid: openid,
      },
    });

    // 创建咨询师服务信息
    await db.collection(COLLECTIONS.THERAPIST_SERVICE).add({
      data: {
        id: openid,
        services: serviceInfo.services,
        promo: serviceInfo.promo,
        workTime: serviceInfo.workTime,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        _openid: openid,
      },
    });

    console.log("注册咨询师 come here", openid);
    return {
      message: "注册成功，请等待审核",
      id: openid,
    };
  } catch (error) {
    console.error("注册咨询师失败:", error);
    throw error;
  }
}
