import { userProfileActions } from '@core/actions/profile.user';
import { useUserProfileStore } from '@stores/profile.user';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';

/**
 * 咨询师收藏状态管理Hook
 * 提供单个咨询师的收藏状态和操作
 *
 * @param therapistId 咨询师ID
 * @param initialFavoriteState 初始收藏状态，如果已知可以传入
 */
export function useTherapistFavorite(
  therapistId: string | null | undefined,
  initialFavoriteState?: boolean
) {
  const [isFavorite, setIsFavorite] = useState<boolean>(!!initialFavoriteState);
  const [loading, setLoading] = useState<boolean>(false);

  // 在组件挂载或therapistId变化时检查收藏状态
  useEffect(() => {
    // 如果提供了初始状态，使用初始状态
    if (initialFavoriteState !== undefined) {
      setIsFavorite(initialFavoriteState);
      return;
    }

    // 否则，如果有ID，则获取收藏状态
    if (therapistId) {
      // 先从userStore中获取收藏状态
      const favoriteTherapists =
        useUserProfileStore.getState().favoriteTherapists;
      if (favoriteTherapists) {
        setIsFavorite(favoriteTherapists.includes(therapistId));
      }
      // 如果userStore中没有收藏状态，则从API获取
      else {
        userProfileActions.fetchFavoriteTherapists().then((res) => {
          setIsFavorite(res.some((t) => t.id === therapistId));
        });
      }
    }
  }, [therapistId, initialFavoriteState]);

  /**
   * 切换收藏状态
   */
  const toggleFavorite = useCallback(async (): Promise<boolean> => {
    if (!therapistId) return false;

    try {
      setLoading(true);

      // 反转当前收藏状态
      const newFavoriteState = !isFavorite;

      // 调用API进行操作
      const result = await userProfileActions.toggleFavorite(
        therapistId,
        newFavoriteState
      );

      // 更新本地状态
      setIsFavorite(newFavoriteState);

      // // 显示操作成功提示
      // Taro.showToast({
      //   title: newFavoriteState ? '已收藏' : '已取消收藏',
      //   icon: 'success',
      //   duration: 1500,
      // });

      return true;
    } catch (error) {
      console.error('收藏操作失败:', error);

      // 显示错误提示
      Taro.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none',
        duration: 2000,
      });

      return false;
    } finally {
      setLoading(false);
    }
  }, [therapistId, isFavorite]);

  return {
    isFavorite,
    loading,
    toggleFavorite,
    setIsFavorite, // 暴露设置状态的方法，以便父组件可以更新
  };
}
