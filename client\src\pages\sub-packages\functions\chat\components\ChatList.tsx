import DotLoading from '@components/common/loading-dots';
import { ChatMessage as ChatMessageType } from '@model/chat.interface';
import { Text, View } from '@tarojs/components';
import ChatMessage from './ChatMessage';

interface ChatListProps {
  messages: ChatMessageType[];
  loading: boolean;
  receiverAvatar: string;
  onResendMessage: (messageId: string) => void;
  onMarkAsRead?: (messageId: string) => void; // 标记消息为已读的回调
}

/**
 * 聊天列表组件
 * 用于显示消息列表，支持加载更多和滚动到底部
 */
export default function ChatList({
  messages,
  loading,
  receiverAvatar,
  onResendMessage,
  onMarkAsRead,
}: ChatListProps) {
  return (
    <View className='p-4 pb-40 flex-1'>
      {messages.length === 0 && (
        <View className='flex items-center justify-center h-[400px]'>
          <Text className='text-secondary'>暂无消息</Text>
        </View>
      )}

      {/* 消息列表 */}
      <View className='flex-1 flex flex-col '>
        {messages.map((message) => (
          <ChatMessage
            key={message.id}
            message={message}
            receiverAvatar={receiverAvatar}
            onResend={onResendMessage}
            onRead={onMarkAsRead}
          />
        ))}
      </View>
      {/* 加载更多 */}
      {messages.length > 0 && (
        <View className='flex justify-center py-2.5 mb-4'>
          {loading ? (
            <DotLoading />
          ) : (
            <Text className='text-xs text-secondary'>上拉刷新</Text>
          )}
        </View>
      )}
    </View>
  );
}
