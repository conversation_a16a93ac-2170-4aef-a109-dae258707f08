import { Icon } from '@antmjs/vantui';
import { AVATAR_DEFAULT } from '@constants/assets';
import { user_public } from '@model/user.interface';
import { Image, Text, View } from '@tarojs/components';

interface UserSimpleCardProps {
  user?: user_public;
  className?: string;
}

export default function UserSimpleCard({
  user,
  className = '',
}: UserSimpleCardProps) {
  if (!user) return null;

  return (
    <View
      className={`bg-white rounded-xl p-4 flex flex-row items-center ${className}`}
    >
      <Image
        src={user.avatar ?? AVATAR_DEFAULT}
        className='w-12 h-12 rounded-full mr-2'
      />
      <Text className='text-base font-bold block'>{user.userName}</Text>
      <View className='flex-1 flex flex-row justify-end'>
        <Icon name='more-o' />
      </View>
    </View>
  );
}
