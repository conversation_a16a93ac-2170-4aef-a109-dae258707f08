import {
  Check<PERSON>ist,
  Field,
  Picker,
  Radio,
  RadioGroup,
  Toast,
} from '@antmjs/vantui';
import { AvatarUploader } from '@components/common/AvatarEdit';
import { AVATAR_DEFAULT } from '@constants/assets';
import {
  CITY_OPTIONS,
  CONSULTATION_DIRECTIONS_MAP,
  SPECIALTY_OPTIONS,
  TITLE_OPTIONS,
} from '@constants/text';
import { my_summary } from '@model/profile.therapist';
import { uploadService } from '@services/upload.service';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';

interface BaseInfoEditerProps {
  summary: Partial<my_summary>;
  onFormChange: (section: string, field: string, value: any) => void;
}

export default function BaseInfoEditer({
  summary,
  onFormChange,
}: BaseInfoEditerProps) {
  const handleChooseAvatar = async (e) => {
    try {
      console.log('handleChooseAvatar', e);
      const { avatarUrl } = e.detail;
      if (!avatarUrl) return;

      // 显示加载提示
      Taro.showLoading({ title: '上传中...' });

      // 上传头像到云存储
      const uploadResult = await uploadService.uploadAvatar(avatarUrl);

      //  setAvatar(uploadResult);
      // handleSaveProfile(uploadResult, nickname);
      // 更新头像URL
      onFormChange('summary', 'avatar', uploadResult);
    } catch (error) {
      console.error('上传头像失败:', error);
      Toast.show('上传头像失败，请重试');
    } finally {
      Taro.hideLoading();
    }
  };
  const handleTitleChange = (e: { detail: Array<string | number> }) => {
    onFormChange('summary', 'titles', e.detail);
  };

  const handleSpecialtyChange = (e: { detail: Array<string | number> }) => {
    onFormChange('summary', 'specialties', e.detail);
  };

  const handleDirectionChange = (e: { detail: Array<string | number> }) => {
    onFormChange('summary', 'directions', e.detail);
  };
  return (
    <View>
      <AvatarUploader
        handleChooseAvatar={handleChooseAvatar}
        avatar={summary.avatar || AVATAR_DEFAULT}
      />
      <View className='mt-4 mx-4'>
        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>姓名</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <Field
            className='text-right flex-1'
            value={summary.name}
            placeholder='请输入真实姓名（中文）'
            onChange={(e) => onFormChange('summary', 'name', e.detail)}
            border={false}
            inputAlign='right'
          />
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>性别</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <RadioGroup
            direction='horizontal'
            value={summary.gender}
            onChange={(e) => onFormChange('summary', 'gender', e.detail)}
          >
            <Radio name='male'>男</Radio>
            <Radio name='female'>女</Radio>
          </RadioGroup>
        </View>

        {/* <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>手机号</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <Field
            className='text-right flex-1'
            type='number'
            inputAlign='right'
            border={false}
            placeholder='请输入手机号'
            value={sensitive.phone}
            onChange={(e) => {
              console.log(e.detail);
              onFormChange('sensitive', 'phone', e.detail);
            }}
          />
        </View> */}

        {/* <View className='mb-3 border rounded-lg px-4 py-2 flex justify-between items-center'>
            <Text className='mb-2'>
              邮箱 <Text className='text-red-500'>*</Text>
            </Text>
            <Field
              className='text-right flex-1'
              type='text'
              border={false}
              placeholder='请输入邮箱'
              value={sensitive.email}
              onChange={(e) => onFormChange('sensitive', 'email', e.detail)}
            />
          </View> */}

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>所在城市</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <Picker
              value={summary.location}
              onInput={(e) => {
                onFormChange('summary', 'location', e.detail);
              }}
              mode='content'
              columns={CITY_OPTIONS}
            />
          </View>
        </View>
      </View>

      <View className='mx-4 mt-6'>
        <Text className='text-base font-bold mb-3 block'>专业信息</Text>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>职称</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={TITLE_OPTIONS}
              value={summary.titles || []}
              onChange={handleTitleChange}
              placeholder='请选择职称'
              showArrowRight
              searchShow
              bodyHeight='30vh'
            />
          </View>
        </View>
        {Array.isArray(summary.titles) && summary.titles.length > 0 && (
          <View className='flex flex-wrap gap-2 mb-3 justify-end w-full'>
            {summary.titles.map((item) => (
              <View
                key={item}
                className='border border-primary text-sm rounded-full px-3 py-1 flex items-center'
              >
                <Text>
                  {TITLE_OPTIONS.find((opt) => opt.id === item)?.name || item}
                </Text>
                <Text
                  className='ml-2 '
                  onClick={() =>
                    onFormChange(
                      'summary',
                      'titles',
                      (summary.titles || []).filter((i) => i !== item)
                    )
                  }
                >
                  ×
                </Text>
              </View>
            ))}
          </View>
        )}

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 py-2 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>专长领域</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={SPECIALTY_OPTIONS}
              value={summary.specialties || []}
              onChange={handleSpecialtyChange}
              placeholder='请选择专长领域'
              showArrowRight
              searchShow
              bodyHeight='30vh'
            />
          </View>
        </View>
        {Array.isArray(summary.specialties) &&
          summary.specialties.length > 0 && (
            <View className='flex flex-wrap gap-2 mb-3 justify-end w-full'>
              {summary.specialties.map((item) => (
                <View
                  key={item}
                  className='border border-secondary text-sm rounded-full px-3 py-1 flex items-center'
                >
                  <Text>
                    {SPECIALTY_OPTIONS.find((opt) => opt.id === item)?.name ||
                      item}
                  </Text>
                  <Text
                    className='ml-2 '
                    onClick={() =>
                      onFormChange(
                        'summary',
                        'specialties',
                        (summary.specialties || []).filter((i) => i !== item)
                      )
                    }
                  >
                    ×
                  </Text>
                </View>
              ))}
            </View>
          )}

        <View className='h-12   mb-3 bg-white border border-border rounded-lg px-4  flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>咨询方向</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={CONSULTATION_DIRECTIONS_MAP.map((item) => ({
                id: item.label,
                name: item.label,
              }))}
              value={
                summary.directions?.map(
                  (item) => CONSULTATION_DIRECTIONS_MAP[item].label
                ) || []
              }
              onChange={handleDirectionChange}
              placeholder='请选择咨询方向'
              showArrowRight
              searchShow
              bodyHeight='30vh'
            />
          </View>
        </View>
        {Array.isArray(summary.directions) && summary.directions.length > 0 && (
          <View className='flex flex-wrap gap-2 mb-3 justify-end w-full'>
            {summary.directions.map((item) => (
              <View
                key={item}
                className='border border-tertiary text-sm rounded-full px-3 py-1 flex items-center'
              >
                <Text>
                  {CONSULTATION_DIRECTIONS_MAP.find((opt) => opt.key === item)
                    ?.label || item}
                </Text>
                <Text
                  className='ml-2 '
                  onClick={() =>
                    onFormChange(
                      'summary',
                      'directions',
                      (summary.directions || []).filter((i) => i !== item)
                    )
                  }
                >
                  ×
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </View>
  );
}
