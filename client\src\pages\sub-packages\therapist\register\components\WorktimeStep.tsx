import { Button } from '@antmjs/vantui';
import WorkTimeEditor from '@components/therapist/WorkTimeEditor';
import { work_time } from '@model/therapist.interface';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

interface WorktimeStepProps {
  workTime: work_time;
  onFormChange: (field: string, value: any) => void;
  onPrevStep: () => void;
  onSubmit: () => void;
}

export default function WorktimeStep({
  workTime,
  onFormChange,
  onPrevStep,
  onSubmit,
}: WorktimeStepProps) {
  // 表单验证
  const validateForm = (): boolean => {
    if (!workTime?.workDays || workTime.workDays.length === 0) {
      Taro.showToast({ title: '请选择工作日', icon: 'none' });
      return false;
    }

    if (!workTime.start || !workTime.end) {
      Taro.showToast({ title: '请设置工作时间', icon: 'none' });
      return false;
    }

    if (workTime.start >= workTime.end) {
      Taro.showToast({ title: '开始时间必须早于结束时间', icon: 'none' });
      return false;
    }

    return true;
  };

  return (
    <View>
      <WorkTimeEditor
        workTime={workTime}
        onFormChange={(newWorkTime) => {
          onFormChange('workTime', newWorkTime);
        }}
      />

      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='default'
          block
          round
          plain
          hairline
          className='flex-1 mr-2'
          onClick={onPrevStep}
        >
          上一步
        </Button>

        <Button
          type='primary'
          block
          round
          className='flex-1 ml-2'
          onClick={() => {
            if (validateForm()) {
              onSubmit();
            }
          }}
        >
          提交申请
        </Button>
      </View>
    </View>
  );
}
