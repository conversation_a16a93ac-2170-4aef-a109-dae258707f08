import {
  ChatMessage,
  ChatSession,
  GetMessagesParams,
  SendMessageParams,
} from '@model/chat.interface';
import { COLLECTIONS } from '@model/db.model';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

/**
 * 聊天服务实现类
 * 遵循架构原则：
 * 1. 用户自己的聊天数据直接从数据库读取
 * 2. 所有写操作通过云函数
 */
class ChatServiceImpl extends BaseService<ChatMessage> {
  private chatSessionCollection: string;

  constructor() {
    super(COLLECTIONS.CHAT_MESSAGE);
    this.chatSessionCollection = COLLECTIONS.CHAT_SESSION;
  }

  /**
   * 获取聊天消息
   * 用户自己的数据，直接读取
   */
  async getMessages(params: GetMessagesParams): Promise<ChatMessage[]> {
    try {
      const { sessionId, lastMessageTimestamp, limit = 20 } = params;

      const db = Taro.cloud.database();
      const _ = db.command;

      // 构建查询条件
      let query: any = {
        sessionId,
      };
      if (lastMessageTimestamp) {
        query.timestamp = _.lt(lastMessageTimestamp);
      }
      query = _.and([
        query,
        _.or([{ senderId: '{openid}' }, { receiverId: '{openid}' }]),
      ]);

      // 获取消息
      const options = {
        orderField: 'timestamp',
        orderDirection: 'desc',
        limit,
      };

      console.log('query', query);
      const messages = await this.directRead(query, options);

      console.log('ChatServiceImpl getMessages messages', messages);
      return messages.reverse(); // 按时间正序返回
    } catch (error) {
      console.error('ChatServiceImpl getMessages 获取聊天消息失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息
   * 写操作，通过云函数
   */
  async sendMessage(params: SendMessageParams): Promise<ChatMessage | null> {
    try {
      const result = await callCloudFunction('chat', 'sendMessage', params);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.message || '发送消息失败');
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  /**
   * 标记消息为已读
   * 写操作，通过云函数
   */
  async markMessageAsRead(
    sessionId: string,
    messageId: string
  ): Promise<boolean> {
    try {
      const result = await callCloudFunction('chat', 'markMessageAsRead', {
        sessionId,
        messageId,
      });
      return result.success;
    } catch (error) {
      console.error('标记消息已读失败:', error);
      throw error;
    }
  }

  /**
   * 获取会话详情
   * 用户自己的数据，直接读取
   */
  async getSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const db = Taro.cloud.database();
      const _ = db.command;
      const result = await db
        .collection(this.chatSessionCollection)
        .where(
          _.and([
            {
              id: sessionId,
            },
            _.or([{ AId: '{openid}' }, { BId: '{openid}' }]),
          ])
        )
        .get();

      if (result.data && result.data.length > 0) {
        return result.data[0] as ChatSession;
      }

      return null;
    } catch (error) {
      console.error('getSession:', error);
      throw error;
    }
  }

  /**
   * 获取会话列表
   * 用户自己的数据，直接读取
   */
  async getSessions(): Promise<ChatSession[]> {
    try {
      const db = Taro.cloud.database();
      const _ = db.command;
      const result = await db
        .collection(this.chatSessionCollection)
        .where(_.or([{ AId: '{openid}' }, { BId: '{openid}' }]))
        .orderBy('timestamp', 'desc')
        .get();

      return result.data as ChatSession[];
    } catch (error) {
      console.error('获取会话列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建会话
   * 写操作，通过云函数
   */
  async createSession(AId: string, BId: string): Promise<ChatSession | null> {
    try {
      const result = await callCloudFunction('chat', 'createSession', {
        AId,
        BId,
      });
      if (result.success && result.session) {
        return result.session;
      }
      throw new Error(result.message || '创建会话失败');
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 标记消息为已读
   * 写操作，通过云函数
   */
  // async markAsRead(sessionId: string): Promise<boolean> {
  //   try {
  //     const result = await callCloudFunction('chat', 'markAsRead', {
  //       sessionId,
  //     });
  //     return result.success;
  //   } catch (error) {
  //     console.error('标记已读失败:', error);
  //     throw error;
  //   }
  // }

  /**
   * 监听新消息
   * 用于实时同步的数据，使用watch监听
   */
  watchNewMessages(
    sessionId: string,
    callback: (messages: ChatMessage[]) => void
  ): () => void {
    try {
      return this.watchData(
        {
          sessionId,
          _openid: '{openid}',
        },
        (snapshot) => {
          if (snapshot.type === 'init') {
            callback(snapshot.docs);
          } else if (snapshot.type === 'update') {
            callback(snapshot.docs);
          }
        }
      );
    } catch (error) {
      console.error('监听新消息失败:', error);
      throw error;
    }
  }
}

export const chatService = new ChatServiceImpl();
