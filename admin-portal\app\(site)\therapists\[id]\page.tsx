import { QRCodeDialog } from "@/components/shared/qrcode-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Award, Bookmark, Clock, MessageCircle, Star } from "lucide-react";

// 模拟咨询师详细数据
const therapist = {
  id: "therapist-1",
  name: "王医生",
  avatar: "/placeholder.svg",
  title: "心理咨询师",
  specialties: ["抑郁症", "焦虑障碍", "情绪管理", "人际关系"],
  price: 299,
  rating: 4.9,
  reviewCount: 125,
  experience: "10年+",
  consultationCount: 1800,
  introduction:
    "国家二级心理咨询师，某某心理研究中心特聘心理咨询师。毕业于北京师范大学心理学专业，硕士学位。从事心理咨询工作10年以上，擅长抑郁症、焦虑障碍等情绪问题的疏导和治疗，以及人际关系、婚姻家庭问题的咨询。",
  education: [
    {
      school: "北京师范大学",
      degree: "心理学硕士",
      year: "2008-2011",
    },
    {
      school: "中国人民大学",
      degree: "应用心理学学士",
      year: "2004-2008",
    },
  ],
  certifications: [
    "国家二级心理咨询师",
    "中国心理学会会员",
    "认知行为治疗(CBT)专业培训",
  ],
  workingMethod:
    "以来访者为中心的人本主义疗法，结合认知行为疗法(CBT)和情绪聚焦疗法(EFT)，根据来访者的具体情况制定个性化的咨询方案。",
  services: [
    {
      name: "视频咨询",
      duration: "50分钟",
      price: 299,
    },
    {
      name: "线下咨询",
      duration: "50分钟",
      price: 399,
    },
  ],
  reviews: [
    {
      id: 1,
      user: "匿名用户",
      rating: 5,
      content:
        "王医生很专业，在她的帮助下，我的焦虑情绪得到了很大的改善，感谢她的耐心倾听和专业指导。",
      date: "2023-05-15",
    },
    {
      id: 2,
      user: "匿名用户",
      rating: 5,
      content: "咨询过程很舒适，王医生能准确把握我的问题核心，并给出有效建议。",
      date: "2023-04-20",
    },
    {
      id: 3,
      user: "匿名用户",
      rating: 4,
      content:
        "王医生很擅长倾听，并能给出专业的分析和建议。咨询后我对自己的问题有了更清晰的认识。",
      date: "2023-03-08",
    },
  ],
};

export default function TherapistDetailPage({
  params,
}: {
  params: { id: string };
}) {
  return (
    <div className="container py-8">
      {/* 咨询师基本信息 */}
      <div className="grid gap-6 md:grid-cols-3 mb-8">
        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="aspect-square relative bg-muted">
              <div className="absolute inset-0 flex items-center justify-center text-muted-foreground text-sm">
                咨询师头像
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h1 className="text-xl font-bold">{therapist.name}</h1>
                <span className="text-sm text-muted-foreground">
                  {therapist.title}
                </span>
              </div>
              <div className="flex items-center gap-2 mb-4">
                <Star className="h-4 w-4 fill-primary text-primary" />
                <span className="text-sm font-medium">{therapist.rating}</span>
                <span className="text-xs text-muted-foreground">
                  ({therapist.reviewCount}评价)
                </span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                {therapist.specialties.map((specialty) => (
                  <Badge key={specialty} variant="secondary">
                    {specialty}
                  </Badge>
                ))}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                <Award className="h-4 w-4" />
                <span>咨询经验：{therapist.experience}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                <MessageCircle className="h-4 w-4" />
                <span>已咨询：{therapist.consultationCount}次</span>
              </div>
              <div className="space-y-2 mt-6">
                <QRCodeDialog title="扫码预约咨询">
                  <Button className="w-full">立即预约</Button>
                </QRCodeDialog>
                <Button variant="outline" className="w-full gap-2">
                  <Bookmark className="h-4 w-4" />
                  收藏
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 咨询师详情信息 */}
        <div className="md:col-span-2">
          <Card>
            <Tabs defaultValue="intro">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="intro">个人介绍</TabsTrigger>
                <TabsTrigger value="service">咨询服务</TabsTrigger>
                <TabsTrigger value="reviews">用户评价</TabsTrigger>
                <TabsTrigger value="schedule">可预约时段</TabsTrigger>
              </TabsList>
              <TabsContent value="intro" className="p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">个人简介</h3>
                    <p className="text-sm text-muted-foreground">
                      {therapist.introduction}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">教育背景</h3>
                    <ul className="text-sm space-y-1">
                      {therapist.education.map((edu, index) => (
                        <li key={index} className="flex justify-between">
                          <span>
                            {edu.school} - {edu.degree}
                          </span>
                          <span className="text-muted-foreground">
                            {edu.year}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">资质证书</h3>
                    <ul className="text-sm list-disc pl-5">
                      {therapist.certifications.map((cert, index) => (
                        <li key={index} className="text-muted-foreground">
                          {cert}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">咨询方法</h3>
                    <p className="text-sm text-muted-foreground">
                      {therapist.workingMethod}
                    </p>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="service" className="p-6">
                <div className="space-y-4">
                  <h3 className="font-semibold mb-4">服务项目</h3>
                  {therapist.services.map((service, index) => (
                    <Card key={index} className="mb-4">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{service.name}</h4>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              <span>{service.duration}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-semibold">
                              ¥{service.price}
                            </div>
                            <QRCodeDialog title={`扫码预约${service.name}`}>
                              <Button size="sm">立即预约</Button>
                            </QRCodeDialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="reviews" className="p-6">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="text-4xl font-bold text-primary">
                      {therapist.rating}
                    </div>
                    <div>
                      <div className="flex items-center">
                        {Array(5)
                          .fill(0)
                          .map((_, i) => (
                            <Star
                              key={i}
                              className={`h-5 w-5 ${
                                i < Math.round(therapist.rating)
                                  ? "fill-primary text-primary"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {therapist.reviewCount}条评价
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    {therapist.reviews.map((review) => (
                      <div key={review.id} className="border-b pb-4">
                        <div className="flex justify-between items-center mb-2">
                          <div className="font-medium">{review.user}</div>
                          <div className="text-sm text-muted-foreground">
                            {review.date}
                          </div>
                        </div>
                        <div className="flex mb-2">
                          {Array(5)
                            .fill(0)
                            .map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating
                                    ? "fill-primary text-primary"
                                    : "text-gray-300"
                                }`}
                              />
                            ))}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {review.content}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="schedule" className="p-6">
                <div className="text-center p-8">
                  <p className="text-muted-foreground">
                    请在小程序中查看咨询师的可预约时段并进行预约
                  </p>
                  <div className="mt-4">
                    <QRCodeDialog title="扫码查看可预约时段">
                      <Button>打开小程序查看</Button>
                    </QRCodeDialog>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  );
}
