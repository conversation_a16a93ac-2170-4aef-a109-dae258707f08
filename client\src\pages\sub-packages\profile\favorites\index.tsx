import { Empty, Loading, Tab, Tabs } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useState } from 'react';

interface FavoriteItem {
  id: string;
  title: string;
  type: 'audio' | 'video' | 'article';
  coverImage: string;
  duration?: string;
  author: string;
  date: string;
}

// 模拟数据
const mockFavorites: FavoriteItem[] = [
  {
    id: '1',
    title: '焦虑缓解呼吸法',
    type: 'audio',
    coverImage: 'https://img.yzcdn.cn/vant/cat.jpeg',
    duration: '10:25',
    author: '张医生',
    date: '2023-11-15',
  },
  {
    id: '2',
    title: '正念冥想指导视频',
    type: 'video',
    coverImage: 'https://img.yzcdn.cn/vant/cat.jpeg',
    duration: '15:40',
    author: '李教授',
    date: '2023-11-10',
  },
  {
    id: '3',
    title: '认知行为疗法基础知识',
    type: 'article',
    coverImage: 'https://img.yzcdn.cn/vant/cat.jpeg',
    author: '王心理',
    date: '2023-11-05',
  },
];

const FavoritesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [favorites, setFavorites] = useState<FavoriteItem[]>(mockFavorites);

  // 模拟加载数据
  const fetchFavorites = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 800);
  };

  const handleTabChange = (event: { detail: { index: number } }) => {
    setActiveTab(event.detail.index);
    fetchFavorites();
  };

  const navigateToDetail = (item: FavoriteItem) => {
    let url = '';
    switch (item.type) {
      case 'audio':
        url = `/pages/sub-packages/audio/player/index?id=${item.id}`;
        break;
      case 'video':
        url = `/pages/sub-packages/video/player/index?id=${item.id}`;
        break;
      case 'article':
        url = `/pages/sub-packages/article/detail/index?id=${item.id}`;
        break;
    }

    if (url) {
      Taro.navigateTo({ url });
    }
  };

  // 根据当前标签筛选收藏内容
  const getFilteredFavorites = () => {
    if (activeTab === 0) return favorites;

    const typeMap = ['', 'audio', 'video', 'article'];
    return favorites.filter((item) => item.type === typeMap[activeTab]);
  };

  const filteredFavorites = getFilteredFavorites();

  // 渲染收藏项目
  const renderFavoriteItem = (item: FavoriteItem) => {
    const isMedia = item.type === 'audio' || item.type === 'video';

    return (
      <View
        key={item.id}
        className='bg-white rounded-lg mb-4 overflow-hidden shadow-sm'
        onClick={() => navigateToDetail(item)}
      >
        <View className='relative'>
          <Image
            src={item.coverImage}
            className='w-full h-48 object-cover'
            mode='aspectFill'
          />
          {isMedia && (
            <View className='absolute bottom-2 right-2 bg-black bg-opacity-60 px-2 py-1 rounded-md'>
              <Text className='text-white text-xs'>{item.duration}</Text>
            </View>
          )}
          <View className='absolute top-2 right-2 bg-primary bg-opacity-90 px-2 py-1 rounded-md'>
            <Text className='text-white text-xs capitalize'>{item.type}</Text>
          </View>
        </View>
        <View className='p-3'>
          <Text className='text-lg font-medium mb-1'>{item.title}</Text>
          <View className='flex justify-between items-center mt-2'>
            <Text className='text-sm text-secondary'>{item.author}</Text>
            <Text className='text-xs text-disabled'>{item.date}</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{
        title: '正念收藏夹',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen bg-gray-50'>
        <Tabs
          active={activeTab}
          onChange={handleTabChange}
          sticky
          swipeable
          className='px-4 pt-2 bg-transparent'
        >
          <Tab title='全部' />
          <Tab title='音频' />
          <Tab title='视频' />
          <Tab title='文章' />
        </Tabs>

        <ScrollView
          scrollY
          enhanced
          showScrollbar={false}
          className='p-4'
          style={{ height: 'calc(100vh - 44px)' }}
        >
          {loading ? (
            <View className='flex justify-center items-center p-8'>
              <Loading type='spinner' color='#1989fa' />
            </View>
          ) : filteredFavorites.length > 0 ? (
            filteredFavorites.map((item) => renderFavoriteItem(item))
          ) : (
            <Empty description='暂无收藏内容' />
          )}
        </ScrollView>
      </View>
    </PageLol>
  );
};

export default FavoritesPage;
