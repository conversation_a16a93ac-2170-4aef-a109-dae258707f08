import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import StatisticCard from '@components/StatisticCard';
import { distributionActions } from '@core/actions/distribution.action';
import { incomeActions } from '@core/actions/income.action';
import { useDistributionStore } from '@stores/distribution.store';
import { useIncomeStore } from '@stores/income.store';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { formatTime5 } from '@utils/time';
import { useEffect } from 'react';
import SharePromotion from './SharePromotion';
import ShareTabs from './ShareTabs';

/**
 * 分享中心页面
 */
export default function DistributionPage() {
  const distributionData = useDistributionStore.use.overview();
  const walletData = useIncomeStore.use.wallet();

  useEffect(() => {
    console.log('distributionData', distributionData);
    const loadData = async () => {
      await Promise.all([
        distributionActions.fetchDistributionOverview(),
        incomeActions.loadMyWallet(),
      ]);
    };
    loadData();
  }, []);

  // 分享概览数据
  // const distributionData = {
  //   totalIncome: 458.8, // 累计收益
  //   totalOrder: 100, // 累计订单数
  //   availableBalance: 200, // 可提现金额
  //   pendingIncome: 58.8, // 待结算收益
  //   invitedUsers: 5, // 邀请用户数
  //   currentMonthIncome: 58.8, // 本月收益
  //   // 假设这是用户专属分享二维码
  //   qrCodeUrl: 'https://img.yzcdn.cn/vant/qrcode.png',
  //   createdAt: 1715769600,
  // };

  // 申请提现
  const handleWithdraw = () => {
    if (walletData?.balance && walletData.balance > 0) {
      appRouter.withdraw();
    } else {
      Taro.showToast({
        title: '暂无可提现金额',
        icon: 'none',
      });
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '分享中心',
        showBackButton: true,
        showSearch: false,
      }}
    >
      <View className='min-h-screen pb-16'>
        {/* 分享数据概览卡片 */}
        <StatisticCard
          className='mb-4 bg-gradient-to-r from-[#E0EAFC] to-[#CFDEF3]'
          title='分享概览'
          renderRight={
            <Text className='text-xs text-secondary'>
              自{formatTime5(distributionData?.createdAt || 0)}
            </Text>
          }
          values={[
            {
              title: '累计收益',
              value: distributionData?.totalIncome || 0,
            },
            {
              title: '累计订单数',
              value: distributionData?.totalOrderCount || 0,
            },
            {
              title: '邀请用户数',
              value: distributionData?.inviteCount || 0,
            },
          ]}
        >
          <View className='flex flex-row w-full justify-between items-center mt-2'>
            <View className='flex flex-row items-baseline flex-1'>
              <Text className='text-md text-secondary mb-1 mr-2'>
                可提现金额
              </Text>
              <Text className='text-lg font-bold text-success'>
                {walletData?.balance || 0}
              </Text>
            </View>
            <Button type='primary' size='small' round onClick={handleWithdraw}>
              申请提现
            </Button>
          </View>
        </StatisticCard>

        {/* 分享推广 */}
        <SharePromotion qrCodeUrl={distributionData?.qrCodeUrl || ''} />

        {/* 分享数据Tab */}
        <ShareTabs />
      </View>
    </PageLol>
  );
}
