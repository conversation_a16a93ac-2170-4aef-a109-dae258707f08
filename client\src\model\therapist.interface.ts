import { ConsultationDirection } from './common.interface';
import { Promo, Service, ServiceType } from './service.interface';

/**
 * 咨询师核心信息数据表 (列表页/首页显示)
 * 包含聚合字段，由云函数计算维护，前端直接从数据库读取
 */
export interface therapist_summary {
  /** 咨询师ID 同时也是用户ID */
  id: string;
  /** 姓名 */
  name: string;
  /** 性别 */
  gender: 'male' | 'female';
  /** 年龄 */
  age: number;

  /** 教育背景 */
  /** 头像URL */
  avatar: string;
  /** 头衔/职称/资质 */
  titles: string[];
  /** 地理位置 */
  location: string;
  /** 收藏数 */
  favoriteCount: number;
  /**聚合字段 提供的咨询服务枚举值列表 */
  service: ServiceType[];
  /** 聚合字段 优惠信息提示 */
  promo: string | null;
  /** 聚合字段 服务价格 */
  price: number;
  /** 聚合字段 可预约状态描述 */
  available: string;
  /** 聚合字段 今天可约状态 */
  todayAvailable: boolean;

  /** 标签 */
  tags: TherapistTag[];
  /** 心理咨询方向 */
  directions: ConsultationDirection[];
  /** 擅长领域 */
  specialties: string[];

  /** 评价分数 */
  rating?: number;
  /** 评价人数 */
  ratingCount?: number;
  /** 累计咨询时长 */
  consultTime?: number;
  /** 累计服务人数 */
  serviceCount?: number;

  /** 创建时间 */
  createdAt?: number;
  /** 更新时间 */
  updatedAt?: number;
  /** 状态: active-可用, inactive-不可用, vacation-休假中 */
  status?: 'active' | 'inactive' | 'vacation';
}

/**
 * 咨询师扩展信息表 (包含个人详情页需要的所有信息，不包含summary)
 * 不包含敏感信息
 */
export interface therapist_extend {
  /** 咨询师ID */
  id: string;
  /** 头像URL */
  photos: string[];
  /** 个人简介 */
  introduction: string;
  /** 教育背景 */
  education: TherapistEducation;
  /** 认证信息 */
  certifications: TherapistCertification[];
  /** 培训经历 */
  trainings: TherapistTraining[];

  /** 社交媒体账号 */
  socialMedia?: {
    /** 平台名称 */
    platform: string;
    /** 账号链接 */
    link: string;
  }[];
}

export interface work_time {
  /** 开始时间 (小时, 0-23) */
  start: number;
  /** 结束时间 (小时, 0-23) */
  end: number;
  /** 工作日 (周一到周日, 1-7) */
  workDays?: number[];
  /** 例外日 (每月不工作的日期) */
  exceptionDays?: number[];
}

/**
 * 咨询师服务信息表
 */
export interface therapist_service {
  /** 咨询师ID */
  id: string;
  /** 服务列表 */
  services: Service[];
  /** 总体优惠 */
  promo?: Promo;
  /** 工作时间 */
  workTime?: work_time;
  // /** 服务约定 */
  // serviceAgreement?: {
  //   /** 取消政策 */
  //   cancellationPolicy: string;
  //   /** 退款政策 */
  //   refundPolicy: string;
  // };
}

/**
 * 咨询师排期信息,以天为单位. 这是数据库表的结构
 */
export interface schedule_item {
  /** 咨询师ID */
  id: string;
  /** 排期日期 (date in milliseconds 精确到天) */
  date: number;
  /** 是否可用 */
  available: boolean;
  /** 排期信息,以小时为单位 */
  slots: number[];
}

/**咨询师排期表 */
export interface therapist_schedule {
  /** 咨询师ID */
  id: string;
  /** 排期信息,以天为单位 */
  schedule: schedule_item[];
}

export enum TherapistCertificationType {
  // 身份认证
  IDENTITY_CERTIFICATION = 0,
  // 执业资格
  PRACTICE_CERTIFICATION = 1,
  // 照片认证
  PHOTO_CERTIFICATION = 2,
}

/**
 * 咨询师认证信息
 */
export interface TherapistCertification {
  /** 认证类型 */
  type: TherapistCertificationType;
  /** 认证标题 */
  title: string;
  /** 获取时间 YYYY-MM-DD */
  when: string;
  /** 认证机构 */
  organization?: string;
  /** 认证描述 */
  description?: string;
  /** 认证图片URL */
  imageUrl?: string;
}

/**
 * 咨询师教育背景
 */
export interface TherapistEducation {
  /** 最高学历 */
  education: string;
  /** 学校列表 */
  schools: {
    /** 学校名称 */
    name: string;
    /** 入学年份 */
    start: string;
    /** 毕业年份 */
    end: string;
    /** 专业 */
    major?: string;
    /** 学位 */
    degree?: string;
  }[];
}

/**
 * 咨询师培训经历
 */
export interface TherapistTraining {
  /** 培训名称 */
  name: string;
  /** 培训机构 */
  organization: string;
  /** 培训时间 */
  start: string;
  /** 培训结束时间 */
  end: string;
  /** 培训时长 */
  duration: string;
  /** 培训项目 */
  items: {
    /** 项目名称 */
    title: string;
    /** 项目时间 */
    start: string;
    /** 项目结束时间 */
    end: string;
    /** 项目描述 */
    description?: string;
  }[];
}

/**
 * 咨询师排期信息,以天为单位
 */
export interface TherapistScheduleItem {
  /** 日期 (date in milliseconds 精确到天) */
  date: number;
  /** 不可预约时间段(小时) 0-23 即已被预约的时间段，比如[10, 11, 12]表示10:00-11:00, 11:00-12:00, 12:00-13:00已被预约*/
  slots: number[];
}

/**
 * 咨询师标签
 */
export interface TherapistTag {
  /** 标签名称 */
  title: string;
  /** 标签值 */
  value: string;
  /** 单位 */
  unit?: string;
}

/**
 * 咨询师筛选参数
 */
export interface TherapistFilters {
  /** 搜索关键词 */
  keyword?: string;

  /** 可用的心理咨询方向 */
  directions?: ConsultationDirection[];
  /** 可用的服务类型 */
  serviceTypes?: ServiceType[];
  /** 可用的地区 */
  location?: string;

  /** 今天可约 */
  todayAvailable?: boolean;
}
