import { callCloudFunction } from '@services/cloud';
import { adminOrderService } from '@services/order.admin';
import { useLoadingStore } from '@stores/loading.store';
import { useAdminOrderStoreSelector } from '@stores/order.store';
import Taro from '@tarojs/taro';

/**
 * 管理员订单操作
 */
export const orderAdminActions = {
  /**
   * 获取所有退款审核中的订单
   */
  fetchRefundAuditingOrders: async () => {
    try {
      const result = await callCloudFunction(
        'order',
        'fetchRefundAuditingOrders'
      );

      console.log('获取退款审核订单result:', result);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('获取退款审核订单失败:', error);
      throw error;
    }
  },

  /**
   * 通过退款申请
   */
  approveRefund: async (orderId: string) => {
    try {
      const result = await callCloudFunction('order', 'refundAuditPass', {
        _id: orderId,
      });
      console.log('通过退款审核result:', result);
      if (result.success && !result.warning) {
        Taro.showToast({
          title: result.message,
          icon: 'success',
          duration: 2000,
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('退款审核通过失败:', error);
      throw error;
    }
  },

  /**
   * 拒绝退款申请
   */
  rejectRefund: async (orderId: string) => {
    try {
      const result = await callCloudFunction('order', 'refundAuditNotPass', {
        _id: orderId,
      });
      console.log('拒绝退款审核result:', result);
      if (result.success) {
        return result;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('退款审核拒绝失败:', error);
      throw error;
    }
  },

  /**
   * 导航到拒绝退款页面
   */
  navigateToRejectRefund: (orderId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/reject-refund/index?orderId=${orderId}`,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },

  /**
   * 获取订单操作日志
   */
  fetchOrderActions: async (orderId: string) => {
    const store = useAdminOrderStoreSelector.getState();
    const actionsInStore = store.getOrderActions(orderId);
    if (actionsInStore.length > 0) {
      return actionsInStore;
    }
    const actions = await adminOrderService.getOrderActions(orderId);
    store.setOrderActions(actions);
    return actions;
  },
};
