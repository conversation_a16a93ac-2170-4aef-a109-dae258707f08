import { Text } from '@tarojs/components';
import { useEffect, useState } from 'react';

import { countdown } from '@utils/time';

export const CountdownDisplay = ({
  date,
  hour,
}: {
  date?: number;
  hour?: number;
}) => {
  const [countdownText, setCountdownText] = useState('计算中...');

  useEffect(() => {
    try {
      if (!date || !hour) {
        setCountdownText('时间计算错误');
        return;
      }
      const text = countdown(date, hour);
      setCountdownText(text);
    } catch (error) {
      console.error('倒计时计算错误:', error);
      setCountdownText('时间计算错误');
    }
  }, [date, hour]);

  return <Text>{countdownText}</Text>;
};
