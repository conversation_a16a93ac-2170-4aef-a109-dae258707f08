/**
 * 统一响应格式模块
 */

const SUCCESS_CODE = 200;
const ERROR_CODES = {
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
};

/**
 * 成功响应
 * @param {any} data 响应数据
 * @param {string} message 成功消息
 */
function success(result = {}) {
  return {
    success: true,
    code: SUCCESS_CODE,
    message: result.message || "操作成功",
    ...result,
  };
}

/**
 * 错误响应
 * @param {string} message 错误消息
 * @param {number} code 错误代码
 * @param {any} error 错误详情
 */
function error(
  message = "操作失败",
  code = ERROR_CODES.INTERNAL_ERROR,
  error = null
) {
  return {
    success: false,
    code,
    message,
    error: process.env.NODE_ENV === "production" ? null : error,
  };
}

module.exports = {
  success,
  error,
  CODES: ERROR_CODES,
};
