.page-meta {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  // overflow: hidden;

  // 为导航栏添加样式
  .navigation-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: var(--page-background-color); // 添加背景色
  }
  // 使用系统导航栏时的样式
  &--system-nav {
    // 系统导航栏的情况下不需要额外处理高度，因为系统会自动处理
    // 但需要确保内容区域不被系统导航栏遮挡
    padding-top: 0;
  }

  &__content {
    //垂直布局
    display: flex;
    flex-direction: column;
    //占满剩余空间
    flex: 1;
    // 宽度100%
    width: 100%;
    // 高度100%
    // height: 100%;
    z-index: 1;
    box-sizing: border-box;
    position: relative;
    overflow: auto;
    -webkit-overflow-scrolling: touch; // 增强iOS的滚动体验
  }

  &__loading,
  &__error,
  &__empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-bottom: 100px;
  }

  &__retry {
    margin-top: 24px;
    padding: 8px 24px;
    border-radius: 20px;
    background-color: var(--color-primary, #2775b6);
    color: #fff;
    font-size: 28px;
  }
}
