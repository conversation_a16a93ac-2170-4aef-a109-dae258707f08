import { create } from 'zustand';
import createSelectors from './libs/selector';

export enum VideoCallStatus {
  IDLE = 'idle',
  INCOMING = 'incoming',
  CONNECTING = 'connecting',
  REJECTED = 'rejected',
  CONNECTED = 'connected',
  // CANCELLED = 'cancelled',
  FAILED = 'failed',
  ENDED = 'ended',
  HANGUP = 'hangup',
}

interface VideoCallState {
  // 状态
  status: VideoCallStatus;
  remoteUserID: string | null;
  isMuted: boolean;
  isCameraOff: boolean;
  error: string | null;
  pusher: any;
  playerList: any[];

  roomID: number | null;
  orderId: string | null;

  therapistAvatar: string | null;
  therapistName: string | null;

  // actions
  updateCallStatus: (status: VideoCallState['status']) => void;
  setRemoteUserID: (userID: string) => void;
  updateMicrophoneState: (isMuted: boolean) => void;
  updateCameraState: (isOff: boolean) => void;
  setError: (error: string | null) => void;
  setPusher: (pusher: any) => void;
  setPlayerList: (playerList: any[]) => void;
  setRoomID: (roomID: number) => void;
  setOrderId: (orderId: string) => void;
  setTherapistAvatar: (avatar: string) => void;
  setTherapistName: (name: string) => void;
  reset: () => void;
}

// 初始状态
const initialState = {
  status: VideoCallStatus.INCOMING,
  remoteUserID: 'therapist-1',
  isMuted: false,
  isCameraOff: false,
  error: null,
  pusher: null,
  playerList: [],
  inviteID: null,
  roomID: null,
  orderId: null,
  therapistAvatar: null,
  therapistName: null,
};

const videoCallStore = create<VideoCallState>((set) => ({
  // 初始状态
  ...initialState,

  // 更新通话状态
  updateCallStatus: (status) => set({ status }),

  // 更新远端用户ID
  setRemoteUserID: (userID) => set({ remoteUserID: userID }),

  // 更新麦克风状态
  updateMicrophoneState: (isMuted) => set({ isMuted }),

  // 更新摄像头状态
  updateCameraState: (isOff) => set({ isCameraOff: isOff }),

  // 设置错误信息
  setError: (error) => set({ error }),

  // 设置pusher
  setPusher: (pusher) => set({ pusher }),

  // 设置playerList
  setPlayerList: (playerList) => set({ playerList }),

  // 设置roomID
  setRoomID: (roomID) => set({ roomID }),

  // 设置orderId
  setOrderId: (orderId) => set({ orderId }),

  setTherapistAvatar: (avatar) => set({ therapistAvatar: avatar }),
  setTherapistName: (name) => set({ therapistName: name }),

  // 重置状态
  reset: () => set(initialState),
}));

export const useVideoCallStore = createSelectors(videoCallStore);

// 提供重置函数
export function useVideoCallReset() {
  videoCallStore.setState(initialState);
}
