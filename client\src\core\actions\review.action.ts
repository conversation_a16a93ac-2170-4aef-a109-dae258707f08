import { Order_review } from '@model/order.interface';
import { reviewService } from '@services/review.service';
import { useLoadingStore } from '@stores/loading.store';

export const reviewActions = {
  fetchOrderReviews: async (orderId: string): Promise<Order_review[]> => {
    try {
      const reviews = await reviewService.fetchOrderReviews(orderId);
      return reviews;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  reviewOrder: async (
    orderId: string,
    score: number,
    comment: string,
    tags: string[]
  ): Promise<void> => {
    try {
      const result = await reviewService.reviewOrder(
        orderId,
        score,
        comment,
        tags
      );
      console.log('reviewOrder result', result);
      if (!result.success) {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },
};
