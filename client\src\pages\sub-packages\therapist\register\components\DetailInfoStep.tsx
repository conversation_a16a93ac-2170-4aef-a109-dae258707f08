import { Button } from '@antmjs/vantui';
import DetailInfoEditer from '@components/therapist/DetailInfoEditer';
import { my_extend } from '@model/profile.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

interface DetailInfoStepProps {
  detailInfo: Partial<my_extend>;
  onFormChange: (field: string, value: any) => void;
  onPrevStep: () => void;
  onNextStep: () => void;
}

export default function DetailInfoStep({
  detailInfo,
  onFormChange,
  onPrevStep,
  onNextStep,
}: DetailInfoStepProps) {
  // 表单验证
  const validateForm = (): boolean => {
    if (!detailInfo.introduction) {
      Taro.showToast({ title: '请输入个人简介', icon: 'none' });
      return false;
    }

    if (detailInfo.photos?.length === 0) {
      Taro.showToast({ title: '请上传至少一张照片', icon: 'none' });
      return false;
    }

    return true;
  };

  // 处理下一步按钮点击
  const handleNextStep = () => {
    if (validateForm()) {
      onNextStep();
    }
  };

  return (
    <View>
      <DetailInfoEditer detailInfo={detailInfo} onFormChange={onFormChange} />

      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='default'
          block
          round
          plain
          hairline
          className='flex-1 mr-2'
          onClick={onPrevStep}
        >
          上一步
        </Button>

        <Button
          type='primary'
          block
          round
          className='flex-1 ml-2'
          onClick={handleNextStep}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
