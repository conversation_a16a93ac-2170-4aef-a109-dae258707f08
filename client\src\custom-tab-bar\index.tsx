import { TAB_TEXTS, THERAPIST_TAB_TEXTS } from '@constants/text';
import Taro, { useDidHide, useDidShow } from '@tarojs/taro';

// 导入自定义的Tabbar和TabbarItem组件
import { Tabbar, TabbarItem } from '@components/common/tabbar';
import { USER_ROLE } from '@model/user.interface';
import { useGlobalStore } from '@stores/global.store';
import { useEffect } from 'react';
import './index.less';

// 定义TabItem类型
interface TabItem {
  pagePath: string;
  iconPath: string;
  selectedIconPath: string;
  text: string;
}

// 用户端的标签页
const userTabList: TabItem[] = [
  {
    pagePath: '/pages/homepage/index',
    iconPath: '../assets/tabbar/home-2_linear.png',
    selectedIconPath: '../assets/tabbar/home-2_bold.png',
    text: TAB_TEXTS.home,
  },
  {
    pagePath: '/pages/measure/index',
    iconPath: '../assets/tabbar/trend-up_linear.png',
    selectedIconPath: '../assets/tabbar/trend-up_bold.png',
    text: TAB_TEXTS.measure,
  },
  {
    pagePath: '/pages/consultation/index',
    iconPath: '../assets/tabbar/discover_linear.png',
    selectedIconPath: '../assets/tabbar/discover_bold.png',
    text: TAB_TEXTS.consultation,
  },
  {
    pagePath: '/pages/treatment/index',
    iconPath: '../assets/tabbar/audio-square_linear.png',
    selectedIconPath: '../assets/tabbar/audio-square_bold.png',
    text: TAB_TEXTS.treatment,
  },
  {
    pagePath: '/pages/profile/index/user',
    iconPath: '../assets/tabbar/profile_linear.png',
    selectedIconPath: '../assets/tabbar/profile_bold.png',
    text: TAB_TEXTS.profile,
  },
];

// 咨询师端的标签页
const therapistTabList: TabItem[] = [
  {
    pagePath: '/pages/homepage/index',
    iconPath: '../assets/tabbar/home-2_linear.png',
    selectedIconPath: '../assets/tabbar/home-2_bold.png',
    text: THERAPIST_TAB_TEXTS.workspace,
  },
  {
    pagePath: '/pages/therapist-orders/index',
    iconPath: '../assets/tabbar/calendar_linear.png',
    selectedIconPath: '../assets/tabbar/calendar_bold.png',
    text: THERAPIST_TAB_TEXTS.orders,
  },
  {
    pagePath: '/pages/consultation-therapist/index',
    iconPath: '../assets/tabbar/message_linear.png',
    selectedIconPath: '../assets/tabbar/message_bold.png',
    text: THERAPIST_TAB_TEXTS.consultation,
  },
  // {
  //   pagePath: '/pages/treatment/index',
  //   iconPath: '../assets/tabbar/users_linear.png',
  //   selectedIconPath: '../assets/tabbar/users_bold.png',
  //   text: THERAPIST_TAB_TEXTS.clients,
  // },
  {
    pagePath: '/pages/profile/index/therapist',
    iconPath: '../assets/tabbar/profile_linear.png',
    selectedIconPath: '../assets/tabbar/profile_bold.png',
    text: THERAPIST_TAB_TEXTS.profile,
  },
];

// interface TabBarState {
//   selected: number;
//   tabList: TabItem[];
// }

// 使用React.Component实现自定义TabBar
export default function CustomTabBar() {
  // const [selected, setSelected] = useState(0);
  const currentRole = useGlobalStore.use.currentRole();
  const selectedTab = useGlobalStore.use.selectedTab();
  const tabList =
    currentRole === USER_ROLE.THERAPIST ? therapistTabList : userTabList;

  // 只在组件挂载时或角色变化时更新选中状态，不主动调用导航
  useEffect(() => {
    console.log('CustomTabBar useEffect', currentRole);
    try {
      const pages = Taro.getCurrentPages();
      if (!pages || pages.length === 0) return;

      const currentPage = pages[pages.length - 1];
      const currentPath = `/${currentPage.route}`;

      console.log('当前页面路径:', currentPath);

      // 查找当前路径对应的索引
      const index = tabList.findIndex((item) => item.pagePath === currentPath);

      console.log('找到的索引:', index);
      if (index !== -1) {
        // 只更新状态，不调用switchTab
        useGlobalStore.getState().setSelectedTab(index);
      }
    } catch (error) {
      console.error('CustomTabBar useEffect error', error);
    }
  }, [currentRole, tabList]);

  // 优化switchTab函数，添加防抖逻辑
  let lastTapTime = 0;
  let lastTapUrl = '';
  const TAP_DELAY = 300; // 设置点击间隔为300ms

  function switchTab(index: number, url: string) {
    // 设置选中状态
    useGlobalStore.getState().setSelectedTab(index);

    // 获取当前时间
    const now = Date.now();

    // 如果是相同的URL并且间隔太短，则不执行导航
    if (url === lastTapUrl && now - lastTapTime < TAP_DELAY) {
      console.log('忽略快速重复点击', url);
      return;
    }

    // 更新最后点击时间和URL
    lastTapTime = now;
    lastTapUrl = url;

    // 执行导航
    try {
      Taro.switchTab({ url });
    } catch (error) {
      console.error('switchTab error', error);
    }
  }

  useDidShow(() => {
    console.log('CustomTabBar useDidShow', selectedTab);
  });

  useDidHide(() => {
    console.log('CustomTabBar useDidHide', selectedTab);
  });

  // componentWillUnmount() {
  //   // 清理订阅
  //   if (this.unsubscribe) {
  //     this.unsubscribe();
  //   }
  // }
  // private unsubscribe: (() => void) | null = null;
  // // Taro页面显示时的生命周期
  // componentDidShow() {
  //   this.updateTabList();
  //   this.updateSelectedFromCurrentPage();
  // }

  // 更新tabList
  // function updateTabList() {
  //   const currentRole = useGlobalStore.getState().currentRole;
  //   const tabList =
  //     currentRole === USER_ROLE.THERAPIST ? therapistTabList : userTabList;
  //   this.setState({ tabList });
  // }

  // // 根据当前页面路径更新选中状态
  // function updateSelectedFromCurrentPage() {
  //   try {
  //     const pages = Taro.getCurrentPages();
  //     if (!pages || pages.length === 0) return;

  //     const currentPage = pages[pages.length - 1];
  //     const currentPath = `/${currentPage.route}`;
  //     const { tabList } = this.state;

  //     console.log('当前页面路径:', currentPath);
  //     console.log('当前tabList:', tabList);

  //     // 查找当前路径对应的索引
  //     const index = tabList.findIndex((item) => item.pagePath === currentPath);

  //     console.log('找到的索引:', index);

  //     // 如果找到匹配的路径，则更新选中状态
  //     if (index !== -1) {
  //       console.log('设置选中状态为:', index);
  //       this.setState({ selected: index });
  //     }
  //   } catch (error) {
  //     console.error('更新选中状态出错:', error);
  //   }
  // }

  // // 实现setSelected方法，供页面调用
  // function setSelected(idx: number) {
  //   console.log('CustomTabBar.setSelected被调用，索引:', idx);
  //   this.setState({ selected: idx });
  // }

  function handleChange(event: { index: number }) {
    const { index } = event;
    console.log('🚀🚀🚀 CustomTabBar handleChange', index);
    switchTab(index, tabList[index].pagePath);
  }

  return (
    <Tabbar
      active={selectedTab}
      onChange={handleChange}
      className='shadow-[0_-1px_2px_rgba(0,0,0,0.05)] text-[#2775b6]'
      fixed
    >
      {tabList.map((item, index) => (
        <TabbarItem
          key={index}
          icon={item.iconPath}
          activeIcon={item.selectedIconPath}
          text={item.text}
        />
      ))}
    </Tabbar>
  );
}

CustomTabBar.options = {
  addGlobalClass: true,
};
