import { useRef } from 'react';

/**
 * 跟踪组件的渲染次数的自定义Hook
 * @param componentName 组件名称，用于日志输出
 * @param enableLog 是否启用日志输出，默认为true
 * @returns 当前渲染次数
 */
export const useRenderCount = (componentName: string, enableLog = true) => {
  const renderCount = useRef(0);

  // 安全地增加计数
  renderCount.current += 1;

  if (enableLog) {
    console.log(`${componentName} 安全渲染次数:`, renderCount.current);
  }

  return renderCount.current;
};

export default useRenderCount;
