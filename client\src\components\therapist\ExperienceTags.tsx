import { Text, View } from '@tarojs/components';

export interface ExperienceTag {
  title: string;
  value: string;
  unit?: string;
}

interface ExperienceTagsProps {
  tags: ExperienceTag[];
  maxCount?: number;
  className?: string;
}

const ExperienceTags: React.FC<ExperienceTagsProps> = ({
  tags,
  maxCount = 2,
  className = '',
}) => {
  if (!tags || tags.length === 0) return null;

  const displayTags = tags.slice(0, maxCount);

  return (
    <View className={`flex flex-row flex-wrap gap-2 w-full ${className}`}>
      {displayTags.map((tag, idx) => (
        <Text className='text-sm text-default font-bold' key={idx}>
          {tag.value}
          {tag.unit}
          {tag.title}
        </Text>
      ))}
    </View>
  );
};

export default ExperienceTags;
