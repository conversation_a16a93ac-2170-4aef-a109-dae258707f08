import {
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from "@/app/model/order.interface";
import { orderCloudFunctions } from "@/lib/cloud-functions";
import { useCallback, useEffect, useRef, useState } from "react";
import { OrderListParams, OrderListRequest, Pagination } from "../model/api";

/**
 * 订单列表管理Hook
 * 提供订单列表的获取、筛选和状态监听功能
 */
export function useOrderList(initialParams: { params?: OrderListParams } = {}) {
  console.log("useOrderList_ai", initialParams);
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order_summary[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [filters, setFilters] = useState<
    OrderListParams & { dateRange?: [number, number] }
  >({
    query: "",
    status: [],
    refundStatus: [],
    complaint: false,
    dateRange: undefined, // 添加日期范围过滤
  });

  const requestParamsRef = useRef({
    filters: initialParams.params,
  });
  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchOrders = useCallback(async (params: OrderListRequest) => {
    console.log("useOrderList fetchOrders", params);
    if (loading) return;
    setLoading(true);
    try {
      const res = await orderCloudFunctions.getOrders(params);
      setOrders(res.data as unknown as Order_summary[]);
      setPagination(res.pagination as unknown as Pagination);
    } catch (error) {
      console.error("获取订单失败:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新列表
  const refreshOrders = useCallback(async () => {
    console.log("useOrderList refreshOrders", requestParamsRef.current.filters);
    try {
      await fetchOrders({
        page: 1,
        pageSize: pagination?.pageSize || 100,
        params: requestParamsRef.current.filters,
      });
    } catch (error) {
      console.error("刷新订单失败:", error);
    }
  }, [fetchOrders, pagination?.pageSize]);

  // 加载更多
  const loadMoreOrders = useCallback(
    async (page: number = pagination?.page + 1) => {
      console.log(
        "useOrderList loadMoreOrders",
        requestParamsRef.current.filters,
        page
      );

      try {
        await fetchOrders({
          page: page,
          pageSize: pagination?.pageSize || 100,
          params: requestParamsRef.current.filters,
        });
      } catch (error) {
        console.error("加载更多订单失败:", error);
      }
    },
    [pagination?.page, pagination?.pageSize, fetchOrders]
  );

  // 搜索订单
  const onSearch = useCallback(
    async (searchWord: string) => {
      console.log("useOrderList onSearch", searchWord);
      const newFilters = {
        ...requestParamsRef.current.filters,
        query: searchWord,
      };

      setFilters(newFilters);

      try {
        await fetchOrders({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("搜索订单失败:", error);
      }
    },
    [fetchOrders, pagination?.pageSize]
  );

  // 筛选订单状态（更新支持退款状态）
  const onStatusChange = useCallback(
    async (
      status: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][] = [],
      complaint: boolean = false,
      refundStatus: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS][] = []
    ) => {
      console.log(
        "useOrderList onStatusChange",
        status,
        complaint,
        refundStatus
      );
      const newFilters = {
        ...requestParamsRef.current.filters,
        status,
        refundStatus,
        complaint,
      };

      setFilters(newFilters);

      try {
        await fetchOrders({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("筛选订单失败:", error);
      }
    },
    [fetchOrders, pagination?.pageSize]
  );

  // 设置日期范围
  const onDateRangeChange = useCallback(
    async (dateRange: [number, number] | undefined) => {
      console.log("useOrderList onDateRangeChange", dateRange);
      const newFilters = {
        ...requestParamsRef.current.filters,
        dateRange,
      };

      setFilters(newFilters);

      try {
        await fetchOrders({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按日期筛选订单失败:", error);
      }
    },
    [fetchOrders, pagination?.pageSize]
  );

  // 初始加载
  useEffect(() => {
    refreshOrders();
  }, [refreshOrders]); // 只在组件挂载时执行一次

  return {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    onStatusChange,
    onSearch,
    onDateRangeChange,
    filters,
  };
}
