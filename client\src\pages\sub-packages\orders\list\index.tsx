import { Tab, Tabs } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import OrderCard from '@components/order-card/OrderCard';
import { ORDER_TABS_USER } from '@constants/config';
import { useOrderList } from '@hooks/useOrderList';
import { ScrollView, View } from '@tarojs/components';
import {
  useDidHide,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
  useReady,
  useRouter,
  useUnload,
} from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { useCallback, useEffect, useRef, useState } from 'react';

const DEFAULT_TAB_INDEX = 0;

export default function OrdersPage() {
  // 安全的增加计数
  const renderCount = useRef(0);
  renderCount.current += 1;

  console.log('OrdersPage 安全渲染次数:', renderCount.current);

  const router = useRouter();
  const { tab } = router.params;

  // 获取初始标签索引
  const getInitialTabIndex = useCallback(
    () =>
      tab ? ORDER_TABS_USER.findIndex((t) => t.key === tab) : DEFAULT_TAB_INDEX,
    [tab]
  );

  const [activeTabIndex, setActiveTabIndex] = useState(getInitialTabIndex());

  // 根据活动标签获取参数
  const getActiveTabParams = useCallback(() => {
    console.log('getActiveTabParams', activeTabIndex);
    const tabConfig =
      ORDER_TABS_USER[activeTabIndex] || ORDER_TABS_USER[DEFAULT_TAB_INDEX];
    return {
      status: tabConfig.statuses,
      refundStatus: tabConfig.refundStatuses,
    };
  }, [activeTabIndex]);

  // 使用自定义hook管理订单列表
  const {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    onStatusChange,
  } = useOrderList({
    params: getActiveTabParams(),
  });

  // 处理标签变化
  const handleTabChange = useCallback(
    (event: { detail: { index: number } }) => {
      const index = event.detail.index;
      console.log('OrdersPage handleTabChange', index);
      if (index === activeTabIndex) {
        return;
      }
      setActiveTabIndex(index);
      const tabConfig = ORDER_TABS_USER[index];
      console.log(
        'OrdersPage handleTabChange',
        tabConfig.statuses,
        tabConfig.refundStatuses
      );
      onStatusChange(tabConfig.statuses, false, tabConfig.refundStatuses);
    },
    [onStatusChange, activeTabIndex]
  );

  // // 处理下拉刷新
  // const handlePullDownRefresh = useCallback(async () => {
  //   await refreshOrders();
  // }, [refreshOrders]);

  // // 处理重试
  // const handleRetry = useCallback(() => {
  //   refreshOrders();
  // }, [refreshOrders]);

  // 处理预约咨询
  const handleMakeAppointment = useCallback(() => {
    appRouter.consultation();
  }, []);

  // 监听滚动到底部事件
  useReachBottom(() => {
    if (pagination?.hasNext && !loading) {
      loadMoreOrders();
    }
  });

  useDidHide(() => {
    console.log('OrdersPage useDidHide');
  });
  useUnload(() => {
    console.log('OrdersPage useUnload');
  });
  usePullDownRefresh(() => {
    console.log('OrdersPage onPullDownRefresh');
  });
  useReady(() => {
    console.log('OrdersPage onReady');
  });
  useLoad(() => {
    console.log('OrdersPage onLoad');
  });
  // 初始化和tab变化时更新状态
  useEffect(() => {
    console.log('OrdersPage useEffect', getInitialTabIndex());
    const newIndex = getInitialTabIndex();
    setActiveTabIndex(newIndex);
    const tabConfig = ORDER_TABS_USER[newIndex];
    onStatusChange(tabConfig.statuses, false, tabConfig.refundStatuses);
  }, [getInitialTabIndex, onStatusChange]);

  // 渲染订单列表
  const renderOrderList = useCallback(() => {
    return (
      <View className='p-4'>
        {orders.map((order) => (
          <OrderCard key={order._id} orderId={order._id} showActions />
        ))}

        {loading && (
          <View className='py-4 text-center text-secondary text-sm'>
            加载中...
          </View>
        )}

        {!loading && pagination && !pagination.hasNext && orders.length > 0 && (
          <View className='py-4 text-center text-secondary text-sm'>
            没有更多数据了
          </View>
        )}
      </View>
    );
  }, [orders, loading, pagination]);

  return (
    <PageLol
      useNav
      navigationProps={{
        title: '我的订单',
        showBackButton: true,
      }}
      onPullDownRefresh={refreshOrders}
      onRetry={refreshOrders}
      isEmpty={!loading && orders.length === 0}
    >
      <View className='min-h-screen pb-[120px]'>
        <Tabs
          active={activeTabIndex}
          onChange={handleTabChange}
          swipeable={false}
        >
          {ORDER_TABS_USER.map((tabItem, index) => (
            <Tab key={index} title={tabItem.title}>
              <ScrollView scrollY className='min-h-[calc(100vh-100px)]'>
                {renderOrderList()}
              </ScrollView>
            </Tab>
          ))}
        </Tabs>
      </View>
    </PageLol>
  );
}
