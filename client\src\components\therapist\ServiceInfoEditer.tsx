import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Picker, Stepper } from '@antmjs/vantui';
import {
  DAYS_OF_WEEK,
  SERVICE_TYPE_MAP,
  SERVICE_TYPE_MAP_SHORT,
} from '@constants/text';
import { my_service } from '@model/profile.therapist';
import { Service, ServiceType } from '@model/service.interface';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';

// 时间段选项
const TIME_SLOTS: string[] = [];
for (let hour = 6; hour <= 22; hour++) {
  const hourStr = hour < 10 ? `0${hour}` : `${hour}`;
  TIME_SLOTS.push(`${hourStr}:00`);
}

interface ServiceInfoEditerProps {
  serviceInfo: Partial<my_service>;
  onFormChange: (field: string, value: any) => void;
}

export default function ServiceInfoEditer({
  serviceInfo,
  onFormChange,
}: ServiceInfoEditerProps) {
  // 新服务表单
  const [newService, setNewService] = useState<Partial<Service>>({
    type: ServiceType.VIDEO,
    duration: 60,
    price: 200,
    finalPrice: 200,
  });

  // 处理服务类型选择变化
  const handleServiceTypeChange = (
    e: { detail: Array<string | number> },
    data: Array<Record<string, any>>
  ) => {
    // 由于是单选，只取第一个值
    const selectedType = e.detail[0] as ServiceType;
    setNewService({
      ...newService,
      type: selectedType,
    });
  };

  // 处理工作日选择变化
  const handleWorkDaysChange = (
    e: { detail: Array<string | number> },
    data: Array<Record<string, any>>
  ) => {
    const selectedDays = e.detail.map(Number); // 确保转换为数字
    onFormChange('workTime', {
      ...(serviceInfo.workTime || { start: 9, end: 18 }),
      workDays: selectedDays,
    });
  };

  // 添加新服务
  const addService = () => {
    if (!newService.price || newService.price <= 0) {
      Taro.showToast({ title: '请输入有效的咨询价格', icon: 'none' });
      return;
    }

    if (!newService.duration || newService.duration <= 0) {
      Taro.showToast({ title: '请输入有效的咨询时长', icon: 'none' });
      return;
    }

    // 计算最终价格（如果有优惠）
    const finalPrice = calculateFinalPrice(newService);

    const service: Service = {
      type: newService.type || ServiceType.VIDEO,
      duration: newService.duration,
      price: newService.price,
      finalPrice: finalPrice,
    };

    const services = [...(serviceInfo.services || []), service];
    onFormChange('services', services);

    // 重置新服务表单
    setNewService({
      type: ServiceType.VIDEO,
      duration: 60,
      price: 200,
      finalPrice: 200,
    });
  };

  // 计算最终价格
  const calculateFinalPrice = (service: Partial<Service>): number => {
    if (!service.price) return 0;

    // 如果有优惠，计算优惠后的价格
    if (service.promo) {
      // 实现优惠计算逻辑
      return service.price; // 暂时返回原价
    }

    return service.price;
  };

  // 删除服务
  const removeService = (index: number) => {
    const services = [...(serviceInfo.services || [])];
    services.splice(index, 1);
    onFormChange('services', services);
  };

  return (
    <View>
      {/* 添加服务表单 */}
      <View className='mx-4 mt-4'>
        <Text className='text-base font-bold mb-3 block'>添加新服务</Text>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>服务类型</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={Object.keys(SERVICE_TYPE_MAP_SHORT).map((item) => ({
                id: item,
                name: SERVICE_TYPE_MAP[item],
              }))}
              value={newService.type ? [newService.type.toString()] : []}
              onChange={handleServiceTypeChange}
              placeholder='请选择服务类型'
              showArrowRight
              limit={1}
              bodyHeight='30vh'
            />
          </View>
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>咨询价格</Text>
          </View>
          <View className='flex items-center'>
            <Text className='mr-2'>¥</Text>
            <Stepper
              value={newService.price}
              min={0}
              step={10}
              onChange={(e) =>
                setNewService({
                  ...newService,
                  price: Number(e.detail),
                  finalPrice: Number(e.detail), // 暂时设置为相同值
                })
              }
            />
            <Text className='ml-2'>元/次</Text>
          </View>
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>咨询时长</Text>
          </View>
          <View className='flex items-center'>
            <Stepper
              value={newService.duration}
              min={30}
              max={180}
              step={15}
              onChange={(e) =>
                setNewService({
                  ...newService,
                  duration: Number(e.detail),
                })
              }
            />
            <Text className='ml-2'>分钟/次</Text>
          </View>
        </View>

        <View className='mb-3 flex justify-center'>
          <Button type='primary' size='small' onClick={addService}>
            添加服务
          </Button>
        </View>
      </View>

      {/* 已添加服务列表 */}
      {(serviceInfo.services || []).length > 0 && (
        <View className='mx-4 mt-6'>
          <Text className='text-base font-bold mb-3 block'>已添加服务</Text>
          {(serviceInfo.services || []).map((service, index) => (
            <View
              key={index}
              className='mb-3 bg-white border border-border rounded-lg p-4'
            >
              <View className='bg-gray-50 p-3 rounded'>
                <View className='flex justify-between items-center'>
                  <Text className='font-medium'>
                    {SERVICE_TYPE_MAP_SHORT[service.type]}
                  </Text>
                  <Text
                    className='text-red-500 text-sm'
                    onClick={() => removeService(index)}
                  >
                    删除
                  </Text>
                </View>
                <View className='flex justify-between mt-2'>
                  <Text className='text-sm'>
                    ¥{service.price}元/{service.duration}分钟
                  </Text>
                  {service.finalPrice !== service.price && (
                    <Text className='text-sm text-red-500'>
                      优惠价: ¥{service.finalPrice}元
                    </Text>
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
      )}

      <View className='mx-4 mt-6'>
        <Text className='text-lg font-bold mb-3 block'>工作时间</Text>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>工作日</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={DAYS_OF_WEEK}
              value={
                serviceInfo.workTime?.workDays?.map((day) => day.toString()) ||
                []
              }
              onChange={handleWorkDaysChange}
              placeholder='请选择工作日'
              showArrowRight
              checkAll
              bodyHeight='30vh'
            />
          </View>
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>工作开始时间</Text>
          </View>
          <View className='flex items-center justify-end flex-1 h-full'>
            <Picker
              className='h-full w-full'
              mode='content'
              columns={[TIME_SLOTS]}
              value={`${String(serviceInfo.workTime?.start ?? 9).padStart(
                2,
                '0'
              )}:00`}
              onInput={(e) => {
                const timeStr = e.detail as string;
                const hour = parseInt(timeStr.split(':')[0], 10);
                onFormChange('workTime', {
                  ...(serviceInfo.workTime || { workDays: [] }),
                  start: hour,
                });
              }}
              allowClear={false}
            />
          </View>
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>工作结束时间</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <Picker
              mode='content'
              columns={[TIME_SLOTS]}
              value={`${String(serviceInfo.workTime?.end ?? 18).padStart(
                2,
                '0'
              )}:00`}
              onInput={(e) => {
                const timeStr = e.detail as string;
                const hour = parseInt(timeStr.split(':')[0], 10);

                onFormChange('workTime', {
                  ...(serviceInfo.workTime || { workDays: [] }),
                  end: hour,
                });
              }}
              placeholder='请选择工作结束时间'
              allowClear={false}
            />
          </View>
        </View>
      </View>
      {/* <Text className='text-lg font-bold my-4 block'>服务约定</Text>
      <CellGroup inset>
        <Cell>
          <View className='flex items-center'>
            <Switch
              checked={Boolean(serviceInfo.agreeTerms)}
              onChange={(e) => onFormChange('agreeTerms', e.detail)}
              size='24px'
            />
            <Text className='ml-2 text-sm'>
              我已阅读并同意
              <Text
                className='text-primary'
                onClick={() => {
                  // 跳转到协议页面
                  Taro.navigateTo({
                    url: '/pages/sub-packages/profile/agreement/index?type=therapist',
                  });
                }}
              >
                《咨询师服务协议》
              </Text>
            </Text>
          </View>
        </Cell>
      </CellGroup> */}
    </View>
  );
}
