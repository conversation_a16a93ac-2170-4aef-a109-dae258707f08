export const enum FeedbackType {
  SERVICE = 'service',
  PAYMENT = 'payment',
  OTHER = 'other',
}

export interface Feedback {
  _id: string;
  userId: string;
  orderId?: string;
  type: FeedbackType;
  content: string;
  images: string[];
  status: 'pending' | 'processing' | 'resolved' | 'rejected';
  createTime: number;
  replyContent?: string;
  replyTime?: number;
}

export interface FeedbackRequest {
  type: FeedbackType;
  content: string;
  images: string[];
}

export interface FeedbackResponse {
  code: number;
  data: Feedback | Feedback[] | null;
  message: string;
}
