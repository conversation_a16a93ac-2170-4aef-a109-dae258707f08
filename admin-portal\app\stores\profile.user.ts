import { user_public } from '@model/user.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  profile: user_public | null;
  favoriteTherapists: string[];

  referralInfo: {
    referralId: string;
    referralType: string;
  } | null;
}

interface Action {
  setUser: (user: State['profile']) => void;
  removeUser: () => void;
  setReferralInfo: (referralId: string, referralType: string) => void;
  clearReferralInfo: () => void;
  reset: () => void;
}

const initialState: State = {
  profile: null,
  favoriteTherapists: [],

  referralInfo: null,
};

const profileStore = create<State & Action>()(
  immer(
    persist(
      (set) => ({
        ...initialState,
        setUser: (user) => set({ profile: user }),
        removeUser: () => set({ profile: initialState.profile }),
        setReferralInfo: (referralId, referralType) => {
          set({ referralInfo: { referralId, referralType } });
        },
        clearReferralInfo: () => {
          set({ referralInfo: null });
        },
        reset: () => set(initialState),
      }),
      {
        name: StorageSceneKey.PROFILE,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          user: state.profile,
          referralInfo: state.referralInfo,
        }),
      }
    )
  )
);

export const useUserProfileStore = createSelectors(profileStore);
