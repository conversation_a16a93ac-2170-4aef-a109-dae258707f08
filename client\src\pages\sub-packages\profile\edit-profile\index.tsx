import PageLol from '@components/common/page-meta';
import { useGlobalStore } from '@stores/global.store';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback } from 'react';
import UserProfileEditor from '../components/UserProfileEditor';

/**
 * 编辑个人资料页面
 */
export default function EditProfilePage() {
  // 编辑完成后返回上一页
  const handleComplete = useCallback(() => {
    Taro.navigateBack();
  }, []);

  return (
    <PageLol
      navigationProps={{
        title: '编辑个人资料',
        showBackButton: true,
        onBack: handleComplete,
      }}
    >
      <View className='flex flex-col items-center h-screen px-4 pt-6'>
        <UserProfileEditor
          onComplete={() => {
            // 加载用户信息
            useGlobalStore.getState().setNeedReload(true);
          }}
        />
      </View>
    </PageLol>
  );
}
