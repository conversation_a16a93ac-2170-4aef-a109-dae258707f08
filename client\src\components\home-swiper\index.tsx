import { Image } from '@antmjs/vantui';
import { Banner } from '@model/home.interface';
import { Swiper, SwiperItem, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';

export default function HomeBanners({ banners }: { banners: Banner[] }) {
  const [initPage] = useState(0);
  const [height] = useState(200);

  return (
    <View>
      <Swiper
        className='h-50'
        indicatorActiveColor='#426543'
        autoplay
        circular
        interval={3000}
        current={initPage}
        indicatorDots
      >
        {banners.map((item, index) => (
          <SwiperItem key={`swiper#demo1${index}`}>
            <View className='w-full h-full relative'>
              <Image
                src={item.image}
                fit='cover'
                width='100%'
                height={`${height}px`}
              />
              {item.target.buttonText && (
                <View
                  className='absolute bottom-0 left-16 px-2 py-1 bg-white text-primary font-bold text-md'
                  onClick={() => {
                    switch (item.target.type) {
                      case 'miniprogram':
                        Taro.navigateTo({
                          url: item.target.path || '',
                        });
                        break;
                      case 'webview':
                        Taro.navigateTo({
                          url:
                            '/pages/sub-packages/function/webview/index?url=' +
                            encodeURIComponent(item.target.url || ''),
                        });
                        break;
                      case 'copy':
                        Taro.setClipboardData({
                          data: item.target.text || '',
                        });
                    }
                  }}
                >
                  {item.target.buttonText}
                </View>
              )}
            </View>
          </SwiperItem>
        ))}
      </Swiper>
    </View>
  );
}
