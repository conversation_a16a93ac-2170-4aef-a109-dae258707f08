import { OrderListRequest } from '@core/api';
import { CacheOptions, CachePolicy } from './cache-types';

/**
 * 订单模块缓存策略配置
 *
 * 缓存策略设计原则:
 * 1. 公开配置数据: 长期缓存，定期后台刷新
 * 2. 用户私有数据: 短期缓存，自动过期或强制更新
 * 3. 高频变更数据: 短期缓存或不缓存，每次使用前校验
 * 4. 敏感数据: 不缓存，每次从云函数获取
 */

// 角色类型
export enum CACHE_ROLE {
  USER = 'user',
  THERAPIST = 'therapist',
}

// 缓存键前缀
export const ORDER_CACHE_PREFIX = {
  // 订单列表
  ORDER_LIST: 'order:list',
  // 订单详情
  ORDER_DETAIL: 'order:detail:',
};

/**
 * 缓存时间配置 (毫秒)
 */
export const ORDER_CACHE_TTL = {
  // 订单列表缓存5分钟
  ORDER_LIST: 5 * 60 * 1000,
  // 订单详情缓存5分钟
  ORDER_DETAIL: 5 * 60 * 1000,
};

/**
 * 订单列表缓存策略
 */
export const orderListCacheOptions: CachePolicy<OrderListRequest> = {
  keyPrefix: ORDER_CACHE_PREFIX.ORDER_LIST,
  ttl: ORDER_CACHE_TTL.ORDER_LIST,
  version: '1.0.0',

  /**
   * 根据查询参数生成缓存键
   * @param params 查询参数
   */
  getCacheKey: (params: OrderListRequest): string => {
    // 将查询参数转换为字符串，用于生成缓存键
    const {
      page,
      pageSize,
      params: queryParams,
      role = CACHE_ROLE.USER,
    } = params;

    // 构建参数字符串
    const paramsStr = [
      `page=${page || 1}`,
      `pageSize=${pageSize || 20}`,
      queryParams?.status
        ? `status=${queryParams.status.join('_')}`
        : 'status=all',
      queryParams?.query ? `query=${queryParams.query}` : '',
      queryParams?.complaint ? `complaint=${queryParams.complaint}` : '',
    ]
      .filter(Boolean)
      .join('&');

    return `${role}_${orderListCacheOptions.keyPrefix}_${paramsStr}`;
  },

  /**
   * 获取当前缓存版本
   */
  getVersion: (): string => {
    return orderListCacheOptions.version;
  },
};

/**
 * 订单详情缓存策略
 */
export const orderDetailCacheOptions: CacheOptions = {
  ttl: ORDER_CACHE_TTL.ORDER_DETAIL,
  backgroundRefresh: false,
  getCacheKey: (id: string, role: CACHE_ROLE = CACHE_ROLE.USER) => {
    return `${role}_${ORDER_CACHE_PREFIX.ORDER_DETAIL}${id}`;
  },
  // 每小时版本更新，确保定期刷新数据
  getVersion: () => {
    const now = new Date();
    return `${now.toISOString().split('T')[0]}:${now.getHours()}`;
  },
  ignoreErrors: false,
};

/**
 * 缓存校验最长时间
 * 当缓存数据超过此时间时，即使TTL未到期也强制刷新
 */
export const ORDER_CACHE_MAX_AGE = {
  // 订单列表最长缓存30分钟
  ORDER_LIST: 30 * 60 * 1000,
  // 订单详情最长缓存15分钟
  ORDER_DETAIL: 15 * 60 * 1000,
};
