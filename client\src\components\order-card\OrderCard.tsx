import { Image, Tag } from '@antmjs/vantui';
import ButtonGroup, { ButtonAction } from '@components/common/ButtonGroup';
import { AVATAR_DEFAULT, SERVICE_TYPE_ICON_PRIMARY } from '@constants/assets';
import { ORDER_STATUS_MAP, SERVICE_TYPE_MAP } from '@constants/text';
import { useOrderTherapist } from '@hooks/useOrderTherapist';
import { ORDER_STATUS } from '@model/order.interface';
import { ServiceType } from '@model/service.interface';
import { Text, View } from '@tarojs/components';
import { orderRouter } from '@utils/router';
import { useCallback, useMemo } from 'react';

export interface OrderCardProps {
  orderId: string;
  cta?: () => void;
  showActions?: boolean;
}
/**
 * 订单卡片 供用户端使用
 * @param orderId 订单ID
 * @param showActions 是否显示操作按钮
 */
export default function OrderCard({
  orderId,
  showActions = false,
}: OrderCardProps) {
  const { order, therapist } = useOrderTherapist(orderId);

  console.log('OrderCard', order, therapist);
  // // 格式化时间
  const displayTime = new Date(order?.startTime || 0).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });

  // 从缓存的信息中解构出需要的数据
  const { status, serviceType } = order || {};

  // 获取订单操作按钮
  const orderActions = useMemo(() => {
    const actions: ButtonAction[] = [];
    if (!order) {
      return actions;
    }

    switch (order.status) {
      case ORDER_STATUS.PENDING_PAYMENT:
        actions.push(
          {
            text: '取消订单',
            onClick: () => orderRouter.cancel(order._id),
            type: 'primary',
            plain: true,
            hairline: true,
            round: true,
          },
          {
            text: '去付款',
            onClick: () => orderRouter.pay(order._id),
            type: 'primary',
            round: true,
          }
        );
        break;
      case ORDER_STATUS.COMPLETED:
        actions.push(
          {
            text: '查看订单',
            onClick: () => orderRouter.detail(order._id),
            type: 'default',
            plain: true,
            hairline: true,
            round: true,
          },
          {
            text: '去评价',
            onClick: () => orderRouter.review(order._id),
            type: 'primary',
            round: true,
          }
        );
        break;
    }

    return actions;
  }, [order]);

  const renderOrderActions = useCallback(() => {
    return (
      <View className='flex flex-col items-center justify-center h-full w-full'>
        {orderActions.length > 0 && (
          <View className='h-px bg-gray-200 my-4 w-full px-4' />
        )}
        <ButtonGroup actions={orderActions} gap={4} />
      </View>
    );
  }, [orderActions]);

  // 如果数据未加载完成，显示骨架屏
  if (!order || !therapist) {
    return (
      <View className='bg-white rounded-2xl p-4 flex flex-col mt-4 animate-pulse'>
        <View className='flex items-center'>
          <View className='bg-gray-200 rounded-full w-[220px] h-[220px]' />
          <View className='flex-1 flex flex-col ml-3 space-y-2'>
            <View className='h-6 bg-gray-200 rounded w-3/4' />
            <View className='h-4 bg-gray-200 rounded w-1/2' />
            <View className='h-4 bg-gray-200 rounded w-2/3' />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View
      className='bg-white rounded-2xl p-4 flex flex-col mt-4'
      onClick={() => {
        orderRouter.detail(orderId);
      }}
    >
      {/* 第一部分：信息行 */}
      <View className='flex flex-row items-center'>
        <Image
          src={therapist?.avatar || AVATAR_DEFAULT}
          radius={24}
          width={220}
          height={220}
          fit='cover'
        />
        <View className='flex-1 flex flex-col ml-3 h-[220px] justify-between'>
          <View>
            {/* 名字和头衔行 */}
            <View className='flex flex-row items-center mb-3'>
              <Text className='font-bold text-base mr-2'>
                {therapist?.name || '咨询师'}
              </Text>
              <Text className='font-bold text-base'>·</Text>
              <Text className='font-bold text-base'>
                {therapist?.titles[0] || ''}
              </Text>
            </View>
            {/* 服务行 */}
            <View className='flex flex-row items-center'>
              {serviceType === ServiceType.VIDEO && (
                <Image
                  src={SERVICE_TYPE_ICON_PRIMARY[serviceType]}
                  width='24px'
                  height='24px'
                  radius={999}
                  fit='contain'
                />
              )}
              <Text className='text-md mr-2'>
                {SERVICE_TYPE_MAP[serviceType!]}
              </Text>
              <Tag
                plain
                size='medium'
                type={
                  status === ORDER_STATUS.PENDING_START
                    ? 'primary'
                    : status === ORDER_STATUS.IN_PROGRESS
                    ? 'warning'
                    : status === ORDER_STATUS.COMPLETED
                    ? 'success'
                    : 'danger'
                }
              >
                {ORDER_STATUS_MAP[status!]}
              </Tag>
            </View>
          </View>
          {/* 日期时间行 */}
          <Text className='text-md font-bold text-default'>{displayTime}</Text>
        </View>
      </View>

      {/* 第二部分：操作按钮（可选） */}
      {showActions && renderOrderActions()}
    </View>
  );
}
