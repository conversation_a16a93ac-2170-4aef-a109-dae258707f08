import { Button } from '@antmjs/vantui';
import AppointmentCalendar from '@components/therapist/AppointmentCalendar';
import {
  therapist_schedule,
  therapist_service,
} from '@model/therapist.interface';
import { useAppointmentStore } from '@stores/appointment.store';
import { Text, View } from '@tarojs/components';

interface TimeSelectionProps {
  occupancy: therapist_schedule | null;
  therapistService: therapist_service | null;
  onBack: () => void;
  onNext: () => void;
}

export default function TimeSelection({
  occupancy,
  therapistService,
  onBack,
  onNext,
}: TimeSelectionProps) {
  const {
    selectedTimeSlot,
    setSelectedTimeSlot,
    selectedDate,
    setSelectedDate,
  } = useAppointmentStore();

  //获取咨询师已排期信息

  // 处理日期和时间选择,已经更新状态到了store
  const handleSelectDateTime = (date: number, timeSlot: number) => {
    setSelectedDate(date);
    setSelectedTimeSlot(timeSlot);
  };

  return (
    <View className='time-selection'>
      <View className='rounded-xl p-4 mb-6'>
        <Text className='text-lg font-semibold mb-4 block'>选择咨询时间</Text>
        <Text className='text-sm text-secondary mb-4 block'>
          请选择您希望与咨询师进行咨询的时间
        </Text>

        <AppointmentCalendar
          occupancy={occupancy?.schedule || []}
          selectedDateUnixStamp={selectedDate || undefined}
          selectedTimeSlotInHour={selectedTimeSlot || undefined}
          onSelectDateTime={handleSelectDateTime}
          workTime={therapistService?.workTime}
        />

        <View className='fixed bottom-3 left-0 w-full bg-white flex p-4 gap-3'>
          <Button type='default' block plain onClick={onBack}>
            上一步
          </Button>
          <Button
            type='primary'
            block
            round
            onClick={onNext}
            disabled={!selectedDate || !selectedTimeSlot}
          >
            下一步
          </Button>
        </View>
      </View>
    </View>
  );
}
