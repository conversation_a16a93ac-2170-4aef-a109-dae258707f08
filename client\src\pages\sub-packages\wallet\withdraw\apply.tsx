import { Button, Field, Icon, Radio, RadioGroup, Toast } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { paymentActions } from '@core/actions/payment.action';
import { WithdrawParams } from '@model/payment.interface';
import { useIncomeStore } from '@stores/income.store';
import { usePaymentStore } from '@stores/payment.store';
import { Text, View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { useState } from 'react';

/**
 * 分销提现页面
 */
export default function WithdrawPage() {
  const wallet = useIncomeStore.use.wallet();
  const isLoading = usePaymentStore.use.loading();
  // 提现金额
  const [amount, setAmount] = useState('');
  // 提现方式
  const [paymentMethod, setPaymentMethod] = useState('wechat');
  // 提现配置
  const [withdrawConfig, setWithdrawConfig] = useState({
    minAmount: 100,
    maxAmount: 50000,
    fee: 0,
    methods: [
      {
        type: 'wechat',
        name: '微信',
        icon: 'wechat',
        enabled: true,
      },
      {
        type: 'alipay',
        name: '支付宝',
        icon: 'alipay',
        enabled: true,
      },
    ],
  });

  // 页面显示时获取数据
  useDidShow(() => {
    loadData();
  });

  // 加载数据
  const loadData = async () => {
    // 获取提现配置
    try {
      const config = await paymentActions.getWithdrawConfig();
      if (config) {
        setWithdrawConfig(config);

        // 设置默认支付方式
        const enabledMethod = config.methods.find((method) => method.enabled);
        if (enabledMethod) {
          setPaymentMethod(enabledMethod.type);
        }
      }
    } catch (error) {
      console.error('Failed to get withdraw config:', error);
    }
  };

  // 设置提现金额
  const handleAmountChange = (event: { detail: string }) => {
    setAmount(event.detail);
  };

  // 提取全部金额
  const handleWithdrawAll = () => {
    if (wallet?.balance) {
      setAmount(String(wallet.balance));
    }
  };

  // 设置提现方式
  const handlePaymentMethodChange = (event) => {
    setPaymentMethod(event.detail);
  };

  // 提交提现申请
  const handleSubmit = async () => {
    // 验证金额
    if (!amount || parseFloat(amount) <= 0) {
      Toast.fail('请输入有效金额');
      return;
    }

    const amountValue = parseFloat(amount);

    if (!wallet?.balance) {
      Toast.fail('获取可提现金额失败');
      return;
    }

    if (amountValue > wallet.balance) {
      Toast.fail('提现金额不能超过可提现金额');
      return;
    }

    if (amountValue < withdrawConfig.minAmount) {
      Toast.fail(`提现金额不能低于${withdrawConfig.minAmount}元`);
      return;
    }

    if (amountValue > withdrawConfig.maxAmount) {
      Toast.fail(`提现金额不能超过${withdrawConfig.maxAmount}元`);
      return;
    }

    // 准备提现参数
    const withdrawParams: WithdrawParams = {
      amount: amountValue,
      type: 'bank',
      bankAccount: wallet?.bankAccount,
      bankName: wallet?.bankName,
      name: wallet?.name || '',
    };

    // 提交提现申请
    const result = await paymentActions.submitWithdrawApplication(
      withdrawParams
    );

    if (result) {
      Toast.success({
        message: '提现申请已提交',
        onClose: () => {
          // 导航到提现记录页面
          appRouter.withdrawRecords();
        },
      });
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '申请提现',
        showBackButton: true,
        showSearch: false,
      }}
      loading={isLoading}
    >
      <View className='min-h-screen pb-16 bg-gray-1'>
        {/* 提现金额输入 */}
        <View className='mx-4 mt-4 bg-bg rounded-xl p-4'>
          <Text className='text-base text-secondary mb-2'>请输入提现金额</Text>
          <View className='flex flex-row items-center mb-4'>
            <Text className='text-xl font-bold mr-2'>¥</Text>
            <View className='flex-1'>
              <Field
                type='digit'
                value={amount}
                placeholder='0.00'
                onChange={handleAmountChange}
                className='text-2xl font-bold'
                border={false}
              />
            </View>
          </View>

          <View className='flex flex-row justify-between items-center py-2 border-t border-solid border-gray-200'>
            <Text className='text-sm text-secondary'>
              可提现金额：¥{wallet?.balance || 0}
            </Text>
            <Text className='text-sm text-primary' onClick={handleWithdrawAll}>
              全部提现
            </Text>
          </View>
        </View>

        {/* 提现方式 */}
        <View className='mx-4 mt-4 bg-bg rounded-xl p-4'>
          <Text className='text-base mb-2'>提现方式</Text>
          <RadioGroup
            value={paymentMethod}
            onChange={handlePaymentMethodChange}
          >
            {withdrawConfig.methods
              .filter((method) => method.enabled)
              .map((method) => (
                <View
                  key={method.type}
                  className='flex flex-row items-center justify-between py-3 border-b border-solid border-gray-200 last:border-0'
                >
                  <View className='flex flex-row items-center'>
                    <Icon
                      name={method.icon}
                      color={
                        method.type === 'wechat'
                          ? '#07c160'
                          : method.type === 'alipay'
                          ? '#1677ff'
                          : '#666'
                      }
                      size={44}
                      className='mr-3'
                    />
                    <Text>提现到{method.name}</Text>
                  </View>
                  <Radio name={method.type} checkedColor='#1989fa' />
                </View>
              ))}
          </RadioGroup>
        </View>

        {/* 提现说明 */}
        <View className='mx-4 mt-4 bg-bg rounded-xl p-4'>
          <Text className='text-base mb-2'>提现说明</Text>
          <View className='mt-2'>
            <Text className='text-sm text-secondary mb-1'>
              1. 提现金额最低{withdrawConfig.minAmount}元起
            </Text>
            <Text className='text-sm text-secondary mb-1'>
              2. 提现申请将在1-3个工作日内处理
            </Text>
            <Text className='text-sm text-secondary'>
              3. 如有疑问，请联系客服400-888-8888
            </Text>
          </View>
        </View>

        {/* 提交按钮 */}
        <View className='px-4 mt-8'>
          <Button
            block
            round
            type='primary'
            loading={isLoading}
            disabled={
              isLoading ||
              !amount ||
              parseFloat(amount) <= 0 ||
              !wallet?.balance ||
              parseFloat(amount) > wallet.balance ||
              parseFloat(amount) < withdrawConfig.minAmount
            }
            onClick={handleSubmit}
          >
            确认提现
          </Button>
        </View>
      </View>
    </PageLol>
  );
}
