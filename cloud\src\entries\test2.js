// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS, ORDER_STATUS } = require("../common/db.constants");
const { safeGet } = require("../common/utils");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

async function handleLogin(code, openid) {
  try {
    console.log("handleLogin", code, openid);
    // 通过code获取微信用户信息（如果有）
    // 注意：新版微信小程序已不再支持通过code直接获取用户信息
    // 这里仅作为示例，实际项目中需要根据微信最新的接口调整
    let wxUserInfo = {};

    // 获取或创建用户
    let user = await getUserInfo(openid);
    if (!user) {
      console.log(`用户不存在，创建新用户: ${openid}`);
      user = await createUser(openid, wxUserInfo);
    }

    // 生成用户签名和令牌
    const userSig = await generateUserSig(user._id);
    const token = await generateToken(user._id);

    // 返回登录结果
    return {
      success: true,
      userInfo: {
        id: user._id,
        nickName: user.userName,
        avatarUrl: user.avatar,
        role: user.role,
      },
      userSig,
      token,
    };
  } catch (err) {
    console.error("登录处理失败:", err);
    throw err;
  }
}

async function generateUserSig(userId) {
  // TODO: 实现实际的签名生成逻辑，这里仅作为示例
  return `user_sig_${userId}_${Date.now()}`;
}

/**
 * 生成访问令牌
 * @param {string} userId 用户ID
 */
async function generateToken(userId) {
  // TODO: 实现实际的token生成逻辑，这里仅作为示例
  return `token_${userId}_${Date.now()}`;
}

/**
 * 创建新用户
 * @param {string} openid 用户openid
 * @param {object} userInfo 微信用户信息
 */
async function createUser(openid, userInfo = {}) {
  try {
    // 创建用户公开信息
    const userData = {
      _openid: openid,
      id: openid,
      userName: userInfo.nickName || "用户",
      avatar: userInfo.avatarUrl || "",
      role: USER_ROLE.USER,
      status: "active",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const result = await db.collection(COLLECTIONS.USER_PUBLIC).add({
      data: userData,
    });

    return {
      ...userData,
      _id: result._id,
    };
  } catch (err) {
    console.error("创建用户失败:", err);
    throw err;
  }
}
// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, { action, openid });

  try {
    // 特殊处理：如果是通过旧的login云函数入口调用
    // if (code && !action) {
    //   const result = await handleLogin(code, openid);
    //   return success(result);
    // }

    // // 特殊处理：如果是通过旧的updateUser云函数入口调用
    // if (!action && data && data.phoneCode) {
    //   const result = await bindPhone(data.phoneCode, openid);
    //   return success(result);
    // }

    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

// 处理函数映射
const handlers = {
  // 登录相关
  login: async (params, context) => {
    const result = await handleLogin(params.code, context.openid);
    return success(result);
  },
};
