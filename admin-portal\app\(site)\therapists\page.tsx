import { TherapistCard } from "@/components/shared/therapist-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";
const AVATAR_DEFAULT =
  "cloud://cloudbase-8g4u2qu9997239c6.636c-cloudbase-8g4u2qu9997239c6-1361949139/avatar/default_avatar.png";

// 模拟咨询师数据
const therapists = [
  {
    id: "therapist-1",
    name: "王医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["抑郁症", "焦虑障碍"],
    price: 299,
    rating: 4.9,
    reviewCount: 125,
    experience: "10年+",
  },
  {
    id: "therapist-2",
    name: "李医生",
    avatar: AVATAR_DEFAULT,
    title: "临床心理学家",
    specialties: ["婚姻家庭", "人际关系"],
    price: 399,
    rating: 4.8,
    reviewCount: 98,
    experience: "8年",
  },
  {
    id: "therapist-3",
    name: "张医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["青少年心理", "学业压力"],
    price: 359,
    rating: 4.7,
    reviewCount: 86,
    experience: "5年",
  },
  {
    id: "therapist-4",
    name: "刘医生",
    avatar: AVATAR_DEFAULT,
    title: "精神科医师",
    specialties: ["强迫症", "情绪管理"],
    price: 459,
    rating: 4.9,
    reviewCount: 103,
    experience: "12年+",
  },
  {
    id: "therapist-5",
    name: "陈医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["创伤治疗", "性心理"],
    price: 499,
    rating: 4.8,
    reviewCount: 78,
    experience: "7年",
  },
  {
    id: "therapist-6",
    name: "赵医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["职场压力", "情感问题"],
    price: 329,
    rating: 4.7,
    reviewCount: 92,
    experience: "6年",
  },
  {
    id: "therapist-7",
    name: "钱医生",
    avatar: AVATAR_DEFAULT,
    title: "心理治疗师",
    specialties: ["精神分析", "梦境解析"],
    price: 599,
    rating: 4.9,
    reviewCount: 65,
    experience: "15年+",
  },
  {
    id: "therapist-8",
    name: "孙医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["儿童心理", "亲子关系"],
    price: 379,
    rating: 4.8,
    reviewCount: 108,
    experience: "9年",
  },
  {
    id: "therapist-9",
    name: "周医生",
    avatar: AVATAR_DEFAULT,
    title: "心理咨询师",
    specialties: ["失眠症", "压力管理"],
    price: 349,
    rating: 4.6,
    reviewCount: 72,
    experience: "4年",
  },
];

// 专业领域选项
const specialtyOptions = [
  { value: "all", label: "所有领域" },
  { value: "depression", label: "抑郁症" },
  { value: "anxiety", label: "焦虑障碍" },
  { value: "family", label: "婚姻家庭" },
  { value: "relationship", label: "人际关系" },
  { value: "youth", label: "青少年心理" },
  { value: "ocd", label: "强迫症" },
  { value: "emotion", label: "情绪管理" },
  { value: "trauma", label: "创伤治疗" },
  { value: "sexual", label: "性心理" },
  { value: "work", label: "职场压力" },
  { value: "sleep", label: "失眠症" },
];

// 价格筛选选项
const priceOptions = [
  { value: "all", label: "所有价格" },
  { value: "0-300", label: "300元以下" },
  { value: "300-500", label: "300-500元" },
  { value: "500+", label: "500元以上" },
];

// 经验筛选选项
const experienceOptions = [
  { value: "all", label: "所有经验" },
  { value: "1-5", label: "1-5年" },
  { value: "5-10", label: "5-10年" },
  { value: "10+", label: "10年以上" },
];

export default function TherapistsPage() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">专业咨询师</h1>
        <p className="text-muted-foreground">
          我们的咨询师都经过严格筛选和认证，拥有专业资质和丰富经验
        </p>
      </div>

      {/* 筛选区域 */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input placeholder="搜索咨询师姓名或专长..." className="pl-10" />
          </div>
          <div className="flex flex-wrap gap-2">
            <Select defaultValue="all">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="专业领域" />
              </SelectTrigger>
              <SelectContent>
                {specialtyOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="价格范围" />
              </SelectTrigger>
              <SelectContent>
                {priceOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="咨询经验" />
              </SelectTrigger>
              <SelectContent>
                {experienceOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button>筛选</Button>
          </div>
        </div>
      </div>

      {/* 咨询师列表 */}
      <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {therapists.map((therapist) => (
          <TherapistCard
            key={therapist.id}
            id={therapist.id}
            name={therapist.name}
            avatar={therapist.avatar}
            title={therapist.title}
            specialties={therapist.specialties}
            price={therapist.price}
            rating={therapist.rating}
            reviewCount={therapist.reviewCount}
            experience={therapist.experience}
          />
        ))}
      </div>

      {/* 分页 */}
      <div className="mt-8 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-primary text-primary-foreground"
          >
            1
          </Button>
          <Button variant="outline" size="sm">
            2
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
}
