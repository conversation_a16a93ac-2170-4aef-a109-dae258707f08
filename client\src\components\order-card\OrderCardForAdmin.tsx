import { Icon, Image } from '@antmjs/vantui';
import {
  AVATAR_DEFAULT,
  DEFAULT_USER_NAME,
  SERVICE_TYPE_ICON_BOLD,
} from '@constants/assets';
import {
  CONSULTATION_DIRECTIONS_MAP,
  ORDER_STATUS_MAP,
  REFUND_REASON_MAP,
  SERVICE_TYPE_MAP,
} from '@constants/text';
import { orderAdminActions } from '@core/actions/order.admin';
import { useTherapistSummary } from '@hooks/useTherapistSummary';
import {
  ACTION_TYPE,
  ORDER_STATUS,
  Order_summary,
} from '@model/order.interface';
import { useAdminOrderStoreSelector } from '@stores/order.store';
import { Text, View } from '@tarojs/components';
import { formatTime2, formatTime3 } from '@utils/time';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import PriceDisplay from './PriceDisplay';

export interface OrderCardForAdminProps {
  order: Order_summary;
}

export default function OrderCardForAdmin({ order }: OrderCardForAdminProps) {
  console.log('OrderCardForAdmin orderData', order);

  // 从缓存的信息中解构出需要的数据
  const { status, serviceType } = order || {};

  const therapist = useTherapistSummary(order.therapistId);
  console.log('OrderCardForAdmin therapist', therapist);

  useEffect(() => {
    orderAdminActions.fetchOrderActions(order._id);
  }, [order._id]);

  const orderActions = useAdminOrderStoreSelector(
    useShallow((state) => state.getOrderActions(order._id))
  );
  console.log('OrderCardForAdmin orderActions', orderActions);

  const refundInfo = orderActions.find(
    (action) => action.action === ACTION_TYPE.REFUND_REQUEST
  );
  const refundReason = refundInfo?.extraData?.reason;
  const refundTime = refundInfo?.actionTime;
  const refundDetail = refundInfo?.extraData?.detail;

  if (
    !order ||
    !order.consultationInfo ||
    serviceType === undefined ||
    status === undefined
  ) {
    console.log('!!! OrderCardForTherapist null check failed');
    return null;
  }

  return (
    <View className='bg-white rounded-xl shadow p-3 w-full'>
      <View className='flex flex-row items-center mb-2'>
        <Image
          src={order.userAvatar || AVATAR_DEFAULT}
          className='mr-2 w-10 h-10 rounded-full'
        />
        <View className='flex-1'>
          <View className='flex flex-col items-start'>
            <Text className='font-semibold text-base mr-1'>
              {order.consultationInfo.name ||
                order.userName ||
                DEFAULT_USER_NAME}
            </Text>
            <View className='flex flex-row items-center gap-2'>
              {order.consultationInfo.name && (
                <Text className='text-md text-secondary'>
                  {order.consultationInfo.name}
                </Text>
              )}
              <View className='px-1.5 py-0.5 border border-border text-sm rounded-md mr-1 text-secondary'>
                {CONSULTATION_DIRECTIONS_MAP.find(
                  (item) => item.key === order.consultationInfo.direction
                )?.label || ''}
              </View>
            </View>
          </View>
        </View>
        <View
          className='px-2 py-0.5 rounded text-xs font-medium ml-2'
          style={{
            background:
              order.status === ORDER_STATUS.PENDING_START
                ? '#F5F5F5'
                : '#F5F5F5',
            color: '#FF9900',
          }}
        >
          {ORDER_STATUS_MAP[order.status]}
        </View>
      </View>
      {/* 分割线 */}
      <View className='h-px bg-gray-100 my-2' />
      {/* 内容区 */}
      <View className='flex flex-row items-center'>
        {/* 左侧信息 */}
        <View className='flex-1 min-w-0 flex flex-col items-start'>
          {/* 咨询师信息 */}
          <View className='flex flex-row items-center mb-1'>
            <Text className='text-md '>咨询师：</Text>
            <Text className='text-md '>{therapist?.name}</Text>
          </View>
          {/* 服务信息 */}
          <View className='flex flex-row items-center mb-1'>
            <Image
              src={SERVICE_TYPE_ICON_BOLD[order.serviceType]}
              className='w-3 h-3 mr-1'
            />
            <Text className='text-md  mr-2'>
              {SERVICE_TYPE_MAP[order.serviceType]}
            </Text>
            <View className='px-1.5 py-0.5 border border-border text-sm rounded-md mr-1 text-secondary'>
              {order.duration}分钟
            </View>
            <View className='px-1.5 py-0.5 bg-secondarylight rounded-md'>
              <PriceDisplay price={order.price} size='sm' />
            </View>
          </View>
          {/* 时间信息 */}
          <View className='flex flex-row items-center mb-1'>
            <Icon
              name='clock'
              size='12px'
              color='var(--color-text-secondary)'
              className='mr-1'
            />
            <Text className='text-md '>
              {formatTime3(
                order.startTime,
                order.startTime + order.duration * 60 * 1000
              )}
            </Text>
          </View>
          {/* 退款信息 */}
          <View className='flex flex-col items-start mb-1'>
            <Text className='text-md '>
              退款原因：
              {REFUND_REASON_MAP.find((item) => item.value === refundReason)
                ?.label || ''}
            </Text>
            <Text className='text-md '>退款详情：{refundDetail}</Text>
            <Text className='text-md '>
              申请时间：{formatTime2(refundTime || 0)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
