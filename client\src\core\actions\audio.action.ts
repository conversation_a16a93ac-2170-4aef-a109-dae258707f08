import { TreatmentAudio } from '@model/treatment.interface';
import { treatmentService } from '@services/treatment.service';
import { useAudioStore } from '@stores/audio.store';
import Taro from '@tarojs/taro';

/**
 * 音频播放器操作
 * 集成了音频播放器服务和音频操作
 */
class AudioActions {
  // 音频上下文
  audioContext: Taro.InnerAudioContext | null = null;
  progressUpdateTimer: number | null = null;

  /**
   * 初始化音频上下文 (修改为每次重建时完整初始化)
   */
  initialize(audio: TreatmentAudio) {
    console.log('audioActions.initialize');

    try {
      console.log('初始化音频上下文');
      if (!this.audioContext) {
        this.audioContext = Taro.createInnerAudioContext();
        // 重新绑定所有事件监听器
        this.bindAudioEvents();
      }

      const store = useAudioStore.getState();
      if (store.audio?.id !== audio.id) {
        // 设置音频源,即启动加载音频
        this.audioContext.src = audio.audioUrl;
      }
      store.setAudio(audio);
      store.setLoading(true);
    } catch (error) {
      console.error('audioActions.initialize 异常', error);
      Taro.showToast({ title: '音频播放器初始化失败', icon: 'none' });
    }
  }

  /**
   * 绑定音频事件监听器 (新增方法)
   */
  private bindAudioEvents() {
    if (!this.audioContext) return;

    this.audioContext.onPlay(() => {
      console.log('audioContext.onPlay duration', this.audioContext?.duration);
      // 开始定时更新进度
      if (this.audioContext?.duration) {
        this.startProgressUpdate(this.audioContext.duration);
      }
      const store = useAudioStore.getState();
      if (store.audio) {
        this._onPlayAudio(store.audio.id);
      }
    });

    this.audioContext.onPause(() => {
      console.log('audioContext.onPause');
      // // 停止定时更新，但保存当前播放位置
      // this.stopProgressUpdate();

      // // 手动更新一次当前进度，确保暂停时的进度被记录
      // if (this.audioContext) {
      //   const currentTime = this.audioContext.currentTime;
      //   const duration = this.audioContext.duration;
      //   this.updatePlaybackProgress(currentTime, duration);
      // }
    });

    // 监听停止事件
    this.audioContext.onStop(() => {
      console.log('音频停止播放');
      // this.pauseAudio();
      // this.stopProgressUpdate();
    });

    // 监听播放结束事件
    this.audioContext.onEnded(() => {
      console.log('音频播放结束');
      // this.pauseAudio();
      // this.stopProgressUpdate();
    });

    // 监听音频已加载事件
    this.audioContext.onCanplay(() => {
      console.log(
        'audioContext.onCanplay duration',
        this.audioContext?.duration
      );
      const store = useAudioStore.getState();
      if (store.playbackState.isPlaying) {
        this.audioContext?.play();
      }
      store.setLoading(false);
    });

    // 监听音频加载中事件
    this.audioContext.onWaiting(() => {
      console.log('音频加载中...');
      const store = useAudioStore.getState();
      store.setLoading(true);
    });

    // 修改错误处理：不再调用stopAudio()
    this.audioContext.onError((res) => {
      console.error('audioContext.onError:', res);

      Taro.showToast({ title: '音频播放异常', icon: 'none' });
    });
  }

  /**
   * 播放音频
   */
  async play() {
    console.log('audioActions.play');
    const store = useAudioStore.getState();
    store.startPlaying();
    if (store.loading) {
      return;
    }

    // 检查是否已经加载完成
    if (this.audioContext?.duration) {
      console.log('audioActions.play 尝试立即播放');
      try {
        this.audioContext?.play();
      } catch (error) {
        Taro.showToast({ title: '播放失败', icon: 'none' });
        console.error('audioActions.playAudio 异常', error);
      }
      this.startProgressUpdate(this.audioContext.duration);
    }
  }

  _onPlayAudio(audioId: string) {
    console.log('audioActions._onPlayAudio', audioId);
    const store = useAudioStore.getState();
    // 更新播放状态
    store.startPlaying();

    // 增加播放次数, 异步
    treatmentService.incrementPlayCount(audioId);

    // 获取当前时间作为记录开始时间
    // const startTime = Date.now();

    // const audio = useAudioStore.getState().audio;
    // 创建收听记录
    // const openid = useGlobalStore.getState().openid;
    // if (openid) {
    //   const record: Omit<ListeningRecord, 'id'> = {
    //     userId: openid,
    //     audioId: audioId,
    //     audioTitle: audio?.title || '',
    //     coverImage: audio?.coverImage || '',
    //     progress: 0,
    //     duration: audio?.duration || 0,
    //     lastPosition: 0,
    //     lastListenTime: startTime,
    //   };

    //   // 更新收听记录, 异步
    //   treatmentService.updateListeningRecord(record);
    // }
  }

  /**
   * 暂停播放
   */
  pauseAudio() {
    const store = useAudioStore.getState();
    console.log('audioActions.pauseAudio', store.audio?.id);
    try {
      if (!this.audioContext) return;

      this.audioContext.pause();
      this.stopProgressUpdate();
      store.pausePlaying();
    } catch (error) {
      console.error('audioActions.pauseAudio 异常', error);
      Taro.showToast({ title: '暂停播放失败', icon: 'none' });
    }
  }

  /**
   * 停止播放
   */
  stopAudio() {
    const store = useAudioStore.getState();
    console.log('audioActions.stopAudio', store.audio?.id);
    try {
      if (!this.audioContext) return;

      this.audioContext.stop();
      store.setAudio(null);
      this.stopProgressUpdate();
      store.pausePlaying();
    } catch (error) {
      console.error('audioActions.stopAudio 异常', error);
      Taro.showToast({ title: '停止播放失败', icon: 'none' });
    }
  }

  /**
   * 跳转到指定位置
   * @param position 播放位置（秒）
   */
  seekAudio(position: number) {
    const store = useAudioStore.getState();
    console.log('audioActions.seekAudio', store.audio?.id, position);
    if (!this.audioContext) return;

    try {
      this.audioContext.seek(position);
    } catch (error) {
      console.error('audioActions.seekAudio 异常', error);
      throw error;
    }
  }

  /**
   * 更新播放进度
   */
  async updatePlaybackProgress(currentTime: number, duration: number) {
    // console.log('audioActions.updatePlaybackProgress', currentTime, duration);
    const store = useAudioStore.getState();

    // 更新播放状态
    store.updateProgress(currentTime, duration);
  }

  /**
   * 开始定时更新进度
   * @param duration 音频总时长
   */
  startProgressUpdate(duration: number) {
    console.log('audioActions.startProgressUpdate', duration);
    // 停止之前的定时器
    this.stopProgressUpdate();

    // 创建新的定时器，每秒更新一次进度
    this.progressUpdateTimer = setInterval(() => {
      if (!this.audioContext) return;

      const currentTime = this.audioContext.currentTime;

      // 更新播放进度
      this.updatePlaybackProgress(currentTime, duration);
    }, 1000);
  }

  /**
   * 停止定时更新进度
   */
  stopProgressUpdate() {
    if (this.progressUpdateTimer) {
      clearInterval(this.progressUpdateTimer);
      this.progressUpdateTimer = null;
    }
  }

  /**
   * 销毁音频上下文
   */
  destroyAudioContext() {
    const store = useAudioStore.getState();
    console.log('audioActions.destroyAudioContext', store.audio?.id);
    this.stopProgressUpdate();

    if (this.audioContext) {
      console.log(
        'audioActions.destroyAudioContext 销毁音频上下文',
        store.audio?.id
      );
      try {
        this.stopAudio();

        this.audioContext.destroy();
        this.audioContext = null;
      } catch (error) {
        console.error('audioActions.destroyAudioContext 异常', error);
      }
    }

    store.setAudio(null);
    store.reset();
  }

  destroy() {
    this.destroyAudioContext();
  }
}

// 导出单例实例
export const audioActions = new AudioActions();
