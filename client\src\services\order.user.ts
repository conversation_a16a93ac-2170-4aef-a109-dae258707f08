import {
  CancelOrderParams,
  CloudFunctionResult,
  CreateOrderParams,
  OrderListRequest,
  OrderListResponse,
  SUCCESS_CODE,
} from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import {
  Order_action,
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from '@model/order.interface';

import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

/**
 * 订单服务类
 * 遵循架构要求：
 * - 读取用户自己的订单数据可以直接从数据库读取
 * - 所有写操作必须通过云函数
 */
class UserOrderService extends BaseService<Order_summary> {
  constructor() {
    super(COLLECTIONS.ORDER);
  }

  /**
   * 获取订单列表 - 直接从数据库读取
   * 用户可以直接读取自己的订单数据
   */
  async getOrders(request: OrderListRequest = {}): Promise<OrderListResponse> {
    try {
      console.log('OrderService getOrders', request);
      const { page = 1, pageSize = 10, params = {} } = request;
      const { status = [], refundStatus = [] } = params;
      const skip = (page - 1) * pageSize;

      // 构建查询条件
      let query: any = { _openid: '{openid}' };

      // 初始化数据库
      const db = Taro.cloud.database();
      // 查询命令
      const _ = db.command;

      // 添加主状态过滤
      if (status && status.length > 0) {
        query.status = _.in(status);
      }

      // 添加退款状态过滤
      if (refundStatus && refundStatus.length > 0) {
        query.refundStatus = _.in(refundStatus);
      }

      // 获取数据
      const options = {
        orderField: 'createdAt',
        orderDirection: 'desc',
        skip,
        limit: pageSize,
      };

      const orders = await this.directRead(query, options);

      console.log('OrderService getOrders orders', orders);

      // 计算分页信息

      return {
        success: true,
        code: 200,
        data: orders,
        pagination: {
          page,
          pageSize,
          hasNext: orders.length === pageSize,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取订单列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个订单详情 - 直接从数据库读取
   * 用户可以直接读取自己的订单数据
   */
  async getOrderById(orderId: string): Promise<Order_summary | null> {
    try {
      const result = await this.directRead({
        _id: orderId,
        _openid: '{openid}',
      });
      return result[0] || null;
    } catch (error) {
      console.error('获取订单详情失败:', error);
      throw error;
    }
  }

  async getOrderActions(orderId: string): Promise<Order_action[]> {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.ORDER_ACTION)
        .where({
          orderId,
        })
        .get()
        .then((res) => res.data.map((item) => item as Order_action));
      console.log('🚀🚀🚀 getOrderActions result', result);
      return result;
    } catch (error) {
      console.error('获取订单操作失败:', error);
      throw error;
    }
  }
  /**
   * 创建订单 - 通过云函数
   * 所有写操作必须通过云函数
   */
  async createOrder(params: CreateOrderParams): Promise<Order_summary> {
    try {
      const result = await callCloudFunction('order', 'createOrder', params);

      if (result.success && result.order) {
        return result.order as Order_summary;
      }

      throw new Error(result.message || '创建订单失败');
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    }
  }

  /**
   * 取消订单 - 通过云函数
   * 所有写操作必须通过云函数
   */
  async cancelOrder(params: CancelOrderParams): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('order', 'cancelOrder', params);
      console.log('🚀🚀🚀 cancelOrder result', result);
      if (result.success) {
        return result;
      }

      throw new Error(result.message || '取消订单失败');
    } catch (error) {
      console.error('取消订单失败:', error);
      throw error;
    }
  }

  /**
   * 监听订单列表变化
   * 用于需要实时同步的数据
   */
  watchOrders(
    params: {
      status?: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][];
      refundStatus?: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS][];
    },
    onChange: (snapshot: any) => void
  ): () => void {
    // 构建查询条件
    let query: any = {
      _openid: '{openid}',
    };

    // 初始化数据库
    const db = Taro.cloud.database();
    // 查询命令
    const _ = db.command;

    // 添加主状态过滤
    if (params.status && params.status.length > 0) {
      query.status = _.in(params.status);
    }

    // 添加退款状态过滤
    if (params.refundStatus && params.refundStatus.length > 0) {
      query.refundStatus = _.in(params.refundStatus);
    }

    // 建立实时监听
    return this.watchData(query, onChange);
  }

  /**
   * 监听单个订单变化
   * 用于需要实时同步的数据
   */
  watchOrderById(
    orderId: string,
    onChange: (snapshot: any) => void
  ): () => void {
    // 建立实时监听
    return this.watchData({ _id: orderId, _openid: '{openid}' }, onChange);
  }

  /**
   * 重新安排订单时间 - 通过云函数
   * 所有写操作必须通过云函数
   */
  async rescheduleOrder(
    orderId: string,
    date: number,
    timeSlot: number
  ): Promise<boolean> {
    try {
      const result = await callCloudFunction('order', 'rescheduleOrder', {
        _id: orderId,
        date,
        timeSlot,
      });
      if (result.success && result.code === SUCCESS_CODE) {
        return true;
      }
      throw new Error(result.message || '重新预约订单失败');
    } catch (error) {
      console.error('重新预约订单失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单
   */
  async submitUserInfo(orderId: string, params: any): Promise<boolean> {
    try {
      const result = await callCloudFunction('order', 'submitUserInfo', {
        _id: orderId,
        ...params,
      });
      if (result.success && result.code === SUCCESS_CODE) {
        return true;
      }
      throw new Error(result.message || '更新订单失败');
    } catch (error) {
      console.error('更新订单失败:', error);
      throw error;
    }
  }
}

// 导出服务实例
export const orderService = new UserOrderService();
