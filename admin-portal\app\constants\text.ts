import { ConsultationDirection } from "@/app/model/common.interface";
import { FeedbackType } from "@/app/model/feedback.interface";
import { FAQItem } from "@/app/model/help.interface";
import {
  ACTION_TYPE,
  CANCEL_REASON,
  ORDER_STATUS,
  REFUND_REASON,
  REFUND_STATUS,
  REJECT_REASON,
} from "@/app/model/order.interface";
import { ServiceType } from "@/app/model/service.interface";
import { TestCategory } from "@/app/model/test.model";

// 应用信息
export const APP = {
  name: "心理咨询",
  shortName: "心理咨询", // 用于小程序标题等简短场景
  description: "专业的在线心理咨询平台",
  copyright: "© 2024 心理咨询. All rights reserved.",
} as const;

// 导航栏标题
export const NAV_TITLES = {
  home: "首页",
  measure: "心理测评",
  consult: "在线咨询",
  profile: "个人中心",
  login: "登录",
  register: "注册",
} as const;

// 标签页文本
export const TAB_TEXTS = {
  home: "首页",
  measure: "测评",
  consultation: "咨询",
  treatment: "疗愈",
  profile: "我的",
} as const;

// 咨询师标签页文本
export const THERAPIST_TAB_TEXTS = {
  workspace: "工作台",
  orders: "预约",
  consultation: "咨询",
  clients: "客户",
  profile: "我的",
} as const;

// 推荐心理师标签（首页）
export const HOME_TABS = [
  { key: "anxiety", label: "焦虑抑郁" },
  { key: "teen", label: "青少年心理" },
  { key: "marriage", label: "婚姻情感" },
  // { key: "growth", label: "成长探索" },
  // { key: "career", label: "职业生涯" },
];

export const CONSULTATION_DIRECTIONS_MAP = [
  { key: ConsultationDirection.ANXIETY, label: "焦虑抑郁" },
  { key: ConsultationDirection.TEEN, label: "青少年" },
  { key: ConsultationDirection.MARRIAGE, label: "婚姻情感" },
  { key: ConsultationDirection.GROWTH, label: "成长探索" },
  { key: ConsultationDirection.CAREER, label: "职业困扰" },
];

// 职称选项
export const TITLE_OPTIONS = [
  { id: "心理咨询师", name: "心理咨询师" },
  { id: "精神科医师", name: "精神科医师" },
  { id: "精神分析师", name: "精神分析师" },
  { id: "婚姻家庭咨询师", name: "婚姻家庭咨询师" },
  { id: "临床心理学家", name: "临床心理学家" },
  { id: "社会工作师", name: "社会工作师" },
  { id: "催眠咨询师", name: "催眠咨询师" },
  { id: "音乐咨询师", name: "音乐咨询师" },
  { id: "艺术咨询师", name: "艺术咨询师" },
  { id: "其他", name: "其他" },
];

// 专长领域选项
export const SPECIALTY_OPTIONS = [
  { id: "抑郁症", name: "抑郁症" },
  { id: "焦虑症", name: "焦虑症" },
  { id: "强迫症", name: "强迫症" },
  { id: "双相情感障碍", name: "双相情感障碍" },
  { id: "创伤后应激障碍", name: "创伤后应激障碍" },
  { id: "恐惧症", name: "恐惧症" },
  { id: "睡眠障碍", name: "睡眠障碍" },
  { id: "进食障碍", name: "进食障碍" },
  { id: "儿童青少年心理问题", name: "儿童青少年心理问题" },
  { id: "婚姻家庭问题", name: "婚姻家庭问题" },
  { id: "职场压力", name: "职场压力" },
  { id: "人际关系", name: "人际关系" },
  { id: "性心理", name: "性心理" },
  { id: "成瘾行为", name: "成瘾行为" },
  { id: "人格障碍", name: "人格障碍" },
  { id: "其他", name: "其他" },
];

// 按钮文本
export const BUTTON_TEXTS = {
  confirm: "确认",
  cancel: "取消",
  submit: "提交",
  save: "保存",
  edit: "编辑",
  delete: "删除",
  back: "返回",
  next: "下一步",
  complete: "完成",
} as const;

// 提示文本
export const TIPS = {
  networkError: "网络异常，请稍后重试",
  loading: "加载中...",
  empty: "暂无数据",
  success: "操作成功",
  failed: "操作失败",
  unauthorized: "请先登录",
} as const;

// 表单相关文本
export const FORM_TEXTS = {
  required: "必填",
  optional: "选填",
  placeholder: "请输入",
  invalid: "格式不正确",
} as const;

// 用户相关文本
export const USER_TEXTS = {
  nickname: "昵称",
  avatar: "头像",
  gender: "性别",
  age: "年龄",
  phone: "手机号",
  email: "邮箱",
  password: "密码",
  confirmPassword: "确认密码",
} as const;

// 咨询相关文本
export const CONSULT_TEXTS = {
  title: "咨询预约",
  duration: "咨询时长",
  price: "咨询费用",
  consultant: "咨询师",
  date: "预约日期",
  time: "预约时间",
  type: "咨询方式",
  status: "咨询状态",
  online: "在线咨询",
  offline: "线下咨询",
  video: "视频咨询",
  voice: "语音咨询",
} as const;

// 测评相关文本
export const MEASURE_TEXTS = {
  title: "心理测评",
  result: "测评结果",
  report: "测评报告",
  duration: "测评时长",
  questions: "题目数量",
  start: "开始测评",
  continue: "继续测评",
  submit: "提交测评",
} as const;

// 取消原因
export const CANCEL_REASON_MAP = [
  { value: CANCEL_REASON.TIME_CONFLICT, label: "时间冲突" },
  { value: CANCEL_REASON.FOUND_BETTER, label: "找到更合适的医生" },
  { value: CANCEL_REASON.PRICE_CONCERN, label: "价格原因" },
  { value: CANCEL_REASON.FORCE_MAJEURE, label: "不可抗力" },
  { value: CANCEL_REASON.OTHER, label: "其他原因" },
];

// 拒绝原因
export const REJECT_REASON_MAP = [
  { value: REJECT_REASON.TIME_CONFLICT, label: "时间冲突" },
  { value: REJECT_REASON.FOUND_UNSUITABLE, label: "不适合的咨询问题" },
  { value: REJECT_REASON.PRICE_CONCERN, label: "价格原因" },
  { value: REJECT_REASON.FORCE_MAJEURE, label: "不可抗力" },
  { value: REJECT_REASON.OTHER, label: "其他" },
];

// 退款原因
export const REFUND_REASON_MAP = [
  { value: REFUND_REASON.LEAVE, label: "请假" },
  { value: REFUND_REASON.FORCE_MAJEURE, label: "不可抗力" },
  { value: REFUND_REASON.OTHER, label: "其他原因" },
];

// 订单状态
export const ORDER_STATUS_MAP = [
  { value: ORDER_STATUS.PENDING_PAYMENT, label: "待支付" },
  { value: ORDER_STATUS.PENDING_CONFIRM, label: "待确认" },
  { value: ORDER_STATUS.PENDING_START, label: "待开始" },
  { value: ORDER_STATUS.IN_PROGRESS, label: "进行中" },
  { value: ORDER_STATUS.COMPLETED, label: "已完成" },
  { value: ORDER_STATUS.CANCELLED, label: "已取消" },
];

// 退款状态
export const REFUND_STATUS_MAP = [
  { value: REFUND_STATUS.NONE, label: "无退款" },
  { value: REFUND_STATUS.AUDITING, label: "退款审核中" },
  { value: REFUND_STATUS.REJECTED, label: "退款拒绝" },
  { value: REFUND_STATUS.PROCESSING, label: "退款中" },
  { value: REFUND_STATUS.COMPLETED, label: "已退款" },
  { value: REFUND_STATUS.FAILED, label: "退款失败" },
];

// 订单处理中状态
export const ORDER_PROCESSING_STATUS = [
  ORDER_STATUS.PENDING_CONFIRM,
  ORDER_STATUS.PENDING_START,
  ORDER_STATUS.IN_PROGRESS,
];

// 订单完成状态
export const ORDER_COMPLETED_STATUS = [ORDER_STATUS.COMPLETED];

// 订单取消状态
export const ORDER_CANCELED_STATUS = [ORDER_STATUS.CANCELLED];

export const ACTION_TYPE_MAP = {
  [ACTION_TYPE.CREATE]: "创建订单",
  [ACTION_TYPE.PAY]: "支付订单",
  [ACTION_TYPE.SUBMIT_USER_INFO]: "提交用户信息",
  [ACTION_TYPE.ACCEPT]: "接受订单",
  [ACTION_TYPE.REJECT]: "拒绝订单",
  [ACTION_TYPE.CANCEL]: "取消订单",
  [ACTION_TYPE.START]: "开始服务",
  [ACTION_TYPE.COMPLETE]: "完成服务",
  [ACTION_TYPE.REVIEW]: "评价订单",
  [ACTION_TYPE.REFUND_REQUEST]: "申请退款",
  [ACTION_TYPE.REFUND_AUDIT_PASS]: "审核退款通过",
  [ACTION_TYPE.REFUND_AUDIT_REJECT]: "审核退款拒绝",
  [ACTION_TYPE.REFUND_REFUNDING]: "退款中",
  [ACTION_TYPE.REFUND_COMPLETE]: "完成退款",
  [ACTION_TYPE.REFUND_FAILED]: "退款失败",
  [ACTION_TYPE.REFUND_ROLLBACK]: "退款回滚",
};

export const SERVICE_TYPE_MAP_SHORT = {
  [ServiceType.VIDEO]: "视频",
  // [ServiceType.VOICE]: '语音',
  // [ServiceType.TEXT]: '文字',
  [ServiceType.FACE_TO_FACE]: "当面",
};

export const SERVICE_TYPE_MAP = {
  [ServiceType.VIDEO]: "视频问诊",
  // [ServiceType.VOICE]: '语音问诊',
  // [ServiceType.TEXT]: '图文问诊',
  [ServiceType.FACE_TO_FACE]: "当面问诊",
};

export const mockFAQs: FAQItem[] = [
  {
    id: "1",
    question: "如何预约心理咨询师?",
    answer:
      '您可以在首页选择"咨询预约"，浏览心理咨询师列表，查看详情后点击"立即预约"，选择合适的时间，完成支付即可成功预约。',
    category: "appointment",
  },
  {
    id: "2",
    question: "咨询前需要准备什么?",
    answer:
      "建议您提前准备好您想要咨询的问题，确保在安静、私密的环境进行咨询，保持网络畅通，按时进入咨询室。",
    category: "appointment",
  },
  {
    id: "3",
    question: "如何修改或取消预约?",
    answer:
      '您可以在"我的-我的订单"中找到相应订单，点击"取消预约"或"修改时间"，按照提示操作即可。请注意，离咨询时间24小时内取消将会收取一定手续费。',
    category: "appointment",
  },
  {
    id: "4",
    question: "如何进行在线咨询?",
    answer:
      '在预约的时间到达前，系统会发送提醒通知。您可以通过"我的-我的订单"找到相应预约，点击"进入咨询室"即可开始视频或语音咨询。',
    category: "consultation",
  },
  {
    id: "5",
    question: "预约咨询的费用如何计算?",
    answer:
      "咨询费用根据咨询师的级别、经验和专业领域有所不同，每位咨询师的收费标准会在其个人主页上明确标注，通常按次收费或按小时收费。",
    category: "payment",
  },
  {
    id: "6",
    question: "如何获取发票?",
    answer:
      '完成付款后，您可以在"我的-订单详情"页面点击"申请发票"，填写发票抬头和税号等信息，我们将在3-5个工作日内为您开具电子发票。',
    category: "payment",
  },
  {
    id: "7",
    question: "咨询内容会保密吗?",
    answer:
      "我们严格遵守心理咨询伦理准则，对您的所有咨询内容和个人信息严格保密。未经您的同意，不会向任何第三方透露您的咨询内容。",
    category: "privacy",
  },
];
// 问题分类
export const HELP_CATEGORIES = [
  { key: "all", name: "全部问题" },
  { key: "appointment", name: "预约问题" },
  { key: "consultation", name: "咨询流程" },
  { key: "payment", name: "支付退款" },
  { key: "privacy", name: "隐私安全" },
];
// 城市选项
export const CITY_OPTIONS = [
  "北京市",
  "上海市",
  "广州市",
  "深圳市",
  "成都市",
  "杭州市",
  "重庆市",
  "西安市",
  "武汉市",
  "南京市",
  "天津市",
  "苏州市",
  "长沙市",
  "郑州市",
  "东莞市",
  "青岛市",
  "沈阳市",
  "宁波市",
  "昆明市",
  "无（线上咨询）",
]; // 学历选项
export const EDUCATION_LEVELS = ["博士", "硕士", "专科", "其他"]; // 周几选项
export const DAYS_OF_WEEK = [
  { id: "1", name: "周一" },
  { id: "2", name: "周二" },
  { id: "3", name: "周三" },
  { id: "4", name: "周四" },
  { id: "5", name: "周五" },
  { id: "6", name: "周六" },
  { id: "7", name: "周日" },
];
// 对应到模型中的 1-7
export const DAY_TO_NUMBER: Record<string, number> = {
  周一: 1,
  周二: 2,
  周三: 3,
  周四: 4,
  周五: 5,
  周六: 6,
  周日: 7,
};

export const TEST_CATEGORY_MAP = {
  [TestCategory.ALL]: "全部",
  [TestCategory.EMOTION]: "情绪",
  [TestCategory.PERSONALITY]: "人格",
  [TestCategory.STRESS]: "压力",
  [TestCategory.SLEEP]: "睡眠",
  [TestCategory.RELATIONSHIP]: "人际关系",
  [TestCategory.CAREER]: "职业",
  [TestCategory.ADDICTION]: "成瘾行为",
  [TestCategory.TRAUMA]: "创伤",
  [TestCategory.GENERAL]: "综合",
  [TestCategory.OTHER]: "其他",
};
export const FEEDBACK_TYPE_MAP = {
  [FeedbackType.SERVICE]: "服务问题",
  [FeedbackType.PAYMENT]: "支付问题",
  [FeedbackType.OTHER]: "其他问题",
};
