# 项目介绍

在线心理咨询平台，用户可以预约咨询师，视频问诊或者线下问诊。
用户预约下单后，15 分钟内付款，否则订单自动取消。
用户付款后，咨询师会收到通知，并确认/拒绝订单。
用户付款 24 小时内，咨询师未确认订单，订单自动取消，在此区间，用户可以手动取消订单，系统会自动退款。
咨询师可以开始视频问诊，以及介绍自己的服务。
用户可以查看咨询师的基本信息、详情、服务、评价、预约时间、预约的详情。

目前提供微信小程序端，以后会提供 app 端。

# 项目架构

小程序端 + 微信云开发作为后端 + 微信云后台作为管理后台

## 技术栈

小程序端(前端)：
Taro(React) + Zustand + @antmjs/vantui + tailwindcss

云开发后端：
微信云开发(云函数、云数据库、云存储)

管理后台：
nextjs14 + tailwindcss + shadcn/ui + zustand

## 目录结构

cloud-mini/
├── cloud/ # 云函数
├── admin-portal/ # 管理后台
├── client/ # 小程序端
├── src/ # 小程序源码
├── core/actions/ # 业务逻辑协调层
├── core/interfaces/ # 接口定义层
├── services/ # 服务 API 层 (目前是云函数调用、云数据库读取/watch)
├── stores/ # Zustand 状态管理模块
├── pages/ # 页面级组件
├── pages/sub-packages/ # 页面级子包
├── components/ # 通用业务组件
├── hooks/ # 自定义 React Hooks
├── utils/ # 通用工具函数
├── constants/ # 常量定义
├── styles/ # 全局及主题样式
├── types/ # TypeScript 类型定义
├── app.config.ts # 小程序全局配置
├── app.less # 全局 Less 样式
├── app.tsx # 应用入口文件

## 前端架构

### 双端架构

咨询师端和用户端是同一套代码，通过以下方式实现：

- TabBar 使用[TabBar](../client/src/custom-tab-bar/index.tsx)组件，根据用户角色[currentRole](../client/src/stores/user.store.ts)显示不同的 TabBar。
- 页面根据[currentRole](../client/src/stores/user.store.ts)显示咨询师/用户的页面。
- 咨询师特有的页面、状态管理、页面协调层、接口定义等，放在在对应主目录下的 therapist 目录。例如咨询师专属的[therapistProfileActions](../client/src/core/actions/therapist/profile.action.ts)

### 流程

页面 -> 业务逻辑协调层 action -> 服务层 service -> 云函数 callCloudFunction -> 云数据库 Taro.cloud.database
| | |
──── 状态管理 |
| | |
──── 数据结构接口定义层

### 页面 src/pages

页面是小程序的入口，页面之间通过路由[appRouter](../client/src/utils/router.ts)跳转。
页面的逻辑部分由[业务协调层处理](../client/src/core/actions)，数据部分由[状态管理处理](../client/src/stores)。
数据结构由[接口定义层](../client/src/core/interfaces)定义。
常量由[常量定义](../client/src/constants)定义。
通用组件由[通用组件](../client/src/components)定义。
业务自身组件随业务页面下，由[业务自身组件](../client/src/pages/sub-packages)定义。
页面级子包由[页面级子包](../client/src/pages/sub-packages)定义。
工具函数由[工具函数](../client/src/utils)定义。
自定义 Hooks 由[自定义 Hooks](../client/src/hooks)定义。
全局样式由[全局样式](../client/src/styles)定义。
全局配置由[全局配置](../client/src/app.config.ts)定义。
全局入口由[全局入口](../client/src/app.tsx)定义。

页面代码保持简洁，只处理页面逻辑，不处理数据和业务逻辑。当逻辑和状态复杂时，可在逻辑协调层之上再封装一个 Hook 层[自定义 Hooks](../client/src/hooks)。

页面使用[PageLogic](../client/src/components/common/page-meta/index.tsx)组件作为顶层视图，统一管理自定义导航、
Loading、Error、Empty 等状态。

按钮提交时，使用[useLoadingStore](../client/src/stores/loading.store.ts)管理 loading 状态。

前端页面参考[src/pages/homepage/index.tsx](../client/src/pages/homepage/index.tsx)

### 业务逻辑协调层 src/core/actions

业务逻辑协调层是小程序的业务逻辑协调层，负责处理页面逻辑和状态管理、调用服务层。
使用参考[userActions](../client/src/core/actions/user.action.ts)
action 负责逻辑处理，调用服务层，处理状态管理，缓存管理。对于一般的错误情况可使用 Toast,或者 Taro.showModal,以减少页面逻辑的复杂度。
action 的调用参考[userActions](../client/src/core/actions/user.action.ts)

### 服务层 src/services

服务层是小程序的数据 API 层，负责读/watch 数据库、调用云函数。
使用参考[orderService](../client/src/services/order.service.ts)
云函数的调用参考[callCloudFunction](../client/src/services/cloud.ts)
云函数调用的返回结果参考[CloudFunctionResult](../client/src/core/api.ts)
service 调用云函数的一般写法：参考[createOrder](../client/src/services/order.user.ts)
函数返回的是业务数据，如果失败，抛出云函数返回的 message 作为异常，并打印错误日志。由调用者 Action 层处理。

```ts
/**
   * 创建订单 - 通过云函数
   * 所有写操作必须通过云函数
   */
  async createOrder(params: CreateOrderParams): Promise<Order_summary> {
    try {
      const result = await callCloudFunction('order', 'createOrder', params);

      if (result.success && result.code === SUCCESS_CODE && result.data) {
        return result.data;
      }

      throw new Error(result.message || '创建订单失败');
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    }
  }
```

### 状态管理 src/stores

状态管理是小程序的状态管理，负责管理页面状态，实现在页面和逻辑协调层之间共享数据。
使用[Zustand](https://github.com/pmndrs/zustand)作为状态管理库。
使用参考[useUserStore](../client/src/stores/user.store.ts)
根据需要持久化必要数据

### 数据缓存

缓存数据，避免重复读取数据库。原则上缓存模块只能由逻辑协调层调用
提供了统一的缓存模块[cache](../client/src/core/cache.ts)，使用参考[therapistActions](../client/src/core/actions/therapist.action.ts)

### 前端读写数据规则

#### 前端直读数据库数据

直接读取适用场景：

- 公开配置信息（SYSTEM_CONFIG）
- 低频变更数据（client\src\model\help.interface.ts、帮助文档、条款协议）
- 用户公开信息（USER_PUBLIC）
- 咨询师的公开数据（THERAPISTS、THERAPIST_SERVICE、THERAPIST_EXTINFO、THERAPIST_SCHEDULES）
- 用户读取自己的数据 （USER_FAVORITES、ORDER、ORDER_EXTINFO、DISTRIBUTION_OVERVIEW、DISTRIBUTION_INVITED_USER、DISTRIBUTION_ORDER、DISTRIBUTION_WITHDRAW_RECORD、NOTIFICATION_LIST、CHAT_SESSION、CHAT_MESSAGE）
- 咨询师读取自己的数据 （DISTRIBUTION_OVERVIEW、DISTRIBUTION_INVITED_USER、DISTRIBUTION_ORDER、DISTRIBUTION_WITHDRAW_RECORD、NOTIFICATION_LIST、CHAT_SESSION、CHAT_MESSAGE）
- 咨询师读取关联到自己的数据（ORDER、ORDER_EXTINFO）

#### 前端需要云函数读取数据的场景：

- 敏感数据（支付密钥、API 令牌 、user_sensitive）
- 需要复杂计算的查询
- 涉及多集合事务操作
- 需要严格权限验证的操作

#### 前端不能直接写数据

## 后端架构

后端采用微信云函数实现(../cloud/)

### 设计原则

- 写数据必须通过云函数
- 复杂逻辑或者耗时操作必须通过云函数
- 用户角色鉴权
- 业务操作鉴权

### 云函数

原始代码在[cloud/src/entries](../cloud/src/entries)目录下
通过打包工具[bundle-modules](../cloud/bundle-modules.js)打包成微信云函数[云函数](../cloud/functions)
云函数代码参考[cloud/src/entries/order.ts](../cloud/src/entries/order.ts)

## 数据/模型管理

使用微信云数据库

### 数据表

[数据库表定义](../client/src/model/db.model.ts)
云数据库的表设计遵循以下原则：

- 表定义与模型一一对应
- 高频查询字段（如订单状态、心理师 ID）建立索引，避免全表扫描 4。
- 大字段分离存储（如心理师介绍文案存独立集合，主表只存摘要）。

### 安全第一原则：

- 永远禁止客户端写操作
- 敏感字段单独存储
- 最小化返回字段范围
- 所有写操作必须通过云函数
- 敏感数据通过云函数处理
- 复杂业务逻辑通过云函数实现

## 管理后台

尽可能使用官方/开源的组件，数据源直接使用现有的后端云数据库，通过微信云函数来交换数据.
