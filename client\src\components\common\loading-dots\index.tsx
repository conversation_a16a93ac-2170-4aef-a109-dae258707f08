import { View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import './index.less';

interface LoadingDotsProps {
  color?: string;
  size?: number;
  className?: string;
}

export default function DotLoading({
  color = '#646566',
  size = 6,
  className = '',
}: LoadingDotsProps) {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % 3);
    }, 300);

    return () => clearInterval(interval);
  }, []);

  return (
    <View className={`loading-dots-container ${className}`}>
      {[0, 1, 2].map((index) => (
        <View
          key={index}
          className={`loading-dot ${activeIndex === index ? 'active' : ''}`}
          style={{
            backgroundColor: color,
            width: `${size}px`,
            height: `${size}px`,
            opacity: activeIndex === index ? 1 : 0.4,
            transform: activeIndex === index ? `scale(1.2)` : 'scale(1)',
          }}
        />
      ))}
    </View>
  );
}
