// ====================== 基础类型和枚举 ======================
export enum ResourceType {
  COURSE = 'course',
  CASE_STUDY = 'case',
  PHRASE = 'phrase',
  SUPERVISION = 'supervision',
}

export enum ContentType {
  VIDEO = 'video',
  QUIZ = 'quiz',
  ARTICLE = 'article',
  INTERACTIVE_CASE = 'interactive_case',
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

// ====================== 资源定义接口 ======================

/** 资源基础元数据 */
interface ResourceMetadata {
  title: string;
  description?: string;
  tags: string[];
  difficulty: DifficultyLevel;
  provider: string;
  creator: string; // 创建者ID
  createdAt: number; // 创建时间 unixtime秒
  updatedAt: number; // 最后更新时间 unixtime秒
}

// /** 资源审批信息 */
// interface ResourceApproval {
//   status: ApprovalStatus;
//   reviewer?: string; // 审核员ID
//   reviewDate?: number; // 审核时间 unixtime秒
//   comments?: string; // 审核意见
// }

/** 课程内容节 */
export interface CourseSection {
  sectionId: string;
  title: string;
  contentType: ContentType;
  duration: number; // 分钟
  resources?: string[]; // 附件ID列表
}

/** 课程资源定义 */
export interface CourseResource {
  id: string;
  type: ResourceType.COURSE;
  metadata: ResourceMetadata & {
    totalDuration: number; // 总时长（分钟）
  };
  content: {
    sections: CourseSection[];
  };
  pricing?: {
    originalPrice: number;
    currentPrice: number;
  };
  //   approval: ResourceApproval;
}

/** 案例资源定义 */
export interface CaseStudyResource {
  id: string;
  type: ResourceType.CASE_STUDY;
  metadata: ResourceMetadata & {
    therapyModel: string;
    sessionsCount: number;
    durationMonths: number;
    demographics: {
      ageRange: string; // "13-17"
      gender: 'male' | 'female' | 'unspecified';
      issueType: string;
    };
  };
  content: {
    clinicalData: {
      assessmentTools: string[];
      baselineScores: Record<string, number>;
      outcomeScores: Record<string, number>;
      improvementRate: number;
    };
    interventionSteps: {
      phase: string;
      techniques: string[];
    }[];
    ethicalFlags: {
      informedConsent: boolean;
      anonymizationLevel: string; // "A+"
    };
    learningFeatures: {
      discussionQuestions: string[];
      relatedCourses: string[]; // 相关课程ID
    };
  };
  //   approval: ResourceApproval;
}

/** 话术资源定义 */
export interface PhraseResource {
  id: string;
  type: ResourceType.PHRASE;
  metadata: ResourceMetadata & {
    scenarios: string[]; // 使用场景
    therapyModel: string;
  };
  content: {
    text: string;
    variations?: {
      scenario: string;
      content: string;
    }[];
  };
  //   approval: ResourceApproval;
}

/** 督导资源定义 */
export interface SupervisionResource {
  id: string;
  type: ResourceType.SUPERVISION;
  metadata: ResourceMetadata;
  content: {
    credentials: string[];
    specialties: string[];
    serviceTypes: ('individual' | 'group' | 'emergency')[];
    pricing: {
      individual: number; // 元/小时
      group: number;
    };
    availability: {
      type: 'recurring' | 'ad_hoc';
      pattern?: 'weekly' | 'biweekly'; // 定期模式
      weekday?: number; // 0-6 (周日到周六)
      date?: number; // 特定日期 unixtime秒
      slots: string[]; // 时间段 ["14:00-15:30"]
    }[];
    emergencyPolicy?: {
      responseTime: number; // 小时
      surcharge: number; // 加价比例 0.5 = 50%
    };
  };
  //   approval: ResourceApproval;
}

/** 资源联合类型 */
export type ResourceDefinition =
  | CourseResource
  | CaseStudyResource
  | PhraseResource
  | SupervisionResource;

// ====================== 学习记录接口 ======================

/** 学习交互动作类型 */
export enum LearningAction {
  START_SECTION = 'start_section',
  COMPLETE_SECTION = 'complete_section',
  VIEW = 'view',
  COPY_TO_SESSION = 'copy_to_session',
  START_SESSION = 'start_session',
}

/** 学习交互记录 */
export interface LearningInteraction {
  timestamp: number; // 时间戳 unixtime秒
  action: LearningAction;
  sectionId?: string; // 课程专用
  duration?: number; // 分钟
  sessionId?: string; // 关联咨询会话
}

/** 用户生成数据 */
export interface UserGeneratedData {
  notes?: {
    sectionId?: string;
    content: string;
    timestamp: number; // 时间戳 unixtime秒
  }[];
  rating?: number; // 1-5
  insights?: string;
  appliedInPractice?: boolean;
  customVariation?: string; // 话术定制
  effectivenessRating?: number; // 话术效果评分
  casePrep?: {
    // 督导专用
    caseId: string;
    focusAreas: string[];
  };
  keyLearnings?: string[]; // 督导专用
  actionItems?: string[]; // 督导专用
}

/** 学习进度 */
export interface LearningProgress {
  completedSections?: string[]; // 课程专用
  completionRate?: number; // 0-1
}

/** 基础学习记录 */
interface BaseLearningRecord {
  id: string;
  consultantId: string;
  resourceId: string;
  resourceType: ResourceType;
  interactions: LearningInteraction[];
  progress: LearningProgress;
  userData: UserGeneratedData;
  createdAt: number; // 创建时间 unixtime秒
}

/** 课程学习记录 */
export interface CourseLearningRecord extends BaseLearningRecord {
  resourceType: ResourceType.COURSE;
}

/** 案例学习记录 */
export interface CaseLearningRecord extends BaseLearningRecord {
  resourceType: ResourceType.CASE_STUDY;
}

/** 话术使用记录 */
export interface PhraseLearningRecord extends BaseLearningRecord {
  resourceType: ResourceType.PHRASE;
}

/** 督导参与记录 */
export interface SupervisionRecord extends BaseLearningRecord {
  resourceType: ResourceType.SUPERVISION;
}

/** 学习记录联合类型 */
export type LearningRecord =
  | CourseLearningRecord
  | CaseLearningRecord
  | PhraseLearningRecord
  | SupervisionRecord;

// ====================== 统计聚合类型 ======================

/** 资源类型统计 */
export interface ResourceTypeStats {
  hours?: number; // 学习时长
  count?: number; // 使用次数
  completionRate?: number; // 完成率
  avgStudyTime?: number; // 平均学习时长
}

/** 时间段统计 */
export interface TimePeriodStats {
  coursesCompleted: number;
  casesStudied: number;
  supervisionHours: number;
}

/** 能力领域评估 */
export interface CompetencyDomain {
  domain: string;
  score: number; // 百分制
  benchmark: number; // 行业基准
  growth: number; // 较上期变化
  recommendedResources: {
    type: ResourceType;
    id: string;
  }[];
}

/** 学习统计聚合 */
export interface LearningStatistics {
  consultantId: string;
  last30d: TimePeriodStats;
  last90d: TimePeriodStats;
  byResourceType: Record<ResourceType, ResourceTypeStats>;
  competencyMatrix: CompetencyDomain[];
  generatedAt: number; // 生成时间 unixtime秒
}

// ====================== 咨询师学习档案 ======================

/** 咨询师学习档案 */
export interface ConsultantLearningProfile {
  consultantId: string;
  currentLevel: string; // "中级咨询师"
  certifications: string[]; // ["CBT", "家庭治疗"]
  learningGoals: string[]; // ["创伤治疗", "儿童心理咨询"]
  learningPlan: {
    weeklyGoal: number; // 每周学习小时数
    currentStreak: number; // 连续学习天数
  };
  statistics: {
    courseHours: number;
    caseStudies: number;
    supervisionSessions: number;
  };
}

// ====================== API 响应类型 ======================

/** 分页响应结构 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    current: number;
    pageSize: number;
    hasMore: boolean;
  };
}

/** 资源列表响应 */
export type ResourceListResponse = PaginatedResponse<ResourceDefinition>;

/** 学习记录响应 */
export type LearningRecordResponse = PaginatedResponse<LearningRecord>;

// ====================== 实用工具类型 ======================

/** 按资源类型筛选 */
export type FilterByResourceType<T, U extends ResourceType> = T extends {
  resourceType: U;
}
  ? T
  : never;

/** 获取特定类型资源 */
export type SpecificResource<T extends ResourceType> = Extract<
  ResourceDefinition,
  { type: T }
>;

/** 获取特定类型学习记录 */
export type SpecificLearningRecord<T extends ResourceType> = Extract<
  LearningRecord,
  { resourceType: T }
>;

// 学习卡片类型枚举
export enum LearningCardType {
  ACTIVE_COURSE = 'active_course', // 进行中的课程
  RECOMMENDED_RESOURCE = 'recommended_resource', // 推荐资源
  UPCOMING_DEADLINE = 'upcoming_deadline', // 即将截止
  NEWLY_ADDED = 'newly_added', // 新添加资源
}

// 学习卡片基础接口
export interface LearningCardBase {
  id: string;
  type: LearningCardType;
  resourceId: string;
  resourceType: ResourceType;
  title: string; // 主标题（必选）
  subtitle?: string; // 副标题（可选）
  description?: string; // 描述（可选）

  // 元数据（最多展示3-4项）
  metadata: {
    // 通用元数据
    duration?: number; // 总时长（分钟）
    sectionsCount?: number; // 章节/课时数
    difficulty?: DifficultyLevel; // 难度级别

    // 进度相关（仅对进行中任务有效）
    progress?: number; // 进度百分比（0-100）
    lastAccessed?: number; // 最后学习时间 unixtime秒
    deadline?: number; // 截止日期（如果有）unixtime秒

    // 推荐相关
    recommendationReason?: string; // 推荐原因
    relevanceScore?: number; // 相关性分数
  };

  // 操作相关
  actions?: {
    primary?: string; // 主要操作（如"继续学习"）
    secondary?: string; // 次要操作（如"查看详情"）
  };

  // 视觉元素
  visual?: {
    thumbnail?: string; // 缩略图URL
    icon?: string; // 资源类型图标 课程：book 案例：case 话术：phrase 督导：supervision
    color?: string; // 卡片主题色
  };

  createdAt: number; // 卡片创建时间 unixtime秒
  updatedAt: number; // 最后更新时间 unixtime秒
}

// 进行中课程卡片（专属字段）
export interface ActiveCourseCard extends LearningCardBase {
  type: LearningCardType.ACTIVE_COURSE;
  metadata: LearningCardBase['metadata'] & {
    progress: number; // 进度百分比（必选） 0-100
    currentSection: string; // 当前章节名称
    nextSection?: string; // 下一章节名称
    timeSpent: number; // 已学习时长（分钟）
    estimatedCompletion?: number; // 预计完成时间 unixtime秒
  };
}

// 推荐资源卡片（专属字段）
export interface RecommendedResourceCard extends LearningCardBase {
  type: LearningCardType.RECOMMENDED_RESOURCE;
  metadata: LearningCardBase['metadata'] & {
    recommendationReason: string; // 推荐原因（必选）
    popularity?: number; // 受欢迎程度（0-5）
    rating?: number; // 评分（1-5）
    tags?: string[]; // 标签
    enrolledCount?: number; // 已学习人数
  };
}

// 即将截止卡片（专属字段）
export interface UpcomingDeadlineCard extends LearningCardBase {
  type: LearningCardType.UPCOMING_DEADLINE;
  metadata: LearningCardBase['metadata'] & {
    deadline: number; // 截止日期（必选） unixtime秒
    daysLeft: number; // 剩余天数
    progress: number; // 进度百分比
    importance: 'high' | 'medium' | 'low'; // 重要性
  };
}

// 新添加资源卡片（专属字段）
export interface NewlyAddedCard extends LearningCardBase {
  type: LearningCardType.NEWLY_ADDED;
  metadata: LearningCardBase['metadata'] & {
    addedAt: number; // 添加时间（必选） unixtime秒
    daysSinceAdded: number; // 已添加天数
    viewed: boolean; // 是否已查看
  };
}

// 学习卡片联合类型
export type LearningCard =
  | ActiveCourseCard
  | RecommendedResourceCard
  | UpcomingDeadlineCard
  | NewlyAddedCard;

/** 首页推荐卡片数据结构示例
```const homeRecommendationCards: LearningCard[] = [
  {
    id: 'rec-001',
    type: LearningCardType.RECOMMENDED_RESOURCE,
    resourceId: 'course-205',
    resourceType: ResourceType.COURSE,
    title: '创伤后应激障碍(PTSD)干预技术',
    subtitle: '高级课程 · 中国心理学会',
    description: '掌握PTSD核心干预技术，提高危机处理能力',
    metadata: {
      duration: 510, // 8.5小时
      sectionsCount: 12,
      difficulty: DifficultyLevel.ADVANCED,
      recommendationReason: "根据您的学习目标'创伤治疗'推荐",
      popularity: 4.8,
      rating: 4.7,
      enrolledCount: 142,
    },
    actions: {
      primary: '立即学习',
      secondary: '查看详情',
    },
    visual: {
      thumbnail: 'https://example.com/thumbs/ptsd-course.jpg',
      icon: 'course-icon',
    },
    createdAt: 1719859200,
    updatedAt: 1719859200,
  },
  {
    id: 'active-003',
    type: LearningCardType.ACTIVE_COURSE,
    resourceId: 'course-101',
    resourceType: ResourceType.COURSE,
    title: '认知行为疗法基础',
    subtitle: '中级课程 · 已完成65%',
    description: '掌握CBT核心技术与应用场景',
    metadata: {
      progress: 0.65,
      duration: 360,
      sectionsCount: 8,
      currentSection: '认知重构技术',
      nextSection: '行为实验设计',
      timeSpent: 234,
      estimatedCompletion: 1719859200,
    },
    actions: {
      primary: '继续学习',
      secondary: '学习计划',
    },
    visual: {
      thumbnail: 'https://example.com/thumbs/cbt-course.jpg',
      color: '#2AA9B0',
    },
    createdAt: 1719859200,
    updatedAt: 1719859200,
  },
];
```
*/

/**  学习培训页面卡片数据结构示例
```
const learningPageCards: LearningCard[] = [
  {
    id: "active-101",
    type: LearningCardType.ACTIVE_COURSE,
    resourceId: "course-205",
    resourceType: ResourceType.COURSE,
    title: "创伤后应激障碍(PTSD)干预技术",
    subtitle: "高级课程 · 进度 35%",
    description: "当前章节：PTSD诊断标准",
    metadata: {
      progress: 0.35,
      duration: 510,
      sectionsCount: 12,
      currentSection: "PTSD诊断标准",
      nextSection: "临床实操演练",
      timeSpent: 180,
      estimatedCompletion: 1719859200,
    },
    actions: {
      primary: "继续学习",
      secondary: "学习笔记"
    },
    visual: {
      thumbnail: "https://example.com/thumbs/ptsd-course.jpg",
      color: "#FF9800"
    },
    createdAt: 1719859200,
    updatedAt: 1719859200,
  },
  {
    id: "deadline-202",
    type: LearningCardType.UPCOMING_DEADLINE,
    resourceId: "course-301",
    resourceType: ResourceType.COURSE,
    title: "儿童心理咨询认证课程",
    subtitle: "截止日期临近",
    description: "完成全部课程以获得认证",
    metadata: {
      deadline: 1719859200,
      daysLeft: 9,
      progress: 0.78,
      duration: 600,
      importance: "high"
    },
    actions: {
      primary: "立即完成",
      secondary: "查看进度"
    },
    visual: {
      icon: "deadline-icon",
      color: "#F44336"
    },
    createdAt: 1719859200,
    updatedAt: 1719859200,
  }
];
```
*/
