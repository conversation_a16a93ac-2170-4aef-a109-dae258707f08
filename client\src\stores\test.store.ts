// stores/testStore.ts
import {
  PsychologicalTest,
  UserAnswer,
  UserTestRecord,
} from '@model/test.model';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useGlobalStore } from './global.store';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  testList: PsychologicalTest[]; // 测试列表
  currentTest: PsychologicalTest | null; // 当前进行中的测试
  currentQuestionIndex: number; // 当前题目索引
  activeRecord: UserTestRecord | null; // 当前测试记录
  testHistory: UserTestRecord[]; // 历史测试记录
  loading: boolean; // 加载状态
  error: Error | null; // 错误信息
}

interface Action {
  getTest: (testId: string) => PsychologicalTest | null;
  setTestList: (testList: PsychologicalTest[]) => void;
  startTest: (test: PsychologicalTest) => void;
  answerQuestion: (answer: UserAnswer) => void;
  goToQuestion: (index: number) => void;
  clearCurrentTest: () => void;
  setCurrentTest: (test: PsychologicalTest | null) => void;
  setActiveRecord: (record: UserTestRecord | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
}

const initialState: State = {
  testList: [],
  currentTest: null,
  currentQuestionIndex: 0,
  activeRecord: null,
  testHistory: [],
  loading: false,
  error: null,
};

const testStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setTestList: (testList) => {
          set((state) => {
            state.testList = testList;
          });
        },
        getTest: (testId) => {
          return get().testList.find((test) => test.id === testId) || null;
        },
        // 开始新测试
        startTest: (test) => {
          set((state) => {
            state.currentTest = test;
            state.currentQuestionIndex = 0;
            state.activeRecord = {
              id: `record_${Date.now()}`,
              userId: useGlobalStore.getState().openid, // 实际应用中替换为真实用户ID
              startTime: Date.now(),
              reportViewed: false,
              report: {
                recordId: `record_${Date.now()}`,
                testId: test.id,
                testTitle: test.title,
                answers: [],
                result: {
                  score: 0,
                  dimensionScores: [],
                },
              },
            } as UserTestRecord;
          });
        },

        // 回答问题
        answerQuestion: (answer) => {
          set((state) => {
            // 更新当前记录
            if (state.activeRecord) {
              const recordIndex = state.activeRecord.report.answers.findIndex(
                (a) => a.questionId === answer.questionId
              );
              if (recordIndex !== -1) {
                state.activeRecord.report.answers[recordIndex] = answer;
              } else {
                state.activeRecord.report.answers.push(answer);
              }
            }
          });
        },

        // 跳转到指定题目
        goToQuestion: (index) => {
          set((state) => {
            if (
              state.currentTest &&
              index >= 0 &&
              index < state.currentTest.questions.length
            ) {
              state.currentQuestionIndex = index;
            }
          });
        },

        // 清除当前测试
        clearCurrentTest: () => {
          set((state) => {
            state.currentTest = null;
            state.currentQuestionIndex = 0;
            state.activeRecord = null;
          });
        },

        // 设置当前测试
        setCurrentTest: (test) => {
          set((state) => {
            state.currentTest = test;
          });
        },

        // 设置当前测试记录
        setActiveRecord: (record) => {
          set((state) => {
            state.activeRecord = record;
          });
        },

        // 设置加载状态
        setLoading: (loading) => {
          set((state) => {
            state.loading = loading;
          });
        },

        // 设置错误信息
        setError: (error) => {
          set((state) => {
            state.error = error;
          });
        },
      }),
      {
        name: StorageSceneKey.TEST,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          testHistory: state.testHistory,
        }),
      }
    )
  )
);

// 添加选择器
export const useTestStore = createSelectors(testStore);
