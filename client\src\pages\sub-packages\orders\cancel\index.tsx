import PageLol from '@components/common/page-meta';
import ReasonForm from '@components/common/reason-form';
import ResultPopup from '@components/common/result-popup';
import { ILLUSTRATION_MAP } from '@constants/assets';
import { SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_CANCEL } from '@constants/config';
import { CANCEL_REASON_MAP } from '@constants/text';
import { orderUserActions } from '@core/actions/order.user';
import { CANCEL_REASON, ORDER_STATUS } from '@model/order.interface';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import './index.less';

export default function CancelPage() {
  const router = useRouter();
  const { orderId } = router.params;
  console.log('CancelPage orderId', orderId, typeof orderId);

  //订阅订单的状态
  const orderStatus = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId ?? '')?.status)
  );

  // 记录订单初始状态
  const initialOrderStatus = useRef(orderStatus);

  const [selectedReason, setSelectedReason] = useState<CANCEL_REASON | null>(
    null
  );
  const [detail, setDetail] = useState<string | null>(null);
  const [resultPopup, setResultPopup] = useState({
    show: false,
    type: 'success' as 'success' | 'error' | 'warning',
    title: '',
    content: '',
    illustration: '',
  });
  const [localError, setLocalError] = useState<string | null>(null);
  const { transactionLoading } = useLoadingStore();

  // 使用useEffect检查订单ID和状态
  useEffect(() => {
    if (!orderId) {
      console.error('订单ID不存在', orderId);
      setLocalError('订单ID不存在');
      return;
    }

    if (
      orderStatus !== ORDER_STATUS.PENDING_PAYMENT &&
      orderStatus !== ORDER_STATUS.PENDING_CONFIRM &&
      orderStatus !== ORDER_STATUS.PENDING_START
    ) {
      setLocalError('订单当前状态无法取消');
    } else {
      setLocalError(null);
    }
  }, [orderId, orderStatus]);

  const handleSubmit = async () => {
    try {
      // 如果已付款，先订阅付款通知
      if (initialOrderStatus.current !== ORDER_STATUS.PENDING_PAYMENT) {
        await Taro.requestSubscribeMessage({
          tmplIds: SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_CANCEL,
          entityIds: [],
        });
      }
      const result = await orderUserActions.cancelOrder(
        orderId!,
        selectedReason!,
        detail ?? ''
      );
      if (result.success && !result.warning) {
        // 显示成功结果
        setResultPopup({
          show: true,
          type: 'success',
          title: '取消成功',
          content:
            initialOrderStatus.current !== ORDER_STATUS.PENDING_PAYMENT
              ? '您的订单已成功取消，退款将在24小时内到账'
              : '您的订单已成功取消',
          illustration: ILLUSTRATION_MAP.COMMON_COMPLETE,
        });
      } else if (result.success && result.warning) {
        setResultPopup({
          show: true,
          type: 'warning',
          title: '订单已取消',
          content: result.warning || result.error || result.message,
          illustration: ILLUSTRATION_MAP.COMMON_WARNING,
        });
      } else {
        setResultPopup({
          show: true,
          type: 'error',
          title: '取消失败',
          content: result.error || result.message,
          illustration: ILLUSTRATION_MAP.COMMON_FAILED,
        });
      }
    } catch (e) {
      console.error('取消订单失败', e);
      setResultPopup({
        show: true,
        type: 'error',
        title: '取消失败',
        content: '取消订单失败，请稍后重试',
        illustration: ILLUSTRATION_MAP.COMMON_FAILED,
      });
    }
  };

  // 关闭结果弹窗
  const handleClosePopup = () => {
    console.log('🚀🚀🚀 handleClosePopup');
    //重定向到订单列表
    setTimeout(() => {
      Taro.navigateBack();
    }, 300); // 延迟300毫秒，等待弹窗关闭动画完成
  };

  // 警告提示内容
  const warningContent = (
    <View className='mx-4 my-5 p-4 bg-warning rounded-lg bg-warning'>
      <Text className='text-sm text-white leading-relaxed'>
        下单成功24小时以后订单不能取消，但是如果您遭遇不可抗力无法履约，请选择不可抗力选项，并填写详细情况，平台审核通过后，予以取消订单并退款
      </Text>
    </View>
  );

  return (
    <PageLol
      navigationProps={{ title: '取消订单', showBackButton: true }}
      error={localError || null}
    >
      <View className='cancel-page'>
        <ReasonForm
          title='请选择取消原因'
          reasonOptions={CANCEL_REASON_MAP}
          selectedReason={selectedReason}
          detail={detail}
          onReasonChange={(value) => setSelectedReason(value as CANCEL_REASON)}
          onDetailChange={(value) => setDetail(value)}
          onSubmit={handleSubmit}
          loading={transactionLoading}
          placeholder='请详细描述取消原因'
          headerContent={warningContent}
        />
      </View>

      <ResultPopup
        show={resultPopup.show}
        type={resultPopup.type}
        illustration={resultPopup.illustration}
        title={resultPopup.title}
        content={resultPopup.content}
        buttonText='确定'
        onButtonClick={handleClosePopup}
        onClose={handleClosePopup}
      />
    </PageLol>
  );
}
