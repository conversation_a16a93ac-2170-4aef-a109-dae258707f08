import { OrderListParams, OrderListRequest } from '@core/api';
import { ORDER_STATUS, REFUND_STATUS } from '@model/order.interface';
import { USER_ROLE } from '@model/user.interface';
import {
  useOrderStoreSelector,
  useTherapistOrderStoreSelector,
} from '@stores/order.store';
import { getCurrentRole, getOrderActions } from '@utils/role';
import { useCallback, useEffect, useRef } from 'react';

/**
 * 订单列表管理Hook
 * 提供订单列表的获取、筛选和状态监听功能
 */
export function useOrderList(initialParams: { params?: OrderListParams } = {}) {
  console.log('useOrderList_ai', initialParams);
  const currentRole = getCurrentRole();
  const useStore =
    currentRole === USER_ROLE.THERAPIST
      ? useTherapistOrderStoreSelector
      : useOrderStoreSelector;

  const orders = useStore.use.orders();
  const pagination = useStore.use.pagination();
  const filters = useStore.use.filters();
  const setFilters = useStore.use.setFilters();
  const loading = useStore.use.loading();

  // 使用ref保存请求参数，避免依赖变化导致函数重建
  const requestParamsRef = useRef({
    filters: initialParams.params,
  });

  // 更新ref值
  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchOrders = useCallback(async (params: OrderListRequest) => {
    console.log('useOrderList fetchOrders', params);
    return getOrderActions().fetchOrders(params);
  }, []);

  // 刷新列表
  const refreshOrders = useCallback(async () => {
    console.log('useOrderList refreshOrders', requestParamsRef.current.filters);
    try {
      await fetchOrders({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
        forceRefresh: true,
      });
    } catch (error) {
      console.error('刷新订单失败:', error);
    }
  }, [fetchOrders, pagination?.pageSize]);

  // 加载更多
  const loadMoreOrders = useCallback(async () => {
    console.log(
      'useOrderList loadMoreOrders',
      requestParamsRef.current.filters
    );
    if (!pagination?.hasNext) return;

    try {
      await getOrderActions().loadMore();
    } catch (error) {
      console.error('加载更多订单失败:', error);
    }
  }, [pagination?.hasNext]);

  // 搜索订单
  const onSearch = useCallback(
    async (searchWord: string) => {
      console.log('useOrderList onSearch', searchWord);
      const newFilters = {
        ...requestParamsRef.current.filters,
        query: searchWord,
      };

      setFilters(newFilters);

      try {
        await fetchOrders({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error('搜索订单失败:', error);
      }
    },
    [setFilters, fetchOrders, pagination?.pageSize]
  );

  // 筛选订单状态（更新支持退款状态）
  const onStatusChange = useCallback(
    async (
      status: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][] = [],
      complaint: boolean = false,
      refundStatus: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS][] = []
    ) => {
      console.log(
        'useOrderList onStatusChange',
        status,
        complaint,
        refundStatus
      );
      const newFilters = {
        ...requestParamsRef.current.filters,
        status,
        refundStatus,
        complaint,
      };

      setFilters(newFilters);

      try {
        await fetchOrders({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error('筛选订单失败:', error);
      }
    },
    [setFilters, fetchOrders, pagination?.pageSize]
  );

  // 初始加载
  useEffect(() => {
    const watcher = getOrderActions().watchOrders();
    return () => {
      if (watcher) {
        watcher.close();
      }
    };
  }, []); // 只在组件挂载时执行一次

  return {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    onStatusChange,
    onSearch,
  };
}
