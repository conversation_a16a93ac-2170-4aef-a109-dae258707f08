import {
  PsychologicalTestSummary,
  RecommendationCard,
} from '@model/measure.model';
import { TestCategory, UserTestRecord } from '@model/test.model';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  featuredCards: RecommendationCard[]; // 推荐卡片列表
  categories: { [key in TestCategory]: PsychologicalTestSummary[] };
  recentHistory: UserTestRecord[];
  lastUpdated: number; // 最后更新时间戳
  loading: boolean;
  error: Error | null;
}

interface Action {
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
  setCategories: (categories: {
    [key in TestCategory]: PsychologicalTestSummary[];
  }) => void;
  setFeaturedCards: (featuredCards: RecommendationCard[]) => void;
  setRecentHistory: (recentHistory: UserTestRecord[]) => void;
}

const initialState: State = {
  featuredCards: [],
  categories: {
    all: [],
    emotion: [],
    personality: [],
    stress: [],
    sleep: [],
    relationship: [],
    career: [],
    addiction: [],
    trauma: [],
    general: [],
    other: [],
  },
  recentHistory: [],
  lastUpdated: 0,
  loading: false,
  error: null,
};

const measureStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        setCategories: (categories) => set({ categories }),
        setFeaturedCards: (featuredCards) => set({ featuredCards }),
        setRecentHistory: (recentHistory) => set({ recentHistory }),
      }),
      {
        name: StorageSceneKey.MEASURE,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 用户列表
        partialize: (state) => ({
          featuredCards: state.featuredCards,
          categories: state.categories,
          recentHistory: state.recentHistory,
          lastUpdated: state.lastUpdated,
        }),
      }
    )
  )
);

export const useMeasureStore = createSelectors(measureStore);
