import { QRCodeDialog } from "@/components/shared/qrcode-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { convertCloudImageUrl } from "@/lib/utils";
import { Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export interface TherapistCardProps {
  id: string;
  name: string;
  avatar?: string;
  title: string;
  specialties: string[];
  price: number;
  rating: number;
  reviewCount: number;
  experience: string;
}

export function TherapistCard({
  id,
  name,
  avatar,
  title,
  specialties,
  price,
  rating,
  reviewCount,
  experience,
}: TherapistCardProps) {
  return (
    <Card className="overflow-hidden group cursor-pointer hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <Link
          href={`/therapists/${id}`}
          target="_blank"
          rel="noopener noreferrer"
          className="block"
        >
          <div className="aspect-[3/2] relative bg-muted">
            <div className="absolute inset-0 flex items-center justify-center text-sm text-muted-foreground">
              <Image src={convertCloudImageUrl(avatar ?? "")} alt={name} fill />
            </div>
          </div>
          <div className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold group-hover:underline">{name}</h3>
              <span className="text-sm text-muted-foreground">{title}</span>
            </div>
            <div className="flex flex-wrap gap-2 mb-3">
              {specialties.map((specialty) => (
                <Badge key={specialty} variant="secondary">
                  {specialty}
                </Badge>
              ))}
            </div>
            <div className="text-sm text-muted-foreground mb-3">
              咨询经验：{experience}
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-primary text-primary" />
                <span className="text-sm font-medium">{rating}</span>
                <span className="text-xs text-muted-foreground">
                  ({reviewCount}评价)
                </span>
              </div>
              <div className="text-sm font-semibold">¥{price}/次</div>
            </div>
          </div>
        </Link>
        <div className="p-4 pt-0">
          <QRCodeDialog title="扫码预约咨询">
            <Button variant="outline" className="w-full">
              立即预约
            </Button>
          </QRCodeDialog>
        </div>
      </CardContent>
    </Card>
  );
}
