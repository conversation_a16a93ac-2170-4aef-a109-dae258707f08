// client/src/hooks/useOrderDetailForTherapist.ts
import { orderTherapistActions } from '@core/actions/order.therapist';
import { therapistActions } from '@core/actions/therapist.action';
import { userActions } from '@core/actions/user.action';
import { useTherapistOrderStoreSelector } from '@stores/order.store';
import { useReviewStore } from '@stores/review.store';
import { useUserStore } from '@stores/user.store';
import { useCallback, useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

/**
 * 订单详情管理Hook
 * 提供订单详情的获取和状态监听功能
 */
export function useOrderDetailForTherapist(orderId: string) {
  const order = useTherapistOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId || ''))
  );
  const user = useUserStore(
    useShallow((state) =>
      state.userList?.find((item) => item.id === order?.userId)
    )
  );
  const orderActions = useTherapistOrderStoreSelector(
    useShallow((state) =>
      state.orderActions.filter((item) => item.orderId === orderId)
    )
  );
  const orderReview = useReviewStore(
    useShallow((state) =>
      state.reviews.filter((item) => item.orderId === orderId)
    )
  );
  // 本地状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 封装数据获取逻辑
  const fetchOrderData = useCallback(async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const orderSummary = await orderTherapistActions.fetchOrderById(
        orderId,
        false
      );
      if (!orderSummary) {
        return;
      }
      await Promise.all([
        orderTherapistActions.fetchOrderActions(orderId, true),
        userActions.fetchUserPublic(orderSummary.userId),
        therapistActions.fetchTherapistPreferRating(
          orderSummary.therapistId,
          true
        ),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败');
    } finally {
      setIsLoading(false);
    }
  }, [orderId]);

  /**
   * 刷新订单详情
   */
  const refresh = useCallback(() => {
    return fetchOrderData();
  }, [fetchOrderData]);

  // 初始加载
  useEffect(() => {
    if (!orderId) return;

    fetchOrderData();

    // 启动监听
    const watcher = orderTherapistActions.watchOrderById(orderId);

    // 组件卸载时清理
    return () => {
      watcher.close();
    };
  }, [orderId, fetchOrderData]);

  return {
    order,
    orderActions,
    user,
    orderReview,
    refresh,
    isLoading,
    error,
  };
}
