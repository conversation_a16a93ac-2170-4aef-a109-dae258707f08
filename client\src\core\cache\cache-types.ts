/**
 * 缓存选项配置接口
 */
export interface CacheOptions {
  /** 缓存生存时间(毫秒) */
  ttl: number;

  /** 是否在后台自动刷新数据 */
  backgroundRefresh: boolean;

  /** 获取缓存键的函数 */
  getCacheKey: (...args: any[]) => string;

  /** 获取缓存版本的函数，用于版本控制 */
  getVersion: () => string;

  /** 是否忽略缓存错误 */
  ignoreErrors: boolean;
}

/**
 * 缓存数据结构
 */
export interface CacheData<T> {
  /** 缓存的数据 */
  data: T;
  /** 缓存时间戳 */
  timestamp: number;
  /** 过期时间戳 */
  expireAt: number;
  /** 缓存版本 */
  version: string;
}

/**
 * 缓存状态
 */
export enum CacheState {
  /** 缓存不存在 */
  NOT_EXIST = 'NOT_EXIST',
  /** 缓存已过期 */
  EXPIRED = 'EXPIRED',
  /** 缓存有效 */
  VALID = 'VALID',
}

/**
 * 缓存刷新策略
 */
export enum CacheRefreshStrategy {
  /** 不刷新 */
  NONE = 'NONE',

  /** 后台刷新 */
  BACKGROUND = 'BACKGROUND',

  /** 强制刷新 */
  FORCE = 'FORCE',
}

/**
 * 缓存策略配置
 */
export interface CachePolicy<T = any> {
  /** 缓存键前缀 */
  keyPrefix: string;
  /** 缓存有效期（毫秒） */
  ttl: number;
  /** 缓存版本 */
  version: string;
  /** 获取缓存键 */
  getCacheKey: (params: T) => string;
  /** 获取缓存版本 */
  getVersion: () => string;
}
