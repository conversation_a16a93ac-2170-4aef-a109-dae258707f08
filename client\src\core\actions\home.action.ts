import { BaseCacheService } from '@core/cache/base-cache.service';
import {
  recommendedCoursesCacheOptions,
  recommendedTherapistsCacheOptions,
} from '@core/cache/therapist-cache-policy';
import { ConsultationDirection } from '@model/common.interface';
import { homeService } from '@services/home.service';
import { useHomeStore } from '@stores/home.store';

export const homeActions = {
  fetchBanners: async (to: 'user' | 'therapist') => {
    try {
      const result = await homeService.fetchBanners(to);
      useHomeStore.setState({ banners: result });
      return result;
    } catch (error) {
      console.error('获取Banner失败:', error);
    }
  },
  fetchNotices: async (to: 'user' | 'therapist') => {
    try {
      const result = await homeService.fetchNotices(to);
      useHomeStore.setState({ notices: result });
      return result;
    } catch (error) {
      console.error('获取公告失败:', error);
    }
  },

  /**
   * 获取推荐咨询师列表
   * @param direction 心理咨询方向
   * @param limit 返回数量限制
   */
  fetchRecommendedTherapists: async (
    direction?: ConsultationDirection,
    forceRefresh: boolean = false,
    limit: number = 10
  ) => {
    try {
      console.log('🚀🚀🚀 fetchRecommendedTherapists direction', direction);
      // 生成缓存键
      const cacheKey = recommendedTherapistsCacheOptions.getCacheKey(direction);

      if (!forceRefresh) {
        // 尝试从缓存获取数据
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          useHomeStore.setState({ recommendedTherapists: cachedData });
          return cachedData;
        }
      }

      // 缓存未命中或强制刷新，从服务获取数据
      useHomeStore.setState({ loading: true });
      const therapists = await homeService.readRecommendedTherapists(
        direction,
        limit
      );

      // 缓存数据
      if (therapists && therapists.length > 0) {
        // 缓存id列表
        BaseCacheService.set(
          cacheKey,
          therapists,
          recommendedTherapistsCacheOptions.ttl,
          recommendedTherapistsCacheOptions.getVersion()
        );
        useHomeStore.setState({ recommendedTherapists: therapists });

        console.log(
          '🚀🚀🚀 fetchRecommendedTherapists therapists save to store',
          therapists.map((therapist) => therapist.id)
        );
        return therapists;
      }
    } catch (error) {
      console.error('获取推荐咨询师列表失败:', error);
    } finally {
      useHomeStore.getState().setLoading(false);
    }
  },
  fetchOrderToday: async () => {
    try {
      const result = await homeService.fetchOrderToday();
      useHomeStore.setState({ orderToday: result });
      return result;
    } catch (error) {
      console.error('获取订单统计失败:', error);
    }
  },
  fetchRecommendedCourses: async (forceRefresh: boolean = false) => {
    const cacheKey = recommendedCoursesCacheOptions.getCacheKey();
    const cachedData = BaseCacheService.get(cacheKey, null);
    if (cachedData && !forceRefresh) {
      useHomeStore.setState({ recommendedCourses: cachedData });
      return cachedData;
    }
    try {
      const result = await homeService.fetchRecommendedCourses();
      useHomeStore.setState({ recommendedCourses: result });
      BaseCacheService.set(
        cacheKey,
        result,
        recommendedCoursesCacheOptions.ttl,
        recommendedCoursesCacheOptions.getVersion()
      );
      return result;
    } catch (error) {
      console.error('获取推荐课程失败:', error);
    }
  },
};
