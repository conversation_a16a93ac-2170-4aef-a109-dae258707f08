import { Order_summary } from '@model/order.interface';
import { Text, View } from '@tarojs/components';
import { formatTime4 } from '@utils/time';
import React from 'react';
import OrderSwipeCard from './OrderSwipeCard';

interface OrderTimelineItemProps {
  order: Order_summary;
}

const OrderTimelineItem: React.FC<OrderTimelineItemProps> = ({ order }) => {
  return (
    <View className='relative flex flex-row items-stretch min-h-[80Px]'>
      {/* 时间 */}
      <View className='w-12 flex  flex-col items-center justify-start'>
        <Text className='text-md text-secondary font-medium'>
          {formatTime4(order.startTime)}
        </Text>
      </View>
      {/* 指示条 */}
      <View className='flex flex-col items-center mr-4 pt-2'>
        {/* 节点 */}
        <View className='w-2 h-2 rounded-full bg-primary z-10' />
        {/* 竖线 */}
        <View className='border border-border flex-1 mt-1' />
      </View>
      {/* 订单卡片 */}
      <View className='flex-1'>
        <OrderSwipeCard order={order} />
      </View>
    </View>
  );
};

export default OrderTimelineItem;
