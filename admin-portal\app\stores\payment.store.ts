import { WithdrawRecord } from '@model/payment.interface';
import { create } from 'zustand';
import createSelectors from './libs/selector';

/**
 * 支付结果状态
 */
export enum PaymentResultStatus {
  Success = 'success',
  Failed = 'failed',
}

interface PaymentState {
  loading: boolean;
  error: string | null;
  payStatus: PaymentResultStatus | null;
  payResult: string | null;
  withdrawRecords: {
    list: WithdrawRecord[];
    total: number;
    loading: boolean;
    error: Error | null;
    page: number;
    pageSize: number;
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    } | null;
  };
}

interface PaymentActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setPayStatus: (payStatus: PaymentResultStatus) => void;
  setPayResult: (payResult: string) => void;
  setWithdrawRecords: (withdrawRecords: WithdrawRecord[]) => void;
  setWithdrawRecordsWithPagination: (data: {
    list: WithdrawRecord[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }) => void;
  resetPayment: () => void;
}

const initialState: PaymentState = {
  loading: false,
  error: null,
  payStatus: null,
  payResult: null,
  withdrawRecords: {
    list: [],
    total: 0,
    loading: false,
    error: null,
    page: 1,
    pageSize: 10,
    pagination: null,
  },
};

const paymentStore = create<PaymentState & PaymentActions>((set) => ({
  ...initialState,
  setLoading: (loading: boolean) => set({ loading }),
  setError: (error: string | null) => set({ error }),
  setPayStatus: (payStatus: PaymentResultStatus) => set({ payStatus }),
  setPayResult: (payResult: string) => set({ payResult }),
  setWithdrawRecords: (withdrawRecords: WithdrawRecord[]) =>
    set((state) => ({
      withdrawRecords: {
        ...state.withdrawRecords,
        list: withdrawRecords,
        total: withdrawRecords.length,
      },
    })),
  setWithdrawRecordsWithPagination: (data) =>
    set((state) => ({
      withdrawRecords: {
        ...state.withdrawRecords,
        list: data.list,
        total: data.pagination.total,
        pagination: data.pagination,
      },
    })),
  resetPayment: () => set(initialState),
}));

export const usePaymentStore = createSelectors(paymentStore);

export function usePaymentReset() {
  paymentStore.setState(initialState);
}
