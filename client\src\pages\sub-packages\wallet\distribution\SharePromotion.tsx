import { Button, Icon } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import { useState } from 'react';
import SharePopup from './SharePopup';

export default function SharePromotion({ qrCodeUrl }: { qrCodeUrl: string }) {
  const [showQrCode, setShowQrCode] = useState(false);
  // 分享操作
  const handleShare = () => {
    setShowQrCode(true);
  };

  return (
    <>
      <View className='mx-4 mt-4 bg-bg rounded-xl p-4'>
        <View className='flex flex-row justify-between items-center mb-4 w-full'>
          <Text className='text-lg font-bold'>分享推广</Text>
          <View className='flex flex-row items-center'>
            <Button
              type='primary'
              size='small'
              round
              plain
              hairline
              onClick={handleShare}
            >
              <Icon name='share-o' size={32} className='mr-1' />
              分享二维码
            </Button>
          </View>
        </View>

        <View className='bg-gray-50 p-3 rounded-lg'>
          <Text className='text-sm text-secondary'>分享规则：</Text>
          <View className='mt-2'>
            <Text className='text-sm'>
              1. 您的好友通过您的专属二维码注册成为新用户
            </Text>
            <Text className='text-sm'>
              2. 好友下单后，您可获得订单金额的10%作为分享佣金
            </Text>
            <Text className='text-sm'>3. 佣金将在订单完成后7天内结算</Text>
            <Text className='text-sm'>4. 佣金满100元可申请提现</Text>
          </View>
        </View>
      </View>
      {/* 二维码分享弹窗 */}
      <SharePopup showQrCode={showQrCode} qrCodeUrl={qrCodeUrl} />
    </>
  );
}
