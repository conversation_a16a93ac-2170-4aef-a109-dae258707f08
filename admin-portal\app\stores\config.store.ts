import { ConsultationDirection } from '@model/common.interface';
import { ServiceType } from '@model/service.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { zustandStorage } from './libs/storage';
/**
 * 应用配置接口
 */
interface AppConfig {
  directions: ConsultationDirection[];
  serviceTypes: ServiceType[];

  //热门城市
  hotCities?: string[];

  // 其他全局配置
  [key: string]: any;
}

/**
 * 配置状态管理
 */
interface ConfigState {
  // 应用配置
  appConfig: AppConfig;
}
interface ConfigAction {
  setAppConfig: (config: Partial<AppConfig>) => void;
  resetAppConfig: () => void;
}
const initialState: ConfigState = {
  appConfig: {
    directions: [
      ConsultationDirection.ANXIETY,
      ConsultationDirection.TEEN,
      ConsultationDirection.MARRIAGE,
      ConsultationDirection.GROWTH,
      ConsultationDirection.CAREER,
    ],
    serviceTypes: [ServiceType.VIDEO, ServiceType.FACE_TO_FACE],
    hotCities: [
      '北京市',
      '上海市',
      '广州市',
      '深圳市',
      '杭州市',
      '南京市',
      '成都市',
      '武汉市',
      '西安市',
    ],
  },
};

/**
 * 配置状态管理 store
 * 使用zustand/persist中间件实现持久化存储
 */
export const useConfigStore = create<ConfigState & ConfigAction>()(
  immer(
    persist(
      (set) => ({
        // 初始配置
        ...initialState,

        // 设置配置
        setAppConfig: (config: Partial<AppConfig>) =>
          set((state) => ({
            appConfig: { ...state.appConfig, ...config },
          })),

        // 重置配置
        resetAppConfig: () =>
          set({
            appConfig: initialState.appConfig,
          }),
      }),
      {
        // 持久化配置
        name: 'mental-app-config',
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化appConfig
        partialize: (state) => ({ appConfig: state.appConfig }),
      }
    )
  )
);
