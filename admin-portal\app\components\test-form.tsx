import {
  PsychologicalTest,
  QuestionType,
  TestCategory,
  TestQuestion,
} from "@/app/model/test.model";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { MoveDown, MoveUp, PlusCircle, Trash2 } from "lucide-react";
import { useState } from "react";

// 初始题目模板
const DEFAULT_QUESTION: TestQuestion = {
  id: "",
  text: "",
  type: QuestionType.LIKERT,
  options: ["从来没有", "偶尔", "有时", "经常", "总是"],
  required: true,
  reverseScoring: false,
  dimension: "总分",
};

// 初始测试模板
const DEFAULT_TEST: PsychologicalTest = {
  id: "",
  title: "",
  shortTitle: "",
  description: "",
  shortDescription: "",
  instructions: "请根据您最近一周的感受，选择最符合您情况的选项。",
  category: TestCategory.EMOTION,
  howmany: 0,
  duration: 5,
  usersCompleted: 0,
  icon: "/assets/icons/emotion-brain.svg",
  coverImage: "",
  isFree: true,
  questions: [],
  scoringRules: [
    {
      dimension: "总分",
      questionIds: [],
      calculation: "sum",
      range: [0, 100],
      interpretation: [
        {
          scoreRange: [0, 50],
          normScore: 0,
          level: "正常",
          description: "您的得分在正常范围内",
          recommendations: ["保持健康的生活方式"],
        },
        {
          scoreRange: [51, 100],
          normScore: 0,
          level: "异常",
          description: "您的得分超出正常范围",
          recommendations: ["建议咨询专业人士"],
        },
      ],
    },
  ],
};

interface TestFormProps {
  initialData?: Partial<PsychologicalTest>;
  onSubmit: (data: PsychologicalTest) => void;
}

export default function TestForm({ initialData, onSubmit }: TestFormProps) {
  const [formData, setFormData] = useState<PsychologicalTest>({
    ...DEFAULT_TEST,
    ...initialData,
    questions: initialData?.questions || [],
    scoringRules: initialData?.scoringRules || DEFAULT_TEST.scoringRules,
  });

  // 更新基本信息
  const handleBasicInfoChange = (
    field: keyof PsychologicalTest,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 添加新题目
  const handleAddQuestion = () => {
    const newQuestion: TestQuestion = {
      ...DEFAULT_QUESTION,
      id: `q-${Date.now()}`,
    };

    setFormData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
      howmany: prev.questions.length + 1,
    }));
  };

  // 更新题目
  const handleQuestionChange = (
    index: number,
    field: keyof TestQuestion,
    value: any
  ) => {
    setFormData((prev) => {
      const updatedQuestions = [...prev.questions];
      updatedQuestions[index] = {
        ...updatedQuestions[index],
        [field]: value,
      };
      return {
        ...prev,
        questions: updatedQuestions,
      };
    });
  };

  // 更新题目选项
  const handleOptionsChange = (index: number, optionsText: string) => {
    const options = optionsText.split("\n").filter((opt) => opt.trim() !== "");
    setFormData((prev) => {
      const updatedQuestions = [...prev.questions];
      updatedQuestions[index] = {
        ...updatedQuestions[index],
        options,
      };
      return {
        ...prev,
        questions: updatedQuestions,
      };
    });
  };

  // 删除题目
  const handleDeleteQuestion = (index: number) => {
    setFormData((prev) => {
      const updatedQuestions = prev.questions.filter((_, i) => i !== index);
      return {
        ...prev,
        questions: updatedQuestions,
        howmany: updatedQuestions.length,
      };
    });
  };

  // 移动题目位置
  const handleMoveQuestion = (index: number, direction: "up" | "down") => {
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === formData.questions.length - 1)
    ) {
      return;
    }

    const newIndex = direction === "up" ? index - 1 : index + 1;

    setFormData((prev) => {
      const updatedQuestions = [...prev.questions];
      const temp = updatedQuestions[index];
      updatedQuestions[index] = updatedQuestions[newIndex];
      updatedQuestions[newIndex] = temp;
      return {
        ...prev,
        questions: updatedQuestions,
      };
    });
  };

  // 提交表单
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">基本信息</h3>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">量表名称</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleBasicInfoChange("title", e.target.value)}
              placeholder="如：抑郁自评量表(SDS)"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="shortTitle">短名称</Label>
            <Input
              id="shortTitle"
              value={formData.shortTitle}
              onChange={(e) =>
                handleBasicInfoChange("shortTitle", e.target.value)
              }
              placeholder="如：抑郁自评"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">描述</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              handleBasicInfoChange("description", e.target.value)
            }
            placeholder="量表的详细描述"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="shortDescription">简短描述</Label>
          <Input
            id="shortDescription"
            value={formData.shortDescription}
            onChange={(e) =>
              handleBasicInfoChange("shortDescription", e.target.value)
            }
            placeholder="如：快速筛查抑郁症状"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="instructions">使用说明</Label>
          <Textarea
            id="instructions"
            value={formData.instructions}
            onChange={(e) =>
              handleBasicInfoChange("instructions", e.target.value)
            }
            placeholder="测试前的指导说明"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="category">分类</Label>
            <Select
              value={formData.category}
              onValueChange={(value) =>
                handleBasicInfoChange("category", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(TestCategory).map(([key, value]) => (
                  <SelectItem key={value} value={value}>
                    {key}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="duration">预计时间(分钟)</Label>
            <Input
              id="duration"
              type="number"
              min="1"
              value={formData.duration}
              onChange={(e) =>
                handleBasicInfoChange("duration", parseInt(e.target.value))
              }
              required
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isFree"
            checked={formData.isFree}
            onChange={(e) => handleBasicInfoChange("isFree", e.target.checked)}
            className="h-4 w-4"
          />
          <Label htmlFor="isFree">免费使用</Label>
        </div>
      </div>

      {/* 题目列表 */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">
            题目列表 ({formData.questions.length})
          </h3>
          <Button
            type="button"
            onClick={handleAddQuestion}
            className="flex items-center gap-1"
          >
            <PlusCircle size={16} />
            添加题目
          </Button>
        </div>

        {formData.questions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            暂无题目，请点击"添加题目"按钮添加
          </div>
        ) : (
          <div className="space-y-6">
            {formData.questions.map((question, index) => (
              <div key={question.id} className="border p-4 rounded-md">
                <div className="flex justify-between items-start mb-4">
                  <h4 className="font-medium">题目 {index + 1}</h4>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMoveQuestion(index, "up")}
                      disabled={index === 0}
                    >
                      <MoveUp size={16} />
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMoveQuestion(index, "down")}
                      disabled={index === formData.questions.length - 1}
                    >
                      <MoveDown size={16} />
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteQuestion(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor={`question-${index}`}>题目内容</Label>
                    <Textarea
                      id={`question-${index}`}
                      value={question.text}
                      onChange={(e) =>
                        handleQuestionChange(index, "text", e.target.value)
                      }
                      placeholder="请输入题目内容"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`question-type-${index}`}>题目类型</Label>
                      <Select
                        value={question.type}
                        onValueChange={(value) =>
                          handleQuestionChange(index, "type", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择题目类型" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(QuestionType).map(([key, value]) => (
                            <SelectItem key={value} value={value}>
                              {key === "LIKERT"
                                ? "李克特量表"
                                : key === "MULTIPLE_CHOICE"
                                ? "多选题"
                                : key === "BINARY"
                                ? "是非题"
                                : "开放题"}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`dimension-${index}`}>所属维度</Label>
                      <Input
                        id={`dimension-${index}`}
                        value={question.dimension || "总分"}
                        onChange={(e) =>
                          handleQuestionChange(
                            index,
                            "dimension",
                            e.target.value
                          )
                        }
                        placeholder="如：总分、抑郁维度等"
                      />
                    </div>
                  </div>

                  {question.type !== QuestionType.OPEN_ENDED && (
                    <div className="space-y-2">
                      <Label htmlFor={`options-${index}`}>
                        选项（每行一个选项）
                      </Label>
                      <Textarea
                        id={`options-${index}`}
                        value={(question.options || []).join("\n")}
                        onChange={(e) =>
                          handleOptionsChange(index, e.target.value)
                        }
                        placeholder="每行输入一个选项"
                        rows={5}
                        required
                      />
                    </div>
                  )}

                  <div className="flex space-x-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`required-${index}`}
                        checked={question.required}
                        onChange={(e) =>
                          handleQuestionChange(
                            index,
                            "required",
                            e.target.checked
                          )
                        }
                        className="h-4 w-4"
                      />
                      <Label htmlFor={`required-${index}`}>必答题</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`reverse-${index}`}
                        checked={question.reverseScoring}
                        onChange={(e) =>
                          handleQuestionChange(
                            index,
                            "reverseScoring",
                            e.target.checked
                          )
                        }
                        className="h-4 w-4"
                      />
                      <Label htmlFor={`reverse-${index}`}>反向计分</Label>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 提交按钮 */}
      <div className="flex justify-end">
        <Button type="submit" disabled={formData.questions.length === 0}>
          保存测量工具
        </Button>
      </div>
    </form>
  );
}
