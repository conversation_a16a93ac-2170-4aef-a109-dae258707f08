import { SERVICE_TYPE_MAP_SHORT } from '@constants/text';
import { chatActions } from '@core/actions/chat.action';
import { orderUserActions } from '@core/actions/order.user';
import { MessageContentType } from '@model/chat.interface';
import { uploadService } from '@services/upload.service';
import { useChatStore } from '@stores/chat.store';
import { useGlobalStore } from '@stores/global.store';
import { useOrderStoreSelector } from '@stores/order.store';
import Taro from '@tarojs/taro';
import { genSessionId } from '@utils/app';
import { formatTime2 } from '@utils/time';
import { useCallback, useEffect, useMemo } from 'react';
import { useShallow } from 'zustand/react/shallow';

/**
 * 聊天功能Hook
 * 提供聊天相关的功能，包括发送消息、上传图片、录音等
 * @param therapistId 接收者ID
 * @param userId 发送者ID
 * @param isTherapist 是否是咨询师端
 */
export function useChat(peerId: string) {
  console.log('🚀🚀🚀 useChat', peerId);
  const myOpenId = useGlobalStore.use.openid();

  // 获取会话，使用经过验证的参数
  const session = useChatStore(
    useShallow((state) =>
      state.getSession(genSessionId(peerId, myOpenId || ''))
    )
  );
  // 使用useShallow减少不必要的重新渲染
  const messages = useChatStore(
    useShallow((state) => state.getMessages(session?.id || ''))
  );
  // 从store获取状态
  const error = useChatStore.use.error();
  const loading = useChatStore.use.loading();

  // 标记消息为已读
  const handleMarkMessageAsRead = useCallback(
    async (messageId: string) => {
      if (!session) return;

      try {
        await chatActions.markMessageAsRead(session.id, messageId);
      } catch (err) {
        console.error('标记消息已读失败:', err);
      }
    },
    [session]
  );

  // 发送文本消息
  const handleSendText = useCallback(
    async (text: string) => {
      console.log('🚀🚀🚀 handleSendText', text, session, peerId);
      if (!session || !peerId) return;

      try {
        await chatActions.sendMessage({
          sessionId: session.id,
          receiverId: peerId,
          content: text,
          contentType: MessageContentType.TEXT,
        });
      } catch (err) {
        console.error('发送消息失败:', err);
      }
    },
    [session, peerId]
  );

  // 发送图片
  const handleSendImage = useCallback(async () => {
    if (!session || !peerId) return;

    try {
      const res = await Taro.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
      });

      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const tempFilePath = res.tempFilePaths[0];
        await chatActions.uploadImage(session.id, peerId, tempFilePath);
      }
    } catch (err) {
      console.error('选择图片失败:', err);
    }
  }, [session, peerId]);

  // 加载更多消息
  const handleLoadMore = useCallback(async () => {
    if (!session || messages.length === 0) return;

    const firstMessage = messages[0];
    if (firstMessage) {
      await chatActions.fetchMessages(session.id, firstMessage.timestamp);
    }
  }, [session, messages]);

  const refreshMessages = useCallback(async () => {
    if (!session) return;
    await chatActions.fetchMessages(session.id);
  }, [session]);

  // 重发消息
  const handleResendMessage = useCallback(
    async (messageId: string) => {
      if (!session) return;

      try {
        await chatActions.resendMessage(session.id, messageId);
      } catch (err) {
        console.error('重发消息失败:', err);
      }
    },
    [session]
  );

  // 发送语音消息
  const handleSendAudio = useCallback(async () => {
    if (!session || !peerId) return;

    try {
      // 开始录音
      const recorderManager = Taro.getRecorderManager();

      // 显示录音UI
      Taro.showToast({
        title: '开始录音，说话后点击停止',
        icon: 'none',
        duration: 2000,
      });

      // 录音参数设置
      recorderManager.start({
        duration: 60000, // 最长录音时间，单位ms
        sampleRate: 44100, // 采样率
        numberOfChannels: 1, // 录音通道数
        encodeBitRate: 192000, // 编码码率
        format: 'mp3', // 音频格式
      });

      // 创建一个Promise来处理录音完成事件
      const recordPromise = new Promise((resolve, reject) => {
        // 录音错误回调
        recorderManager.onError((err) => {
          console.error('录音失败:', err);
          reject(err);
        });

        // 录音完成回调
        recorderManager.onStop(async (res) => {
          const { tempFilePath, duration } = res;
          console.log('录音完成:', tempFilePath, duration);

          try {
            // 上传录音文件
            const result = await uploadService.uploadAudio(tempFilePath);

            if (result) {
              // 发送语音消息
              await chatActions.sendMessage({
                sessionId: session.id,
                receiverId: peerId,
                content: '[语音]',
                contentType: MessageContentType.VOICE,
                extra: {
                  audioUrl: result,
                  duration: Math.floor(duration / 1000), // 转换为秒
                },
              });
              resolve(result);
            } else {
              throw new Error('上传录音失败');
            }
          } catch (err) {
            console.error('上传录音失败:', err);
            reject(err);
          }
        });
      });

      // 显示停止录音按钮
      Taro.showModal({
        title: '正在录音',
        content: '点击确定停止录音',
        showCancel: false,
        confirmText: '停止录音',
        success: () => {
          // 停止录音
          recorderManager.stop();
        },
      });

      // 等待录音完成
      await recordPromise;
    } catch (err) {
      console.error('发送语音失败:', err);
      Taro.showToast({
        title: '发送语音失败',
        icon: 'none',
      });
    }
  }, [session, peerId]);

  // 发送订单消息 (仅用户端可用)
  const handleSendOrder = useCallback(async () => {
    if (!session || !peerId) return;

    try {
      // 显示加载中
      Taro.showLoading({ title: '加载订单中...' });

      // 获取用户订单列表
      await orderUserActions.fetchOrders({
        page: 1,
        pageSize: 20,
      });

      // 隐藏加载中
      Taro.hideLoading();

      // 从store中获取订单列表数据
      const orders = useOrderStoreSelector.getState().orders;
      console.log('🚀🚀🚀 handleSendOrder orders', orders);

      if (!orders || orders.length === 0) {
        Taro.showToast({
          title: '暂无订单数据',
          icon: 'none',
        });
        return;
      }

      // 生成订单选项
      const orderOptions = orders.map(
        (order) =>
          `${order.therapistName} (${
            SERVICE_TYPE_MAP_SHORT[order.serviceType]
          }) ${formatTime2(order.startTime)}`
      );

      // 显示订单选择器
      Taro.showActionSheet({
        itemList: orderOptions,
        success: async (res) => {
          const index = res.tapIndex;
          if (index >= 0 && index < orders.length) {
            const selectedOrder = orders[index];

            // 发送订单消息
            await chatActions.sendMessage({
              sessionId: session.id,
              receiverId: peerId,
              content: selectedOrder._id,
              contentType: MessageContentType.ORDER,
              extra: {
                serviceType: selectedOrder.serviceType,
                price: selectedOrder.price,
                status: selectedOrder.status,
              },
            });
          }
        },
        fail: (err) => {
          console.log('选择订单取消', err);
        },
      });
    } catch (err) {
      console.error('发送订单失败:', err);
      Taro.hideLoading();
      Taro.showToast({
        title: '获取订单失败',
        icon: 'none',
      });
    }
  }, [session, peerId]);

  useEffect(() => {
    chatActions.fetchSession([peerId, myOpenId || '']);
  }, [peerId, myOpenId]);
  useEffect(() => {
    chatActions.fetchMessages(
      session?.id || ''
      // session?.lastMessage?.timestamp
    );
  }, [session]);
  useEffect(() => {
    return () => {
      console.log('🚀🚀🚀 useChat session unmount');
      useChatStore.getState().setError(null);
      useChatStore.getState().setLoading(false);
      useChatStore.getState().setCurrentSessionId(null);
    };
  }, [session]);
  // 使用useMemo返回结果对象，避免每次渲染时创建新的对象引用
  return useMemo(
    () => ({
      session,
      messages,
      error,
      loading,
      handleSendText,
      handleSendImage,
      handleSendAudio,
      handleSendOrder,
      handleLoadMore,
      handleResendMessage,
      handleMarkMessageAsRead,
      refreshMessages,
    }),
    [
      session,
      messages,
      error,
      loading,
      handleSendText,
      handleSendImage,
      handleSendAudio,
      handleSendOrder,
      handleLoadMore,
      handleResendMessage,
      handleMarkMessageAsRead,
      refreshMessages,
    ]
  );
}
