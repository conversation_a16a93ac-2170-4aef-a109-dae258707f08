import { COLLECTIONS } from '@model/db.model';
import {
  Feedback,
  FeedbackRequest,
  FeedbackResponse,
} from '@model/feedback.interface';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class FeedbackService extends BaseService<Feedback> {
  constructor() {
    super(COLLECTIONS.FEEDBACK);
  }
  /**
   * 提交反馈/投诉
   * @param data 反馈内容
   */
  async submitFeedback(data: FeedbackRequest): Promise<FeedbackResponse> {
    try {
      const result = await callCloudFunction(
        'feedback',
        'createFeedback',
        data
      );

      if (result.success) {
        return {
          code: 0,
          data: result.data,
          message: '提交成功',
        };
      } else {
        console.error('提交反馈失败:', result.message);
        return {
          code: -1,
          data: null,
          message: result.message || '提交反馈失败，请稍后再试',
        };
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      return {
        code: -1,
        data: null,
        message: '提交反馈失败，请稍后再试',
      };
    }
  }

  /**
   * 获取用户反馈列表
   */
  async getFeedbackList(): Promise<Feedback[]> {
    try {
      const result = await this.directRead();

      return result as Feedback[];
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取反馈详情
   * @param id 反馈ID
   */
  async getFeedbackDetail(id: string): Promise<Feedback> {
    try {
      const result = await this.directRead({ _id: id });

      return result[0] as Feedback;
    } catch (error) {
      console.error('获取反馈详情失败:', error);
      throw error;
    }
  }
}

export const feedbackService = new FeedbackService();
