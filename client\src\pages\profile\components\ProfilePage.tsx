import { Cell, CellGroup } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { ensureLoggedIn, switchRole } from '@core/actions/auth.action';
import { userProfileActions } from '@core/actions/profile.user';
import useRenderCount from '@hooks/useRenderCount';
import { USER_ROLE } from '@model/user.interface';
import { useGlobalStore } from '@stores/global.store';
import { Text, View } from '@tarojs/components';
import { orderRouter } from '@utils/router';
import { useEffect } from 'react';
import TherapistPage from './TherapistPage';
import UserPage from './UserPage';

/**
 * 管理员功能卡片组件
 */
const AdminFunctionsCard = () => {
  return (
    <View className='mt-4'>
      <CellGroup border={false} inset>
        <Cell
          title='审核退款'
          icon='balance-list-o'
          isLink
          border={false}
          onClick={() => {
            orderRouter.audit();
          }}
        />
      </CellGroup>
    </View>
  );
};

/**
 * 版本信息组件
 */
const VersionInfo = () => (
  <View className='flex flex-col items-center mt-6 mb-2'>
    <Text className='text-gray-400 text-xs mb-1'>V1.0.0</Text>
    <Text className='text-gray-300 text-xs'>
      深圳市黑暗森林科技有限公司提供技术支持
    </Text>
  </View>
);

/**
 * 用户个人资料页面
 */
export default function ProfilePage() {
  // 跟踪渲染次数
  useRenderCount('ProfilePage');

  // 使用全局状态管理
  const currentRole = useGlobalStore.use.currentRole();
  const role = useGlobalStore.use.role();
  const needReload = useGlobalStore.use.needReload();
  useEffect(() => {
    console.log('ProfilePage user effect', needReload, currentRole);
    if (!needReload) return;
    ensureLoggedIn();
  }, [needReload, currentRole]);

  // 切换角色处理函数
  const handleSwitchRole = (roleType: USER_ROLE) => {
    switchRole(roleType);
  };

  return (
    <PageLol
      navigationProps={{
        showBackButton: false,
        showSearch: false,
        buttons: [
          {
            icon: 'chat-o',
            onClick: userProfileActions.handleMessage,
          },
        ],
      }}
    >
      <View className='min-h-screen pb-16'>
        {currentRole === USER_ROLE.THERAPIST ? (
          <TherapistPage onSwitchRole={handleSwitchRole} />
        ) : currentRole === USER_ROLE.USER ? (
          <UserPage onSwitchRole={handleSwitchRole} />
        ) : null}
        {/* 管理员功能 */}
        {role?.includes(USER_ROLE.ADMIN) && <AdminFunctionsCard />}
        {/* 版本号 */}
        <VersionInfo />
      </View>
    </PageLol>
  );
}
