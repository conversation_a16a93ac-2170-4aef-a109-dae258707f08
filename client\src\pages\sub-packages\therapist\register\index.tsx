import { Steps } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { DEFAULT_WORK_TIME } from '@constants/config';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { useUserProfileStore } from '@stores/profile.user';
import { useTherapistRegisterStore } from '@stores/register.store';
import { View } from '@tarojs/components';
import { useEffect } from 'react';
import BasicInfoStep from './components/BasicInfoStep';
import DetailInfoStep from './components/DetailInfoStep';
import ServiceInfoStep from './components/ServiceInfoStep';
import WorktimeStep from './components/WorktimeStep';

// 步骤配置
const STEPS = [
  { text: '基础信息', desc: '' },
  { text: '详细信息', desc: '' },
  { text: '服务信息', desc: '' },
  { text: '工作时间', desc: '' },
];

/**
 * 咨询师注册页面
 */
export default function TherapistRegisterPage() {
  // 用户信息
  const { profile: user } = useUserProfileStore();

  // 使用 registerStore 管理状态
  const currentStep = useTherapistRegisterStore((state) => state.currentStep);
  const formData = useTherapistRegisterStore((state) => state.formData);
  const updateFormField = useTherapistRegisterStore(
    (state) => state.updateFormField
  );
  const nextStep = useTherapistRegisterStore((state) => state.nextStep);
  const prevStep = useTherapistRegisterStore((state) => state.prevStep);
  const initFormData = useTherapistRegisterStore((state) => state.initFormData);

  // 初始化数据
  useEffect(() => {
    // 初始化表单数据
    if (user) {
      initFormData({
        userName: user.userName,
        avatar: user.avatar,
      });
    }
  }, []);

  // 表单字段变更处理
  const handleFormChange = (section: string, field: string, value: any) => {
    updateFormField(section as any, field, value);
  };

  // 提交表单
  const handleSubmit = async () => {
    await therapistProfileActions.register();
  };

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <BasicInfoStep
            summary={formData.summary}
            onFormChange={handleFormChange}
            onNextStep={nextStep}
          />
        );
      case 1:
        return (
          <DetailInfoStep
            detailInfo={formData.extend}
            onFormChange={(field, value) =>
              handleFormChange('extend', field, value)
            }
            onPrevStep={prevStep}
            onNextStep={nextStep}
          />
        );
      case 2:
        return (
          <ServiceInfoStep
            formData={formData.serviceInfo}
            onFormChange={(field, value) =>
              handleFormChange('serviceInfo', field, value)
            }
            onPrevStep={prevStep}
            onNextStep={nextStep}
          />
        );
      case 3:
        return (
          <WorktimeStep
            workTime={formData.serviceInfo.workTime || DEFAULT_WORK_TIME}
            onFormChange={(field, value) =>
              handleFormChange('serviceInfo', field, value)
            }
            onPrevStep={prevStep}
            onSubmit={handleSubmit}
          />
        );
      default:
        return null;
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '申请成为咨询师',
        showBackButton: true,
        showSearch: false,
      }}
    >
      <View className='min-h-screen pb-24'>
        {/* 步骤指示器 */}
        <View className='px-8 py-4'>
          <Steps
            steps={STEPS}
            active={currentStep}
            activeColor='var(--color-primary)'
            className='mb-6'
          />
        </View>

        {/* 步骤内容 */}
        <View>{renderStepContent()}</View>
      </View>
    </PageLol>
  );
}
