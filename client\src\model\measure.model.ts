// ====================== 基础类型定义 ======================

import { TestCategory } from './test.model';

// 卡片显示类型
export enum CardDisplayType {
  RECOMMENDED = 'recommended', // 推荐卡片
  CATEGORY = 'category', // 分类卡片
}

// ====================== 核心数据结构 ======================
// 心理测量量表接口
export interface PsychologicalTestSummary {
  id: string; // 唯一ID (例如: "sds-depression")
  title: string; // 量表名称 (例如: "SDS抑郁自评量表")
  shortTitle?: string; // 短名称 (例如: "抑郁自评")
  description: string; // 描述
  shortDescription?: string; // 简短描述 (例如: "快速筛查抑郁症状")
  category: TestCategory; // 分类
  howmany: number; // 题目数量
  duration: number; // 预计完成时间(分钟)
  usersCompleted: number; // 完成人数
  icon: string; // 图标URL或base64
  coverImage?: string; // 封面图URL (可选)
  isFree: boolean; // 是否免费
}

// 推荐卡片接口
export interface RecommendationCard {
  id: string; // 卡片唯一ID
  testId: string; // 关联的量表ID
  displayType: CardDisplayType; // 显示类型
  displayOrder: number; // 显示顺序
  recommendationReason?: string; // 推荐理由 (例如: "根据您的历史记录推荐")
  // 以下字段可能来自关联的量表，但为优化性能而冗余存储
  shortTitle?: string; // 冗余存储，避免关联查询
  shortDescription?: string; // 冗余存储，避免关联查询
  category: TestCategory; // 冗余存储，避免关联查询
  icon: string; // 冗余存储
  howmany: number; // 冗余存储
  duration: number; // 冗余存储
  usersCompleted: number; // 冗余存储
}

// ====================== 页面数据结构 ======================
export interface TestCategoryData {
  category: TestCategory;
  items: PsychologicalTestSummary[];
}
// 测量主页数据
// export interface MeasurementHomeData {
//   featuredCards: RecommendationCard[]; // 推荐卡片列表
//   categories: Category[];
//   recentHistory: MeasureHistory[];
//   lastUpdated: number; // 最后更新时间戳
// }

// ====================== API 响应结构 ======================
// 推荐卡片API响应
export interface RecommendationResponse {
  code: number;
  message: string;
  data: {
    cards: RecommendationCard[]; // 推荐卡片列表
    expiresAt: number; // 数据过期时间戳
  };
}

// ====================== 组件Props ======================
// 推荐卡片组件Props
export interface RecommendationCardProps {
  card: RecommendationCard;
  onClick?: (testId: string) => void;
}

// 分类卡片组件Props
export interface CategoryCardProps {
  category: {
    id: TestCategory;
    name: string;
    count: number;
  };
  onClick?: (categoryId: TestCategory) => void;
}
export interface TestCategoryTab {
  title: string;
  category: TestCategory;
  count: number;
}

// 量表数据示例
// const sdsTest: PsychologicalTestSummary = {
//   id: 'sds-depression',
//   title: 'SDS抑郁自评量表',
//   shortTitle: '抑郁自评',
//   description: '快速筛查抑郁症状',
//   category: TestCategory.EMOTION,
//   howmany: 20,
//   duration: 7,
//   usersCompleted: 2400,
//   icon: '/assets/icons/emotion-brain.svg',
//   isFree: true,
// };

// 推荐卡片数据示例
// const sdsCard: RecommendationCard = {
//   id: 'rec-sds-202307',
//   testId: 'sds-depression',
//   displayType: CardDisplayType.RECOMMENDED,
//   displayOrder: 1,
//   recommendationReason: '超过90%用户完成',
//   // 冗余字段
//   shortTitle: '抑郁自评',
//   shortDescription: '快速筛查抑郁症状',
//   category: TestCategory.EMOTION,
//   icon: '/assets/icons/emotion-brain.svg',
//   howmany: 20,
//   duration: 7,
//   usersCompleted: 2400,
// };

// // 主页数据示例
// const homeData: MeasurementHomeData = {
//   featuredCards: [
//     sdsCard,
//     {
//       id: 'rec-gad7-202307',
//       testId: 'gad7-anxiety',
//       displayType: CardDisplayType.RECOMMENDED,
//       displayOrder: 2,
//       title: 'GAD-7焦虑量表',
//       icon: '/assets/icons/anxiety-waves.svg',
//       questions: 7,
//       duration: 3,
//       usersCompleted: 3100,
//     },
//   ],
//   categories: [
//     { id: TestCategory.EMOTION, name: '情绪', count: 12, items: [] },
//     { id: TestCategory.PERSONALITY, name: '人格', count: 8, items: [] },
//     { id: TestCategory.STRESS, name: '压力', count: 6, items: [] },
//     { id: TestCategory.SLEEP, name: '睡眠', count: 5, items: [] },
//     { id: TestCategory.RELATIONSHIP, name: '人际', count: 4, items: [] },
//     { id: TestCategory.CAREER, name: '职业', count: 3, items: [] },
//     { id: TestCategory.ADDICTION, name: '成瘾', count: 2, items: [] },
//     { id: TestCategory.GENERAL, name: '综合', count: 4, items: [] },
//   ],
//   recentHistory: [
//     {
//       id: 'hist-001',
//       testId: 'pss-stress',
//       testTitle: '压力感知量表',
//       date: 1689000000000, // 2023-07-10
//       score: 18,
//       resultLevel: '中度压力',
//     },
//   ],
//   lastUpdated: 1689000000000,
// };
