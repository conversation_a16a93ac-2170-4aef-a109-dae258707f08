import { IncomeListParams, Pagination } from '@core/api';
import {
  IncomeDetail,
  StatSummary,
  TherapistMonthlyStat,
  Wallet,
} from '@model/income.model';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

export interface IncomeState {
  wallet: Wallet | null;
  incomeList: IncomeDetail[];
  incomeChart: TherapistMonthlyStat[];
  statSummary: StatSummary | null;
  loading: boolean;
  loadingText: string | null;
  error: string | null;
  pagination: Pagination | null;
  filters: IncomeListParams;
}

export interface IncomeAction {
  setWallet: (wallet: Wallet) => void;
  setIncomeList: (incomeList: IncomeDetail[]) => void;
  setIncomeChart: (incomeChart: TherapistMonthlyStat[]) => void;
  setStatSummary: (statSummary: StatSummary) => void;
  setLoading: (loading: boolean) => void;
  setLoadingText: (loadingText: string | null) => void;
  setError: (error: string | null) => void;
  setPagination: (pagination: Pagination | null) => void;
  setFilters: (filters: IncomeListParams) => void;
  updateIncome: (income: IncomeDetail) => void;
  reset: () => void;
}

const initialState: IncomeState = {
  wallet: null,
  incomeList: [],
  incomeChart: [],
  statSummary: null,
  loading: false,
  loadingText: null,
  error: null,
  pagination: null,
  filters: {},
};

const incomeStore = create<IncomeState & IncomeAction>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setWallet: (wallet) => set({ wallet }),
        setIncomeList: (incomeList) => set({ incomeList }),
        setIncomeChart: (incomeChart) => set({ incomeChart }),
        setStatSummary: (statSummary) => set({ statSummary }),
        setLoading: (loading) => set({ loading }),
        setLoadingText: (loadingText) => set({ loadingText }),
        setError: (error) => set({ error }),
        setPagination: (pagination) => set({ pagination }),
        setFilters: (filters) => set({ filters }),
        updateIncome: (income) =>
          set((state) => {
            const index = state.incomeList.findIndex(
              (item) => item.orderId === income.orderId
            );
            if (index !== -1) {
              state.incomeList[index] = income;
            }
          }),
        reset: () => set(initialState),
      }),
      {
        name: StorageSceneKey.INCOME_LIST,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          incomeList: state.incomeList,
          pagination: state.pagination,
          filters: state.filters,
        }),
      }
    )
  )
);

export const useIncomeStore = createSelectors(incomeStore);
