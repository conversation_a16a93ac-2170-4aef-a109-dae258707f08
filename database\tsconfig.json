{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "react-jsx", "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types"], "paths": {}}, "include": ["./*.ts", "../model/src/interfaces/db.model.ts", "mockData.ts", "../client/src/core/interfaces/*", "../model/src/interfaces/distribution.interface.ts", "../model/src/interfaces/feedback.interface.ts", "../model/src/interfaces/help.interface.ts", "../model/src/interfaces/payment.interface.ts", "../model/src/interfaces/review.interface.ts", "../model/src/interfaces/videoCall.interface.ts"], "compileOnSave": false}