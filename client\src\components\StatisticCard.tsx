import { Text, View } from '@tarojs/components';

interface StatisticCardProps {
  onClick?: () => void;
  title: string | number;
  subTitle?: string | number;
  renderRight?: React.ReactNode;
  values: {
    title: string;
    value: string | number;
    unit?: string;
    color?: string;
    extra?: React.ReactNode;
    onClick?: () => void;
  }[];
  className?: string;
  children?: React.ReactNode;
}
export default function StatisticCard({
  onClick,
  title,
  subTitle,
  renderRight,
  values,
  className,
  children,
}: StatisticCardProps) {
  return (
    <View
      className={`mx-4 bg-white rounded-xl px-4 py-3 mb-4 shadow-sm ${className}`}
      onClick={onClick}
    >
      {/* 头部 */}
      <View className='flex flex-row items-center justify-between mb-6'>
        <View className='flex flex-col items-start justify-between'>
          <Text className='text-2xl font-bold text-default'>{title}</Text>
          {subTitle && (
            <Text className='text-xs text-secondary'>{subTitle}</Text>
          )}
        </View>
        {renderRight}
      </View>
      {/* 内容 */}
      <View className='flex flex-row justify-between items-center bg-gray-100 rounded-xl px-4 py-3'>
        {values.map((value, index) => (
          <>
            <View
              className='flex flex-col items-start '
              onClick={(e) => {
                e.stopPropagation();
                value.onClick?.();
              }}
            >
              <Text className='text-xs text-default mb-1'>{value.title}</Text>
              <View className='flex flex-row items-center'>
                <Text
                  className={`text-md font-medium ${
                    value.color || 'text-secondary'
                  }`}
                >
                  {value.value}
                  {value.unit}
                </Text>
                {value.extra && (
                  <View
                    className={`bg-${
                      value.color || 'success'
                    } text-xs text-white rounded-full px-2 py-1`}
                  >
                    {value.extra}
                  </View>
                )}
              </View>
            </View>
            {index < values.length - 1 && (
              <View className='w-px h-6 bg-placeholder' />
            )}
          </>
        ))}
      </View>
      {children}
    </View>
  );
}
