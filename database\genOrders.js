const fs = require("fs");
const { v4: uuidv4 } = require("uuid");

// 定义常量
const THERAPIST_ID = "oLFkE7oRmctDByQ9rHOJJE_SWvHM";
const THERAPIST_AVATAR =
  "cloud://cloudbase-8g4u2qu9997239c6.636c-cloudbase-8g4u2qu9997239c6-1361949139/avatar/rUYyvR8x8plz9319a9d1b6619a1fc10cea63687486c3.jpeg";
const THERAPIST_NAME = "真咨询师";
const USER_ID = "oLFkE7lzKUPbOcFPLcDo4M_GSjyU";
const USER_AVATAR =
  "cloud://cloudbase-8g4u2qu9997239c6.636c-cloudbase-8g4u2qu9997239c6-1361949139/avatar/tmp_8ce8f49502da6c780bee400472929846.jpg";
const USER_NAME = "海阔天空";

// 定义枚举和常量
const INITATOR_SOURCE = {
  USER: 0,
  THERAPIST: 1,
  ADMIN: 2,
  SYSTEM: 3,
};

const ConsultationDirection = {
  ANXIETY: 0,
  TEEN: 1,
  MARRIAGE: 2,
  GROWTH: 3,
  CAREER: 4,
  OTHER: 5,
};

const ServiceType = {
  VIDEO: 0,
  FACE_TO_FACE: 3,
};

const ORDER_STATUS = {
  PENDING_PAYMENT: 10,
  PENDING_CONFIRM: 11,
  PENDING_START: 20,
  IN_PROGRESS: 21,
  COMPLETED: 30,
  REVIEWED: 31,
  REJECTED: 40,
  CANCELLED: 41,
};

const REFUND_STATUS = {
  NONE: 0,
  AUDITING: 1,
  REJECTED: 2,
  PROCESSING: 3,
  COMPLETED: 4,
  FAILED: 5,
};

const ACTION_TYPE = {
  CREATE: 0,
  PAY: 1,
  SUBMIT_USER_INFO: 2,
  ACCEPT: 3,
  REJECT: 4,
  CANCEL: 5,
  START: 6,
  COMPLETE: 7,
  REVIEW: 8,
  REFUND_REQUEST: 9,
  REFUND_AUDIT_PASS: 10,
  REFUND_AUDIT_REJECT: 11,
  REFUND_REFUNDING: 12,
  REFUND_COMPLETE: 13,
  REFUND_FAILED: 14,
  REFUND_ROLLBACK: 15,
};

// 生成订单摘要数据
function generateOrderSummaries(N) {
  const orders = [];
  const now = Date.now();

  for (let i = 0; i < N; i++) {
    const orderId = `order_${uuidv4()}`;
    const createdAt = now - i * 3 * 24 * 60 * 60 * 1000; // 每单间隔3天
    const startTime = createdAt + 2 * 24 * 60 * 60 * 1000;
    const duration = [30, 45, 60, 90][Math.floor(Math.random() * 4)];

    const order = {
      _id: orderId,
      userId: USER_ID,
      userAvatar: USER_AVATAR,
      userName: USER_NAME,
      therapistId: THERAPIST_ID,
      therapistAvatar: THERAPIST_AVATAR,
      therapistName: THERAPIST_NAME,
      status: getRandomOrderStatus(),
      refundStatus: REFUND_STATUS.NONE,
      serviceType: getRandomServiceType(),
      price: [199, 299, 399, 499][Math.floor(Math.random() * 4)],
      duration,
      startTime,
      endTime: startTime + duration * 60000,
      location: "线上视频咨询",
      consultationInfo: {
        name: USER_NAME,
        direction: getRandomDirection(),
        desc: `咨询问题描述 #${i + 1}`,
      },
      hasComplaint: Math.random() > 0.8,
      createdAt,
      updatedAt: createdAt + Math.floor(Math.random() * 24 * 60 * 60 * 1000),
    };

    // 处理退款状态
    if (Math.random() > 0.7) {
      order.refundStatus = getRandomRefundStatus();
    }

    orders.push(order);
  }

  return orders;
}

// 生成订单操作记录
function generateOrderActions(orders) {
  const actions = [];

  orders.forEach((order) => {
    // 每个订单至少有一个创建操作
    actions.push(
      createAction(
        order._id,
        ACTION_TYPE.CREATE,
        INITATOR_SOURCE.USER,
        order.createdAt
      )
    );

    // 根据状态生成其他操作
    if (order.status !== ORDER_STATUS.PENDING_PAYMENT) {
      actions.push(
        createAction(
          order._id,
          ACTION_TYPE.PAY,
          INITATOR_SOURCE.USER,
          order.createdAt + 1000
        )
      );

      if (order.status === ORDER_STATUS.CANCELLED) {
        actions.push(
          createAction(
            order._id,
            ACTION_TYPE.CANCEL,
            INITATOR_SOURCE.USER,
            order.createdAt + 2000,
            {
              reason: [0, 1, 2][Math.floor(Math.random() * 3)],
              detail: "取消原因详细描述",
            }
          )
        );
      } else {
        actions.push(
          createAction(
            order._id,
            ACTION_TYPE.SUBMIT_USER_INFO,
            INITATOR_SOURCE.USER,
            order.createdAt + 2000,
            {
              name: "用户姓名",
              direction: getRandomDirection(),
              desc: "咨询问题描述",
            }
          )
        );

        if (order.status >= ORDER_STATUS.REJECTED) {
          actions.push(
            createAction(
              order._id,
              ACTION_TYPE.REJECT,
              INITATOR_SOURCE.THERAPIST,
              order.createdAt + 3000,
              {
                reason: [0, 1, 2][Math.floor(Math.random() * 3)],
                detail: "拒绝原因详细描述",
              }
            )
          );
        }

        if (order.status >= ORDER_STATUS.PENDING_START) {
          actions.push(
            createAction(
              order._id,
              ACTION_TYPE.ACCEPT,
              INITATOR_SOURCE.THERAPIST,
              order.createdAt + 3000
            )
          );

          if (order.status >= ORDER_STATUS.IN_PROGRESS) {
            actions.push(
              createAction(
                order._id,
                ACTION_TYPE.START,
                INITATOR_SOURCE.THERAPIST,
                order.startTime
              )
            );

            if (order.status >= ORDER_STATUS.COMPLETED) {
              actions.push(
                createAction(
                  order._id,
                  ACTION_TYPE.COMPLETE,
                  INITATOR_SOURCE.THERAPIST,
                  order.endTime
                )
              );

              if (order.status === ORDER_STATUS.REVIEWED) {
                actions.push(
                  createAction(
                    order._id,
                    ACTION_TYPE.REVIEW,
                    INITATOR_SOURCE.USER,
                    order.endTime + 1000
                  )
                );
              }
            }
          }
        }
      }
    }

    // 处理退款操作
    if (order.refundStatus !== REFUND_STATUS.NONE) {
      if (order.refundStatus === REFUND_STATUS.AUDITING) {
        actions.push(
          createAction(
            order._id,
            ACTION_TYPE.REFUND_REQUEST,
            INITATOR_SOURCE.ADMIN,
            order.updatedAt - 3000,
            {
              // 随机 0/1/2
              reason: [0, 1, 2][Math.floor(Math.random() * 3)],
              detail: "退款原因详细描述",
            }
          )
        );
      } else if (order.refundStatus === REFUND_STATUS.REJECTED) {
        actions.push(
          createAction(
            order._id,
            ACTION_TYPE.REFUND_AUDIT_REJECT,
            INITATOR_SOURCE.ADMIN,
            order.updatedAt - 3000
          )
        );
      } else if (order.refundStatus >= REFUND_STATUS.PROCESSING) {
        actions.push(
          createAction(
            order._id,
            ACTION_TYPE.REFUND_AUDIT_PASS,
            INITATOR_SOURCE.ADMIN,
            order.updatedAt - 3000
          )
        );

        if (order.refundStatus === REFUND_STATUS.COMPLETED) {
          actions.push(
            createAction(
              order._id,
              ACTION_TYPE.REFUND_COMPLETE,
              INITATOR_SOURCE.SYSTEM,
              order.updatedAt
            )
          );
        } else if (order.refundStatus === REFUND_STATUS.FAILED) {
          actions.push(
            createAction(
              order._id,
              ACTION_TYPE.REFUND_FAILED,
              INITATOR_SOURCE.SYSTEM,
              order.updatedAt
            )
          );
        }
      }
    }
  });

  return actions;
}

// 辅助函数
function createAction(orderId, action, initiator, actionTime, extraData) {
  return {
    orderId,
    action,
    initiator,
    // reason: getActionReason(action),
    actionTime,
    extraData,
  };
}

function getRandomOrderStatus() {
  const statuses = Object.values(ORDER_STATUS);
  return statuses[Math.floor(Math.random() * statuses.length)];
}

function getRandomRefundStatus() {
  const statuses = Object.values(REFUND_STATUS).filter((s) => s !== 0);
  return statuses[Math.floor(Math.random() * statuses.length)];
}

function getRandomServiceType() {
  return Math.random() > 0.7 ? ServiceType.FACE_TO_FACE : ServiceType.VIDEO;
}

function getRandomDirection() {
  const directions = Object.values(ConsultationDirection);
  return directions[Math.floor(Math.random() * directions.length)];
}

// function getActionReason(action) {
//   const reasons = {
//     [ACTION_TYPE.CREATE]: "用户创建咨询订单",
//     [ACTION_TYPE.PAY]: "用户完成支付",
//     [ACTION_TYPE.SUBMIT_USER_INFO]: "用户提交咨询信息",
//     [ACTION_TYPE.ACCEPT]: "咨询师接受订单",
//     [ACTION_TYPE.REJECT]: "咨询师拒绝订单",
//     [ACTION_TYPE.CANCEL]: "用户取消订单",
//     [ACTION_TYPE.START]: "咨询服务开始",
//     [ACTION_TYPE.COMPLETE]: "咨询服务完成",
//     [ACTION_TYPE.REVIEW]: "用户评价服务",
//     [ACTION_TYPE.REFUND_REQUEST]: "用户申请退款",
//     [ACTION_TYPE.REFUND_AUDIT_PASS]: "管理员审核通过",
//     [ACTION_TYPE.REFUND_AUDIT_REJECT]: "管理员审核拒绝",
//     [ACTION_TYPE.REFUND_COMPLETE]: "退款已完成",
//     [ACTION_TYPE.REFUND_FAILED]: "退款失败",
//   };
//   return reasons[action] || "系统操作";
// }

// 保存数据到文件（微信云数据库格式）
function saveToFile(data, filename) {
  const stream = fs.createWriteStream(filename);

  data.forEach((item, index) => {
    // 移除空值和undefined字段
    const cleanItem = Object.fromEntries(
      Object.entries(item).filter(([_, v]) => v !== undefined && v !== null)
    );

    stream.write(JSON.stringify(cleanItem));
    stream.write("\n");
  });

  stream.end();
  console.log(`已生成 ${data.length} 条数据到 ${filename}`);
}

// 主函数
function generateData(N) {
  const orders = generateOrderSummaries(N);
  const actions = generateOrderActions(orders);

  saveToFile(orders, "order_summary.json");
  saveToFile(actions, "order_action.json");
}

// 生成240条订单数据
generateData(240);
