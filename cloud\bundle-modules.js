#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");
const esbuild = require("esbuild");
const { nodeExternalsPlugin } = require("esbuild-node-externals");

// 配置参数
const DEFAULT_ENTRY_DIR = "src/entries";
const DEFAULT_OUTPUT_DIR = "functions";
const PROJECT_PACKAGE_JSON = require(path.join(process.cwd(), "package.json"));

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const params = {
    entryFile: null,
    entryDir: DEFAULT_ENTRY_DIR,
    outputDir: DEFAULT_OUTPUT_DIR,
    minify: true,
    sourcemap: true,
    help: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case "-f":
      case "--file":
        params.entryFile = args[++i];
        break;
      case "-d":
      case "--dir":
        params.entryDir = args[++i];
        break;
      case "-o":
      case "--output":
        params.outputDir = args[++i];
        break;
      case "--no-minify":
        params.minify = false;
        break;
      case "--no-sourcemap":
        params.sourcemap = false;
        break;
      case "-h":
      case "--help":
        params.help = true;
        break;
    }
  }

  return params;
}

// 显示帮助信息
function showHelp() {
  console.log(`
TypeScript/JavaScript 模块化打包工具

用法:
  bundle-modules [选项]

选项:
  -f, --file <path>     指定单个入口文件打包
  -d, --dir <path>      指定入口目录 (默认: ${DEFAULT_ENTRY_DIR})
  -o, --output <path>   指定输出目录 (默认: ${DEFAULT_OUTPUT_DIR})
  --no-minify           禁用代码压缩
  --no-sourcemap        禁用源映射生成
  -h, --help            显示帮助信息

示例:
  打包所有模块: bundle-modules
  打包单个文件: bundle-modules -f src/entries/main.js
  指定输出目录: bundle-modules -o dist/functions
`);
  process.exit(0);
}

// 确保esbuild已安装
function ensureEsbuild() {
  try {
    require.resolve("esbuild");
  } catch (e) {
    console.log("正在安装esbuild...");
    execSync("npm install esbuild@0.18 esbuild-node-externals@2 --no-save", {
      stdio: "inherit",
    });
  }
}

// 获取所有入口文件
function getEntryFiles(dir) {
  const files = [];

  if (!fs.existsSync(dir)) {
    console.error(`错误: 入口目录 ${dir} 不存在`);
    process.exit(1);
  }

  const entries = fs.readdirSync(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      files.push(...getEntryFiles(fullPath));
    } else if (
      entry.isFile() &&
      (entry.name.endsWith(".js") || entry.name.endsWith(".ts"))
    ) {
      files.push(fullPath);
    }
  }

  return files;
}

// 从文件路径生成模块名称
function generateModuleName(filePath, entryDir) {
  const relativePath = path.relative(entryDir, filePath);
  return relativePath
    .replace(/\.[jt]s$/, "")
    .replace(/[\\/]/g, "-")
    .toLowerCase();
}

// 生成package.json内容
function generatePackageJson(moduleName) {
  return {
    name: `${moduleName}`,
    version: PROJECT_PACKAGE_JSON.version || "1.0.0",
    main: "index.js",
    type: "commonjs",
    description: `Auto-generated module for ${moduleName}`,
    license: PROJECT_PACKAGE_JSON.license || "MIT",
  };
}

// 处理单个入口文件
async function processEntryFile(
  entryFile,
  outputDir,
  entryDir,
  minify,
  sourcemap
) {
  const moduleName = generateModuleName(entryFile, entryDir);
  const moduleOutputDir = path.join(outputDir, moduleName);
  const jsFile = path.join(moduleOutputDir, "index.js");

  // 确保输出目录存在
  if (!fs.existsSync(moduleOutputDir)) {
    fs.mkdirSync(moduleOutputDir, { recursive: true });
  }

  console.log(
    `打包: ${path.relative(process.cwd(), entryFile)} → ${path.relative(
      process.cwd(),
      jsFile
    )}`
  );

  try {
    // 使用esbuild打包所有依赖（包括npm包）
    await esbuild.build({
      entryPoints: [entryFile],
      outfile: jsFile,
      bundle: true,
      platform: "node",
      format: "cjs",
      minify: minify,
      sourcemap: sourcemap,
      target: "node18",

      // 关键配置：确保所有依赖都被内联打包
      plugins: [
        nodeExternalsPlugin({
          packagePath: path.join(process.cwd(), "package.json"),
        }),
      ],

      // 处理特殊情况
      define: {
        "process.env.NODE_ENV": '"production"',
      },
      banner: {
        js: `
        // 所有依赖已内联打包
        // 生成时间: ${new Date().toISOString()}
        `,
      },
    });

    // 验证打包结果是否包含依赖
    const jsContent = fs.readFileSync(jsFile, "utf8");
    if (jsContent.includes("require(") && !jsContent.includes("// require")) {
      console.warn(
        `警告: ${moduleName} 可能包含未打包的依赖 - 尝试强制打包...`
      );

      // 尝试修复：强制打包所有依赖
      await esbuild.build({
        entryPoints: [entryFile],
        outfile: jsFile,
        bundle: true,
        platform: "node",
        format: "cjs",
        minify: minify,
        sourcemap: sourcemap,
        target: "node18",
        external: [], // 强制不排除任何依赖
        define: {
          "process.env.NODE_ENV": '"production"',
        },
      });
    }

    // 生成package.json
    const packageJson = generatePackageJson(moduleName);
    fs.writeFileSync(
      path.join(moduleOutputDir, "package.json"),
      JSON.stringify(packageJson, null, 2)
    );

    return { success: true, moduleName, outputDir: moduleOutputDir };
  } catch (error) {
    console.error(`打包失败: ${entryFile}`, error);
    return { success: false, moduleName, error };
  }
}

// 主函数
async function main() {
  const params = parseArgs();

  if (params.help) {
    showHelp();
    return;
  }

  console.log("=".repeat(60));
  console.log("TypeScript/JavaScript 模块化打包工具 (增强版)");
  console.log("=".repeat(60));
  console.log(`入口目录: ${params.entryDir}`);
  console.log(`输出目录: ${params.outputDir}`);
  console.log(`代码压缩: ${params.minify ? "启用" : "禁用"}`);
  console.log(`源映射: ${params.sourcemap ? "启用" : "禁用"}`);
  console.log("模式: 全依赖内联打包");
  console.log("=".repeat(60));

  ensureEsbuild();

  // 创建输出目录
  if (!fs.existsSync(params.outputDir)) {
    fs.mkdirSync(params.outputDir, { recursive: true });
  }

  // 获取所有入口文件
  let entryFiles = [];

  if (params.entryFile) {
    // 处理单个文件
    const fullPath = path.resolve(params.entryFile);
    if (!fs.existsSync(fullPath)) {
      console.error(`错误: 文件 ${fullPath} 不存在`);
      process.exit(1);
    }
    entryFiles = [fullPath];
  } else {
    // 处理整个目录
    entryFiles = getEntryFiles(params.entryDir);
  }

  if (entryFiles.length === 0) {
    console.log(`在${params.entryDir}目录中未找到任何JS/TS文件`);
    return;
  }

  console.log(`找到${entryFiles.length}个入口文件，开始处理...\n`);

  // 处理所有文件
  const results = [];
  for (const entryFile of entryFiles) {
    results.push(
      await processEntryFile(
        entryFile,
        params.outputDir,
        params.entryDir,
        params.minify,
        params.sourcemap
      )
    );
  }

  // 输出摘要
  console.log("\n" + "=".repeat(60));
  console.log("打包完成!");
  console.log("=".repeat(60));

  const successCount = results.filter((r) => r.success).length;
  const errorCount = results.filter((r) => !r.success).length;

  console.log(`模块总数: ${results.length}`);
  console.log(`成功: ${successCount}, 失败: ${errorCount}`);
  console.log(`输出目录: ${params.outputDir}`);

  if (successCount > 0) {
    console.log("\n生成的模块:");
    results
      .filter((r) => r.success)
      .forEach((r) => {
        console.log(`- ${r.moduleName}: ${r.outputDir}`);
      });
  }

  if (errorCount > 0) {
    console.log("\n失败的模块:");
    results
      .filter((r) => !r.success)
      .forEach((r) => {
        console.log(`- ${r.moduleName}: ${r.error.message}`);
      });
    process.exit(1); // 退出码1表示有错误
  }

  console.log("\n使用说明:");
  console.log(`1. 所有模块已输出到 ${params.outputDir} 目录`);
  console.log("2. 每个模块是独立可执行单元，无需额外依赖");
  console.log("3. 直接运行: node function/module-name/index.js");
  console.log("=".repeat(60));
}

// 启动打包过程
main().catch(console.error);
