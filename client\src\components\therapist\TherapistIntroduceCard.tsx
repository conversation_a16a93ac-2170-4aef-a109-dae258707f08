import { CONSULTATION_DIRECTIONS_MAP } from '@constants/text';
import { ConsultationDirection } from '@model/common.interface';
import { Text, View } from '@tarojs/components';

interface TherapistIntroduceCardProps {
  directions: ConsultationDirection[];
  specialties?: string[];
  introduction?: string;
  className?: string;
}

const TherapistIntroduceCard: React.FC<TherapistIntroduceCardProps> = ({
  directions,
  specialties = [],
  introduction = '',
  className = '',
}) => {
  return (
    <View className={`flex flex-col gap-4 ${className}`}>
      {/* 个人介绍 */}
      {introduction && (
        <View className='mb-2'>
          <Text className='font-semibold text-lg block mb-2'>个人简介</Text>
          <Text className='text-md text-default whitespace-pre-wrap'>
            {introduction}
          </Text>
        </View>
      )}

      {/* 擅长领域 */}
      {specialties && specialties.length > 0 && (
        <View className='mb-2'>
          <Text className='font-semibold text-lg block mb-2'>擅长领域</Text>
          <View className='flex flex-row flex-wrap gap-2'>
            {specialties.map((specialty) => (
              <View
                key={specialty}
                className='px-3 py-1 bg-gray-100 rounded-full'
              >
                <Text className='text-sm'>{specialty}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* 咨询方向 */}
      {directions && directions.length > 0 && (
        <View>
          <Text className='font-semibold text-lg block mb-2'>咨询方向</Text>
          {directions.map((direction) => (
            <Text key={direction} className='font-medium mr-2 last:mr-0'>
              {
                CONSULTATION_DIRECTIONS_MAP.find(
                  (item) => item.key === direction
                )?.label
              }
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

export default TherapistIntroduceCard;
