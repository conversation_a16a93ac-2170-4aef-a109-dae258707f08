import { View } from '@tarojs/components';
import { useEffect, useRef, useState } from 'react';

export default function WaveAnimation({
  isPlaying,
  frameClassName,
}: {
  isPlaying: boolean;
  frameClassName: string;
}) {
  const [playingAnimationFrame, setPlayingAnimationFrame] = useState(0);
  const animationInterval = useRef<any | null>(null);

  useEffect(() => {
    if (!isPlaying) {
      if (animationInterval?.current) {
        clearInterval(animationInterval.current);
      }
      return;
    }

    let frame = 0;
    animationInterval.current = setInterval(() => {
      frame = (frame + 1) % 3;
      setPlayingAnimationFrame(frame);
    }, 300);

    return () => {
      if (animationInterval?.current) {
        clearInterval(animationInterval.current);
      }
    };
  }, [isPlaying]);

  return (
    <View className='flex items-end h-4'>
      <View
        className={`w-1 mx-0.5 ${frameClassName} rounded-full animate-voice-1`}
        style={{
          height:
            playingAnimationFrame === 0
              ? '6px'
              : playingAnimationFrame === 1
              ? '12px'
              : '8px',
        }}
      ></View>
      <View
        className={`w-1 mx-0.5 ${frameClassName} rounded-full animate-voice-2`}
        style={{
          height:
            playingAnimationFrame === 0
              ? '12px'
              : playingAnimationFrame === 1
              ? '6px'
              : '12px',
        }}
      ></View>
      <View
        className={`w-1 mx-0.5 ${frameClassName} rounded-full animate-voice-3`}
        style={{
          height:
            playingAnimationFrame === 0
              ? '8px'
              : playingAnimationFrame === 1
              ? '12px'
              : '6px',
        }}
      ></View>
    </View>
  );
}
