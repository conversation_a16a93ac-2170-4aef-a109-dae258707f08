import { Toast } from '@antmjs/vantui';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { userProfileActions } from '@core/actions/profile.user';
import { USER_ROLE } from '@model/user.interface';
import { uploadService } from '@services/upload.service';
import { useGlobalStore } from '@stores/global.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useUserProfileStore } from '@stores/profile.user';
import { Input, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { FC, useState } from 'react';
import { AvatarUploader } from '../../../../components/common/AvatarEdit';

const UserProfileEditor: FC<{
  onComplete?: () => void;
}> = ({ onComplete }) => {
  const currentRole = useGlobalStore.use.currentRole();
  const currentUserAvatar =
    currentRole === USER_ROLE.USER
      ? useUserProfileStore.getState().profile?.avatar
      : useTherapistProfileStore.getState().myProfile?.avatar;
  const currentUserNickname =
    currentRole === USER_ROLE.USER
      ? useUserProfileStore.getState().profile?.userName
      : useTherapistProfileStore.getState().myProfile?.name;

  const [avatar, setAvatar] = useState<string>(currentUserAvatar || '');
  const [nickname, setNickname] = useState<string>(currentUserNickname || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 处理头像选择
  const handleChooseAvatar = async (e) => {
    try {
      console.log('handleChooseAvatar', e);
      const { avatarUrl } = e.detail;
      if (!avatarUrl) return;

      // 显示加载提示
      Taro.showLoading({ title: '上传中...' });

      // 上传头像到云存储
      const uploadResult = await uploadService.uploadAvatar(avatarUrl);

      setAvatar(uploadResult);
      handleSaveProfile(uploadResult, nickname);
    } catch (error) {
      console.error('上传头像失败:', error);
      Toast.show('上传头像失败，请重试');
    } finally {
      Taro.hideLoading();
    }
  };

  // 处理昵称输入
  const handleNicknameChange = (e) => {
    console.log('handleNicknameChange', e);
    setNickname(e.detail.value);
    handleSaveProfile(avatar, e.detail.value);
  };

  // 保存用户资料
  const handleSaveProfile = async (a: string, n: string) => {
    console.log('handleSaveProfile', isSubmitting);
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      const actions =
        currentRole === USER_ROLE.USER
          ? userProfileActions
          : therapistProfileActions;
      await actions.updateAvatarAndNickname(a, n);
      onComplete?.();
    } catch (error) {
      console.error('保存用户资料失败:', error);
      Toast.show('保存失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View className='flex flex-col w-full p-4'>
      <View className='flex flex-col w-full space-y-6'>
        <AvatarUploader
          handleChooseAvatar={handleChooseAvatar}
          avatar={avatar}
        />
        <View className='h-px bg-gray-200 w-full' />
        <View className='flex flex-row w-full items-center my-1'>
          <Text className='text-base text-default mr-2'>昵称</Text>
          <Input
            className='flex-1 border border-border rounded-md px-3'
            type='nickname'
            value={nickname}
            placeholder='请输入昵称'
            maxlength={20}
            onInput={handleNicknameChange}
          />
        </View>
        <View className='h-px bg-gray-200 w-full' />

        {/* <Button
          className='mt-8 w-full rounded-full'
          type='primary'
          loading={isSubmitting}
          disabled={isSubmitting}
          onClick={handleSaveProfile}
        >
          保存
        </Button> */}
      </View>
    </View>
  );
};

export default UserProfileEditor;
