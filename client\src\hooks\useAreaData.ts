import { useConfigStore } from '@stores/config.store';
import { areaList } from '@vant/area-data';
import { useEffect, useState } from 'react';

/**
 * 自定义Hook，用于处理省市数据的初始化和管理
 * @returns 省市数据和相关状态
 */
export function useAreaData() {
  // 地区数据状态
  const [activeProvince, setActiveProvince] = useState('');
  const [activeCity, setActiveCity] = useState('');
  const [provinces, setProvinces] = useState<{ [key: string]: string }>({});
  const [cities, setCities] = useState<{
    [key: string]: { [key: string]: string };
  }>({});
  const [location, setLocation] = useState('');

  // 从配置store中获取热门城市
  const { hotCities } = useConfigStore((state) => state.appConfig);

  // 初始化省市数据
  useEffect(() => {
    if (areaList) {
      // 处理省份数据
      const provinceData: { [key: string]: string } = {};
      Object.entries(areaList.province_list || {}).forEach(([code, name]) => {
        provinceData[code] = name;
      });
      setProvinces(provinceData);

      // 处理城市数据
      const cityData: { [key: string]: { [key: string]: string } } = {};
      Object.entries(areaList.city_list || {}).forEach(([code, name]) => {
        const provinceCode = code.substring(0, 2) + '0000';
        if (!cityData[provinceCode]) {
          cityData[provinceCode] = {};
        }
        cityData[provinceCode][code] = name;
      });
      setCities(cityData);
    }
  }, [areaList]);

  // 选择省份
  const handleProvinceSelect = (code: string, name: string) => {
    setActiveProvince(code);
    setActiveCity('');
    // 如果没有下级城市，直接设置地区筛选
    if (!cities[code] || Object.keys(cities[code]).length === 0) {
      setLocation(name);
      return true;
    }
    return false;
  };

  // 选择城市
  const handleCitySelect = (code: string, name: string) => {
    setActiveCity(code);
    setLocation(name);
    return true;
  };

  // 选择热门城市
  const handleHotCitySelect = (cityName: string) => {
    setLocation(cityName);
    return true;
  };

  // 重置地区选择
  const resetAreaSelection = () => {
    setActiveProvince('');
    setActiveCity('');
    setLocation('');
  };

  return {
    // 状态
    activeProvince,
    activeCity,
    provinces,
    cities,
    location,
    hotCities,

    // 方法
    handleProvinceSelect,
    handleCitySelect,
    handleHotCitySelect,
    resetAreaSelection,
    setLocation,
  };
}
