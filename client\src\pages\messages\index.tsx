import { Badge, Empty, Icon, Image } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { AVATAR_DEFAULT } from '@constants/assets';
import { chatActions } from '@core/actions/chat.action';
import {
  AnySession,
  ChatSession,
  MessageContentType,
  SystemChatSession,
} from '@model/chat.interface';
import { useChatStore } from '@stores/chat.store';
import { useGlobalStore } from '@stores/global.store';
import { Text, View } from '@tarojs/components';
import { appRouter } from '@utils/router';
import { formatTime6 } from '@utils/time';
import { useCallback, useEffect } from 'react';

export default function MessagesPage() {
  // 获取会话列表
  const sessions = useChatStore.use.sessions();
  const loading = useChatStore.use.loading();
  const error = useChatStore.use.error();

  // 加载会话列表
  useEffect(() => {
    chatActions.fetchSessions();
  }, []);
  const refresh = useCallback(async () => {
    await chatActions.fetchSessions();
  }, []);

  // // 模拟一个系统会话
  // const systemSession: SystemChatSession = {
  //   id: 'system',
  //   title: '系统消息',
  //   avatar: ICONS_BOLD_PRIMARY.NOTIFICATION || '',
  //   unreadCount: 0,
  //   timestamp: Date.now(),
  //   type: 'system',
  // };
  // todo: 系统会话

  // 合并系统会话和普通会话
  const allSessions: AnySession[] = [...sessions];

  // 按时间戳倒序排序
  const sortedSessions = [...allSessions].sort(
    (a, b) => b.timestamp - a.timestamp
  );

  // 处理会话点击
  const handleSessionClick = (session: AnySession) => {
    if ('type' in session && session.type === 'system') {
      // 跳转到系统消息页面
      appRouter.systemChat();
    } else {
      // 跳转到聊天页面
      const chatSession = session as ChatSession;
      appRouter.chat(
        chatSession.AId === useGlobalStore.getState().openid
          ? chatSession.BId
          : chatSession.AId
      );
    }
  };

  // 渲染消息内容预览
  const renderLastMessageContent = (session: AnySession) => {
    const lastMessage = session.lastMessage;

    if (!lastMessage) {
      return '暂无消息';
    }

    switch (lastMessage.contentType) {
      case MessageContentType.IMAGE:
        return '[图片]';
      case MessageContentType.VOICE:
        return '[语音]';
      case MessageContentType.VIDEO:
        return '[视频]';
      case MessageContentType.FILE:
        return '[文件]';
      case MessageContentType.ORDER:
        return '[订单]';
      case MessageContentType.LOCATION:
        return '[位置]';
      default:
        return lastMessage.content;
    }
  };

  // 渲染会话项
  const renderSessionItem = (session: AnySession) => {
    // 获取会话头像
    const avatar =
      'type' in session && session.type === 'system'
        ? session.avatar || ''
        : (session as ChatSession).AId === useGlobalStore.getState().openid
        ? (session as ChatSession).BAvatar || AVATAR_DEFAULT
        : (session as ChatSession).AAvatar || AVATAR_DEFAULT;

    // 获取会话标题
    const title =
      'type' in session && session.type === 'system'
        ? session.title
        : (session as ChatSession).AId === useGlobalStore.getState().openid
        ? (session as ChatSession).BName || '未知用户'
        : (session as ChatSession).AName || '未知用户';

    // 格式化时间
    const time = session.lastMessage
      ? formatTime6(new Date(session.lastMessage.timestamp))
      : '';

    const unreadCount =
      'type' in session && session.type === 'system'
        ? (session as SystemChatSession).unreadCount
        : (session as ChatSession).AId === useGlobalStore.getState().openid
        ? (session as ChatSession).AUnreadCount
        : (session as ChatSession).BUnreadCount;

    console.log('🚀🚀🚀 session unreadCount', session, unreadCount);
    return (
      <View
        key={session.id}
        onClick={() => handleSessionClick(session)}
        className='px-4 py-2 mb-3'
      >
        <View className='flex flex-row items-center w-full'>
          {/* 头像区域 */}
          <View className='relative mr-4'>
            <Image
              src={avatar}
              radius={100}
              className='w-14 h-14 rounded-full'
            />
          </View>

          {/* 内容区域 */}
          <View className='flex-1'>
            <View className='flex flex-row justify-between items-start mb-1'>
              <Text className='text-base font-bold text-default'>{title}</Text>
              <Text className='text-sm font-bold text-secondary'>{time}</Text>
            </View>
            <View className='flex flex-row justify-between items-end'>
              <View className='text-md text-secondary items-center justify-start flex flex-1 truncate mr-4'>
                {renderLastMessageContent(session)}
              </View>
              {unreadCount > 0 && <Badge content={unreadCount} />}
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <PageLol
      onPullDownRefresh={refresh}
      navigationProps={{
        title: '消息',
        showBackButton: true,
      }}
      error={error || null}
    >
      <View className='min-h-screen'>
        {loading && sortedSessions.length === 0 ? (
          <View className='flex items-center justify-center h-64'>
            <Icon name='loading' size={32} />
          </View>
        ) : sortedSessions.length > 0 ? (
          <View className='pt-4'>
            {sortedSessions.map((session) => renderSessionItem(session))}
          </View>
        ) : (
          <View className='mt-8'>
            <Empty description='暂无消息会话' />
          </View>
        )}
      </View>
    </PageLol>
  );
}
