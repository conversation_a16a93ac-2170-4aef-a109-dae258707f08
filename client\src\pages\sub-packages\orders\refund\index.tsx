import PageLol from '@components/common/page-meta';
import ReasonForm from '@components/common/reason-form';
import { REFUND_REASON_MAP } from '@constants/text';
import { orderTherapistActions } from '@core/actions/order.therapist';
import {
  ORDER_STATUS,
  REFUND_REASON,
  REFUND_STATUS,
} from '@model/order.interface';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';
import { View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

export default function RefundPage() {
  const router = useRouter();
  const { orderId } = router.params;
  console.log('RefundPage orderId', orderId, typeof orderId);
  const { transactionLoading } = useLoadingStore();
  const [reason, setReason] = useState<REFUND_REASON | null>(null);
  const [detail, setDetail] = useState<string | null>(null);
  //订阅订单的状态
  const order = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId ?? ''))
  );

  const [localError, setLocalError] = useState<string | null>(null);

  if (!orderId) {
    console.error('订单ID不存在', orderId);
    return;
  }

  if (order?.status && order.status <= ORDER_STATUS.PENDING_CONFIRM) {
    setLocalError('订单状态不正确');
  }

  // 检查是否已经有退款申请
  if (order?.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
    setLocalError('已有退款申请，无法重复申请');
  }

  const handleSubmit = async () => {
    try {
      await orderTherapistActions.refundRequest(orderId, reason!, detail ?? '');
    } catch (error) {
      setLocalError(error.message || '退款申请失败');
    }
  };

  return (
    <PageLol
      navigationProps={{ title: '退款申请', showBackButton: true }}
      error={localError || null}
    >
      <View className='cancel-page'>
        <ReasonForm
          title='请选择退款原因'
          reasonOptions={REFUND_REASON_MAP}
          selectedReason={reason}
          detail={detail}
          onReasonChange={(value) => setReason(value as REFUND_REASON)}
          onDetailChange={(value) => setDetail(value)}
          onSubmit={handleSubmit}
          loading={transactionLoading}
          placeholder='请详细描述退款原因'
        />
      </View>
    </PageLol>
  );
}
