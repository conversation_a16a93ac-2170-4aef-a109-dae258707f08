import { Circle, Icon, Image, NoticeBar, Rate } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import HomeBanners from '@components/home-swiper';
import { ICONS_BOLD, ICONS_BULK } from '@constants/assets';
import { homeActions } from '@core/actions/home.action';
import { incomeActions } from '@core/actions/income.action';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import {
  ActiveCourseCard,
  LearningCard,
  LearningCardType,
  RecommendedResourceCard,
  ResourceType,
} from '@model/course.interface';
import { useHomeStore } from '@stores/home.store';
import { useIncomeStore } from '@stores/income.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { therapistRouter } from '@utils/therapistRouter';
import { formatDate2 } from '@utils/time';
import { useEffect, useRef, useState } from 'react';

export default function TherapistHomePage() {
  const myProfile = useTherapistProfileStore((state) => state.myProfile);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const { notices, banners, orderToday, recommendedCourses, loading } =
    useHomeStore();

  const statSummary = useIncomeStore.use.statSummary();

  // 安全的增加计数
  const renderCount = useRef(0);
  renderCount.current += 1;

  console.log('TherapistHomePage 安全渲染次数:', renderCount.current);

  // 导航栏自定义内容 - 包含问候语和头像
  const NavTitleWithGreeting = () => {
    const hour = new Date().getHours();
    let greeting = '晚上好';

    if (hour < 12) {
      greeting = '早上好';
    } else if (hour < 18) {
      greeting = '下午好';
    }

    return (
      <View className='flex flex-row items-center '>
        {myProfile?.avatar && (
          <Image
            src={myProfile.avatar}
            width='24px'
            height='24px'
            radius='999px'
          />
        )}
        <Text className='text-base font-medium ml-2 text-default'>
          {greeting}，{myProfile?.name || '咨询师'}
        </Text>
      </View>
    );
  };

  // 个人提升课程
  // const [courses, setCourses] = useState<LearningCard[]>([
  //   {
  //     id: 'active-101',
  //     type: LearningCardType.ACTIVE_COURSE,
  //     resourceId: 'course-205',
  //     resourceType: ResourceType.COURSE,
  //     title: 'PTSD干预',
  //     subtitle: '高级课程 · 进度 35%',
  //     description: '当前章节：PTSD诊断标准',
  //     metadata: {
  //       progress: 35,
  //       duration: 510,
  //       sectionsCount: 12,
  //       currentSection: 'PTSD诊断标准',
  //       nextSection: '临床实操演练',
  //       timeSpent: 180,
  //       estimatedCompletion: 1719859200,
  //     },
  //     actions: {
  //       primary: '继续学习',
  //       secondary: '学习笔记',
  //     },
  //     visual: {
  //       thumbnail: 'https://example.com/thumbs/ptsd-course.jpg',
  //       color: '#FF9800',
  //     },
  //     createdAt: 1719859200,
  //     updatedAt: 1719859200,
  //   },
  //   {
  //     id: 'active-101',
  //     type: LearningCardType.ACTIVE_COURSE,
  //     resourceId: 'course-205',
  //     resourceType: ResourceType.COURSE,
  //     title: 'PTSD干预',
  //     subtitle: '高级课程 · 进度 35%',
  //     description: '当前章节：PTSD诊断标准',
  //     metadata: {
  //       progress: 35,
  //       duration: 510,
  //       sectionsCount: 12,
  //       currentSection: 'PTSD诊断标准诊断标准',
  //       nextSection: '临床实操演练',
  //       timeSpent: 180,
  //       estimatedCompletion: 1719859200,
  //     },
  //     actions: {
  //       primary: '继续学习',
  //       secondary: '学习笔记',
  //     },
  //     visual: {
  //       thumbnail: 'https://example.com/thumbs/ptsd-course.jpg',
  //       color: '#FF9800',
  //     },
  //     createdAt: 1719859200,
  //     updatedAt: 1719859200,
  //   },
  //   {
  //     id: 'deadline-202',
  //     type: LearningCardType.UPCOMING_DEADLINE,
  //     resourceId: 'course-301',
  //     resourceType: ResourceType.COURSE,
  //     title: '儿童心理',
  //     subtitle: '截止日期临近',
  //     description: '完成全部课程以获得认证',
  //     metadata: {
  //       deadline: 1719859200,
  //       daysLeft: 9,
  //       progress: 78,
  //       duration: 600,
  //       importance: 'high',
  //     },
  //     actions: {
  //       primary: '立即完成',
  //       secondary: '查看进度',
  //     },
  //     visual: {
  //       icon: 'deadline-icon',
  //       color: '#F44336',
  //     },
  //     createdAt: 1719859200,
  //     updatedAt: 1719859200,
  //   },
  // ]);
  // 加载数据
  const loadData = async (forceRefresh = false) => {
    useHomeStore.getState().setLoading(true);
    try {
      await Promise.all([
        therapistProfileActions.loadMyProfile(forceRefresh),
        homeActions.fetchNotices('therapist'),
        homeActions.fetchBanners('therapist'),
        homeActions.fetchOrderToday(),
        homeActions.fetchRecommendedCourses(forceRefresh),
        incomeActions.fetchStatSummary(forceRefresh),
      ]);
    } catch (err) {
      console.error('加载咨询师工作台数据失败:', err);
      setErrorMsg('加载数据失败，请重试');
    } finally {
      useHomeStore.getState().setLoading(false);
    }
  };
  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 跳转到个人资料页面
  const navigateToProfile = () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/therapist-updater/index/index',
    });
  };

  // 跳转到工作时间页面
  const navigateToWorkTime = () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/therapist-updater/work-time/index',
    });
  };
  // 跳转到排班编辑页面
  const navigateToSchedule = () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/therapist-updater/schedule-info/index',
    });
  };

  // 跳转到特殊约定页面
  const navigateToSpecial = () => {
    Taro.navigateTo({
      url: '/pages/sub-packages//settings/index',
    });
  };

  // 跳转到订单列表
  const navigateToOrders = (tab?: string) => {
    therapistRouter.orders(tab);
  };

  // 渲染订单汇总导航
  const renderOrderSummary = () => {
    const orderTypes = [
      {
        key: 'pending_confirm',
        label: '待确认',
        icon: 'checked',
        count: orderToday?.pendingConfirm,
      },
      {
        key: 'refund',
        label: '退款/售后',
        icon: 'after-sale',
        count: orderToday?.refundOrAfterSale,
      },
      {
        key: 'complaint',
        label: '投诉',
        icon: 'comment-o',
        count: orderToday?.complaint,
      },
    ];

    return (
      <View className='mx-4 mt-4 py-3 bg-gradient-to-r from-[#E0EAFC] to-[#CFDEF3]  rounded-2xl'>
        <Text className='text-base font-bold mx-4'>今日订单</Text>

        <View className='mt-6 mx-4 flex flex-row justify-between  items-center '>
          {/* 待确认 */}
          <View
            className='flex flex-col justify-start'
            onClick={() => navigateToOrders('pending_confirm')}
          >
            <View className='flex flex-row items-center'>
              <View className='w-2 h-2 rounded-full bg-danger mr-1 ' />
              <Text className='text-sm font-medium text-default'>待确认</Text>
            </View>
            <View className='flex flex-row items-baseline'>
              <Text className='text-2xl font-bold text-default'>
                {orderToday?.pendingConfirm}
              </Text>
              <Text className='text-sm text-secondary'>
                /{orderToday?.totalOrder}单
              </Text>
            </View>
          </View>
          {/* 分割线 */}
          <View className='w-px h-10 bg-placeholder' />
          {/* 退款/售后 */}
          <View
            className='flex flex-col justify-start'
            onClick={() => navigateToOrders('refund')}
          >
            <View className='flex flex-row items-center'>
              <View className='w-2 h-2 rounded-full bg-warning mr-1 ' />
              <Text className='text-sm font-medium text-default'>
                退款/售后
              </Text>
            </View>
            <View className='flex flex-row items-baseline'>
              <Text className='text-2xl font-bold text-default'>
                {orderToday?.refundOrAfterSale}
              </Text>
              <Text className='text-sm text-secondary'>
                /{orderToday?.totalAmount}￥
              </Text>
            </View>
          </View>
          {/* 分割线 */}
          <View className='w-px h-10 bg-placeholder ' />
          {/* 投诉 */}
          <View
            className='flex flex-col justify-start'
            onClick={() => navigateToOrders('complaint')}
          >
            <View className='flex flex-row items-center'>
              <View className='w-2 h-2 rounded-full bg-gray-400 mr-1 ' />
              <Text className='text-sm font-medium text-default'>投诉</Text>
            </View>
            <View className='flex flex-row items-baseline'>
              <Text className='text-2xl font-bold text-default'>
                {orderToday?.complaint}
              </Text>
              <Text className='text-sm text-secondary'>
                /{orderToday?.totalOrder}单
              </Text>
            </View>
          </View>
        </View>

        {/* 规则中心提示 */}
        <View className='mt-6 mb-3 py-1.5 pl-8 pr-2 mx-4  bg-white rounded-full flex flex-row justify-between items-center text-nowrap'>
          <View className='flex flex-row items-center text-nowrap'>
            <Text className='text-sm text-default'>规则中心</Text>
            <Text className='text-sm text-secondary mx-2'>|</Text>
            <Text className='text-sm text-secondary'>查看规则中心</Text>
          </View>
          <Icon name='arrow' size='16px' />
        </View>
      </View>
    );
  };

  // 渲染快捷功能
  const renderQuickActions = () => {
    return (
      <View className='mx-4 mt-4'>
        <View className='flex flex-row justify-between  items-center px-4 py-4 bg-white rounded-xl'>
          {/* 个人资料 */}
          <View
            className='px-3   flex flex-col items-center'
            onClick={navigateToProfile}
          >
            <View className='w-10 h-10 rounded-full flex items-center justify-center bg-gray-100'>
              <Icon name={ICONS_BULK.PROFILE} size='24px' />
            </View>
            <Text className='text-sm font-medium mt-2'>个人资料</Text>
          </View>

          {/* 排班编辑 */}
          <View
            className='px-3   flex flex-col items-center'
            onClick={navigateToSchedule}
          >
            <View className='w-10 h-10 rounded-full flex items-center justify-center bg-gray-100'>
              <Icon name={ICONS_BULK.SCHEDULE} size='24px' />
            </View>
            <Text className='text-sm font-medium mt-2'>排班编辑</Text>
          </View>

          {/* 工作时间 */}
          <View
            className='px-3  flex flex-col items-center'
            onClick={navigateToWorkTime}
          >
            <View className='w-10 h-10 rounded-full flex items-center justify-center bg-gray-100'>
              <Icon name={ICONS_BULK.WORK_TIME} size='24px' />
            </View>
            <Text className='text-sm font-medium mt-2'>工作时间</Text>
          </View>

          {/* 特殊约定 */}
          <View
            className='px-3   flex flex-col items-center'
            onClick={navigateToSpecial}
          >
            <View className='w-10 h-10 rounded-full flex items-center justify-center bg-gray-100'>
              <Icon name={ICONS_BULK.NOTE} size='24px' />
            </View>
            <Text className='text-sm font-medium mt-2'>特殊约定</Text>
          </View>
        </View>
      </View>
    );
  };

  // 渲染课程项卡片。支持3种样式：1. 标题+描述+环形进度条 2. 日期/more icon + 主标题文字 + 子标题/百分号进度/进度条 3. 图片+标题文字
  // 3. 标签组 + 标题/描述 + 课时时长/播放文本语音按钮
  const renderCourseItem = (card: LearningCard, index: number) => {
    const renderContent = () => {
      switch (card.type) {
        //进行中的课程 支持两种样式，交替展示：1. 标题+描述+环形进度条 2. 日期/more icon + 主标题文字 + 子标题/百分号进度/进度条
        case LearningCardType.ACTIVE_COURSE: {
          const {
            title,
            description,
            metadata: { progress, duration, currentSection, lastAccessed },
          } = card as ActiveCourseCard;

          return index % 2 === 0 ? (
            <>
              <View className='flex flex-col items-start w-full'>
                {/* 主标题 + indicator*/}
                <View className='flex flex-row items-center  w-full justify-between'>
                  <Text className='text-base font-bold text-default'>
                    {title}
                  </Text>
                  <View className='w-2 h-2 rounded-full bg-green-400' />
                </View>
                {/*arrow + 子标题   */}
                <View className='flex flex-row items-center mt-1 w-full'>
                  {/* <Icon
                    name='arrow'
                    size='12px'
                    className='mr-1 text-secondary'
                  /> */}
                  <View className='text-sm font-medium text-secondary whitespace-pre-line'>
                    {currentSection || description}
                  </View>
                </View>
              </View>
              {/* 底部信息 环形进度条*/}
              <View className='flex w-full justify-end'>
                <Circle
                  value={progress}
                  text={`${progress}%`}
                  size={40}
                  strokeWidth={4}
                  layerColor='#DEDDDD'
                />
              </View>
            </>
          ) : (
            <>
              {/* 日期 */}
              <View className='flex flex-row items-center justify-between w-full'>
                <Text className='text-xs font-medium text-default'>
                  {formatDate2(lastAccessed || 0)}
                </Text>
                {/* <Icon name='more-o' size='16px' color={COLORS.secondary} /> */}
              </View>
              {/* 主标题 */}
              <View className='flex flex-row items-center w-full'>
                <Text className='text-base font-bold text-default'>
                  {title}
                </Text>
              </View>
              {/* 底部信息 */}
              <View className='flex flex-col w-full'>
                {/* 子标题+百分号进度 */}
                <View className='flex flex-row w-full items-center justify-between'>
                  <View className='w-full  truncate text-sm font-medium text-secondary'>
                    {currentSection}
                  </View>
                  <Text className='text-sm font-medium text-secondary'>
                    {progress}%
                  </Text>
                </View>
                {/* 条形进度条 */}
                <View className='w-full h-1.5 bg-gray-200 rounded-full mt-2'>
                  <View
                    className='h-full bg-primary rounded-full'
                    style={{ width: `${progress}%` }}
                  />
                </View>
              </View>
            </>
          );
        }
        //推荐资源 支持两种样式，交替展示：1. 标签组 + 标题/描述 + 课时时长/播放文本语音按钮 2. ICON + 主标题 + 描述 + 评分
        case LearningCardType.RECOMMENDED_RESOURCE: {
          const {
            title,
            description,
            resourceType,
            metadata: { tags, duration, rating },
            visual,
          } = card as RecommendedResourceCard;

          if (tags && tags.length > 0) {
            return (
              <>
                {/* 标签组 只显示前2个， 背景分别用secondaryLight和tertiaryLight*/}
                <View className='flex flex-row items-center w-full'>
                  {tags.slice(0, 2).map((tag, i) => (
                    <View
                      key={tag}
                      className={`mr-2 px-2 py-1 rounded-full text-sm font-medium text-nowrap text-default ${
                        i === 0 ? 'bg-secondaryLight' : 'bg-tertiaryLight'
                      }`}
                    >
                      {tag}
                    </View>
                  ))}
                </View>
                {/* 主标题 + 描述*/}
                <View className='flex flex-col items-start w-full'>
                  <Text className='text-base font-bold text-default'>
                    {title}
                  </Text>
                  <View className='text-md  text-secondary whitespace-pre-line'>
                    {description}
                  </View>
                </View>
                {/* 课时时长 + 播放文本语音按钮*/}
                <View className='flex flex-row items-center justify-between w-full'>
                  <Text className='text-xs font-medium text-secondary'>
                    {duration ? `${duration / 60}课时` : ''}
                  </Text>
                  <View className='flex flex-row items-center w-full'>
                    <Icon
                      name={visual?.icon || ''}
                      size='24px'
                      color='var(--color-primary)'
                    />
                  </View>
                </View>
              </>
            );
          } else if (rating) {
            return (
              <>
                {/* ICON */}
                <View className='flex flex-row items-center justify-center bg-gray-100 rounded-full w-8 h-8 '>
                  <Icon
                    name={visual?.icon ?? ''}
                    size='24px'
                    color='var(--color-primary)'
                  />
                </View>
                {/* 主标题 + 描述*/}
                <View className='flex flex-col items-start w-full'>
                  <Text className='text-base font-bold text-default'>
                    {title}
                  </Text>
                  <View className='w-full text-md  text-secondary whitespace-pre-line'>
                    {description}
                  </View>
                </View>
                {/* 评分 */}
                <View className='flex flex-row items-center w-full'>
                  <Rate value={rating} size='16px' readonly />
                </View>
              </>
            );
          }
        }
        default:
          const { title, description, visual, resourceType } = card;
          return (
            <>
              {/* ICON */}
              <View className='flex flex-row items-center w-full  '>
                <Icon
                  name={
                    visual?.icon || resourceType === ResourceType.COURSE
                      ? 'book'
                      : ''
                  }
                  size='24px'
                  color='var(--color-primary)'
                />
              </View>

              {/* 主标题 + 描述*/}
              <View className='flex flex-col items-start w-full'>
                <Text className='text-base font-bold text-default'>
                  {title}
                </Text>
                <View
                  className='w-full text-md  text-secondary  '
                  style={{
                    whiteSpace: 'pre-wrap',
                  }}
                >
                  {description}
                </View>
              </View>
            </>
          );
      }
    };
    return (
      <View className='w-[128Px] h-[160Px] bg-white rounded-xl px-4 py-3 flex flex-col items-start justify-between mr-4'>
        {renderContent()}
      </View>
    );
  };
  // 渲染个人提升区域
  const renderProfessionalDevelopment = () => {
    return (
      <View className='px-4 py-3 mt-4'>
        <View className='flex flex-row justify-between items-center mb-3'>
          <Text className='text-base font-bold'>为您推荐</Text>
          <Text className='text-sm text-secondary'>查看全部</Text>
        </View>
        {/* 课程列表 横向滚动*/}
        <View className='w-full'>
          <ScrollView
            scrollX
            className='whitespace-nowrap py-2'
            // scrollWithAnimation
          >
            <View className='inline-flex'>
              {
                /* 渲染课程列表 先排序：1. 进行中的课程 2. 推荐资源 3. 即将截止 4. 新添加资源*/
                recommendedCourses
                  ?.slice(0, 3)
                  .sort((a, b) => {
                    if (a.type === LearningCardType.ACTIVE_COURSE) {
                      return -1;
                    } else if (
                      a.type === LearningCardType.RECOMMENDED_RESOURCE
                    ) {
                      return 1;
                    }
                    return 0;
                  })
                  .map((course, index) => renderCourseItem(course, index))
              }
            </View>
          </ScrollView>
        </View>
      </View>
    );
  };

  // 渲染收入统计卡片
  const renderIncomeStats = () => {
    return (
      statSummary && (
        <View className='mx-4 mt-4' onClick={appRouter.incomeDashboard}>
          <View className='flex flex-col bg-white rounded-xl px-4 py-3'>
            <View className='flex flex-row justify-between items-center'>
              <Text className='text-base font-bold'>收入统计</Text>
              <Text className='text-sm text-secondary'>查看详情</Text>
            </View>

            <View className='mt-5 flex flex-row justify-between items-center gap-4'>
              {/* 邀请收益卡片 */}
              <View className='p-3 bg-white border border-border rounded-2xl flex flex-col items-start justify-between h-[128Px] flex-1 shadow-sm'>
                <View className='flex flex-col items-start'>
                  <View className='flex justify-center items-center bg-green-50 rounded-full w-8 h-8'>
                    <Icon name={ICONS_BOLD.DISTRIBUTION} size='16px' />
                  </View>
                  <Text className='text-sm text-default font-medium'>
                    咨询收入
                  </Text>
                </View>
                <View className='flex flex-row items-baseline'>
                  <Text className='text-sm font-bold text-secondary'>¥</Text>
                  <Text className='text-base font-bold text-default'>
                    {statSummary.income.completed}
                  </Text>
                </View>
              </View>
              {/* 推广收益卡片 */}
              <View className='p-3 bg-white border border-border rounded-2xl flex flex-col items-start justify-between h-[128Px] flex-1 shadow-sm'>
                <View className='flex flex-col items-start'>
                  <View className='flex justify-center items-center bg-blue-50 rounded-full w-8 h-8'>
                    <Icon name={ICONS_BOLD.INCOME} size='16px' />
                  </View>
                  <Text className='text-sm text-default font-medium'>
                    推广收入
                  </Text>
                </View>
                <View className='flex flex-row items-baseline'>
                  <Text className='text-sm font-bold text-secondary'>¥</Text>
                  <Text className='text-base font-bold text-default'>
                    {statSummary.income.distribution}
                  </Text>
                </View>
              </View>
              {/* 退款记录卡片 */}
              <View className='p-3 bg-white border border-border rounded-2xl flex flex-col items-start justify-between h-[128Px] flex-1 shadow-sm'>
                <View className='flex flex-col items-start'>
                  <View className='flex justify-center items-center bg-purple-50 rounded-full w-8 h-8'>
                    <Image
                      src={ICONS_BOLD.REFUND}
                      className='w-4 h-4'
                      fit='none'
                    />
                  </View>
                  <Text className='text-sm text-default font-medium'>
                    退款金额
                  </Text>
                </View>
                <View className='flex flex-row items-baseline'>
                  <Text className='text-sm font-bold text-secondary'>¥</Text>
                  <Text className='text-base font-bold text-tertiary'>
                    {statSummary.income.refunded}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      )
    );
  };

  return (
    <PageLol
      navigationProps={{
        children: <NavTitleWithGreeting />,
      }}
      loading={loading}
      error={errorMsg || null}
      onPullDownRefresh={loadData}
      withTabBarSpace
    >
      {/* 顶部通知栏 */}
      {notices && notices.length > 0 && (
        <NoticeBar
          className='rounded-lg mx-4 mt-3'
          leftIcon='volume-o'
          text={notices[0].content}
          mode={notices[0].model}
          url={notices[0].url}
          openType={notices[0].openType}
        />
      )}
      {/* 订单汇总导航 */}
      {renderOrderSummary()}

      {/* 轮播图 */}
      {banners && banners.length > 0 && <HomeBanners banners={banners} />}

      {/* 快捷功能 */}
      {renderQuickActions()}

      {/* 收入统计 */}
      {renderIncomeStats()}

      {/* 个人提升 */}
      {renderProfessionalDevelopment()}
    </PageLol>
  );
}
