import { SUCCESS_CODE } from '@core/api';
import {
  my_extend,
  my_schedule,
  my_service,
  my_summary,
} from '@model/profile.therapist';
import { ProfileService } from '@services/profile.therapist';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistRegisterStore } from '@stores/register.store';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import Taro from '@tarojs/taro';
import { ensureLoggedIn, ensureLogin } from './auth.action';
import { userProfileActions } from './profile.user';

/**
 * 咨询师个人资料管理操作
 * 处理咨询师端的个人信息相关业务逻辑
 */
export const therapistProfileActions = {
  /**
   * 提交咨询师注册表单
   */
  register: async (): Promise<string | null> => {
    try {
      ensureLogin();

      Taro.showLoading({ title: '提交中...', mask: true });

      const store = useTherapistRegisterStore.getState();
      const { formData } = store;
      store.setLoading(true);

      // 表单验证
      if (!formData.summary.name) {
        Taro.showToast({ title: '请填写姓名', icon: 'none' });
        return null;
      }

      if (!formData.extend.introduction) {
        Taro.showToast({ title: '请填写个人简介', icon: 'none' });
        return null;
      }

      if (formData.serviceInfo.services?.length === 0) {
        Taro.showToast({ title: '请添加至少一项服务', icon: 'none' });
        return null;
      }

      // 调用服务提交注册信息
      const result = await ProfileService.register({
        summary: formData.summary,
        detailInfo: formData.extend,
        serviceInfo: formData.serviceInfo,
      });

      if (result && result.success) {
        // 清空注册表单
        // store.resetFormData(false);

        Taro.hideLoading();

        if (result.code === SUCCESS_CODE && result.data?.id) {
          Taro.showToast({
            title: '注册信息提交成功，等待审核',
            icon: 'success',
            duration: 2000,
          });
        } else {
          Taro.showToast({
            title: result.message,
            icon: 'none',
            duration: 2000,
          });
        }

        // 拉取最新用户信息, 当前还是用户身份
        await userProfileActions.fetchMyPublicProfile();

        // 延迟返回
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);

        return result.data.id ?? null;
      } else {
        Taro.hideLoading();
        Taro.showToast({
          title: result.message || '提交失败，请重试',
          icon: 'none',
          duration: 2000,
        });
        store.setLoading(false);
        return null;
      }
    } catch (error) {
      console.error('提交咨询师注册表单失败:', error);
      Taro.hideLoading();
      Taro.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none',
        duration: 2000,
      });
      useTherapistRegisterStore.setState({
        loading: false,
      });
      return null;
    } finally {
      useTherapistRegisterStore.setState({
        loading: false,
      });
    }
  },

  /**
   * 加载咨询师个人基本信息
   * @param forceRefresh 是否强制刷新
   */
  loadMyProfile: async (forceRefresh = false): Promise<boolean> => {
    try {
      ensureLoggedIn();

      const store = useTherapistProfileStore.getState();

      // 如果已有数据且不强制刷新，则直接返回
      if (store.myProfile && !forceRefresh) {
        return true;
      }

      // 调用服务获取数据
      const myProfile = await ProfileService.getMyProfile();

      if (myProfile) {
        // 更新状态
        store.setMyProfile(myProfile);
        return true;
      }

      return false;
    } catch (error) {
      console.error('加载咨询师个人基本信息失败:', error);
      return false;
    }
  },

  /**
   * 加载咨询师详细信息
   * @param forceRefresh 是否强制刷新
   */
  loadMyExtendInfo: async (forceRefresh = false): Promise<boolean> => {
    try {
      ensureLoggedIn();

      const store = useTherapistProfileStore.getState();

      // 如果已有数据且不强制刷新，则直接返回
      if (store.myExtendInfo && !forceRefresh) {
        return true;
      }

      // 调用服务获取数据
      const myExtendInfo = await ProfileService.getMyExtendInfo();

      if (myExtendInfo) {
        // 更新状态
        store.setMyExtendInfo(myExtendInfo as my_extend);
        return true;
      }

      return false;
    } catch (error) {
      console.error('加载咨询师详细信息失败:', error);
      return false;
    }
  },

  /**
   * 加载咨询师服务信息
   * @param forceRefresh 是否强制刷新
   */
  loadMyServices: async (forceRefresh = false): Promise<boolean> => {
    try {
      ensureLoggedIn();

      const store = useTherapistProfileStore.getState();

      // 如果已有数据且不强制刷新，则直接返回
      if (store.myServices && !forceRefresh) {
        return true;
      }

      // 调用服务获取数据
      const myServices = await ProfileService.getMyServices();
      console.log(
        'therapistProfileActions loadMyServices myServices',
        myServices
      );
      if (myServices) {
        // 更新状态
        store.setMyServices(myServices as my_service);
        return true;
      }

      return false;
    } catch (error) {
      console.error('加载咨询师服务信息失败:', error);
      return false;
    }
  },

  /**
   * 加载咨询师排期信息
   * @param forceRefresh 是否强制刷新
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  loadMySchedule: async (
    forceRefresh = false,
    startDate?: number,
    endDate?: number
  ): Promise<boolean> => {
    try {
      console.log(
        'therapistProfileActions loadMySchedule forceRefresh',
        forceRefresh
      );
      ensureLoggedIn();

      const store = useTherapistProfileStore.getState();

      // 如果已有数据且不强制刷新，则直接返回
      if (store.mySchedule && !forceRefresh) {
        return true;
      }

      //
      const schedule = await ProfileService.getMySchedule(startDate, endDate);
      console.log('therapistProfileActions loadMySchedule schedule', schedule);
      if (schedule) {
        store.setMySchedule({
          id: store.myProfile?.id ?? '',
          schedule,
        } as my_schedule);
      }
      return true;
    } catch (error) {
      console.error('加载咨询师排期信息失败:', error);
      return false;
    }
  },

  /**
   * 更新排期信息
   */
  updateSchedule: async (schedule: my_schedule): Promise<void> => {
    console.log('therapistProfileActions updateSchedule schedule', schedule);
    const store = useTherapistUpdaterStore.getState();
    try {
      store.setLoading(true);
      store.setError(null);

      // 调用服务更新排期
      const result = await ProfileService.updateMySchedule(schedule);

      if (result.success && result.code === SUCCESS_CODE) {
        await therapistProfileActions.loadMySchedule(true);
        Taro.showToast({
          title: '排期更新成功',
          icon: 'success',
          duration: 2000,
        });

        return;
      } else {
        console.error('排期更新失败:', result.message);
        store.setError(result.message || '排期更新失败');
      }
    } catch (error) {
      console.error('更新排期信息失败:', error);
      store.setError(error.message || '排期更新失败');
      store.setLoading(false);
    } finally {
      store.setLoading(false);
    }
  },

  updateAvatarAndNickname: async (avatar: string, nickname: string) => {
    console.log(
      'therapistProfileActions updateAvatarAndNickname',
      avatar,
      nickname
    );
    const result = await ProfileService.updateAvatarAndNickname(
      avatar,
      nickname
    );
    if (result.success) {
      useTherapistProfileStore.setState({
        myProfile: {
          ...useTherapistProfileStore.getState().myProfile!,
          avatar,
          name: nickname,
        },
      });
    } else {
      console.error('更新用户信息失败:', result.message);
      throw new Error(result.message || '更新用户信息失败');
    }
    return result;
  },

  updateMyBaseInfo: async (summary: Partial<my_summary>): Promise<void> => {
    console.log('therapistProfileActions updateMyBaseInfo summary', summary);
    useTherapistUpdaterStore.setState({
      loading: true,
      error: null,
    });
    try {
      const result = await ProfileService.updateMyProfile(summary);
      if (result.success && result.code === SUCCESS_CODE) {
        await therapistProfileActions.loadMyProfile(true);
        Taro.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000,
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      }
    } catch (error) {
      console.error('更新咨询师基本信息失败:', error);
      useTherapistUpdaterStore.setState({
        loading: false,
        error: error.message || '更新失败',
      });
    } finally {
      useTherapistUpdaterStore.setState({
        loading: false,
      });
    }
  },
  updateMyExtendInfo: async (extend: Partial<my_extend>): Promise<void> => {
    console.log('therapistProfileActions updateMyExtendInfo extend', extend);
    useTherapistUpdaterStore.setState({
      loading: true,
      error: null,
    });
    try {
      const result = await ProfileService.updateMyExtendInfo(extend);
      if (result.success && result.code === SUCCESS_CODE) {
        await therapistProfileActions.loadMyExtendInfo(true);
        Taro.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000,
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      }
    } catch (error) {
      console.error('更新咨询师详细信息失败:', error);
      useTherapistUpdaterStore.setState({
        loading: false,
        error: error.message || '更新失败',
      });
    } finally {
      useTherapistUpdaterStore.setState({
        loading: false,
      });
    }
  },

  updateMyServices: async (services: Partial<my_service>): Promise<void> => {
    useTherapistUpdaterStore.setState({
      loading: true,
      error: null,
    });
    try {
      console.log(
        'therapistProfileActions updateMyServices services',
        services
      );
      const result = await ProfileService.updateMyServices(services);
      if (result.success && result.code === SUCCESS_CODE) {
        await therapistProfileActions.loadMyServices(true);
        Taro.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000,
        });
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
        return;
      } else {
        console.error('更新咨询师服务信息失败:', result.message);
        useTherapistUpdaterStore.setState({
          loading: false,
          error: result.message || '更新失败',
        });
      }
    } catch (error) {
      console.error('更新咨询师服务信息失败:', error);
      useTherapistUpdaterStore.setState({
        loading: false,
        error: error.message || '更新失败',
      });
    } finally {
      useTherapistUpdaterStore.setState({
        loading: false,
      });
    }
  },
};
