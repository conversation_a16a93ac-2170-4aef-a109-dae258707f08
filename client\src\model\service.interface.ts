//服务类型
export enum ServiceType {
  VIDEO = 0,
  FACE_TO_FACE = 3,
}

export interface Service {
  type: ServiceType;
  duration: number;
  unit?: string;
  price: number;
  promo?: Promo; // 单个服务优惠
  finalPrice: number; // 计算优惠后的最终价格
  enabled: boolean;
}

// 优惠类型
export enum PromoType {
  DISCOUNT = 0, // 折扣
  REDUCE = 1, // 满减
}
// 优惠
export interface Promo {
  type: PromoType;
  value: number;
  // 满减的满值
  threshold?: number;
  desc: string;
}
