import { Button, Checkbox, Image, Toast } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { handleLoginRedirect, wxLogin } from '@core/actions/auth.action';
import UserProfileEditor from '@pages/sub-packages/profile/components/UserProfileEditor';
import { useGlobalStore } from '@stores/global.store';
import { Text, View } from '@tarojs/components';
import { appRouter } from '@utils/router';
import { useCallback, useState } from 'react';

const LoginPage = () => {
  const [privacyChecked, setPrivacyChecked] = useState(false);
  const [requesting, setRequesting] = useState(false);
  const [showProfileEditor, setShowProfileEditor] = useState(false);

  // // 检查用户资料是否完整
  // const checkUserProfile = useCallback(() => {
  //   console.log('checkUserProfile');
  //   try {
  //     // 如果是用户，则检查用户资料是否完整,如果是咨询师，不检查，手动编辑
  //     if (currentRole === USER_ROLE.USER) {
  //       // 如果用户没有头像或昵称为默认值，则显示资料编辑器
  //       if (!currentUserAvatar || currentUserNickname === '用户') {
  //         setShowProfileEditor(true);
  //       }
  //     }
  //   } catch (error) {
  //     console.error('checkUserProfile error', error);
  //   }
  // }, [currentUserAvatar, currentUserNickname, currentRole]);

  // 处理微信官方获取手机号按钮回调
  const handleGetPhoneNumber = useCallback(async (e) => {
    try {
      setRequesting(true);

      const { errMsg, cloudID } = e.detail;
      if (!cloudID) {
        Toast.show(errMsg || '登录过程出现错误，请重试');
        return;
      }
      const result = await wxLogin(cloudID);
      // 新用户需要完善资料
      if (result && result.isNewUser) {
        // 如果用户是新用户，则跳转到编辑资料页面
        setShowProfileEditor(true);
      } else {
        setShowProfileEditor(false);
        handleLoginRedirect();
      }
    } catch (error) {
      console.error('处理获取手机号失败:', error);
      Toast.show(error?.message || '登录过程出现错误，请重试');
    } finally {
      setRequesting(false);
    }
  }, []);

  // 资料编辑完成后的回调
  const handleProfileComplete = useCallback(() => {
    setShowProfileEditor(false);
    // 完成登录流程，处理重定向
    handleLoginRedirect();
  }, []);

  // 如果显示资料编辑器，则渲染资料编辑组件
  if (showProfileEditor) {
    return (
      <PageLol
        navigationProps={{
          title: '完善资料',
          showBackButton: true,
          onBack: handleProfileComplete,
        }}
      >
        <View className='flex flex-col items-center h-screen px-4 pt-6'>
          <Text className='text-lg font-bold mb-6'>请完善您的个人资料</Text>
          <UserProfileEditor
            onComplete={() => {
              // 加载用户信息
              useGlobalStore.getState().setNeedReload(true);
            }}
          />
        </View>
      </PageLol>
    );
  }

  return (
    <PageLol navigationProps={{ title: '登录' }}>
      <View className='flex flex-col items-center h-screen px-4 mt-12'>
        {/* Logo 和平台名称 */}
        <View className='flex flex-col items-center py-8'>
          <Image
            className='mb-2'
            src='/assets/logo.png'
            fit='contain'
            width='78px'
            height='78px'
            radius='100px'
          />
          <Text className='text-lg font-bold '>心之安</Text>
        </View>

        {/* 隐私政策和用户协议 */}
        <View className='w-full mb-8 flex flex-row items-center justify-center'>
          <Checkbox
            value={privacyChecked}
            onChange={(e) => setPrivacyChecked(e.detail)}
            shape='round'
            iconSize='20px'
          >
            <Text className='text-md '>
              我已阅读并同意
              <Text
                className='text-primary text-md'
                onClick={() => appRouter.privacy()}
              >
                {` 心之安平台隐私政策 `}
              </Text>
              和
              <Text
                className='text-primary text-md'
                onClick={() => appRouter.agreement()}
              >
                {` 心之安平台用户协议`}
              </Text>
            </Text>
          </Checkbox>
        </View>

        {/* 登录按钮 */}
        <Button
          type='primary'
          round
          className='w-full'
          loading={requesting}
          disabled={!privacyChecked || requesting}
          openType='getPhoneNumber'
          onGetPhoneNumber={handleGetPhoneNumber}
        >
          微信一键登录
        </Button>
      </View>
    </PageLol>
  );
};

export default LoginPage;
