// app/lib/wxCloud.js
"use server";

import { CloudFunctionResult } from "@/app/model/api";
import { JwtPayload } from "jsonwebtoken";

function isJwtPayload(obj: unknown): obj is JwtPayload {
  return typeof obj === "object" && obj !== null && "unionid" in obj;
}

/**
 * 通用云函数调用方法
 * @param name 云函数名称
 * @param action 操作名称
 * @param data 请求参数
 * @returns 云函数返回结果
 */
export async function callCloudFunction(
  name: string,
  action: string,
  params?: unknown
): Promise<CloudFunctionResult> {
  try {
    // 1. 获取 access_token（带缓存机制）
    const accessToken = await getAccessToken();

    // 2. 获取当前管理员会话
    // const session = await getAdminSession();

    // if (!isJwtPayload(session) || !session.isAdmin || !session.unionid) {
    //   throw new Error("未授权操作");
    // }

    // 3. 准备请求参数
    const body = {
      params,
      action,
      opToken: process.env.OP_SECRET, // 操作令牌
      // unionid: session.unionid as string,
      unionid: "o-1234567890",
    };

    // 4. 调用微信开放平台API
    const apiUrl = `https://api.weixin.qq.com/tcb/invokecloudfunction?access_token=${accessToken}&env=${process.env.WX_CLOUD_ENV}&name=${name}`;

    const response = await fetch(apiUrl, {
      method: "POST",

      body: JSON.stringify(body),
    });

    if (!response.ok) throw new Error("微信API调用失败");

    // 5. 记录操作日志
    // await logOperation({
    //   functionName: name,
    //   // operator: session.unionid,
    //   operator: "o-1234567890",
    //   data: params,
    //   result: response,
    // });

    // 6. 处理返回结果
    const result = await response.json();
    if (result?.errcode === 0) {
      console.log("callCloudFunction response", result);
      return JSON.parse(result.resp_data);
    } else {
      console.error("云函数调用失败:", result);
      throw new Error(result.errmsg || "服务暂时不可用，请稍后重试");
    }
  } catch (error) {
    console.error("云函数调用失败:", error);
    throw new Error("服务暂时不可用，请稍后重试");
  }
}

// 获取 access_token（带缓存）
let tokenCache = { value: null, expireTime: 0 };

async function getAccessToken() {
  if (tokenCache.value && Date.now() < tokenCache.expireTime) {
    return tokenCache.value;
  }

  const response = await fetch(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${process.env.WX_APPID}&secret=${process.env.WX_APPSECRET}`
  );

  const data = await response.json();
  console.log("getAccessToken", data);
  tokenCache = {
    value: data.access_token,
    expireTime: Date.now() + (data.expires_in - 300) * 1000, // 提前5分钟刷新
  };

  return tokenCache.value;
}

// 记录操作日志
async function logOperation(details: {
  functionName: string;
  operator: string;
  data: unknown;
  result: unknown;
}) {
  try {
    // 调用专门的日志云函数
    await fetch("/api/log", {
      method: "POST",
      body: JSON.stringify({
        type: "cloud_function_call",
        ...details,
      }),
    });
  } catch (logError) {
    console.error("日志记录失败:", logError);
  }
}
