import { ScrollView, Text, View } from '@tarojs/components';
import { DAY_NAMES } from '@utils/time';
import { useCallback, useMemo } from 'react';

interface DateSelectorProps {
  datesToShow: number[];
  isDateAvailable: (date: number) => boolean;
  selectedDate: number | null;
  onDateSelect: (date: number) => void;
}

export default function DateSelector({
  datesToShow,
  isDateAvailable,
  selectedDate,
  onDateSelect,
}: DateSelectorProps) {
  // 计算需要滚动到的目标日期（往前第2个）
  const scrollTargetDate = useMemo(() => {
    if (!selectedDate) return null;

    const selectedIndex = datesToShow.findIndex(
      (date) => date === selectedDate
    );
    if (selectedIndex === -1) return selectedDate;

    // 往前第2个日期，但确保不超出数组范围
    const targetIndex = Math.max(0, selectedIndex - 2);
    return datesToShow[targetIndex];
  }, [selectedDate, datesToShow]);

  // 渲染日期项
  const renderDateItem = useCallback(
    (date: number) => {
      const dateObj = new Date(date);
      const day = dateObj.getDate();
      const dayName = DAY_NAMES[dateObj.getDay()];
      const available = isDateAvailable(date);
      const isSelected = selectedDate === date;

      return (
        <View
          key={date}
          id={`date-${date}`}
          className={`w-[3.75rem] h-16 rounded-2xl flex flex-col items-center justify-center mr-4 ${
            !available
              ? 'border-2 border-border'
              : isSelected
              ? 'bg-primary'
              : 'border-2 border-black '
          } ${available ? 'cursor-pointer' : 'cursor-not-allowed'}`}
          onClick={() => available && onDateSelect(date)}
        >
          <Text
            className={`text-base font-bold ${
              !available
                ? 'text-placeholder'
                : isSelected
                ? 'text-white'
                : 'text-default'
            }`}
          >
            {day}
          </Text>
          <Text
            className={` text-md font-medium ${
              !available
                ? 'text-placeholder'
                : isSelected
                ? 'text-white'
                : 'text-default'
            }`}
          >
            {dayName}
          </Text>
        </View>
      );
    },
    [isDateAvailable, selectedDate, onDateSelect]
  );

  return (
    <View className='w-full'>
      <ScrollView
        scrollX
        className='whitespace-nowrap py-2'
        scrollIntoView={
          scrollTargetDate ? `date-${scrollTargetDate}` : undefined
        }
        scrollWithAnimation
      >
        <View className='inline-flex'>
          {datesToShow.map((date) => renderDateItem(date))}
        </View>
      </ScrollView>
    </View>
  );
}
