// lib/auth.js
import jwt from "jsonwebtoken";
import { cookies } from "next/headers";

const JWT_SECRET = process.env.JWT_SECRET;

// 生成管理员会话令牌
export function createAdminSession(unionid: string) {
  const token = jwt.sign(
    {
      unionid,
    },
    JWT_SECRET as string,
    { expiresIn: "2h" }
  );

  cookies().set("admin_token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 7200, // 2小时
  });
}

// 获取当前会话信息
export async function getAdminSession() {
  const token = cookies().get("admin_token")?.value;

  if (!token) return null;

  try {
    return jwt.verify(token, JWT_SECRET as string);
  } catch (error) {
    console.error("JWT验证失败:", error);
    return null;
  }
}

// 销毁会话
export function destroyAdminSession() {
  cookies().delete("admin_token");
}
