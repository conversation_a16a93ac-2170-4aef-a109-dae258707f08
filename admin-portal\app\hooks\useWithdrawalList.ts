import { WithdrawRecord } from "@/app/model/payment.interface";
import { financeCloudFunctions } from "@/lib/cloud-functions";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
  Pagination,
  SUCCESS_CODE,
  WithdrawalListParams,
  WithdrawalListRequest,
} from "../model/api";

/**
 * 提现记录列表管理Hook
 * 提供提现记录列表的获取、筛选和状态监听功能
 */
export function useWithdrawalList(
  initialParams: { params?: WithdrawalListParams } = {}
) {
  const [loading, setLoading] = useState(false);
  const [withdrawals, setWithdrawals] = useState<WithdrawRecord[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [filters, setFilters] = useState<
    WithdrawalListParams & { dateRange?: [number, number] }
  >({
    query: "",
    status: "",
    dateRange: undefined,
  });

  const requestParamsRef = useRef({
    filters: initialParams.params,
  });

  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchWithdrawals = useCallback(
    async (params: WithdrawalListRequest) => {
      if (loading) return;
      setLoading(true);
      try {
        const res = await financeCloudFunctions.getWithdrawals(params);
        setWithdrawals(res.data as WithdrawRecord[]);
        setPagination(res.pagination as Pagination);
      } catch (error) {
        console.error("获取提现记录失败:", error);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 刷新列表
  const refreshWithdrawals = useCallback(async () => {
    try {
      await fetchWithdrawals({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
      });
    } catch (error) {
      console.error("刷新提现记录失败:", error);
    }
  }, [fetchWithdrawals, pagination?.pageSize]);

  // 加载更多/翻页
  const loadMoreWithdrawals = useCallback(
    async (page: number = pagination?.page + 1) => {
      try {
        await fetchWithdrawals({
          page: page,
          pageSize: pagination?.pageSize || 10,
          params: requestParamsRef.current.filters,
        });
      } catch (error) {
        console.error("加载更多提现记录失败:", error);
      }
    },
    [pagination?.page, pagination?.pageSize, fetchWithdrawals]
  );

  // 搜索提现记录
  const onSearch = useCallback(
    async (searchWord: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        query: searchWord,
      };

      setFilters(newFilters);

      try {
        await fetchWithdrawals({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("搜索提现记录失败:", error);
      }
    },
    [fetchWithdrawals, pagination?.pageSize]
  );

  // 筛选提现状态
  const onStatusChange = useCallback(
    async (status: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        status,
      };

      setFilters(newFilters);

      try {
        await fetchWithdrawals({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("筛选提现状态失败:", error);
      }
    },
    [fetchWithdrawals, pagination?.pageSize]
  );

  // 设置日期范围
  const onDateRangeChange = useCallback(
    async (dateRange: [number, number] | undefined) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        dateRange,
      };

      setFilters(newFilters);

      try {
        await fetchWithdrawals({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按日期筛选提现记录失败:", error);
      }
    },
    [fetchWithdrawals, pagination?.pageSize]
  );

  // 处理提现申请
  const completeWithdrawal = useCallback(
    async (withdrawalId: string) => {
      setLoading(true);
      try {
        const result = await financeCloudFunctions.completeWithdrawal({
          id: withdrawalId,
        });
        if (result.success && result.code === SUCCESS_CODE) {
          // 处理成功后刷新列表
          await refreshWithdrawals();
          toast.success("提现申请已完成处理");
        } else {
          throw new Error(result.message);
        }
        return result;
      } catch (error) {
        console.error("处理提现申请失败:", error);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [refreshWithdrawals]
  );

  // 初始加载
  useEffect(() => {
    refreshWithdrawals();
  }, [refreshWithdrawals]); // 只在组件挂载时执行一次

  return {
    withdrawals,
    pagination,
    loading,
    refreshWithdrawals,
    loadMoreWithdrawals,
    onStatusChange,
    onSearch,
    onDateRangeChange,
    completeWithdrawal,
    filters,
  };
}
