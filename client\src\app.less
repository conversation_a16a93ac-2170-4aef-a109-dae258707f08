@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import '@antmjs/vantui/lib/index.less';
@import 'styles/index.less';
@import 'styles/vant.less';

//本设计是灰底+白色卡片，所以需要重定义一些样式
page {
  //自定义样式，用于.less 和 tailwindcss(tailwindcss.config.js)
  --color-primary-bg: @bgColor; //卡片背景色，白色
  --color-border: @border-color;
  --color-success: @green;
  --color-danger: @red;
  --color-warning: @orange;

  --color-primarylight: #bfdbfe;
  --color-secondary: #428675;
  --color-secondarylight: #def4e0;
  --color-tertiary: #c08eaf;
  --color-tertiarylight: #f7f1f4;
  --color-text-default: @text-color;
  --color-text-primary: @primary-color;
  --color-text-secondary: @text-color-secondary;
  --color-text-disabled: @text-color-disabled;
  --color-text-placeholder: @text-color-placeholder;
  --color-star: #ffc978;
  //-----------------------------------------------

  //@antmjs/vantui 组件样式 重定义

  --color-primary: @primary-color;

  --page-background-color: @page-back; //页面背景色，灰底

  --search-background-color: @bgColor; // Search组件背景色

  --button-mini-font-size: 28px;
  --button-small-font-size: 28px;
  --button-normal-font-size: 28px;
  --button-large-font-size: 32px;

  --tabs-nav-background-color: transparent;
  --van-cell-background-color: transparent;
  --van-cell-active-color: transparent;
  --van-cell-active-background-color: transparent;
  --steps-background-color: transparent;
  --button-primary-background-color: @primary-color;
  // --button-plain-background-color: transparent;

  .van-step--horizontal .van-step__circle-container {
    background-color: @page-back;
  }
  //-------------------------------------

  //页面css定义
  font-size: 28px;
  font-family: @base-font-family;
  color: @black;
  background-color: @page-back; // 页面背景色
  //----------------------------------------------------
}

view {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  display: none;
}
