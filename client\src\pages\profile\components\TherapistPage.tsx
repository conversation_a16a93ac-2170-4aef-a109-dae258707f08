import { Cell, CellGroup, Icon } from '@antmjs/vantui';
import IncomeStatisticCard from '@components/IncomeStatisticCard';
import { ICONS_BULK } from '@constants/assets';
import { distributionActions } from '@core/actions/distribution.action';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import useRenderCount from '@hooks/useRenderCount';
import { USER_ROLE } from '@model/user.interface';
import { useDistributionStore } from '@stores/distribution.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

const TOOLBOX_TABS = [
  { key: 'tool', label: '工具箱' },
  { key: 'asset', label: '个人资产' },
];

export default function TherapistPage({ onSwitchRole }) {
  const [tab, setTab] = useState('tool');
  const profile = useTherapistProfileStore.use.myProfile();
  const distribution = useDistributionStore.use.overview();

  useRenderCount('TherapistPage');
  useEffect(() => {
    const loadMyInfo = async () => {
      console.log('loadMyInfo');
      await Promise.all([
        therapistProfileActions.loadMyProfile(),
        distributionActions.fetchDistributionOverview(),
      ]);
    };
    loadMyInfo();
  }, []);
  return (
    profile && (
      <View className='pt-4 pb-2'>
        {/* 个人信息卡片 */}
        <View className='mx-4 bg-white rounded-xl px-4 py-3 flex flex-col mb-4 shadow-sm'>
          {/* 头部行 */}
          <View className='flex flex-row items-center'>
            {/* 头像 */}
            <Image
              src={profile.avatar}
              className='w-10 h-10 rounded-full mr-4'
            />
            <View className='flex-1 flex flex-col'>
              <View className='flex flex-row items-center mb-1'>
                <Text className='text-lg font-semibold mr-2'>
                  {profile.name}
                </Text>
                <View className='px-1 py-0.5 mr-2 bg-tertiary rounded-sm text-xs text-white'>
                  咨询师
                </View>
                <View
                  className='px-1 py-0.5 bg-primary rounded-sm text-xs text-white'
                  onClick={() => {
                    Taro.navigateTo({
                      url: '/pages/sub-packages/therapist-updater/index/index',
                    });
                  }}
                >
                  <Icon name='edit' size={12} />
                  编辑资料
                </View>
              </View>
              <Text className='text-xs text-secondary'>
                {profile.titles[0]}
              </Text>
            </View>
            {/* 二维码 */}
            <View className='flex flex-col items-center ml-2'>
              <Icon
                name='qr'
                size={96}
                className='mb-1'
                onClick={() =>
                  Taro.previewImage({
                    urls: [distribution?.qrCodeUrl || ''],
                  })
                }
              />
            </View>
          </View>
          {/* 咨询时长、累计服务、好评率、累计被请求 */}
          <View className='flex flex-row mt-4 justify-between'>
            <View className='flex flex-row items-start'>
              <Icon name={ICONS_BULK.CLOCK32} size='16px' />
              <View className='flex flex-col items-start ml-1'>
                <Text className='text-xs text-secondary'>咨询时长</Text>
                <Text className='text-base font-bold text-default'>
                  {profile.consultTime}
                  <Text className='text-xs text-secondary'>小时</Text>
                </Text>
              </View>
            </View>
            <View className='flex flex-row items-start'>
              <Icon name={ICONS_BULK.PERSONS32} size='16px' />
              <View className='flex flex-col items-start ml-1'>
                <Text className='text-xs text-secondary'>累计服务</Text>
                <Text className='text-base font-bold text-default'>
                  {profile.serviceCount}
                  <Text className='text-xs text-secondary'>人</Text>
                </Text>
              </View>
            </View>
            <View className='flex flex-row items-start'>
              <Icon name={ICONS_BULK.STAR32} size='16px' />
              <View className='flex flex-col items-start ml-1'>
                <Text className='text-xs text-secondary'>好评率</Text>
                <Text className='text-base font-bold text-default'>
                  {profile.rating}
                  <Text className='text-xs text-secondary'>%</Text>
                </Text>
              </View>
            </View>
            <View className='flex flex-row items-start'>
              <Icon name={ICONS_BULK.SEND32} size='16px' />
              <View className='flex flex-col items-start ml-1'>
                <Text className='text-xs text-secondary'>累计邀请</Text>
                <Text className='text-base font-bold text-default'>
                  {distribution?.inviteCount}
                  <Text className='text-xs text-secondary'>人</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* 累计收入卡片 */}
        <IncomeStatisticCard />

        {/* 工具箱/个人资产 Tabs 卡片 */}
        <View className='m-4 bg-white rounded-xl p-4  shadow-sm'>
          {/* 头部tab 工具箱/个人资产 切换 */}
          <View className='flex flex-row mb-4 gap-2.5'>
            {TOOLBOX_TABS.map((t) => (
              <View
                key={t.key}
                className={`text-center py-2 font-bold cursor-pointer ${
                  tab === t.key
                    ? 'text-default text-base '
                    : 'text-secondary text-md '
                }`}
                onClick={() => setTab(t.key)}
              >
                <Text>{t.label}</Text>
              </View>
            ))}
          </View>
          {tab === 'tool' ? (
            <View className='grid grid-cols-4 gap-4'>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-blue-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-blue-500'>📋</Text>
                </View>
                <Text className='text-sm font-medium text-default'>
                  测评模板
                </Text>
              </View>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-blue-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-blue-500'>❓</Text>
                </View>
                <Text className='text-sm font-medium text-default'>话术库</Text>
              </View>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-blue-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-blue-500'>📖</Text>
                </View>
                <Text className='text-sm font-medium text-default'>案例库</Text>
              </View>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-blue-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-blue-500'>👨‍🏫</Text>
                </View>
                <Text className='text-sm font-medium text-default'>
                  督导资源
                </Text>
              </View>
            </View>
          ) : (
            <View className='grid grid-cols-3 gap-4'>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-green-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-green-500'>💌</Text>
                </View>
                <Text className='text-sm font-medium text-default'>感谢信</Text>
              </View>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-green-100 rounded flex items-center justify-center mb-2'>
                  <Text className='text-xl text-green-500'>👥</Text>
                </View>
                <Text className='text-sm font-medium text-default'>粉丝</Text>
              </View>
              <View className='flex flex-col items-center'>
                <View className='w-10 h-10 bg-green-100 rounded flex items-center justify-center mb-1'>
                  <Text className='text-xl text-green-500'>🎫</Text>
                </View>
                <Text className='text-sm font-medium text-default'>
                  卡券管理
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* 功能列表 */}
        <CellGroup inset>
          <Cell
            title='个人资料'
            isLink
            size='large'
            icon='user-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist-updater/index/index',
              });
            }}
          />
          <Cell
            title='工作时间'
            isLink
            size='large'
            icon='clock-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist-updater/work-time/index',
              });
            }}
          />
          <Cell
            title='排班管理'
            isLink
            size='large'
            icon='calendar-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist-updater/schedule-info/index',
              });
            }}
          />
          <Cell
            title='特殊约定'
            isLink
            size='large'
            icon='star-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist-updater/special-agreement/index',
              });
            }}
          />
          <Cell
            title='分享中心'
            isLink
            size='large'
            icon='friends-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/wallet/distribution/therapist',
              });
            }}
          />
          {/* <Cell
            title='客服'
            isLink
            size='large'
            icon='service-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist-updater/index/index',
              });
            }}
          /> */}
          <Cell
            title='设置'
            isLink
            size='large'
            icon='setting-o'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/profile/settings/index',
              });
            }}
          />
          <Cell
            title='切换到用户'
            icon='user-o'
            isLink
            border={false}
            onClick={() => onSwitchRole(USER_ROLE.USER)}
          />
        </CellGroup>
      </View>
    )
  );
}
