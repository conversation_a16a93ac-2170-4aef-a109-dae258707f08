/**
 * 自动取消订单定时触发云函数
 *
 * 1. 查询所有订单，状态为待支付，且创建时间超过15分钟
 * 2. 取消订单
 */

// 云函数入口文件
const cloud = require("wx-server-sdk");
const {
  COLLECTIONS,
  ORDER_STATUS,
  INITATOR_SOURCE,
  ACTION_TYPE,
} = require("../common/db.constants");
const {
  _releaseSchedule,
  _addOrderAction,
} = require("../service/orderOperation");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  console.log("自动取消订单定时触发:", event);

  try {
    const expiredTime = Date.now() - 15 * 60 * 1000;
    // 查询所有订单，状态为待支付，且创建时间超过15分钟
    const orderResult = await db
      .collection(COLLECTIONS.ORDER)
      .where({
        status: ORDER_STATUS.PENDING_PAYMENT,
        createdAt: db.command.lt(expiredTime),
      })
      .get();
    const orders = orderResult.data;
    for (const order of orders) {
      await _cancelOrder(order);
    }
  } catch (error) {
    console.error("自动取消订单定时触发失败:", error);
    return { errcode: 1, errmsg: "自动取消订单定时触发失败" };
  }
};

async function _cancelOrder(order) {
  console.log("_cancelOrder order", order);
  const { _id, startTime, duration, therapistId } = order;

  await db
    .collection(COLLECTIONS.ORDER)
    .doc(_id)
    .update({
      data: {
        status: ORDER_STATUS.CANCELLED,
        updatedAt: Date.now(),
      },
    });
  // 添加订单操作记录
  await _addOrderAction(order._id, ACTION_TYPE.CANCEL, {
    initiator: INITATOR_SOURCE.SYSTEM,
    extraData: {
      reason: "支付超时",
    },
  });

  // 释放排班
  await _releaseSchedule(startTime, duration, therapistId);
}
