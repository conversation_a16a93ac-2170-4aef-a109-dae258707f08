import { Calendar, Icon } from '@antmjs/vantui';
import { schedule_item } from '@model/therapist.interface';
import { Text, View } from '@tarojs/components';
import { useCallback, useMemo, useState } from 'react';
import { useCalendar } from '../../hooks/useAppointmentCalendar';
import DateSelector from './DateSelector';
import TimeSlotSelector from './TimeSlotSelector';

interface AppointmentCalendarProps {
  occupancy: schedule_item[] | null;
  selectedDateUnixStamp?: number;
  selectedTimeSlotInHour?: number;
  onSelectDateTime?: (date: number, timeSlot: number) => void;
  workTime?: {
    /** 开始时间 (小时, 0-23) */
    start: number;
    /** 结束时间 (小时, 0-23) */
    end: number;
    /** 工作日 (周一到周日, 1-7) */
    workDays?: number[];
  };
}

export default function AppointmentCalendar({
  occupancy,
  selectedDateUnixStamp,
  selectedTimeSlotInHour,
  onSelectDateTime,
  workTime,
}: AppointmentCalendarProps) {
  const {
    selectedDate,
    selectedTimeSlot,
    datesToShow,
    isDateAvailable,
    isTimeSlotAvailable,
    getWorkTimeSlots,
    handleDateSelect,
    handleTimeSlotSelect,
    handleCalendarDateSelect,
    formatHour,
    isAvailableDay,
  } = useCalendar({
    occupancy: occupancy || undefined,
    selectedDateUnixStamp,
    selectedTimeSlotInHour,
    workTime,
  });

  const [showCalendarPicker, setShowCalendarPicker] = useState(false);

  // 处理日期选择变化
  const handleDateChange = useCallback(
    (date: number) => {
      handleDateSelect(date);
    },
    [handleDateSelect]
  );

  // 处理时间段选择变化
  const handleTimeSlotChange = useCallback(
    (hour: number) => {
      handleTimeSlotSelect(hour);
      if (selectedDate) {
        onSelectDateTime?.(selectedDate, hour);
      }
    },
    [handleTimeSlotSelect, selectedDate, onSelectDateTime]
  );

  // 处理日历日期选择
  const handleCalendarSelect = useCallback(
    (event: any) => {
      console.log('handleCalendarSelect 被调用', event);
      const calendarSelectedDate = new Date(event.detail.value).getTime();
      console.log('calendarSelectedDate', calendarSelectedDate);
      setShowCalendarPicker(false);
      handleCalendarDateSelect(calendarSelectedDate);
    },
    [handleCalendarDateSelect]
  );

  // 处理日历打开
  const handleCalendarOpen = useCallback(() => {
    setShowCalendarPicker(true);
  }, []);

  // 计算日历的日期范围
  const calendarDateRange = useMemo(() => {
    const now = new Date();
    const minDate = now.getTime();
    const maxDate = new Date(
      now.getFullYear(),
      now.getMonth() + 3,
      0
    ).getTime();
    return { minDate, maxDate };
  }, []);

  return (
    <View className='flex flex-col w-full gap-6'>
      {/* 头部 */}
      <View className='flex flex-row items-center justify-between w-full'>
        <Text className='text-base font-bold'>可约时间</Text>
        <View
          className='flex flex-row items-center flex-1 justify-end'
          onClick={handleCalendarOpen}
        >
          <Text className='text-sm text-secondary '>
            {selectedDate
              ? new Date(selectedDate).getMonth() + 1
              : new Date().getMonth() + 1}
            月
          </Text>
          <Icon name='arrow' size='12px' color='var(--color-text-secondary)' />
        </View>
      </View>

      {/* 日期选择区 */}
      <DateSelector
        datesToShow={datesToShow}
        isDateAvailable={isDateAvailable}
        selectedDate={selectedDate}
        onDateSelect={handleDateChange}
      />

      {/* 时间段选择区 */}
      {selectedDate && (
        <TimeSlotSelector
          isAvailableDay={isAvailableDay}
          availableTimeSlots={getWorkTimeSlots()}
          isTimeSlotAvailable={isTimeSlotAvailable}
          selectedTimeSlot={selectedTimeSlot}
          onTimeSlotSelect={handleTimeSlotChange}
          formatHour={formatHour}
        />
      )}

      <Calendar
        show={showCalendarPicker}
        minDate={calendarDateRange.minDate}
        maxDate={calendarDateRange.maxDate}
        onClose={() => setShowCalendarPicker(false)}
        onConfirm={handleCalendarSelect}
      />
    </View>
  );
}
