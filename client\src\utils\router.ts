import { useLoadingStore } from '@stores/loading.store';
import Taro from '@tarojs/taro';

export const tab_pages = [
  'pages/homepage/index', // 首页
  'pages/measure/index', // 测评
  'pages/consultation/index', // 咨询
  'pages/treatment/index', // 疗愈
  'pages/profile/index/user', // 我的 用户
  'pages/profile/index/therapist', // 我的 咨询师
  'pages/therapist-orders/index', // 咨询师端订单列表
  'pages/consultation-therapist/index',
];
export const main_pages = [
  ...tab_pages,
  'pages/messages/index', // 用户端消息
  'pages/login/index', // 登录页面
];

// 订单相关路由
export const orderRouter = {
  // 订单列表
  list: ({ tab, redirect }: { tab?: string; redirect?: boolean } = {}) => {
    const url =
      `/pages/sub-packages/orders/list/index` + (tab ? `?tab=${tab}` : '');
    if (redirect) {
      Taro.redirectTo({
        url,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
        fail: (e) => {
          console.error('🚀🚀🚀 redirectTo fail', url, e);
        },
        success: () => {
          console.log('🚀🚀🚀 redirectTo success', url);
        },
      });
    } else {
      Taro.navigateTo({
        url,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
        fail: (e) => {
          console.error('🚀🚀🚀 navigateTo fail', e);
        },
        success: () => {
          console.log('🚀🚀🚀 navigateTo success');
        },
      });
    }
  },

  // 订单详情
  detail: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/detail/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/detail/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  // 取消订单
  cancel: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/cancel/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/cancel/index?orderId=${orderId}`,
        complete: () => {
          console.log('🚀🚀🚀 Taro.navigateTo complete');
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  // 评价订单
  review: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/review/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/review/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  create: (therapistId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/create/index?therapistId=${therapistId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/create/index?therapistId=${therapistId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  submitInfo: (orderId: string, redirect?: boolean, source?: string) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/submit-info/index?orderId=${orderId}${
          source ? `&source=${source}` : ''
        }`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/submit-info/index?orderId=${orderId}${
          source ? `&source=${source}` : ''
        }`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  reschedule: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/reschedule/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/reschedule/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },

  pay: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/payment/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/payment/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },
  reject: (orderId: string, redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/reject/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/reject/index?orderId=${orderId}`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },
  audit: (redirect?: boolean) => {
    if (redirect) {
      Taro.redirectTo({
        url: `/pages/sub-packages/orders/audit/index`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    } else {
      Taro.navigateTo({
        url: `/pages/sub-packages/orders/audit/index`,
        complete: () => {
          useLoadingStore.getState().setTransactionLoading(false);
        },
      });
    }
  },
};

// 服务相关路由
// export const serviceRouter = {
//   // 跳转到视频通话页
//   videoCall: (orderId: string) => {
//     Taro.navigateTo({
//       url: `/pages/sub-packages/services/video/index?orderId=${orderId}`,
//       complete: () => {
//         useLoadingStore.getState().setTransactionLoading(false);
//       },
//     });
//   },
// };

// 通用路由方法
export const appRouter = {
  // 返回上一页
  back: () => {
    Taro.navigateBack();
  },

  // 跳转到首页
  home: () => {
    Taro.switchTab({
      url: '/pages/index/index',
    });
  },
  // 跳转到心理测量页
  measurement: () => {
    Taro.switchTab({
      url: '/pages/measure/index',
    });
  },
  // 跳转到正念·冥想页
  treatment: () => {
    Taro.switchTab({
      url: '/pages/treatment/index',
    });
  },

  // 跳转到登录页
  login: () => {
    Taro.navigateTo({
      url: '/pages/login/index',
    });
  },
  // 跳转到登录页
  relaunchLogin: () => {
    Taro.reLaunch({
      url: '/pages/login/index',
    });
  },

  // 跳转到咨询页
  consultation: () => {
    Taro.switchTab({
      url: '/pages/consultation/index',
    });
  },
  // 跳转到心理师详情页
  therapistDetail: (id: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/therapist/detail/index?id=${id}`,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },

  // 跳转到收藏的咨询师页面
  favoriteTherapists: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/therapist/favorites/index',
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },

  // 跳转到语音通话页
  voiceCall: (orderId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/voice-call/index?orderId=${orderId}`,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },

  // 跳转到视频通话页
  videoCall: (orderId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/video-call/index?orderId=${orderId}`,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },

  // 跳转到文字通话页
  textCall: (orderId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/orders/text-call/index?orderId=${orderId}`,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },
  chat: (peerId: string) => {
    const url = `/pages/sub-packages/functions/chat/index?peerId=${peerId}`;
    Taro.navigateTo({
      url,
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },
  systemChat: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/functions/chat/system',
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },
  userMessages: () => {
    Taro.navigateTo({
      url: '/pages/messages/index',
      complete: () => {
        useLoadingStore.getState().setTransactionLoading(false);
      },
    });
  },
  // 跳转到提现页
  withdraw: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/distribution/withdraw/apply',
    });
  },
  // 跳转到提现记录页
  withdrawRecords: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/distribution/withdraw/records',
    });
  },
  // 跳转到隐私政策页
  privacy: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/profile/privacy/index',
    });
  },
  // 跳转到用户协议页
  agreement: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/profile/agreement/index',
    });
  },
  // 跳转到关于我们页
  about: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/profile/about/index',
    });
  },

  incomeDashboard: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/dashboard/therapist',
    });
  },

  // 跳转到收入明细页
  incomeListTherapist: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/income/index',
    });
  },
  distributionDashboardTherapist: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/distribution/therapist',
    });
  },
  distributionDashboardUser: () => {
    Taro.navigateTo({
      url: '/pages/sub-packages/wallet/distribution/user',
    });
  },
};
