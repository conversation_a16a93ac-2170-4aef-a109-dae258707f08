// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { safeGet, genTestRecordId } = require("../common/utils");
const { COLLECTIONS } = require("../common/db.constants");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

/**
 * 获取推荐卡片
 * @param {string} context context
 */
async function getFeaturedCards(context) {
  try {
    console.log("getFeaturedCards", context);

    const result = await db
      .collection(COLLECTIONS.MEASURES_TEST)
      .field({
        id: true,
        shortTitle: true,
        shortDescription: true,
        category: true,
        howmany: true,
        duration: true,
        usersCompleted: true,
        icon: true,
        coverImage: true,
        isFree: true,
      })
      .limit(2)
      .get();
    return {
      success: true,
      data: result.data,
    };
  } catch (err) {
    console.error("getFeaturedCards 失败:", err);
    throw err;
  }
}

/**
 * 提交测试
 * @param {object} params 参数
 * @param {object} params.record 测试记录
 * @param {object} context 上下文
 */
async function submitTest(params, context) {
  try {
    const { record } = params;
    const { openid } = context;
    console.log("submitTest", record, context);

    if (!record) {
      return {
        success: false,
        message: "测试记录不能为空",
      };
    }

    if (record.userId !== openid) {
      throw new Error("测试记录的userId与当前用户不一致");
    }

    // 查询记录是否存在
    const recordResult = await db
      .collection(COLLECTIONS.MEASURES_TEST_RECORD)
      .where({
        startTime: record.startTime,
        userId: openid,
      })
      .get();

    if (recordResult.data.length === 0) {
      const recordId = genTestRecordId();
      // 记录不存在，创建新记录
      await db.collection(COLLECTIONS.MEASURES_TEST_RECORD).add({
        data: {
          ...record,
          id: recordId,
          _openid: openid,
          createdAt: Date.now(),
        },
      });
      return {
        success: true,
        id: recordId,
      };
    } else {
      throw new Error("记录已存在, 不能重复提交");
    }
  } catch (err) {
    console.error("submitTest 失败:", err);
    throw err;
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = safeGet(userInfo, "role", USER_ROLE.GUEST);
    const userId = safeGet(userInfo, "_id", null);

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};
// 处理函数映射
const handlers = {
  getFeaturedCards: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await getFeaturedCards(context);
      return success(result);
    });
  },

  submitTest: async (params, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await submitTest(params, context);
      return success(result);
    });
  },
};
