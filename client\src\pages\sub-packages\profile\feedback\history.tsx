import PageLol from '@components/common/page-meta';
import { FEEDBACK_TYPE_MAP } from '@constants/text';
import { feedbackActions } from '@core/actions/feedback.action';
import { useFeedbackSelectors } from '@stores/feedback.store';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatTime2 } from '@utils/time';
import { useEffect } from 'react';

// 获取状态对应的颜色和文本
const getStatusInfo = (
  status: 'pending' | 'processing' | 'resolved' | 'rejected'
) => {
  switch (status) {
    case 'pending':
      return { color: 'text-warning', text: '待处理' };
    case 'processing':
      return { color: 'text-primary', text: '处理中' };
    case 'resolved':
      return { color: 'text-success', text: '已解决' };
    case 'rejected':
      return { color: 'text-danger', text: '已驳回' };
    default:
      return { color: 'text-secondary', text: '未知状态' };
  }
};

export default function FeedbackHistory() {
  // 从store获取状态
  const feedbackList = useFeedbackSelectors.use.feedbackList();
  const isLoading = useFeedbackSelectors.use.isLoading();

  // 初始化时加载数据
  useEffect(() => {
    const loadData = async () => {
      await feedbackActions.fetchFeedbackList();
    };

    loadData();
  }, []);

  const handlePreviewImage = (images: string[], current: string) => {
    Taro.previewImage({
      urls: images,
      current,
    });
  };
  return (
    <PageLol
      navigationProps={{ title: '历史记录', showBackButton: true }}
      loading={isLoading}
    >
      <View className='p-4'>
        <View className='space-y-4'>
          {feedbackList?.map((feedback) => {
            const statusInfo = getStatusInfo(feedback.status);

            return (
              <View
                key={feedback._id}
                className='p-4 bg-bg rounded-lg overflow-hidden'
              >
                <View className=' border-b border-border flex flex-col'>
                  <View className='flex justify-between items-center mb-2'>
                    <Text className='text-lg font-medium'>
                      {FEEDBACK_TYPE_MAP[feedback.type]}
                    </Text>
                    <Text className={`${statusInfo.color}`}>
                      {statusInfo.text}
                    </Text>
                  </View>

                  <Text className='text-sm text-secondary mb-2'>
                    {formatTime2(feedback.createTime)}
                  </Text>
                  <Text className='text-base mb-3'>{feedback.content}</Text>

                  {/* 图片展示 */}
                  {feedback.images?.length > 0 && (
                    <View className='flex flex-wrap'>
                      {feedback.images?.map((img, index) => (
                        <Image
                          key={index}
                          src={img}
                          className='w-16 h-16 rounded mr-2 mb-2'
                          onClick={() =>
                            handlePreviewImage(feedback.images, img)
                          }
                        />
                      ))}
                    </View>
                  )}
                </View>

                {/* 回复内容 */}
                {feedback.replyContent && (
                  <View className=' bg-gray-100 p-4 rounded-lg'>
                    <Text className='text-sm text-secondary mb-1'>
                      官方回复 · {feedback.replyTime}
                    </Text>
                    <Text className='text-base'>{feedback.replyContent}</Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>
      </View>
    </PageLol>
  );
}
