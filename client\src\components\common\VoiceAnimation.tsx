import { View } from '@tarojs/components';
import React, { useEffect, useState } from 'react';

interface VoiceAnimationProps {
  isPlaying: boolean;
  isSend: boolean;
  frame: number;
}

/**
 * 语音播放动画组件
 * @param isPlaying 是否正在播放
 * @param isSend 是否为发送方
 * @param frame 当前动画帧（0/1/2）
 */
const VoiceAnimation: React.FC<VoiceAnimationProps> = ({
  isPlaying,
  isSend,
}) => {
  const [playingAnimationFrame, setPlayingAnimationFrame] = useState(0);
  useEffect(() => {
    if (isPlaying) {
      setPlayingAnimationFrame((prev) => prev + 1);
    }
  }, [isPlaying, playingAnimationFrame]);
  // 三条动画条的高度
  const heights = [
    [6, 12, 8],
    [12, 6, 12],
    [8, 12, 6],
  ];
  return (
    <View className='flex items-end h-4'>
      {[0, 1, 2].map((i) => (
        <View
          key={i}
          className={`w-1 mx-0.5 ${
            isSend ? 'bg-white' : 'bg-primary'
          } rounded-full`}
          style={{
            height: `${isPlaying ? heights[playingAnimationFrame][i] : 8}px`,
          }}
        ></View>
      ))}
    </View>
  );
};

export default VoiceAnimation;
