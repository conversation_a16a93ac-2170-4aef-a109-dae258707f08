import { createOrderStore } from './libs/order-store.factory';
import createSelector from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';
// 创建用户端订单store
const userOrderStore = createOrderStore({
  name: StorageSceneKey.USER_ORDER,
  storage: zustandStorage,
});

// 创建咨询师端订单store
const therapistOrderStore = createOrderStore({
  name: StorageSceneKey.THERAPIST_ORDER,
  storage: zustandStorage,
});

// 创建管理员端订单store
const adminOrderStore = createOrderStore({
  name: StorageSceneKey.ADMIN_ORDER,
  storage: zustandStorage,
});

// 创建selector
export const useOrderStoreSelector = createSelector(userOrderStore);
export const useTherapistOrderStoreSelector =
  createSelector(therapistOrderStore);
export const useAdminOrderStoreSelector = createSelector(adminOrderStore);

// /**
//  * 获取当前角色的订单store
//  */
// export const useOrderStore = () => {
//   const role = useProfileStore.getState().currentRole;
//   return role === USER_ROLE.THERAPIST
//     ? useTherapistOrderStoreSelector
//     : useOrderStoreSelector;
// };

/**
 * 获取当前角色的store实例
 * 用于非React组件中
 */
// export const getOrderStore = () => {
//   const role = useProfileStore.getState().currentRole;
//   return role === USER_ROLE.THERAPIST ? therapistOrderStore : userOrderStore;
// };

// 导出具体的store实例，用于不需要角色判断的场景
// export const useUserOrderStore = userOrderStore;
// export const useTherapistOrderStore = therapistOrderStore;
