import { Button, Field, Icon } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useState } from 'react';

interface ChatInputProps {
  onSendText: (text: string) => void;
  onSendImage: () => void;
  onSendAudio: () => void;
  onSendOrder: () => void;
  disabled?: boolean;
  placeholder?: string;
}

/**
 * 聊天输入组件
 * 包含文本输入框和图片发送按钮
 */
export const ChatInput: React.FC<ChatInputProps> = ({
  onSendText,
  onSendImage,
  onSendAudio,
  onSendOrder,
  disabled = false,
  placeholder = '输入消息...',
}) => {
  const [inputValue, setInputValue] = useState('');

  const handleSendMessage = () => {
    if (!inputValue.trim() || disabled) return;
    onSendText(inputValue.trim());
    setInputValue('');
  };

  const handleSendImage = async () => {
    if (disabled) return;
    onSendImage();
  };

  const handleSendOrder = () => {
    console.log('handleSendOrder');
    onSendOrder();
  };

  const handleSendAudio = () => {
    console.log('handleSendAudio');
    onSendAudio();
  };

  const handleMore = () => {
    console.log('handleMore');
    Taro.showActionSheet({
      itemList: ['发送订单', '发送图片', '发送语音'],
      success: (result) => {
        console.log(result);
      },
    });
  };

  return (
    <View className='fixed bottom-0 left-0 right-0 bg-white pb-2.5 px-4 flex flex-col border-t border-border box-border z-10'>
      <View className='flex flex-row h-12 items-center justify-between'>
        <Text
          className='text-sm font-medium text-tertiary'
          onClick={handleSendOrder}
        >
          #发送订单
        </Text>
        <Text className='text-sm font-bold text-tertiary' onClick={handleMore}>
          ⋮
        </Text>
      </View>
      <View className='flex flex-row items-center'>
        <View className='px-2'>
          <Icon
            name='photo-o'
            size='24px'
            color={disabled ? '#ccc' : '#666'}
            onClick={handleSendImage}
          />
        </View>
        <View className='px-2'>
          <Icon
            name='audio'
            size='24px'
            color={disabled ? '#ccc' : '#666'}
            onClick={handleSendAudio}
          />
        </View>

        <View className='flex-1 mx-2 bg-[#f7f8fa] rounded-[18px] px-3 py-1.5'>
          <Field
            className='w-full h-9 text-base bg-transparent'
            value={inputValue}
            placeholder={placeholder}
            confirmType='send'
            onChange={(e) => setInputValue(e.detail)}
            onConfirm={handleSendMessage}
            disabled={disabled}
            size='large'
          />
        </View>

        <Button
          className='ml-2 min-w-[120px]'
          size='small'
          type='primary'
          disabled={!inputValue.trim() || disabled}
          onClick={handleSendMessage}
        >
          发送
        </Button>
      </View>
    </View>
  );
};
