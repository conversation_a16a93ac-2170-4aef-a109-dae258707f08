// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { createDistributionOrder } = require("../service/distribution");
const {
  COLLECTIONS,
  ORDER_STATUS,
  REFUND_STATUS,
  INITATOR_SOURCE,
  ACTION_TYPE,
  ServiceType,
} = require("../common/db.constants");
const {
  formatBeijingTimeSlots,
  getBeijingDateAndHour,
} = require("../common/utils");
const {
  _addOrderAction,
  _releaseSchedule,
  _checkExistsOrder,
  _addSchedule,
  _settleOrderIncome,
  refundAuditPass,
  refundAuditNotPass,
  refundComplete,
  _handleRefundOrder,
} = require("../service/orderOperation");
const {
  testSubscribeMessage,
  sendOrderConfirmNotification,
  sendOrderRejectNotification,
} = require("../service/wxSubscribe");
const { coordinator } = require("../common/transaction-coordinator");
const { ORDER_INCOME_RATE } = require("../common/config");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
exports.db = db;
const _ = db.command;

/**
 * 接口列表:
 * 1. 创建订单
 * 2. 取消订单
 * 3. 确认订单
 * 4. 重新安排时间
 * 5. 拒绝订单
 * 6. 开始服务
 * 7. 完成服务
 *
 * todo: 关联数据库操作要事务化，确保数据一致性。 分销操作要异步化，确保不影响主流程。
 *
 *
 */

// 路由处理函数
const handlers = {
  // 创建订单，由用户发起，创建后，订单状态变为待支付
  createOrder: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await createOrder(data, context.userInfo);
        return success(result);
      }
    );
  },
  // 提交用户信息，由用户发起，在服务开始前，可以修改用户信息
  submitUserInfo: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await submitUserInfo(data, context.openid);
        return success(result);
      }
    );
  },
  // 确认订单，由咨询师发起，确认后，订单状态变为待开始
  confirmOrder: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await acceptOrder(data._id, context.openid);
        return success(result);
      }
    );
  },
  // 取消订单，由用户发起，退款到原支付方式，距离订单开始时间超过24小时，不退款
  cancelOrder: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await cancelOrder(data, context.openid);
        return success(result);
      }
    );
  },

  // 退款申请，当用户的订单状态不能退款时，可线下向咨询师申请退款，由咨询师发起，申请退款，提交管理员审核，状态变为审核中
  refundRequest: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_AND_USER_SELF,
      context,
      async () => {
        const result = await refundRequest(data, context.openid);
        return success(result);
      }
    );
  },

  fetchRefundAuditingOrders: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await fetchRefundAuditingOrders();
        return success(result);
      }
    );
  },
  // 退款审核通过，由管理员发起，审核通过后，发起退款，状态变为退款中
  refundAuditPass: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await refundAuditPass(data._id, context.openid);
        return success(result);
      }
    );
  },
  // 退款审核不通过，由管理员发起，审核不通过后，订单状态变为原状态(PENDING)
  refundAuditNotPass: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await refundAuditNotPass(data._id, context.openid);
        return success(result);
      }
    );
  },
  // 退款完成，由管理员发起，代表手工退款后，更新相关状态。正常情况下，是审核通过后自动发起退款，不走这个接口
  refundComplete: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const result = await refundComplete(data._id, context.openid);
        return success(result);
      }
    );
  },

  // 重新安排时间，由用户发起，重新安排时间后，订单状态变为待确认
  // 暂时不支持重新安排时间
  // rescheduleOrder: async (data, context) => {
  //   return await withPermission(
  //     PERMISSION_LEVEL.USER_ONLY,
  //     context,
  //     async () => {
  //       const result = await rescheduleOrder(
  //         data._id,
  //         data.date,
  //         data.timeSlot,
  //         context.openid
  //       );
  //       return success(result);
  //     }
  //   );
  // },
  // 拒绝订单，由咨询师发起，拒绝后，订单状态变为已取消
  rejectOrder: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await rejectOrder(data, context.openid);
        return success(result);
      }
    );
  },

  // 开始服务，由咨询师发起，开始服务后，订单状态变为进行中
  startService: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await startService(data._id, context.openid);
        return success(result);
      }
    );
  },

  // 完成服务，由咨询师发起，完成服务后，订单状态变为已完成
  completeService: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await completeService(data._id, context.openid);
        return success(result);
      }
    );
  },

  // 测试订阅消息，由用户发起，测试订阅消息
  testSubscribeMessage: async (data, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await testSubscribeMessage(data, context.openid);
      return success(result);
    });
  },
  // 获取今日订单数量
  getOrderToday: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.THERAPIST_ONLY,
      context,
      async () => {
        const result = await getOrderToday(data, context.openid);
        return success(result);
      }
    );
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = userInfo?.role || [USER_ROLE.GUEST];
    const userId = userInfo?._id;

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

async function _checkSchedule(startTime, duration, therapistId) {
  const { beijingDate, timeSlots } = formatBeijingTimeSlots(
    startTime,
    duration
  );

  // 检查排期是否被占用
  const timeSlotResult = await db
    .collection(COLLECTIONS.THERAPIST_SCHEDULES)
    .where({
      id: therapistId,
      date: beijingDate,
      // 检查排期是否被占用,即timeSlots中的小时数在排期中存在
      slots: _.in(timeSlots),
    })
    .get();
  return timeSlotResult.data && timeSlotResult.data.length > 0;
}

// 创建订单
async function createOrder(orderData, userInfo) {
  console.log("🚀🚀🚀 createOrder orderData", orderData);
  console.log("🚀🚀🚀 createOrder userInfo", userInfo);
  try {
    // 检查咨询师是否存在
    const therapistResult = await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: orderData.therapistId })
      .get();
    if (therapistResult.data && therapistResult.data.length === 0) {
      throw new Error("咨询师不存在");
    }

    // 检查是否和咨询师的服务类型和价格一致

    const therapistServiceResult = await db
      .collection(COLLECTIONS.THERAPIST_SERVICE)
      .where({
        id: orderData.therapistId,
      })
      .get();
    if (
      !therapistServiceResult.data ||
      therapistServiceResult.data.length === 0
    ) {
      throw new Error("咨询师没有服务");
    }

    const therapistService = therapistServiceResult.data[0];
    const service = therapistService.services.find(
      (service) => service.type === orderData.serviceType
    );
    if (!service) {
      throw new Error("咨询师没有这个服务");
    }
    if (service.finalPrice !== orderData.price) {
      throw new Error("咨询师的价格和订单的价格不一致");
    }

    // 检查预约时间是否在咨询师的工作时间范围内
    const workTime = therapistService.workTime;

    console.log("当前时区偏移:", new Date().getTimezoneOffset()); // UTC时区应返回0
    console.log("ISO字符串:", new Date().toISOString());
    console.log("UTC时间:", new Date().toUTCString());
    console.log("本地时间字符串:", new Date().toString());
    console.log("orderData.startTime:", orderData.startTime);

    const {
      date: beijingDate,
      hour: beijingHour,
      dayOfWeek,
    } = getBeijingDateAndHour(orderData.startTime);

    console.log("beijingDate:", beijingDate);
    console.log("beijingHour:", beijingHour);
    console.log("dayOfWeek:", dayOfWeek);
    if (workTime) {
      const workDays = workTime.workDays;
      if (workDays) {
        if (!workDays.includes(dayOfWeek)) {
          throw new Error(
            "预约时间不在咨询师的工作日期范围内" +
              "咨询师的工作日期为：" +
              workDays.join(",") +
              "预约日期为：" +
              dayOfWeek
          );
        }
      }

      const hour = beijingHour;
      if (hour < workTime.start || hour > workTime.end) {
        throw new Error(
          "预约时间不在咨询师的工作时间范围内" +
            "咨询师的工作时间为：" +
            workTime.start +
            " - " +
            workTime.end +
            "预约时间为：" +
            hour
        );
      }
    }

    // 计算排期信息, 占用的时间槽
    if (
      await _checkSchedule(
        orderData.startTime,
        orderData.duration,
        orderData.therapistId
      )
    ) {
      console.log("🚀🚀🚀 _checkSchedule 排期已被占用");
      throw new Error("排期已被占用");
    }
    console.log("🚀🚀🚀 准本创建订单", userInfo);
    // 构建订单数据
    const order = {
      ...orderData,
      status: ORDER_STATUS.PENDING_PAYMENT, // 待支付
      refundStatus: REFUND_STATUS.NONE, // 无退款
      createdAt: Date.now(),
      updatedAt: Date.now(),
      _openid: userInfo._openid,
      userId: userInfo.id,
      userAvatar: userInfo.avatar,
      userName: userInfo.name,
      therapistId: orderData.therapistId,
      therapistAvatar: therapistResult.data[0].avatar,
      therapistName: therapistResult.data[0].name,
      hasComplaint: false,
    };
    console.log("orderData", order);

    // 保存订单
    const result = await db.collection(COLLECTIONS.ORDER).add({
      data: order,
    });
    // 添加订单ID
    order._id = result._id;

    // 添加订单操作记录
    await _addOrderAction(order._id, ACTION_TYPE.CREATE, INITATOR_SOURCE.USER);

    // 更新排期信息
    await _addSchedule(
      orderData.startTime,
      orderData.duration,
      orderData.therapistId
    );

    return { order, message: "创建订单成功" };
  } catch (error) {
    console.error("创建订单失败:", error);
    throw error;
  }
}

async function submitUserInfo(data, openid) {
  try {
    const order = await _checkExistsOrder(data._id);
    if (order.userId !== openid) {
      throw new Error("无权提交用户信息");
    }
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(data._id)
      .update({
        data: {
          consultationInfo: {
            ...data.consultationInfo,
          },
          updatedAt: Date.now(),
        },
      });
    await _addOrderAction(
      data._id,
      ACTION_TYPE.SUBMIT_USER_INFO,
      INITATOR_SOURCE.USER,
      data.consultationInfo
    );
    return { message: "提交用户信息成功" };
  } catch (error) {
    console.error("提交用户信息失败:", error);
    throw error;
  }
}

// 取消订单,退款。
async function _cancelOrder(order, source, reason, detail) {
  console.log("🚀🚀🚀 _cancelOrder order", order, source, reason, detail);

  // 开始一个分布式事务
  const txId = await coordinator.startTransaction(order._id, "order_cancel", {
    source,
    reason,
    detail,
    orderStatus: order.status,
  });

  try {
    // 步骤1: 更新订单状态
    await coordinator.addStep(txId, {
      name: "update_order_status",
      data: {
        orderId: order._id,
        originalStatus: order.status,
        newStatus: ORDER_STATUS.CANCELLED,
      },
      rollbackHandler: "rollbackOrderStatus",
    });

    // 执行订单状态更新
    if (reason || detail) {
      await db
        .collection(COLLECTIONS.ORDER)
        .doc(order._id)
        .update({
          data: {
            status: ORDER_STATUS.CANCELLED,
            cancel_info: {
              source,
              reason,
              detail,
            },
            updatedAt: Date.now(),
            _txId: txId,
          },
        });
    } else {
      await db
        .collection(COLLECTIONS.ORDER)
        .doc(order._id)
        .update({
          data: {
            status: ORDER_STATUS.CANCELLED,
            updatedAt: Date.now(),
            _txId: txId,
          },
        });
    }

    // 标记步骤完成
    await coordinator.completeStep(txId, "update_order_status");

    // 步骤2: 添加订单操作记录
    await coordinator.addStep(txId, {
      name: "add_order_action",
      data: {
        orderId: order._id,
        action: ACTION_TYPE.CANCEL,
        source,
        reason,
        detail,
      },
    });

    // 执行添加订单操作记录
    await _addOrderAction(order._id, ACTION_TYPE.CANCEL, source, {
      reason,
      detail,
      _txId: txId,
    });

    // 标记步骤完成
    await coordinator.completeStep(txId, "add_order_action");

    // 步骤3: 更新排期信息
    const { beijingDate, timeSlots } = formatBeijingTimeSlots(
      order.startTime,
      order.duration
    );

    await coordinator.addStep(txId, {
      name: "remove_schedule",
      data: {
        therapistId: order.therapistId,
        startTime: order.startTime,
        duration: order.duration,
        beijingDate,
        timeSlots,
      },
      rollbackHandler: "rollbackScheduleRemove",
    });

    // 执行更新排期信息
    await _releaseSchedule(order.startTime, order.duration, order.therapistId);

    // 标记步骤完成
    await coordinator.completeStep(txId, "remove_schedule");

    // 步骤4: 处理退款
    await coordinator.addStep(txId, {
      name: "handle_refund",
      data: {
        orderId: order._id,
        source,
      },
    });

    // 执行退款处理
    const refundResult = await _handleRefundOrder(order, source, txId);

    // 标记步骤完成
    await coordinator.completeStep(txId, "handle_refund", refundResult);

    // 提交事务
    await coordinator.commitTransaction(txId);

    return refundResult;
  } catch (error) {
    console.error("取消订单失败:", error);

    // 回滚事务
    try {
      await coordinator.rollbackTransaction(txId, error.message);
    } catch (rollbackError) {
      console.error("事务回滚失败:", rollbackError);
    }

    throw error;
  }
}

// 取消订单,用户取消订单，管理员的取消操作直接在数据后台进行
async function cancelOrder(data, openid) {
  console.log("🚀🚀🚀 cancelOrder data", data, openid);
  try {
    const {
      _id,
      cancel_info: { source, reason, detail },
    } = data;
    const order = await _checkExistsOrder(_id);

    // 验证是否是用户自己的订单
    if (order.userId !== openid) {
      throw new Error("无权取消该订单");
    }

    // 检查退款状态，如果正在退款中，不能取消
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      throw new Error("订单正在退款中，不能取消");
    }

    // 未支付状态或者已接受后24小时内的订单才能取消 24小时后不能取消
    if (
      order.status !== ORDER_STATUS.PENDING_PAYMENT &&
      order.status !== ORDER_STATUS.PENDING_CONFIRM &&
      order.status !== ORDER_STATUS.PENDING_START
    ) {
      throw new Error("当前订单状态不允许取消");
    }

    // 如果订单状态是已接受，则需要判断当前时间与预约时间是否超过24小时
    if (order.status !== ORDER_STATUS.PENDING_PAYMENT) {
      const now = Date.now();
      const appointmentTime = order.startTime;
      const diffTime = appointmentTime - now;
      console.log("appointmentTime", appointmentTime);
      console.log("now", now);
      console.log("diffTime", diffTime);
      if (diffTime < 24 * 60 * 60 * 1000) {
        throw new Error("距离预约开始不足24小时，请联系咨询师取消");
      }
    }
    const result = await _cancelOrder(order, source, reason, detail);

    if (result.warning) {
      return {
        returnCode: 0,
        message: "订单取消成功，但退款处理失败，请联系客服",
        warning: result.message,
        error: result.error,
      };
    }

    return { returnCode: 0, message: "订单取消成功" };
  } catch (error) {
    console.error("取消订单失败:", error);
    throw error;
  }
}

// 咨询师接受用户的订单
async function acceptOrder(orderId, therapistId) {
  console.log("🚀🚀🚀 acceptOrder orderId", orderId, therapistId);
  try {
    const order = await _checkExistsOrder(orderId);

    // 验证是否是咨询师自己的订单
    if (order.therapistId !== therapistId) {
      throw new Error("无权确认该订单");
    }

    // 只有待确认的订单才能确认
    if (order.status !== ORDER_STATUS.PENDING_CONFIRM) {
      throw new Error("当前订单状态不允许确认");
    }

    // 验证退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      throw new Error("订单正在退款中，不能确认");
    }

    // 检查下是否已经到了订单的预约时间，如果不足1个小时，给出警告
    const now = Date.now();
    const appointmentTime = order.startTime;
    const diffTime = appointmentTime - now;
    if (diffTime < 0) {
      throw new Error("预约时间已过，请重新预约");
    }
    if (diffTime < 1 * 60 * 60 * 1000) {
      return {
        message: "已更新订单状态为已开始",
        warning: "当前距离预约时间不足1小时，请确认",
      };
    }

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .update({
        data: {
          status: ORDER_STATUS.PENDING_START,
          updatedAt: Date.now(),
        },
      });

    await _addOrderAction(
      orderId,
      ACTION_TYPE.ACCEPT,
      INITATOR_SOURCE.THERAPIST
    );

    // 向用户发送订单确认通知
    await sendOrderConfirmNotification(order);

    return { message: "订单确认成功" };
  } catch (error) {
    console.error("确认订单失败:", error);
    throw error;
  }
}

// 咨询师拒绝用户的订单
async function rejectOrder(data, openid) {
  console.log("🚀🚀🚀 rejectOrder data", data, openid);
  try {
    const {
      _id: orderId,
      reject_info: { reason, desc },
    } = data;
    const order = await _checkExistsOrder(orderId);

    // 验证是否是咨询师自己的订单
    if (order.therapistId !== openid) {
      throw new Error("无权拒绝该订单");
    }

    // 验证订单状态 只有待确认的订单才能拒绝
    if (order.status !== ORDER_STATUS.PENDING_CONFIRM) {
      throw new Error("当前订单状态不允许拒绝");
    }

    // 验证退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      throw new Error("订单正在退款中，不能拒绝");
    }

    await _addOrderAction(
      orderId,
      ACTION_TYPE.REJECT,
      INITATOR_SOURCE.THERAPIST,
      {
        reason,
      }
    );

    // 处理退款
    const cancelResult = await _cancelOrder(
      order,
      INITATOR_SOURCE.THERAPIST,
      reason,
      desc
    );

    // 向用户发送订单拒绝通知
    await sendOrderRejectNotification(
      orderId,
      INITATOR_SOURCE.THERAPIST_CANCEL,
      reason,
      desc
    );

    if (cancelResult.warning) {
      return {
        message: "订单拒绝成功，但退款处理失败，请联系管理员",
        warning: cancelResult.message,
        error: cancelResult.error,
      };
    }

    return { message: "订单拒绝成功" };
  } catch (error) {
    console.error("拒绝订单失败:", error);
    throw error;
  }
}

// 重新安排订单时间
// async function rescheduleOrder(orderId, date, timeSlot, openid) {
//   try {
//     const order = await _checkExistsOrder(orderId);

//     // 验证权限 - 用户自己的订单
//     if (order.userId !== openid) {
//       throw new Error("无权重新安排该订单");
//     }

//     // 验证订单状态 只有待支付待确认PENDING的订单才能重新安排时间
//     if (
//       order.status !== ORDER_STATUS.PENDING_PAYMENT &&
//       order.status !== ORDER_STATUS.PENDING_CONFIRM &&
//       order.status !== ORDER_STATUS.PENDING
//     ) {
//       throw new Error("当前订单状态不允许重新安排时间");
//     }

//     // 更新订单时间
//     await db
//       .collection(COLLECTIONS.ORDER)
//       .doc(orderId)
//       .update({
//         data: {
//           appointmentTime: {
//             date,
//             timeSlot,
//           },
//           status: ORDER_STATUS.PENDING_CONFIRM,
//           updatedAt: Date.now(),
//         },
//       });

//     // 向咨询师发送重新安排时间通知
//     await sendRescheduleOrderNotification(orderId, date, timeSlot);

//     return { message: "订单时间重新安排成功" };
//   } catch (error) {
//     console.error("重新安排订单时间失败:", error);
//     throw error;
//   }
// }

// 开始服务
async function startService(orderId, therapistId) {
  console.log("🚀🚀🚀 startService orderId", orderId, therapistId);
  try {
    const order = await _checkExistsOrder(orderId);

    // 验证是否是咨询师自己的订单
    if (order.therapistId !== therapistId) {
      throw new Error("无权确认该订单");
    }

    // 验证订单状态 只有PENDING_START的订单才能开始服务
    if (order.status !== ORDER_STATUS.PENDING_START) {
      throw new Error("当前订单状态不允许开始服务");
    }

    // 验证退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      throw new Error("订单正在退款中，不能开始服务");
    }

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .update({
        data: {
          status: ORDER_STATUS.IN_PROGRESS,
          updatedAt: Date.now(),
        },
      });

    await _addOrderAction(
      orderId,
      ACTION_TYPE.START,
      INITATOR_SOURCE.THERAPIST
    );

    // 检查下是否已经到了订单的预约时间，如果没到，给出警告
    const now = Date.now();
    const appointmentTime = order.startTime;
    const diffTime = appointmentTime - now;
    // 如果预约时间大于5分钟，则给出警告
    if (diffTime > 5 * 60 * 1000) {
      return {
        message: "已更新订单状态为已开始",
        warning: "当前距离预约时间还有" + diffTime + "分钟，请确认",
      };
    }

    return { message: "开始服务成功" };
  } catch (error) {
    console.error("开始服务失败:", error);
    throw error;
  }
}

// 完成服务
async function completeService(orderId, therapistId) {
  console.log("🚀🚀🚀 completeService orderId", orderId, therapistId);
  try {
    const order = await _checkExistsOrder(orderId);

    // 验证是否是咨询师自己的订单
    if (order.therapistId !== therapistId) {
      throw new Error("无权确认该订单");
    }

    // 验证退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      throw new Error("订单正在退款中，不能完成服务");
    }

    // 验证订单状态 IN_PROGRESS 或者是面对面服务的PENDING_START
    if (
      order.status === ORDER_STATUS.IN_PROGRESS ||
      (order.status === ORDER_STATUS.PENDING_START &&
        order.serviceType === ServiceType.FACE_TO_FACE)
    ) {
      // 更新订单状态
      await db
        .collection(COLLECTIONS.ORDER)
        .doc(orderId)
        .update({
          data: {
            status: ORDER_STATUS.COMPLETED,
            endTime: Date.now(),
            updatedAt: Date.now(),
          },
        });

      await _addOrderAction(
        orderId,
        ACTION_TYPE.COMPLETE,
        INITATOR_SOURCE.THERAPIST
      );

      try {
        // 结算咨询师钱包和收入明细
        await _settleOrderIncome(order);

        // 创建分销订单
        await createDistributionOrder(order);
      } catch (error) {
        console.error("结算订单收入失败:", error);
        return {
          warning: "完成服务成功，但结算订单收入失败，请联系管理员",
          error: error.message,
        };
      }

      // 检查下是否已经到了订单的预约完成时间，如果没到，给出警告
      const now = Date.now();
      const appointmentTime = order.startTime + order.duration * 60 * 1000;
      const diffTime = appointmentTime - now;
      if (diffTime > 5 * 60 * 1000) {
        return {
          message: "已更新订单状态为已结束",
          warning: "当前距离预约完成时间还有" + diffTime + "分钟，请确认",
        };
      }

      return { message: "完成服务成功" };
    }
    throw new Error("当前订单状态不允许结束服务");
  } catch (error) {
    console.error("完成服务失败:", error);
    throw error;
  }
}

// 退款申请，由咨询师发起，申请退款，状态变为审核中
async function refundRequest(data, openid) {
  const { _id: orderId, reason, detail } = data;
  console.log("🚀🚀🚀 refundRequest orderId", orderId, reason, detail);
  try {
    const order = await _checkExistsOrder(orderId);

    // 验证是否是咨询师自己的订单
    if (order.therapistId !== openid) {
      throw new Error("不是自己的订单，无权申请退款");
    }

    // 验证订单状态
    if (order.status <= ORDER_STATUS.PENDING_PAYMENT) {
      throw new Error("当前订单状态未付款");
    }

    // 验证是否已经在退款流程中
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      if (order.refundStatus === REFUND_STATUS.REJECTED) {
        // 如果之前被拒绝，可以重新申请
        console.log("退款申请被拒绝过，允许重新申请");
      } else {
        throw new Error(
          `当前订单退款状态为${order.refundStatus}，不能申请退款`
        );
      }
    }

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .update({
        data: { refundStatus: REFUND_STATUS.AUDITING, updatedAt: Date.now() },
      });

    await _addOrderAction(
      orderId,
      ACTION_TYPE.REFUND_REQUEST,
      INITATOR_SOURCE.THERAPIST,
      {
        reason,
        detail,
      }
    );

    return { message: "退款申请操作成功，请等待审核" };
  } catch (error) {
    console.error("退款申请失败:", error);
    throw error;
  }
}

async function fetchRefundAuditingOrders() {
  const result = await db
    .collection(COLLECTIONS.ORDER)
    .where({
      refundStatus: REFUND_STATUS.AUDITING,
    })
    .orderBy("updatedAt", "desc")
    .get();
  return {
    success: true,
    data: result.data,
  };
}

async function getOrderToday(data, openid) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const startOfDay = today.getTime();

  today.setHours(23, 59, 59, 999);
  const endOfDay = today.getTime();

  // 获取今日所有订单
  const result = await db
    .collection(COLLECTIONS.ORDER)
    .where({
      therapistId: openid,
      createdAt: _.gte(startOfDay).and(_.lte(endOfDay)),
    })
    .get();

  const orders = result.data || [];

  // 统计数据
  const response = {
    pendingConfirm: 0, // 待确认订单数
    refundOrAfterSale: 0, // 退款/售后订单数
    complaint: 0, // 投诉订单数
    totalOrder: orders.length, // 今日新增订单总数
    totalAmount: 0, // 今日新增订单总金额
  };

  // 遍历订单计算各项统计数据
  orders.forEach((order) => {
    // 计算总金额
    response.totalAmount += order.price || 0;

    // 统计待确认订单
    if (order.status === ORDER_STATUS.PENDING_CONFIRM) {
      response.pendingConfirm++;
    }

    // 统计退款/售后订单
    if (
      order.refundStatus &&
      order.refundStatus !== REFUND_STATUS.NONE &&
      order.refundStatus !== REFUND_STATUS.REJECTED
    ) {
      response.refundOrAfterSale++;
    }

    // 统计投诉订单
    if (order.hasComplaint) {
      response.complaint++;
    }
  });

  return { data: response };
}
