// client/src/hooks/useOrderDetail.ts
import { orderUserActions } from '@core/actions/order.user';
import { therapistActions } from '@core/actions/therapist.action';
import { useOrderStoreSelector } from '@stores/order.store';
import { useReviewStore } from '@stores/review.store';
import { useTherapistStore } from '@stores/therapist.store';
import { useCallback, useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

/**
 * 订单详情管理Hook
 * 提供订单详情的获取和状态监听功能
 */

export function useOrderDetail(orderId: string) {
  // 使用store
  const order = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId || ''))
  );
  const therapist = useTherapistStore(
    useShallow((state) =>
      state.therapists?.find((item) => item.id === order?.therapistId)
    )
  );

  const orderActions = useOrderStoreSelector(
    useShallow((state) => state.getOrderActions(orderId))
  );
  const orderReview = useReviewStore(
    useShallow((state) =>
      state.reviews.filter((item) => item.orderId === orderId)
    )
  );
  // 本地状态
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 封装数据获取逻辑
  const fetchOrderData = useCallback(async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const orderSummary = await orderUserActions.fetchOrderById(orderId, true);
      if (!orderSummary) {
        return;
      }

      await Promise.all([
        orderUserActions.fetchOrderActions(orderId, true),
        therapistActions.fetchTherapistSummary(orderSummary.therapistId),
        therapistActions.fetchTherapistPreferRating(
          orderSummary.therapistId,
          true
        ),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败');
    } finally {
      setIsLoading(false);
    }
  }, [orderId]);

  /**
   * 刷新订单详情
   */
  const refresh = useCallback(() => {
    return fetchOrderData();
  }, [fetchOrderData]);

  // 初始加载
  useEffect(() => {
    if (!orderId) return;

    fetchOrderData();

    // 启动监听
    const watcher = orderUserActions.watchOrderById(orderId);

    // 组件卸载时清理
    return () => {
      watcher.close();
    };
  }, [orderId, fetchOrderData]);

  return {
    order,
    therapist,
    orderActions,
    orderReview,
    refresh,
    isLoading,
    error,
  };
}
