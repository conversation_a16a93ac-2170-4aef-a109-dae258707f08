import { Cell, Empty, Tab, Tabs } from '@antmjs/vantui';
import DotLoading from '@components/common/loading-dots';
import { AVATAR_DEFAULT, DEFAULT_USER_NAME } from '@constants/assets';
import { distributionActions } from '@core/actions/distribution.action';
import { incomeActions } from '@core/actions/income.action';
import { useDistributionStore } from '@stores/distribution.store';
import { useIncomeStore } from '@stores/income.store';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { usePullDownRefresh, useReachBottom } from '@tarojs/taro';
import { formatTime2 } from '@utils/time';
import { useCallback, useEffect, useState } from 'react';
import IncomeItem from '../components/IncomeItem';

export default function ShareTabs() {
  const [activeTab, setActiveTab] = useState(0);
  const incomePagination = useIncomeStore.use.pagination();
  const incomeLoading = useIncomeStore.use.loading();
  const invitedUsers = useDistributionStore.use.invitedUsers();
  const incomeList = useIncomeStore.use.incomeList();

  // 刷新收入列表
  const refreshIncomeList = useCallback(async () => {
    await incomeActions.fetchIncomeList({
      page: 1,
      pageSize: 10,
      params: {
        incomeType: 2, // 分销收入
      },
      forceRefresh: true,
    });
  }, []);

  // 加载更多收入
  const loadMoreIncome = useCallback(async () => {
    if (incomePagination?.hasNext && !incomeLoading) {
      await incomeActions.loadMoreIncomeDetails();
    }
  }, [incomePagination?.hasNext, incomeLoading]);

  // 刷新邀请用户列表
  const refreshInvitedUsers = useCallback(async () => {
    await distributionActions.fetchInvitedUsers({
      page: 1,
      pageSize: 10,
    });
  }, []);

  // 加载更多邀请用户
  const loadMoreInvitedUsers = useCallback(async () => {
    if (invitedUsers.pagination?.hasNext && !invitedUsers.loading) {
      await distributionActions.loadMoreInvitedUsers();
    }
  }, [invitedUsers.pagination?.hasNext, invitedUsers.loading]);

  // 监听滚动到底部事件
  useReachBottom(() => {
    if (activeTab === 0) {
      loadMoreIncome();
    } else {
      loadMoreInvitedUsers();
    }
  });

  // 监听下拉刷新事件
  usePullDownRefresh(() => {
    if (activeTab === 0) {
      refreshIncomeList();
    } else {
      refreshInvitedUsers();
    }
  });

  // Tab切换时刷新数据
  useEffect(() => {
    if (activeTab === 0) {
      refreshIncomeList();
    } else {
      refreshInvitedUsers();
    }
  }, [activeTab, refreshIncomeList, refreshInvitedUsers]);

  return (
    <View className='mx-4 mt-4 bg-bg rounded-xl overflow-hidden'>
      <Tabs active={activeTab} onChange={(e) => setActiveTab(e.detail.index)}>
        <Tab title='分享订单' />
        <Tab title='邀请用户' />
      </Tabs>

      {activeTab === 0 ? (
        <ScrollView scrollY className='max-h-96'>
          <View className='p-2'>
            {incomeList.length > 0 ? (
              incomeList.map((income) => (
                <IncomeItem key={income.id} income={income} />
              ))
            ) : (
              <Empty description='暂无数据' />
            )}
            {incomeLoading && <DotLoading />}
            {/* 加载更多 */}
            {incomeList.length > 0 &&
              !incomeLoading &&
              incomePagination?.hasNext && (
                <View className='py-3 text-center text-sm text-secondary'>
                  <Text className='px-4 py-2' onClick={loadMoreIncome}>
                    加载更多
                  </Text>
                </View>
              )}
            {incomeList.length > 0 &&
              !incomeLoading &&
              !incomePagination?.hasNext && (
                <View className='py-3 text-center text-sm text-secondary'>
                  <Text className='px-4 py-2'>没有更多了</Text>
                </View>
              )}
          </View>
        </ScrollView>
      ) : (
        <ScrollView scrollY className='max-h-96'>
          <View className='p-2'>
            {invitedUsers.list.length > 0 ? (
              invitedUsers.list.map((user) => (
                <View key={user.inviteeId} className='mb-2'>
                  <View className='flex flex-row'>
                    <Image
                      src={user.inviteeAvatar || AVATAR_DEFAULT}
                      className='w-10 h-10 rounded-full mr-3 self-start mt-2'
                      mode='aspectFill'
                    />
                    <View className='flex-1'>
                      <Cell
                        title={user.inviteeName || DEFAULT_USER_NAME}
                        label={`注册时间: ${formatTime2(user.invitedTime)}`}
                        border={false}
                      ></Cell>
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <Empty description='暂无数据' />
            )}

            {/* 加载更多 */}
            <View className='py-3 text-center text-sm text-secondary'>
              {invitedUsers.loading && <DotLoading />}
              {invitedUsers.pagination?.hasNext && !invitedUsers.loading && (
                <Text className='px-4 py-2' onClick={loadMoreInvitedUsers}>
                  加载更多
                </Text>
              )}
              {invitedUsers.list.length > 0 &&
                !invitedUsers.loading &&
                !invitedUsers.pagination?.hasNext && (
                  <Text className='px-4 py-2'>没有更多了</Text>
                )}
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  );
}
