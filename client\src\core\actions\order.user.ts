import { CreateOrderParams, OrderListRequest } from '@core/api';
import { OrderCacheManager } from '@core/cache/order-cache-manager';
import { CACHE_ROLE } from '@core/cache/order-cache-policy';
import {
  ConsultationDirection,
  INITATOR_SOURCE,
} from '@model/common.interface';
import {
  CANCEL_REASON,
  Order_action,
  ORDER_STATUS,
  Order_summary,
} from '@model/order.interface';

import { orderService } from '@services/order.user';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';

/**
 * 用户端订单操作
 */
export const orderUserActions = {
  /**
   * 获取订单列表
   */
  fetchOrders: async (params: OrderListRequest = {}): Promise<void> => {
    const store = useOrderStoreSelector.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);

    try {
      const { forceRefresh } = params;

      if (!forceRefresh) {
        // 尝试从缓存获取
        const cachedData = OrderCacheManager.getOrderList(
          params,
          CACHE_ROLE.USER
        );
        if (cachedData) {
          store.setOrders(cachedData.data);
          store.setPagination(cachedData.pagination);
          store.setLoading(false);
          return;
        }
      }

      const result = await orderService.getOrders(params);

      if (result.success) {
        store.setOrders(result.data);
        store.setPagination(result.pagination || null);

        // 设置缓存
        OrderCacheManager.setOrderList(
          params,
          result.data,
          result.pagination,
          CACHE_ROLE.USER
        );

        return;
      }

      throw new Error('获取订单列表失败');
    } catch (error) {
      store.setError('获取订单列表失败');
      console.error('获取订单列表失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 获取单个订单summary
   */
  fetchOrderById: async (
    id: string,
    forceRefresh = false
  ): Promise<Order_summary | null> => {
    const store = useOrderStoreSelector.getState();
    store.setLoading(true);

    try {
      if (!forceRefresh) {
        // 尝试从缓存获取
        const cachedData = OrderCacheManager.getOrderSummary(
          id,
          CACHE_ROLE.USER
        );
        if (cachedData) {
          store.updateOrder(cachedData);
          store.setCurrentOrder(id);
          store.setLoading(false);
          return cachedData;
        }
      }

      const order = await orderService.getOrderById(id);

      store.updateOrder(order as Order_summary);
      store.setCurrentOrder(id);

      // 设置缓存
      OrderCacheManager.setOrderSummary(
        id,
        order as Order_summary,
        CACHE_ROLE.USER
      );

      return order;
    } catch (error) {
      store.setError('获取订单详情失败');
      console.error('获取订单详情失败:', error);
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  // 用户订单操作日志
  fetchOrderActions: async (
    orderId: string,
    forceRefresh = false
  ): Promise<Order_action[]> => {
    const store = useOrderStoreSelector.getState();
    const actionsInStore = store.getOrderActions(orderId);
    if (actionsInStore.length > 0 && !forceRefresh) {
      return actionsInStore;
    }
    try {
      const actions = await orderService.getOrderActions(orderId);
      store.setOrderActions(actions);
      return actions;
    } catch (error) {
      console.error('获取订单操作失败:', error);
      throw error;
    }
  },

  /**
   * 创建订单
   */
  createOrder: async (orderData: CreateOrderParams): Promise<string> => {
    const store = useOrderStoreSelector.getState();
    store.setLoading(true);

    try {
      // 调用服务创建订单
      console.log('🚀🚀🚀 createOrder orderData', orderData);
      const dateInMillis = new Date(orderData.startTime);
      const date = new Date(
        dateInMillis.getFullYear(),
        dateInMillis.getMonth(),
        dateInMillis.getDate()
      ).getTime();
      console.log('🚀🚀🚀 createOrder date', date);
      const hour = dateInMillis.getHours();
      console.log('🚀🚀🚀 createOrder hour', hour);

      // 调用服务创建订单
      // 这里应该返回整个订单并更新store，否则可能接下来支付时，订单信息并没有通过watch即时更新
      const order = await orderService.createOrder(orderData);

      console.log('🚀🚀🚀 createOrder order', order);
      // 更新状态
      store.addOrder(order);

      // 清除列表缓存
      OrderCacheManager.clearOrderList();

      return order._id;
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 取消订单
   */
  cancelOrder: async (
    orderId: string,
    reason?: CANCEL_REASON,
    detail?: string
  ): Promise<{
    success: boolean;
    message: string;
    warning?: string;
    error?: string;
  }> => {
    useLoadingStore.getState().setTransactionLoading(true);
    try {
      // 调用服务取消订单
      const result = await orderService.cancelOrder({
        _id: orderId,
        cancel_info: {
          source: INITATOR_SOURCE.USER,
          reason: reason || CANCEL_REASON.OTHER,
          detail: detail || '',
        },
      });

      console.log('🚀🚀🚀 cancelOrder result', result);
      return result;
    } catch (error) {
      console.error('取消订单失败:', error);

      throw error;
    }
  },

  /**
   * 开始监听订单列表
   * @param status 订单状态
   */
  watchOrders: (
    status?: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][]
  ): any => {
    console.log('orderUserActions watchOrders status', status);
    return orderService.watchOrders({ status }, (snapshot) => {
      console.log('orderUserActions watchOrders snapshot', snapshot);
      if (snapshot.type === 'init') {
        console.log('订单监听初始化:', snapshot.docs.length);
      } else {
        // 数据变更
        snapshot.docChanges.forEach((change) => {
          console.log('orderUserActions watchOrders change', change);
          if (change.dataType === 'update' || change.dataType === 'add') {
            const order = change.doc as Order_summary;
            orderUserActions.updateOrder(order);
          }
        });
      }
    });
  },

  /**
   * 开始监听单个订单
   * @param orderId 订单ID
   */
  watchOrderById: (orderId: string): any => {
    return orderService.watchOrderById(orderId, (snapshot) => {
      if (snapshot.type === 'init') {
        console.log('订单详情监听初始化:', snapshot.docs.length);
      } else {
        // 数据变更
        console.log('orderUserActions watchOrderById snapshot', snapshot);
        snapshot.docChanges.forEach((change) => {
          if (change.dataType === 'update' || change.dataType === 'add') {
            const order = change.doc as Order_summary;
            orderUserActions.updateOrder(order);
          }
        });
      }
    });
  },

  updateOrder: (order: Order_summary) => {
    const store = useOrderStoreSelector.getState();
    store.updateOrder(order);

    // 更新缓存
    OrderCacheManager.setOrderSummary(order._id, order, CACHE_ROLE.USER);
  },

  /**
   * 加载更多订单
   */
  loadMore: async (): Promise<void> => {
    const store = useOrderStoreSelector.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);
    const { pagination, filters } = store;

    // 没有更多数据了
    if (!pagination || !pagination.hasNext) return;

    // 构建请求参数
    const params: OrderListRequest = {
      page: pagination.page + 1,
      pageSize: pagination.pageSize,
      params: {
        status: filters?.status || undefined,
        query: filters?.query,
        complaint: filters?.complaint,
      },
      role: CACHE_ROLE.USER,
    };

    try {
      // 从服务获取数据
      const result = await orderService.getOrders(params);

      if (result.success) {
        const { orders: currentOrders } = store;

        // 将新数据添加到现有数据后面
        const combinedOrders = [...(currentOrders || []), ...result.data];

        // 更新状态
        store.setOrders(combinedOrders);
        store.setPagination(result.pagination || null);

        // 缓存数据
        OrderCacheManager.setOrderList(
          params,
          result.data,
          result.pagination,
          CACHE_ROLE.USER
        );
      }
    } catch (error) {
      console.error('加载更多订单失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  rescheduleOrder: async (
    orderId: string,
    date: number,
    timeSlot: number
  ): Promise<boolean> => {
    const store = useOrderStoreSelector.getState();
    store.setLoading(true);

    try {
      const success = await orderService.rescheduleOrder(
        orderId,
        date,
        timeSlot
      );
      if (success) {
        //重新拉取订单
        await orderUserActions.fetchOrderById(orderId, true);
      }
    } catch (error) {
      store.setError('重新预约订单失败');
      console.error('重新预约订单失败:', error);
      return false;
    } finally {
      store.setLoading(false);
    }
    return true;
  },

  /**
   * 提交用户信息
   */
  submitUserInfo: async (
    orderId: string,
    name?: string,
    category?: ConsultationDirection,
    desc?: string
  ): Promise<boolean> => {
    try {
      const success = await orderService.submitUserInfo(orderId, {
        consultationInfo: {
          name: name,
          category: category,
          desc: desc,
        },
      });
      return success;
    } catch (error) {
      console.error('提交用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 清空状态
   */
  resetStore: () => {
    useOrderStoreSelector.getState().reset();
  },
};
