/**
 * 平台其它用户信息存储
 */

import { user_public, user_sensitive } from '@model/user.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  userList: user_public[] | null;
  userSensitiveList: user_sensitive[] | null;
  loading: boolean;
  error: string | null;
}

interface Action {
  setUserList: (userList: user_public[]) => void;
  setUserSensitiveList: (userSensitiveList: user_sensitive[]) => void;
  getUser: (userId: string) => user_public | null;
  updateUser: (user: user_public) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

const initialState: State = {
  userList: null,
  userSensitiveList: null,
  loading: false,
  error: null,
};

const userStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setUserList: (userList) => set({ userList }),
        setUserSensitiveList: (userSensitiveList) => set({ userSensitiveList }),
        getUser: (userId) =>
          get().userList?.find((user) => user.id === userId) || null,
        updateUser: (user) =>
          set((state) => {
            const index = state.userList?.findIndex((u) => u.id === user.id);
            if (index !== undefined && index !== -1) {
              const newUserList = [...state.userList!];
              newUserList[index] = user;
              return { userList: newUserList };
            } else {
              if (state.userList) {
                return { userList: [...state.userList, user] };
              } else {
                return { userList: [user] };
              }
            }
          }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        reset: () => set(initialState),
      }),
      {
        name: StorageSceneKey.USER,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 用户列表
        partialize: (state) => ({
          userList: state.userList,
        }),
      }
    )
  )
);

export const useUserStore = createSelectors(userStore);
