import {
  AudioCategory,
  ListeningRecord,
  TreatmentAudio,
} from '@model/treatment.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

// 按分类组织的音频列表类型
interface AudioListByCategory {
  // 每个分类的音频列表
  [categoryId: string]: TreatmentAudio[];
}

interface State {
  // 当前选中的分类
  activeCategory: AudioCategory | null;
  // 推荐声音
  featuredAudios: TreatmentAudio[];
  // 音频列表 - 按分类存储
  audioListByCategory: AudioListByCategory;

  // 收听记录
  listeningRecords: ListeningRecord[];
  // 是否正在加载中
  loading: boolean;
  // 错误信息
  error: Error | null;
  // 上次更新时间
  lastUpdated: number;
  // 当前选中的音频ID（用于跳转到播放页面）
  selectedAudioId: string | null;
}

interface Action {
  setActiveCategory: (category: AudioCategory | null) => void;
  setFeaturedAudios: (audios: TreatmentAudio[]) => void;
  setAudios: (audioList: TreatmentAudio[], category?: AudioCategory) => void;
  // 获取特定分类的音频列表
  getAudioListByCategory: (
    categoryId: AudioCategory | 'all'
  ) => TreatmentAudio[];
  // 获取所有音频列表（扁平化）
  getAllAudioList: () => TreatmentAudio[];
  setListeningRecords: (records: ListeningRecord[]) => void;
  getAudio: (audioId: string) => TreatmentAudio | null;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
  setSelectedAudioId: (id: string | null) => void;
  reset: () => void;
}

const initialState: State = {
  activeCategory: null,
  featuredAudios: [],
  audioListByCategory: {},
  listeningRecords: [],
  loading: false,
  error: null,
  lastUpdated: 0,
  selectedAudioId: null,
};

const treatmentStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setActiveCategory: (category) =>
          set((state) => {
            state.activeCategory = category;
          }),
        setFeaturedAudios: (audios) =>
          set((state) => {
            state.featuredAudios = audios;
          }),
        setAudios: (audioList, category) =>
          set((state) => {
            if (category) {
              // 更新特定分类的音频列表
              state.audioListByCategory[category] = audioList;
            } else {
              // 如果没有指定分类，则按分类整理并存储
              const newAudioListByCategory: AudioListByCategory = {};

              // 将音频按分类分组
              audioList.forEach((audio) => {
                const audioCategory = audio.category;
                if (!newAudioListByCategory[audioCategory]) {
                  newAudioListByCategory[audioCategory] = [];
                }
                newAudioListByCategory[audioCategory].push(audio);
              });

              state.audioListByCategory = newAudioListByCategory;
            }
            state.lastUpdated = Date.now();
          }),
        getAudioListByCategory: (categoryId) => {
          const state = get();
          if (categoryId === 'all') {
            return get().getAllAudioList();
          }
          return state.audioListByCategory[categoryId] || [];
        },
        getAllAudioList: () => {
          const state = get();
          // 将所有分类的音频合并为一个数组
          return Object.values(state.audioListByCategory).reduce(
            (allAudios, categoryAudios) => {
              return [...allAudios, ...categoryAudios];
            },
            [] as TreatmentAudio[]
          );
        },

        setListeningRecords: (records) =>
          set((state) => {
            state.listeningRecords = records;
          }),
        setLoading: (loading) =>
          set((state) => {
            state.loading = loading;
          }),
        setError: (error) =>
          set((state) => {
            state.error = error;
          }),
        setSelectedAudioId: (id) =>
          set((state) => {
            state.selectedAudioId = id;
          }),
        getAudio: (audioId) => {
          const state = get();
          return (
            state.getAllAudioList().find((audio) => audio.id === audioId) ||
            null
          );
        },

        reset: () => set(initialState),
      }),
      {
        name: StorageSceneKey.TREATMENT,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          audioListByCategory: state.audioListByCategory,
          listeningRecords: state.listeningRecords,
          lastUpdated: state.lastUpdated,
        }),
      }
    )
  )
);

export const useTreatmentStore = createSelectors(treatmentStore);
