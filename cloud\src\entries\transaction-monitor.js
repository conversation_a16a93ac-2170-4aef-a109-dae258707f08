// 云函数入口文件
const cloud = require("wx-server-sdk");
const { coordinator, TX_STATUS } = require("../common/transaction-coordinator");
require("../common/transaction-handlers"); // 加载回滚处理函数
const { withPermission } = require("../common/auth");
const { PERMISSION_LEVEL } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 路由处理函数
const handlers = {
  // 获取事务详情
  getTransaction: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const { txId } = data;
        if (!txId) {
          return error("缺少事务ID", CODES.BAD_REQUEST);
        }

        try {
          const result = await coordinator.getTransactionStatus(txId);
          return success(result);
        } catch (err) {
          return error(err.message, CODES.NOT_FOUND);
        }
      }
    );
  },

  // 查询事务列表
  queryTransactions: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const { businessType, status, businessId, limit = 20, skip = 0 } = data;
        const query = {};

        if (businessType) query.businessType = businessType;
        if (status) query.status = status;
        if (businessId) query.businessId = businessId;

        const result = await db
          .collection("transaction_logs")
          .where(query)
          .orderBy("createdAt", "desc")
          .skip(skip)
          .limit(limit)
          .get();

        const total = await db
          .collection("transaction_logs")
          .where(query)
          .count();

        return success({
          transactions: result.data,
          total: total.total,
          limit,
          skip,
        });
      }
    );
  },

  // 手动提交事务
  commitTransaction: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const { txId } = data;
        if (!txId) {
          return error("缺少事务ID", CODES.BAD_REQUEST);
        }

        try {
          await coordinator.commitTransaction(txId);
          return success({ message: "事务已提交" });
        } catch (err) {
          return error(err.message, CODES.INTERNAL_ERROR);
        }
      }
    );
  },

  // 手动回滚事务
  rollbackTransaction: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        const { txId, reason } = data;
        if (!txId) {
          return error("缺少事务ID", CODES.BAD_REQUEST);
        }

        try {
          await coordinator.rollbackTransaction(
            txId,
            reason || "管理员手动回滚"
          );
          return success({ message: "事务已回滚" });
        } catch (err) {
          return error(err.message, CODES.INTERNAL_ERROR);
        }
      }
    );
  },

  // 获取事务统计信息
  getTransactionStats: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.ADMIN_ONLY,
      context,
      async () => {
        // 统计各状态的事务数量
        const statusCounts = {};
        for (const status of Object.values(TX_STATUS)) {
          const count = await db
            .collection("transaction_logs")
            .where({ status })
            .count();
          statusCounts[status] = count.total;
        }

        // 统计最近24小时的事务数量
        const last24Hours = Date.now() - 24 * 60 * 60 * 1000;
        const recent = await db
          .collection("transaction_logs")
          .where({ createdAt: db.command.gte(last24Hours) })
          .count();

        // 统计各业务类型的事务数量
        const businessTypes = await db
          .collection("transaction_logs")
          .aggregate()
          .group({
            _id: "$businessType",
            count: db.command.aggregate.sum(1),
          })
          .end();

        return success({
          statusCounts,
          recentCount: recent.total,
          businessTypes: businessTypes.list.map((item) => ({
            type: item._id,
            count: item.count,
          })),
        });
      }
    );
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 构建上下文
    const actionContext = {
      openid,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};
