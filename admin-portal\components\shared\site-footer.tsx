import Link from "next/link";

export function SiteFooter() {
  return (
    <footer className="border-t py-8 md:py-12">
      <div className="container grid gap-8 md:grid-cols-2 lg:grid-cols-4">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">关于我们</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link href="#" className="hover:text-primary">
                平台简介
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-primary">
                联系我们
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-primary">
                加入我们
              </Link>
            </li>
          </ul>
        </div>
        <div className="space-y-4">
          <h3 className="text-lg font-medium">帮助中心</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link href="#" className="hover:text-primary">
                咨询指南
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-primary">
                常见问题
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-primary">
                用户协议
              </Link>
            </li>
          </ul>
        </div>
        <div className="space-y-4">
          <h3 className="text-lg font-medium">业务合作</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link href="#" className="hover:text-primary">
                机构合作
              </Link>
            </li>
            <li>
              <Link href="#" className="hover:text-primary">
                商务合作
              </Link>
            </li>
          </ul>
        </div>
        <div className="space-y-4">
          <h3 className="text-lg font-medium">联系我们</h3>
          <div className="text-sm text-muted-foreground">
            <p>客服邮箱：<EMAIL></p>
            <p>服务时间：周一至周日 9:00-18:00</p>
            <div className="flex items-center gap-4 mt-4">
              <div className="bg-muted rounded-md p-2 w-24 h-24 flex items-center justify-center text-xs text-muted-foreground text-center">
                微信公众号
              </div>
              <div className="bg-muted rounded-md p-2 w-24 h-24 flex items-center justify-center text-xs text-muted-foreground text-center">
                小程序码
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container mt-8 border-t pt-8">
        <p className="text-center text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} 心理咨询平台 保留所有权利
        </p>
      </div>
    </footer>
  );
}
