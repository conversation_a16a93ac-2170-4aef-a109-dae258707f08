import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import DetailInfoEditer from '@components/therapist/DetailInfoEditer';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

export default function DetailInfoUpdater() {
  const extend = useTherapistUpdaterStore((state) => state.formData.extend);
  const updateFormField = useTherapistUpdaterStore(
    (state) => state.updateFormField
  );
  const loading = useTherapistUpdaterStore((state) => state.loading);
  const error = useTherapistUpdaterStore((state) => state.error);

  useEffect(() => {
    console.log('extend', extend);
    const loadMyExtendInfo = async () => {
      await therapistProfileActions.loadMyExtendInfo();
      const myExtendInfo = useTherapistProfileStore.getState().myExtendInfo;
      if (myExtendInfo) {
        useTherapistUpdaterStore.getState().setFormData({
          ...useTherapistUpdaterStore.getState().formData,
          extend: myExtendInfo,
        });
      }
    };
    loadMyExtendInfo();
    return () => {
      useTherapistUpdaterStore.setState({
        loading: false,
        error: null,
      });
    };
  }, []);

  // 表单验证
  const validateForm = (): boolean => {
    if (!extend.introduction) {
      Taro.showToast({ title: '请输入个人简介', icon: 'none' });
      return false;
    }

    if (extend.photos?.length === 0) {
      Taro.showToast({ title: '请上传至少一张照片', icon: 'none' });
      return false;
    }

    return true;
  };

  // 处理下一步按钮点击
  const handleSubmit = async () => {
    if (validateForm()) {
      console.log('submit', extend);
      await therapistProfileActions.updateMyExtendInfo(extend);
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '详细信息编辑',
        showBackButton: true,
        showSearch: false,
      }}
      error={error}
    >
      {/* <Text className='text-lg font-bold mb-4 block'>基本信息</Text> */}

      <DetailInfoEditer
        detailInfo={extend}
        onFormChange={(field, value) => updateFormField('extend', field, value)}
      />
      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='primary'
          block
          round
          loading={loading}
          disabled={loading}
          className='flex-1 ml-2'
          onClick={handleSubmit}
        >
          提交
        </Button>
      </View>
    </PageLol>
  );
}
