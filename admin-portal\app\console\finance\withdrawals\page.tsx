"use client";

import { useWithdrawalList } from "@/app/hooks/useWithdrawalList";
import { WithdrawRecord } from "@/app/model/payment.interface";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DotLoading } from "@/components/ui/dot-loading";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { useState } from "react";

// 提现状态对应的样式
const statusStyles = {
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800",
  completed: "bg-green-100 text-green-800",
  failed: "bg-red-100 text-red-800",
};

// 格式化时间戳为可读日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return "-";
  return format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss");
};

// 将金额分转为元
const formatAmount = (amount: number) => {
  return (amount / 100).toFixed(2);
};

// 时间范围选项
const TIME_RANGES = {
  "7d": { label: "最近7天", days: 7 },
  "30d": { label: "最近30天", days: 30 },
  "90d": { label: "最近90天", days: 90 },
  all: { label: "所有时间", days: 0 },
};

// 获取提现状态显示
const getWithdrawalStatus = (withdrawal: WithdrawRecord) => {
  switch (withdrawal.status) {
    case "pending":
      return { status: "待处理", style: statusStyles.pending };
    case "processing":
      return { status: "处理中", style: statusStyles.processing };
    case "completed":
      return { status: "已完成", style: statusStyles.completed };
    case "failed":
      return { status: "已失败", style: statusStyles.failed };
    default:
      return { status: "未知", style: "" };
  }
};

// 确认对话框组件
interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  description: React.ReactNode;
  onConfirm: () => Promise<void>;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

function ConfirmDialog({
  isOpen,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = "确认",
  cancelText = "取消",
}: ConfirmDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onConfirm();
      onCancel();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {cancelText}
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? <DotLoading size={8} /> : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 错误对话框组件
interface ErrorDialogProps {
  isOpen: boolean;
  title: string;
  errorMessage: string;
  onClose: () => void;
}

function ErrorDialog({
  isOpen,
  title,
  errorMessage,
  onClose,
}: ErrorDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription className="text-red-500 mt-2">
            {errorMessage}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onClose}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function WithdrawalsPage() {
  const [searchInput, setSearchInput] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [timeRange, setTimeRange] = useState("7d");

  const {
    withdrawals,
    pagination,
    loading,
    refreshWithdrawals,
    loadMoreWithdrawals,
    onStatusChange,
    onSearch,
    onDateRangeChange,
    completeWithdrawal,
  } = useWithdrawalList();

  // 对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: React.ReactNode;
    onConfirm: () => Promise<void>;
  }>({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: async () => {},
  });
  const [errorDialog, setErrorDialog] = useState<{
    isOpen: boolean;
    title: string;
    errorMessage: string;
  }>({
    isOpen: false,
    title: "操作失败",
    errorMessage: "",
  });

  // 处理搜索
  const handleSearch = () => {
    onSearch(searchInput);
  };

  // 处理搜索框按回车
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // 处理状态过滤变化
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    onStatusChange(value === "all" ? "" : value);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);

    if (value === "all") {
      onDateRangeChange(undefined);
    } else {
      const days = TIME_RANGES[value as keyof typeof TIME_RANGES].days;
      const endDate = Date.now();
      const startDate = endDate - days * 24 * 60 * 60 * 1000;
      onDateRangeChange([startDate, endDate]);
    }
  };

  // 处理审批提现
  const handleApproveWithdrawal = (withdrawalId: string) => {
    setConfirmDialog({
      isOpen: true,
      title: "确认",
      description: `确定已完成该提现操作吗？`,
      onConfirm: async () => {
        try {
          await completeWithdrawal(withdrawalId);
        } catch (error: unknown) {
          setErrorDialog({
            isOpen: true,
            title: "审批提现失败",
            errorMessage:
              error instanceof Error ? error.message : "操作失败，请稍后重试",
          });
        }
      },
    });
  };

  const closeConfirmDialog = () => {
    setConfirmDialog({ ...confirmDialog, isOpen: false });
  };
  const closeErrorDialog = () => {
    setErrorDialog({ ...errorDialog, isOpen: false });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">提现管理</h1>
        <p className="text-muted-foreground">审核和管理提现申请</p>
      </div>

      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Input
            placeholder="搜索用户ID/姓名..."
            className="max-w-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <div className="flex flex-wrap gap-2">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="提现状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="pending">待处理</SelectItem>
                <SelectItem value="processing">处理中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="failed">已失败</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
                <SelectItem value="all">所有时间</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>搜索</Button>
            <Button variant="outline" onClick={refreshWithdrawals}>
              刷新
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <DotLoading size={8} color="#1e40af" className="mx-auto" />
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>申请ID</TableHead>
                    <TableHead>用户ID</TableHead>
                    <TableHead>姓名</TableHead>
                    <TableHead>提现方式</TableHead>
                    <TableHead>账户信息</TableHead>
                    <TableHead className="text-right">金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>申请时间</TableHead>
                    <TableHead>完成时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {withdrawals && withdrawals.length > 0 ? (
                    withdrawals.map((withdrawal) => (
                      <TableRow key={withdrawal.id}>
                        <TableCell className="font-medium">
                          {withdrawal.id}
                        </TableCell>
                        <TableCell>{withdrawal.userId}</TableCell>
                        <TableCell>{withdrawal.name}</TableCell>
                        <TableCell>
                          {withdrawal.bankName ? "银行卡" : "微信"}
                        </TableCell>
                        <TableCell>
                          {withdrawal.bankAccount || "微信提现"}
                        </TableCell>
                        <TableCell className="text-right">
                          ¥{formatAmount(withdrawal.amount)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={getWithdrawalStatus(withdrawal).style}
                          >
                            {getWithdrawalStatus(withdrawal).status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatDate(withdrawal.createdAt)}
                        </TableCell>
                        <TableCell>
                          {withdrawal.completedAt
                            ? formatDate(withdrawal.completedAt)
                            : "-"}
                        </TableCell>
                        <TableCell>
                          {withdrawal.status === "pending" ? (
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() =>
                                  handleApproveWithdrawal(withdrawal.id)
                                }
                              >
                                已完成处理
                              </Button>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">
                              {withdrawal.remark || "-"}
                            </span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={10} className="h-24 text-center">
                        暂无提现申请记录
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                共 {pagination?.total} 条记录，第 {pagination?.page}/
                {pagination?.totalPages || 1} 页
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasPrev}
                  onClick={() => loadMoreWithdrawals(pagination?.page - 1)}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasNext}
                  onClick={() => loadMoreWithdrawals(pagination?.page + 1)}
                >
                  下一页
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>

      {/* 确认和错误对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
        onCancel={closeConfirmDialog}
      />
      <ErrorDialog
        isOpen={errorDialog.isOpen}
        title={errorDialog.title}
        errorMessage={errorDialog.errorMessage}
        onClose={closeErrorDialog}
      />
    </div>
  );
}
