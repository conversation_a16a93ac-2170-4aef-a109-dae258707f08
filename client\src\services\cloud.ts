import { CloudFunctionResult } from '@core/api';
import Taro from '@tarojs/taro';

/**
 * 调用云函数的接口
 * @param name 云函数名称
 * @param action 云函数action
 * @param params 传递给云函数的参数
 * @returns 云函数返回的结果
 */
export const callCloudFunction = async (
  name: string,
  action: string,
  params: any = {}
): Promise<CloudFunctionResult> => {
  try {
    const res = await Taro.cloud.callFunction({
      name,
      data: {
        action,
        params,
      },
    });

    return res.result as CloudFunctionResult;
  } catch (error) {
    console.error(`调用云函数 ${name} 失败:`, error);
    throw error;
  }
};

/**
 * 初始化云环境
 * @param env 云环境ID
 */
export const initCloud = (cloudbase: string) => {
  console.log('初始化云环境', cloudbase);
  try {
    if (!Taro.cloud) {
      console.error('当前环境不支持云能力');
      return false;
    }

    Taro.cloud.init({
      env: cloudbase,
      traceUser: true,
    });

    return true;
  } catch (error) {
    console.error('初始化云环境失败:', error);
    return false;
  }
};

/**
 * 上传文件到云存储
 * @param filePath 本地文件路径
 * @param cloudPath 云存储路径，不包含文件名
 * @returns 上传后的文件ID
 */
export const uploadFile = async (
  filePath: string,
  cloudPath: string
): Promise<string> => {
  try {
    const res = await Taro.cloud.uploadFile({
      cloudPath,
      filePath,
    });

    return res.fileID;
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
};

/**
 * 获取云存储文件的临时链接
 * @param fileID 文件ID
 * @returns 临时链接
 */
export const getTempFileURL = async (fileID: string): Promise<string> => {
  try {
    const res = await Taro.cloud.getTempFileURL({
      fileList: [fileID],
    });

    return res.fileList[0].tempFileURL;
  } catch (error) {
    console.error('获取临时链接失败:', error);
    throw error;
  }
};
