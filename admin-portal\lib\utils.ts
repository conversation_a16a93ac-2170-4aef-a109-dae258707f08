import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// utils/imageUtils.js
export function convertCloudImageUrl(cloudUrl: string, styleName?: string) {
  if (!cloudUrl.startsWith("cloud://")) {
    return cloudUrl; // 如果不是云存储URL，直接返回
  }

  const strs = cloudUrl.split(".");
  const strs_2 = strs[1];
  const strs_3 = strs_2.split("/");
  const envId = strs_3[0];
  const https_prefix = `https://${envId}.tcb.qcloud.la/`;
  let filePath = "";
  for (let i = 1; i < strs_3.length; i++) {
    if (i === strs_3.length - 1) {
      const fileName = strs_3[i] + "." + strs[strs.length - 1];
      filePath += fileName;
    } else {
      filePath += strs_3[i] + "/";
    }
  }
  let httpsUrl = https_prefix + filePath;
  if (styleName) httpsUrl += "/" + styleName;
  return httpsUrl;
}
