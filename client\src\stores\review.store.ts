import { Order_review } from '@model/order.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface ReviewState {
  reviews: Order_review[];
}

interface ReviewAction {
  setReviews: (reviews: Order_review[]) => void;
}

const initialState: ReviewState = {
  reviews: [],
};

export const reviewStore = create<ReviewState & ReviewAction>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setReviews: (reviews) => set({ reviews }),
      }),
      {
        name: StorageSceneKey.ORDER_REVIEWS,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 用户列表
        partialize: (state) => ({
          reviews: state.reviews,
        }),
      }
    )
  )
);

export const useReviewStore = createSelectors(reviewStore);
