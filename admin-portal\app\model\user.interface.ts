/**
 * 用户信息
 * 云数据库中每张表自动填充了_id, _openid, _createTime, _updateTime
 * 为兼容，仍在数据表中增加id字段（与_openid相同）
 */

export interface user_public {
  /** 用户ID */
  id: string;
  /** 用户名 */
  userName: string;
  /** 头像 */
  avatar: string;
  /** 角色 */
  role: USER_ROLE[];
  /** 状态 */
  status: 'active' | 'inactive' | 'deleted' | 'banned';

  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

/**
 * 用户敏感信息
 * 仅通过云函数访问，不在客户端直接存储
 */
export interface user_sensitive {
  /** 用户ID */
  id: string;
  /** 电话 */
  phone: string;
  /** 邮箱 */
  email: string;
  /** 真实姓名 (可能与显示名不同) */
  realName?: string;
  /** 身份认证信息 */
  identityVerification?: {
    /** 证件类型 */
    type: 'idcard' | 'passport' | 'other';
    /** 证件号码 (已脱敏) */
    number: string;
    /** 验证状态 */
    verified: boolean;
    /** 验证时间 */
    verifiedAt?: number;
  };
  /** 银行账户信息 */
  bankAccount?: {
    /** 银行名称 */
    bank: string;
    /** 账户名 */
    accountName: string;
    /** 账号 (已脱敏) */
    accountNumber: string;
  };
  /** 紧急联系人 */
  emergencyContact?: {
    /** 联系人姓名 */
    name: string;
    /** 关系 */
    relationship: string;
    /** 电话 */
    phone: string;
  };
}

export interface user_favorite {
  /** 用户ID */
  id: string;
  /** 咨询师ID */
  therapistId: string;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

export enum USER_ROLE {
  /** 用户 */
  USER = 'user',
  /** 咨询师 */
  THERAPIST = 'therapist',
  /** 管理员 */
  ADMIN = 'admin',
  /** 游客 */
  GUEST = 'guest',
}
