const fs = require("fs");
const path = require("path");
const { v4: uuidv4 } = require("uuid");

// 给定用户ID
const USER_ID = "oLFkE7lzKUPbOcFPLcDo4M_GSjyU";

// 退款状态常量
const REFUND_STATUS = {
  SUCCESS: "SUCCESS",
  FAIL: "FAIL",
  PROCESSING: "PROCESSING",
};

// 退款来源常量
const INITATOR_SOURCE = {
  USER: "user",
  SYSTEM: "system",
  ADMIN: "admin",
};

// 生成提现记录ID
function generateWithdrawId() {
  return `WD${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * 生成支付记录
 * @param {number} count 生成数量
 * @returns {Array} 支付记录数组
 */
function generatePaymentRecords(count) {
  const records = [];
  const now = Date.now();

  for (let i = 0; i < count; i++) {
    const timestamp = now - i * 3600000; // 每小时一条记录
    const amount = Math.floor(Math.random() * 10000) + 100; // 100-10000分 (1-100元)
    const statuses = ["pending", "paid", "refunded", "failed"];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    const record = {
      orderId: `order_${timestamp}_${i}`,
      userId: USER_ID,
      amount: amount,
      description: `支付订单 #${i + 1}`,
      status: status,
      paymentMethod: ["wechat", "alipay", "bank"][
        Math.floor(Math.random() * 3)
      ],
      outTradeNo: `trade_${timestamp}_${i}`,
      transactionId: `trans_${timestamp}_${i}`,
      createdAt: timestamp,
      updatedAt: timestamp,
      _openid: USER_ID,
    };

    records.push(record);
  }

  return records;
}

/**
 * 生成退款记录
 * @param {Array} payments 支付记录数组
 * @param {number} count 生成数量
 * @returns {Array} 退款记录数组
 */
function generateRefundRecords(payments, count) {
  const records = [];
  const now = Date.now();
  const refundablePayments = payments.filter(
    (p) => p.status === "paid" || p.status === "refunded"
  );

  if (refundablePayments.length === 0) return records;

  for (let i = 0; i < Math.min(count, refundablePayments.length); i++) {
    const payment = refundablePayments[i];
    const timestamp = now - i * 3600000; // 每小时一条记录
    const refundStatuses = Object.values(REFUND_STATUS);
    const status =
      refundStatuses[Math.floor(Math.random() * refundStatuses.length)];

    const record = {
      orderId: payment.orderId,
      source: Object.values(INITATOR_SOURCE)[Math.floor(Math.random() * 3)],
      status: status,
      outRefundNo: `refund_${timestamp}_${i}`,
      outTradeNo: payment.outTradeNo,
      refundFee: Math.floor(payment.amount * 0.8), // 退80%
      totalFee: payment.amount,
      createdAt: timestamp,
      updatedAt: timestamp,
    };

    if (status === REFUND_STATUS.FAIL) {
      record.errDesc = `退款失败 #${i + 1}`;
    }

    records.push(record);
  }

  return records;
}

/**
 * 生成提现记录
 * @param {number} count 生成数量
 * @returns {Array} 提现记录数组
 */
function generateWithdrawRecords(count) {
  const records = [];
  const now = Date.now();
  const bankNames = [
    "中国银行",
    "工商银行",
    "建设银行",
    "农业银行",
    "招商银行",
  ];
  const names = ["张三", "李四", "王五", "赵六", "钱七"];

  for (let i = 0; i < count; i++) {
    const timestamp = now - i * ********; // 每天一条记录
    const amount = Math.floor(Math.random() * 50000) + 10000; // 100-500元
    const statuses = ["pending", "processing", "completed", "failed"];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    const id = generateWithdrawId();
    const record = {
      _id: id,
      id: id,
      userId: USER_ID,
      amount: amount,
      status: status,
      bankAccount: `622588******${Math.floor(1000 + Math.random() * 9000)}`,
      bankName: bankNames[Math.floor(Math.random() * bankNames.length)],
      name: names[Math.floor(Math.random() * names.length)],
      createdAt: timestamp,
      updatedAt: timestamp,
      remark: `提现申请 #${i + 1}`,
    };

    if (status === "completed") {
      record.completedAt = timestamp + 3600000; // 1小时后完成
    }

    records.push(record);
  }

  return records;
}

/**
 * 保存数据到文件（符合微信云数据库导入格式）
 * @param {Array} data 数据数组
 * @param {string} collectionName 集合名称
 */
function saveDataForWxCloud(data, collectionName) {
  const dirPath = path.join(__dirname, "wxCloudData");

  // 创建目录（如果不存在）
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath);
  }

  const filePath = path.join(dirPath, `${collectionName}.json`);

  // 微信云数据库格式：每行一个JSON对象，没有逗号分隔
  const content = data.map((item) => JSON.stringify(item)).join("\n");

  fs.writeFileSync(filePath, content);
  console.log(
    `已生成 ${data.length} 条${collectionName}记录，保存到: ${filePath}`
  );
}

// 主函数
function main() {
  try {
    console.log("开始生成模拟数据...");
    console.log(`用户ID: ${USER_ID}`);

    // 生成数据
    const payments = generatePaymentRecords(20);
    const refunds = generateRefundRecords(payments, 15);
    const withdraws = generateWithdrawRecords(10);

    // 保存数据
    saveDataForWxCloud(payments, "payment");
    saveDataForWxCloud(refunds, "refund");
    saveDataForWxCloud(withdraws, "withdraw");

    console.log("数据生成完成！");
    console.log("请查看 wxCloudData 目录下的文件");
  } catch (error) {
    console.error("生成数据时出错:", error);
  }
}

// 执行主函数
main();
