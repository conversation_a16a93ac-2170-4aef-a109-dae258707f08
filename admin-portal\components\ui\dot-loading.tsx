"use client";

import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface DotLoadingProps {
  color?: string;
  size?: number;
  className?: string;
}

export function DotLoading({
  color = "#646566",
  size = 6,
  className = "",
}: DotLoadingProps) {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % 3);
    }, 300);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className="transition-all duration-300"
          style={{
            backgroundColor: color,
            width: `${size}px`,
            height: `${size}px`,
            borderRadius: "50%",
            opacity: activeIndex === index ? 1 : 0.4,
            transform: activeIndex === index ? `scale(1.2)` : "scale(1)",
          }}
        />
      ))}
    </div>
  );
}
