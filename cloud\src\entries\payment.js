// 云函数入口文件
const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS, ORDER_STATUS } = require("../common/db.constants");
const { genNonceStr, genOutTradeNo } = require("../common/utils");
const { onPaid } = require("../service/orderOperation");
const { SUB_MCH_ID, ENV_ID } = require("../common/config");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 路由处理函数
const handlers = {
  createPayment: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await createPayment(data, context.openid);
        return success(result);
      }
    );
  },

  validatePaymentSuccess: async (data, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await validatePaymentSuccess(
          data.orderId,
          data.outTradeNo,
          context.openid
        );
        return success(result);
      }
    );
  },
  testValidatePaymentSuccess: async (data, context) => {
    return await withPermission(PERMISSION_LEVEL.PUBLIC, context, async () => {
      const result = await testValidatePaymentSuccess(
        data.orderId,
        data.outTradeNo,
        context.openid
      );
      return success(result);
    });
  },
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = userInfo?.role || USER_ROLE.GUEST;
    const userId = userInfo?._id;

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, CODES.INTERNAL_ERROR, err.stack);
  }
};

/**
 * 创建支付
 * @param {Object} paymentData 支付数据
 * @param {string} openid 用户openid
 */
async function createPayment(paymentData, openid) {
  try {
    const { orderId, amount, description } = paymentData;

    // 验证订单是否存在
    const orderResult = await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .get();

    console.log("orderResult", orderResult);
    if (!orderResult.data || orderResult.data.length === 0) {
      throw new Error("订单不存在-server");
    }

    const order = orderResult.data;

    // 验证是否是用户自己的订单
    if (order.userId !== openid) {
      throw new Error("无权支付该订单");
    }

    // 验证订单状态
    if (order.status !== ORDER_STATUS.PENDING_PAYMENT) {
      throw new Error("订单状态不允许支付");
    }

    // 验证支付金额
    if (amount !== order.price) {
      throw new Error("支付金额与订单金额不符");
    }

    // 验证是否已经支付
    const paymentResults = await db
      .collection(COLLECTIONS.PAYMENT)
      .where({ orderId, status: "paid" })
      .get();
    if (paymentResults.data && paymentResults.data.length > 0) {
      throw new Error("订单已支付，请勿重复支付");
    }

    const outTradeNo = genOutTradeNo();
    // 调用微信支付API
    const paymentResult = await cloud.cloudPay.unifiedOrder({
      body: description || "心理咨询服务",
      outTradeNo,
      spbillCreateIp: "127.0.0.1",
      subMchId: SUB_MCH_ID,
      totalFee: Math.round(amount * 100), // 微信支付以分为单位
      envId: ENV_ID,
      functionName: "paymentcallback",
      tradeType: "JSAPI",
      openid: openid,
    });

    console.log("paymentResult", paymentResult);

    if (
      paymentResult.returnCode !== "SUCCESS" ||
      paymentResult.resultCode !== "SUCCESS"
    ) {
      throw new Error(paymentResult.errCodeDes || "创建支付失败");
    }

    // 记录支付信息
    const payment = {
      orderId,
      userId: openid,
      amount,
      description: description || "心理咨询服务",
      status: "pending",
      paymentMethod: "wechat",
      outTradeNo,
      createdAt: Date.now(),
      _openid: openid,
    };

    await db.collection(COLLECTIONS.PAYMENT).add({
      data: payment,
    });

    return {
      paymentData: paymentResult.payment,
    };
  } catch (error) {
    console.error("创建支付失败:", error);
    throw error;
  }
}

/**
 * 验证支付是否成功
 * @param {string} orderId 订单ID
 * @param {string} openid 用户openid
 */
async function validatePaymentSuccess(orderId, outTradeNo, openid) {
  try {
    // 验证订单是否存在
    const orderResult = await db
      .collection(COLLECTIONS.ORDER)
      .where({ _id: orderId })
      .get();

    if (!orderResult.data || orderResult.data.length === 0) {
      console.error("订单不存在", orderId);
      throw new Error("订单不存在");
    }

    const order = orderResult.data[0];

    // 验证是否是用户自己的订单
    if (order.userId !== openid) {
      console.error("无权查询该订单", orderId, openid);
      throw new Error("无权查询该订单");
    }

    // 查询支付记录
    const paymentResult = await db
      .collection(COLLECTIONS.PAYMENT)
      .where({ orderId, outTradeNo })
      .orderBy("createdAt", "desc")
      .limit(1)
      .get();

    if (!paymentResult.data || paymentResult.data.length === 0) {
      console.error("支付记录不存在", orderId, outTradeNo);
      throw new Error("支付记录不存在");
    }

    const payment = paymentResult.data[0];
    // 已经通过支付回调处理了订单状态
    if (payment.status === "paid") {
      console.log("支付记录已支付", orderId, outTradeNo);
      return {
        orderId,
        status: "paid",
      };
    }

    // 如果支付记录状态不是已支付，则主动查询订单，再次确认
    const queryResult = await cloud.cloudPay.queryOrder({
      subMchId: SUB_MCH_ID,
      outTradeNo: outTradeNo,
      nonceStr: genNonceStr(),
    });
    console.log("主动查询订单结果:", queryResult);

    if (queryResult.returnCode !== "SUCCESS") {
      console.error("主动查询订单失败", orderId, outTradeNo);
      throw new Error(queryResult.returnMsg || "主动查询订单失败");
    }

    if (queryResult.resultCode !== "SUCCESS") {
      console.error("主动查询订单失败", orderId, outTradeNo);
      throw new Error(queryResult.errCodeDes || "主动查询订单失败");
    }

    if (queryResult.tradeState !== "SUCCESS") {
      console.warn("主动查询,订单支付失败", orderId, outTradeNo, queryResult);
      throw new Error(queryResult.errCodeDes || "主动查询订单失败");
    }

    await onPaid(orderId, outTradeNo, openid);
    return {
      orderId,
      status: "paid",
    };
  } catch (error) {
    console.error("查询支付状态失败:", error);
    throw error;
  }
}

async function testValidatePaymentSuccess(orderId, outTradeNo, openid) {
  // 如果支付记录状态不是已支付，则主动查询订单，再次确认
  const queryResult = await cloud.cloudPay.queryOrder({
    subMchId: SUB_MCH_ID,
    outTradeNo: outTradeNo,
    nonceStr: genNonceStr(),
  });
  console.log("主动查询订单结果:", queryResult);
}
