import { TestCategoryTab } from '@model/measure.model';
import { ScrollView, View } from '@tarojs/components';
import { useCallback } from 'react';

interface MeasureCategoryTagsProps {
  categories: TestCategoryTab[];
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

export default function TestCategoryTags({
  categories,
  activeCategory,
  onCategoryChange,
}: MeasureCategoryTagsProps) {
  const handleCategoryChange = useCallback(
    (categoryId: string) => {
      onCategoryChange(categoryId);
    },
    [onCategoryChange]
  );

  return (
    <ScrollView
      scrollX
      enhanced
      showScrollbar={false}
      className='whitespace-nowrap'
    >
      <View className='inline-flex gap-2'>
        {categories.map((category) => (
          <View
            key={category.category}
            className={`px-2 py-1 rounded-full text-sm font-bold ${
              activeCategory === category.category
                ? 'bg-primary text-white border-primary'
                : 'bg-gray-100 text-secondary'
            }`}
            onClick={() => handleCategoryChange(category.category)}
          >
            {category.title}
          </View>
        ))}
      </View>
    </ScrollView>
  );
}
