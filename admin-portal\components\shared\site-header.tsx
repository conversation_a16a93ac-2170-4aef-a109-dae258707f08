import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface NavItemProps {
  href: string;
  text: string;
  isActive?: boolean;
}

function NavItem({ href, text, isActive = false }: NavItemProps) {
  return (
    <Link
      href={href}
      className={cn(
        "px-4 py-2 text-sm font-medium transition-colors hover:text-primary",
        isActive ? "text-primary" : "text-muted-foreground"
      )}
    >
      {text}
    </Link>
  );
}

export function SiteHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="font-bold text-xl">
            心理咨询平台
          </Link>
          <nav className="hidden md:flex items-center gap-2">
            <NavItem href="/" text="首页" isActive />
            <NavItem href="/therapists" text="咨询师" />
            <NavItem href="/assessments" text="心理测量" />
            <NavItem href="#" text="成为咨询师" />
          </nav>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/login" passHref>
            <Button variant="outline">登录</Button>
          </Link>
          <Link href="/console" passHref>
            <Button variant="default">管理后台</Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
