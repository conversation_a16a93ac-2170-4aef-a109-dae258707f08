import { ServiceType } from './service.interface';

/**
 * 钱包信息,咨询师和用户共享数据结构
 */
export interface Wallet {
  /** 用户ID */
  id: string;
  /** 当前可提现金额（单位：分） */
  balance: number;
  /** 历史总收入（含已提现） */
  totalIncome: number;
  /** 冻结中金额（提现审核期锁定） */
  frozenAmount: number;
  /** 上次结算时间 */
  lastSettlement: number;
  /** 提现密码（加密存储） */
  withdrawPassword: string;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

/**
 * 用户/咨询师统计摘要
 * 与钱包分离，独立更新
 */
export interface StatSummary {
  /** 关联用户ID */
  id: string;

  /** 订单统计 */
  orders: {
    completed: number; // 已完成订单
    refunded: number; // 已退款订单
  };
  /** 分销订单统计 */
  distribution: {
    total: number; //分销订单数
    refunded: number; //分销退款订单数
  };
  /** 收入统计 */
  income: {
    total: number; // 总收入（分）completed + distribution - refunded - refundedDistribution
    completed: number; // 已完成订单收入,不含已退款订单金额
    refunded: number; // 已退款订单金额
    distribution: number; // 分销收入，不含分销退款订单金额
    refundedDistribution: number; // 分销退款订单金额
  };

  /** 更新时间戳 */
  lastUpdated: number;
}

/**
 * 收入明细,咨询师和用户共享数据结构
 */
export interface IncomeDetail {
  /** 咨询师ID */
  id: string;

  /** 关联咨询订单ID/分销订单ID */
  orderId: string;

  /** 用户头像 冗余存储*/
  userAvatar?: string;
  /** 用户昵称 冗余存储*/
  userName?: string;
  /** 用户ID 冗余存储*/
  userId: string;

  /** 收入类型 1:咨询 2:分销 3:退款 */
  incomeType: 1 | 2 | 3;

  /** 咨询订单业务类型 0:视频咨询 3:面对面咨询  */
  serviceType: ServiceType;

  /** 入账金额（单位：元） */
  amount: number;
  /** 状态：settled(已入账)/frozen(冻结中)/refunded(已退款) */
  status: 'settled' | 'frozen' | 'refunded';
  /** 创建时间 */
  createdAt: number; // unix时间戳毫秒
  /** 更新时间 */
  updatedAt: number; // unix时间戳毫秒
}

/** 咨询师月度收入统计 */
export interface TherapistMonthlyStat {
  id: string; // 格式: therapistId_year_month (如: therapist_123_2023_08)
  therapistId: string;

  /** 收入统计 */
  income: {
    total: number; // 总收入（分）completed + distribution
    increase: number; // 收入增长 12.5
    completed: number; // 已完成订单收入,不含已退款订单金额
    refunded: number; // 已退款订单金额
    distribution: number; // 分销收入，不含分销退款订单金额
    refundedDistribution: number; // 分销退款订单金额
  };

  /** 咨询订单统计 */
  orders: {
    total: number; // 总订单数
    completed: number; // 已完成订单数
    refunded: number; // 退款订单数
    cancelled: number; // 取消订单数
  };

  /** 分销订单统计 */
  distribution: {
    total: number; //分销订单数
    refunded: number; //分销退款订单数
  };

  /** 咨询订单服务类型统计 */
  serviceTypes: {
    [serviceType in ServiceType]: { count: number; amount: number }; //0 视频咨询 3 面对面咨询
  };

  updateTime: number; // 最后更新时间 unix时间戳毫秒
  startDate: number; // 统计周期开始日期 unix时间戳毫秒 2025-07-01 00:00:00
  endDate: number; // 统计周期结束日期 unix时间戳毫秒
}
