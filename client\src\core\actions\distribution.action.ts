import { distributionService } from '@services/distribution.service';
import { useDistributionStore } from '@stores/distribution.store';
import Taro from '@tarojs/taro';

/**
 * 分销业务逻辑协调层
 */
export const distributionActions = {
  /**
   * 获取分销概览数据
   */
  async fetchDistributionOverview() {
    try {
      useDistributionStore.getState().setLoading(true);
      useDistributionStore.getState().setError(null);

      const response = await distributionService.getDistributionOverview();
      useDistributionStore.getState().setOverview(response);

      return response;
    } catch (error) {
      useDistributionStore.getState().setError(error as Error);
      console.error('Failed to fetch distribution overview:', error);
      return null;
    } finally {
      useDistributionStore.getState().setLoading(false);
    }
  },

  /**
   * 获取分销订单列表
   */
  async fetchDistributionOrders(
    params: {
      page?: number;
      pageSize?: number;
      status?: 'pending' | 'settled' | 'cancelled';
    } = {}
  ) {
    try {
      const state = useDistributionStore.getState();
      const { page = state.orders.page, pageSize = state.orders.pageSize } =
        params;

      state.orders.loading = true;
      state.orders.error = null;

      const response = await distributionService.getDistributionOrders({
        ...params,
        page,
        pageSize,
      });
      useDistributionStore
        .getState()
        .setOrders({ list: response.list, total: response.pagination.total });

      return response;
    } catch (error) {
      useDistributionStore.getState().orders.error = error as Error;
      console.error('Failed to fetch distribution orders:', error);
      return { list: [], total: 0 };
    } finally {
      useDistributionStore.getState().orders.loading = false;
    }
  },

  /**
   * 获取邀请用户列表
   */
  async fetchInvitedUsers(
    params: {
      page?: number;
      pageSize?: number;
      forceRefresh?: boolean;
    } = {}
  ) {
    try {
      const store = useDistributionStore.getState();
      if (store.invitedUsers.loading) {
        return;
      }
      useDistributionStore.setState((state) => ({
        invitedUsers: {
          ...state.invitedUsers,
          loading: true,
          error: null,
        },
      }));

      const { forceRefresh } = params;

      if (!forceRefresh) {
        // 如果已有数据且不强制刷新，则直接返回
        if (store.invitedUsers.list.length > 0) {
          return;
        }
      }

      const response = await distributionService.getInvitedUsers({
        page: params.page || 1,
        pageSize: params.pageSize || 10,
      });

      store.setInvitedUsersWithPagination({
        list: response.list,
        pagination: response.pagination,
      });
      return;
    } catch (error) {
      console.error('Failed to fetch invited users:', error);
      useDistributionStore.setState((state) => ({
        invitedUsers: {
          ...state.invitedUsers,
          error: error as Error,
        },
      }));
    } finally {
      useDistributionStore.setState((state) => ({
        invitedUsers: {
          ...state.invitedUsers,
          loading: false,
        },
      }));
    }
  },

  /**
   * 加载更多邀请用户
   */
  async loadMoreInvitedUsers(): Promise<void> {
    const store = useDistributionStore.getState();
    if (store.invitedUsers.loading) {
      return;
    }
    useDistributionStore.setState((state) => ({
      invitedUsers: {
        ...state.invitedUsers,
        loading: true,
      },
    }));

    // 没有更多数据了
    if (
      !store.invitedUsers.pagination ||
      !store.invitedUsers.pagination.hasNext
    )
      return;

    try {
      // 从服务获取数据
      const response = await distributionService.getInvitedUsers({
        page: store.invitedUsers.pagination.page + 1,
        pageSize: store.invitedUsers.pagination.pageSize,
      });

      const { list: currentList } = store.invitedUsers;

      // 将新数据添加到现有数据后面
      const combinedList = [...(currentList || []), ...response.list];

      // 更新状态
      store.setInvitedUsersWithPagination({
        list: combinedList,
        pagination: response.pagination,
      });
    } catch (error) {
      console.error('加载更多邀请用户失败:', error);
    } finally {
      useDistributionStore.setState((state) => ({
        invitedUsers: {
          ...state.invitedUsers,
          loading: false,
        },
      }));
    }
  },

  /**
   * 保存分销二维码到相册
   */
  async saveQrCodeToAlbum(qrCodeUrl: string) {
    try {
      await Taro.saveImageToPhotosAlbum({
        filePath: qrCodeUrl,
      });

      Taro.showToast({
        title: '保存成功',
        icon: 'success',
      });

      return true;
    } catch (error) {
      console.error('Failed to save QR code to album:', error);

      Taro.showToast({
        title: '保存失败',
        icon: 'none',
      });

      return false;
    }
  },

  /**
   * 重置状态
   */
  resetState() {
    useDistributionStore.getState().resetState();
  },
};
