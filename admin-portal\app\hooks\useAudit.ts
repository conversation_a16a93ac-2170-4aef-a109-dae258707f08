import { RefundAuditOrder } from "@/app/model/order.interface";
import { toast } from "@/components/ui/toast";
import { orderCloudFunctions } from "@/lib/cloud-functions";
import { useCallback, useEffect, useState } from "react";
import { Pagination, SUCCESS_CODE } from "../model/api";

/**
 * 订单列表管理Hook
 * 提供订单列表的获取、筛选和状态监听功能
 */
export function useAudit() {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<RefundAuditOrder[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 100,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // 统一请求函数
  const fetchOrders = useCallback(
    async (params: { page?: number; pageSize?: number }) => {
      console.log("useAudit fetchOrders", params);
      if (loading) return;
      setLoading(true);
      try {
        const res = await orderCloudFunctions.getRefundAuditOrders(params);
        setOrders(res.data as unknown as RefundAuditOrder[]);
        setPagination(res.pagination as unknown as Pagination);
      } catch (error) {
        console.error("获取订单失败:", error);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 刷新列表
  const refreshOrders = useCallback(async () => {
    console.log("useAudit refreshOrders");
    try {
      await fetchOrders({
        page: 1,
        pageSize: pagination?.pageSize || 100,
      });
    } catch (error) {
      console.error("刷新订单失败:", error);
    }
  }, [fetchOrders, pagination?.pageSize]);

  // 加载更多
  const loadMoreOrders = useCallback(
    async (page: number = pagination?.page + 1) => {
      console.log("useOrderList loadMoreOrders", page);

      try {
        await fetchOrders({
          page: page,
          pageSize: pagination?.pageSize || 100,
        });
      } catch (error) {
        console.error("加载更多订单失败:", error);
      }
    },
    [pagination?.page, pagination?.pageSize, fetchOrders]
  );

  // 通过退款
  const approveRefund = useCallback(
    async (orderId: string) => {
      console.log("useAudit approveRefund", orderId);
      try {
        const result = await orderCloudFunctions.approveRefund({
          orderId: orderId,
        });
        if (result.success && result.code === SUCCESS_CODE) {
          // 刷新订单列表
          await refreshOrders();

          // 返回成功结果
          toast.success("退款申请已通过");
          return result;
        } else {
          throw new Error((result.warning as string) || result.message);
        }
      } catch (error) {
        console.error("退款审核通过失败:", error);
        throw error; // 将错误向上传递，以便调用者处理
      }
    },
    [refreshOrders]
  );

  // 拒绝退款
  const rejectRefund = useCallback(
    async (orderId: string) => {
      console.log("useAudit rejectRefund", orderId);
      try {
        const result = await orderCloudFunctions.rejectRefund({
          orderId: orderId,
        });
        if (result.success && result.code === SUCCESS_CODE) {
          // 刷新订单列表
          await refreshOrders();

          // 返回成功结果
          toast.success("已拒绝退款申请");
          return result;
        } else {
          throw new Error((result.warning as string) || result.message);
        }
      } catch (error) {
        console.error("退款审核拒绝失败:", error);
        throw error; // 将错误向上传递，以便调用者处理
      }
    },
    [refreshOrders]
  );

  // 初始加载
  useEffect(() => {
    console.log("useAudit useEffect");
    refreshOrders();
  }, []); // 只在组件挂载时执行一次

  return {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    approveRefund,
    rejectRefund,
  };
}
