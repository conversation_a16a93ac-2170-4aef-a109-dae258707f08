.homepage-content {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部Tabbar留出空间
}

/* 自定义导航栏样式 */
.nav-title-with-logo {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 8px;

  .nav-logo {
    display: flex;
    align-items: center;
    width: 32px;
    height: 32px;
  }

  .nav-brand-title {
    font-size: 24px;
    font-weight: 600;
    color: #2775b6;
    margin-left: 8px;
    flex: 1;
  }
  /* 咨询师首页样式 */
  .therapist-home {
    background-color: #f5f5f5;
    min-height: 100vh;
  }

  .greeting-section {
    padding: 16px;
  }

  .card {
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .card-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stats-item {
    padding: 12px;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .stats-value {
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }

  .stats-label {
    font-size: 14px;
    color: #666;
  }

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .action-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
  }

  .action-label {
    font-size: 14px;
    color: #333;
  }

  /* 课程卡片样式 */
  .course-card {
    display: flex;
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .course-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
  }

  .course-info {
    flex: 1;
    padding: 12px;
  }

  .course-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .course-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
  }

  .course-duration {
    font-size: 13px;
    color: #666;
  }
}
