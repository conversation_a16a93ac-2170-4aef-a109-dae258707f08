import {
  my_extend,
  my_schedule,
  my_service,
  my_summary,
} from '@model/profile.therapist';

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

/**
 * 咨询师管理状态 (咨询师端视角)
 * 管理咨询师自己的信息、服务和排期
 */
interface State {
  // 基本信息
  myProfile: my_summary | null;
  // 详细信息
  myExtendInfo: my_extend | null;
  // 服务信息
  myServices: my_service | null;
  // 排期信息
  mySchedule: my_schedule | null;

  // 加载状态
  loading: boolean;
  // 错误信息
  error: string | null;
}

interface Action {
  // 设置数据
  setMyProfile: (profile: my_summary) => void;
  setMyExtendInfo: (extendInfo: my_extend) => void;
  setMyServices: (services: my_service) => void;
  setMySchedule: (schedule: my_schedule) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

/**
 * 咨询师管理状态 store
 */
const therapistProfileStore = create<State & Action>()(
  immer(
    persist(
      (set) => ({
        // 初始状态
        myProfile: null,
        myExtendInfo: null,
        myServices: null,
        mySchedule: null,
        loading: false,
        error: null,

        // 设置数据
        setMyProfile: (profile) => set({ myProfile: profile }),
        setMyExtendInfo: (extendInfo) => set({ myExtendInfo: extendInfo }),
        setMyServices: (services) => set({ myServices: services }),
        setMySchedule: (schedule) => set({ mySchedule: schedule }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
      }),
      {
        name: StorageSceneKey.THERAPIST_MANAGEMENT,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          myProfile: state.myProfile,
          myExtendInfo: state.myExtendInfo,
          myServices: state.myServices,
        }),
      }
    )
  )
);

export const useTherapistProfileStore = createSelectors(therapistProfileStore);
