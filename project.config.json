{"description": "心理咨询小程序", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "client/weapp/", "compileType": "miniprogram", "cloudfunctionRoot": "cloud/functions/", "projectname": "mental-AI", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "appid": "wx6920c81fab188467", "libVersion": "3.8.5", "srcMiniprogramRoot": "client/weapp/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}