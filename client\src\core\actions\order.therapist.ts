/**
 * 咨询师端订单操作
 */
import { OrderListRequest } from '@core/api';

import { OrderCacheManager } from '@core/cache/order-cache-manager';
import { CACHE_ROLE } from '@core/cache/order-cache-policy';
import {
  Order_action,
  ORDER_STATUS,
  Order_summary,
  REFUND_REASON,
  REFUND_STATUS,
  REJECT_REASON,
} from '@model/order.interface';
import { ServiceType } from '@model/service.interface';
import { therapistOrderService } from '@services/order.therapist';
import { userService } from '@services/user.service';
import { useLoadingStore } from '@stores/loading.store';
import { useTherapistOrderStoreSelector } from '@stores/order.store';
import Taro from '@tarojs/taro';
import { videoCallActions } from './videoCall.action';

export const orderTherapistActions = {
  /**
   * 获取订单列表
   * @param params 请求参数
   */
  fetchOrders: async (params: OrderListRequest = {}): Promise<void> => {
    console.log('orderTherapistActions.fetchOrders', params);
    const store = useTherapistOrderStoreSelector.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);

    try {
      const { forceRefresh } = params;

      // 尝试从缓存获取
      if (!forceRefresh) {
        const cachedData = OrderCacheManager.getOrderList(
          params,
          CACHE_ROLE.THERAPIST
        );
        if (cachedData) {
          store.setOrders(cachedData.data);
          store.setPagination(cachedData.pagination);
          store.setLoading(false);
          return;
        }
      }

      // 从服务获取数据
      const result = await therapistOrderService.getOrders(params);

      if (result.success) {
        // 更新状态
        store.setOrders(result.data);
        store.setPagination(result.pagination || null);

        // 缓存数据
        OrderCacheManager.setOrderList(
          params,
          result.data,
          result.pagination,
          CACHE_ROLE.THERAPIST
        );
      }
    } catch (error) {
      store.setError('获取订单列表失败');
      console.error('获取订单列表失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 获取单个订单详情
   * @param id 订单ID
   * @param forceRefresh 是否强制刷新
   */
  fetchOrderById: async (
    id: string,
    forceRefresh = false
  ): Promise<Order_summary | null> => {
    const store = useTherapistOrderStoreSelector.getState();
    store.setLoading(true);

    try {
      // 尝试从缓存获取
      if (!forceRefresh) {
        const cachedData = OrderCacheManager.getOrderSummary(
          id,
          CACHE_ROLE.THERAPIST
        );
        if (cachedData) {
          store.updateOrder(cachedData);
          store.setCurrentOrder(id);
          store.setLoading(false);
          return cachedData;
        }
      }

      // 从服务获取数据
      const order = await therapistOrderService.getOrderById(id);

      // 更新状态
      store.updateOrder(order as Order_summary);
      store.setCurrentOrder(id);

      // 缓存数据
      OrderCacheManager.setOrderSummary(
        id,
        order as Order_summary,
        CACHE_ROLE.THERAPIST
      );

      return order;
    } catch (error) {
      store.setError('获取订单详情失败');
      console.error('获取订单详情失败:', error);
      return null;
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 获取订单操作日志
   */
  fetchOrderActions: async (
    orderId: string,
    forceRefresh = false
  ): Promise<Order_action[]> => {
    const store = useTherapistOrderStoreSelector.getState();
    const actionsInStore = store.getOrderActions(orderId);
    if (actionsInStore.length > 0 && !forceRefresh) {
      return actionsInStore;
    }
    try {
      const actions = await therapistOrderService.getOrderActions(orderId);
      store.setOrderActions(actions);
      return actions;
    } catch (error) {
      console.error('获取订单操作失败:', error);
      throw error;
    }
  },
  /**
   * 清除订单列表缓存
   */
  clearOrderListCache: () => {
    OrderCacheManager.clearOrderList(CACHE_ROLE.THERAPIST);
  },

  /**
   * 加载更多订单
   */
  loadMore: async (): Promise<void> => {
    const store = useTherapistOrderStoreSelector.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);
    const { pagination, filters } = store;

    // 没有更多数据了
    if (!pagination || !pagination.hasNext) return;

    // 构建请求参数
    const params: OrderListRequest = {
      page: pagination.page + 1,
      pageSize: pagination.pageSize,
      params: filters,
      role: CACHE_ROLE.THERAPIST,
    };

    try {
      // 从服务获取数据
      const result = await therapistOrderService.getOrders(params);

      if (result.success) {
        const { orders: currentOrders } = store;

        // 将新数据添加到现有数据后面
        const combinedOrders = [...(currentOrders || []), ...result.data];

        // 更新状态
        store.setOrders(combinedOrders);
        store.setPagination(result.pagination || null);

        // 缓存数据
        OrderCacheManager.setOrderList(
          params,
          result.data,
          result.pagination,
          CACHE_ROLE.THERAPIST
        );
      }
    } catch (error) {
      console.error('加载更多订单失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 咨询师确认订单
   */
  confirmOrder: async (orderId: string): Promise<void> => {
    try {
      // 调用服务确认订单
      const result = await therapistOrderService.confirmOrder(orderId);
      console.log('confirmOrder result', result);
      if (result.success && !result.warning) {
        Taro.showToast({
          title: result.message || '确认订单成功',
          icon: 'success',
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('确认订单失败:', error);
      throw error;
    } finally {
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },

  /**
   * 咨询师拒绝订单
   */
  rejectOrder: async (
    orderId: string,
    reason: REJECT_REASON,
    detail?: string
  ): Promise<void> => {
    useLoadingStore.getState().setTransactionLoading(true);
    try {
      const result = await therapistOrderService.rejectOrder({
        _id: orderId,
        cancel_info: {
          reason: reason,
          detail: detail || '',
        },
      });

      console.log('rejectOrder result', result);
      if (result.success && !result.warning) {
        Taro.showToast({
          title: result.message,
          icon: 'success',
          duration: 2000,
        });

        // 延迟返回，确保Toast能够显示
        setTimeout(() => {
          Taro.navigateBack();
          useLoadingStore.getState().setTransactionLoading(false);
        }, 2000);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('拒绝订单失败:', error);
      throw error;
    }
  },

  /**
   * 咨询师为用户申请退款
   */
  refundRequest: async (
    orderId: string,
    reason: REFUND_REASON,
    detail?: string
  ): Promise<void> => {
    useLoadingStore.getState().setTransactionLoading(true);
    try {
      const result = await therapistOrderService.refundRequest(
        orderId,
        reason,
        detail
      );
      console.log('refundRequest result', result);
      if (result.success && !result.warning) {
        Taro.showToast({
          title: result.message || '退款申请操作成功，请等待审核',
          icon: 'success',
          duration: 2000,
        });

        // 延迟返回，确保Toast能够显示
        setTimeout(() => {
          Taro.navigateBack();
          useLoadingStore.getState().setTransactionLoading(false);
        }, 2000);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('退款申请失败:', error);
      throw error;
    } finally {
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },
  /**
   * 咨询师开始服务
   */
  startService: async (
    orderId: string,
    serviceType: ServiceType
  ): Promise<void> => {
    useLoadingStore.getState().setTransactionLoading(true);
    try {
      // 调用服务开始服务
      const result = await therapistOrderService.startService(orderId);
      console.log('startService result', result);
      if (result.success) {
        Taro.showToast({
          title: `即将开始拨号...`,
          icon: 'success',
          duration: 2000,
        });

        // 延迟执行，确保Toast能够显示
        setTimeout(() => {
          useLoadingStore.getState().setTransactionLoading(false);
          if (serviceType === ServiceType.VIDEO) {
            videoCallActions.call(orderId);
          }
        }, 2000);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('开始服务失败:', error);
      throw error;
    } finally {
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },

  /**
   * 咨询师完成服务
   */
  completeService: async (orderId: string): Promise<void> => {
    useLoadingStore.getState().setTransactionLoading(true);
    try {
      // 调用服务完成服务
      const result = await therapistOrderService.completeService(orderId);
      console.log('completeService result', result);
      if (result.success) {
        Taro.showToast({
          title: '完成服务成功',
          icon: 'success',
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('完成服务失败:', error);
      Taro.showToast({
        title: error.message || '完成服务失败',
        icon: 'error',
        duration: 2000,
      });
    } finally {
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },

  /**
   * 监听单个订单
   */
  watchOrderById: (orderId: string): any => {
    const store = useTherapistOrderStoreSelector.getState();
    return therapistOrderService.watchOrderById(orderId, (snapshot) => {
      if (snapshot.type === 'init') {
        console.log('订单详情监听初始化:', snapshot.docs.length);
        if (snapshot.docs.length > 0) {
          const order = snapshot.docs[0] as Order_summary;
          store.updateOrder(order);

          // 更新缓存
          OrderCacheManager.setOrderSummary(
            orderId,
            order,
            CACHE_ROLE.THERAPIST
          );
        }
      } else {
        // 数据变更
        snapshot.docChanges.forEach((change) => {
          if (change.dataType === 'update' || change.dataType === 'add') {
            const order = change.doc as Order_summary;
            orderTherapistActions.updateOrder(order);
          }
        });
      }
    });
  },

  /**
   * 监听订单列表
   */
  watchOrders: (): any => {
    const params = {
      status: [
        ORDER_STATUS.PENDING_PAYMENT,
        ORDER_STATUS.PENDING_CONFIRM,
        ORDER_STATUS.PENDING_START,
        ORDER_STATUS.IN_PROGRESS,
      ], // 监听所有状态
      refundStatus: [
        REFUND_STATUS.PROCESSING,
        REFUND_STATUS.COMPLETED,
        REFUND_STATUS.FAILED,
        REFUND_STATUS.REJECTED,
      ],
    };
    console.log('therapistOrderService watchOrders params', params);
    return therapistOrderService.watchTherapistOrders(params, (snapshot) => {
      console.log('therapistOrderService watchOrders', snapshot);
      if (snapshot.type === 'init') {
        console.log('订单列表监听初始化:', snapshot.docs.length);
      } else {
        // 数据变更
        console.log(
          'therapistOrderService watchOrders docChanges',
          snapshot.docChanges
        );
        try {
          snapshot.docChanges.forEach((change) => {
            if (change.dataType === 'update' || change.dataType === 'add') {
              const order = change.doc as Order_summary;
              orderTherapistActions.updateOrder(order);
            }
          });
        } catch (error) {
          console.error('订单列表监听数据变更回调异常:', error);
        }
      }
    });
  },

  /**
   * 更新订单
   */
  updateOrder: (order: Order_summary) => {
    console.log('orderTherapistActions updateOrder order', order);
    const store = useTherapistOrderStoreSelector.getState();
    store.updateOrder(order);
    // 更新缓存
    OrderCacheManager.setOrderSummary(order._id, order, CACHE_ROLE.THERAPIST);
  },

  /**
   * 拨打电话
   */
  callUser: async (userId: string, orderId: string): Promise<void> => {
    console.log('orderTherapistActions callUser userId', userId);
    Taro.showLoading({
      title: '正在准备拨打电话...',
    });
    try {
      const phoneNumber = await userService.getPhoneNumber(userId, orderId);
      if (phoneNumber) {
        Taro.makePhoneCall({
          phoneNumber: phoneNumber,
        });
      }
    } catch (error) {
      console.error('拨打电话失败:', error);
    } finally {
      Taro.hideLoading();
    }
  },

  /**
   * 清空状态
   */
  resetStore: () => {
    useTherapistOrderStoreSelector.getState().reset();
  },
};
