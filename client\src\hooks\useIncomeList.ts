import { incomeActions } from '@core/actions/income.action';
import { IncomeListParams, IncomeListRequest } from '@core/api';
import { useIncomeStore } from '@stores/income.store';
import { useCallback, useEffect, useRef } from 'react';

/**
 * 收入列表管理Hook
 * 提供收入列表的获取、筛选和状态监听功能
 */
export function useIncomeList(
  initialParams: { params?: IncomeListParams } = {}
) {
  console.log('useIncomeList', initialParams);

  const incomeList = useIncomeStore.use.incomeList();
  const pagination = useIncomeStore.use.pagination();
  const filters = useIncomeStore.use.filters();
  const setFilters = useIncomeStore.use.setFilters();
  const loading = useIncomeStore.use.loading();

  // 使用ref保存请求参数，避免依赖变化导致函数重建
  const requestParamsRef = useRef({
    filters: initialParams.params,
  });

  // 更新ref值
  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchIncomeList = useCallback(async (params: IncomeListRequest) => {
    console.log('useIncomeList fetchIncomeList', params);
    return incomeActions.fetchIncomeList(params);
  }, []);

  // 刷新列表
  const refreshIncomeList = useCallback(async () => {
    console.log(
      'useIncomeList refreshIncomeList',
      requestParamsRef.current.filters
    );
    try {
      await fetchIncomeList({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
        forceRefresh: true,
      });
    } catch (error) {
      console.error('刷新收入列表失败:', error);
    }
  }, [fetchIncomeList, pagination?.pageSize]);

  // 加载更多
  const loadMoreIncome = useCallback(async () => {
    console.log(
      'useIncomeList loadMoreIncome',
      requestParamsRef.current.filters
    );
    if (!pagination?.hasNext) return;

    try {
      await incomeActions.loadMoreIncomeDetails();
    } catch (error) {
      console.error('加载更多收入失败:', error);
    }
  }, [pagination?.hasNext]);

  // 筛选收入
  const onFilterChange = useCallback(
    async (newFilters: IncomeListParams) => {
      console.log('useIncomeList onFilterChange', newFilters);
      setFilters(newFilters);

      try {
        await fetchIncomeList({
          page: 1,
          pageSize: pagination?.pageSize || 20,
          params: newFilters,
          forceRefresh: true,
        });
      } catch (error) {
        console.error('筛选收入失败:', error);
      }
    },
    [setFilters, fetchIncomeList, pagination?.pageSize]
  );

  // 初始加载
  useEffect(() => {
    const loadInitialData = async () => {
      if (incomeList.length === 0) {
        await fetchIncomeList({
          page: 1,
          pageSize: 20,
          params: initialParams.params,
        });
      }
    };
    loadInitialData();
  }, []); // 只在组件挂载时执行一次

  return {
    incomeList,
    pagination,
    loading,
    refreshIncomeList,
    loadMoreIncome,
    onFilterChange,
  };
}
