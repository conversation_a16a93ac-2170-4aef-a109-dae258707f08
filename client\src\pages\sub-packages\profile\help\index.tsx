import {
  Cell,
  CellGroup,
  Collapse,
  CollapseItem,
  Icon,
  Loading,
} from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { HELP_CATEGORIES, mockFAQs } from '@constants/text';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { useState } from 'react';

export default function HelpCenterPage() {
  const [loading, setLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  const [activeNames, setActiveNames] = useState<string[]>([]);

  // 按分类筛选问题
  const getFilteredFAQs = () => {
    if (activeCategory === 'all') return mockFAQs;
    return mockFAQs.filter((faq) => faq.category === activeCategory);
  };

  // 处理手风琴切换
  const onCollapseChange = (event) => {
    setActiveNames(event.detail);
  };

  // 联系客服
  const contactCustomerService = () => {
    // 实际应用中这里会调用微信小程序的客服接口
    Taro.showToast({
      title: '正在接入客服，请稍候...',
      icon: 'loading',
      duration: 2000,
      success: () => {
        // 模拟客服接入成功
        setTimeout(() => {
          Taro.showToast({
            title: '客服接入成功',
            icon: 'success',
          });
        }, 2000);
      },
    });
  };

  return (
    <PageLol
      navigationProps={{
        title: '帮助中心',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen '>
        {/* 顶部客服入口 */}
        <View className='p-4'>
          <View
            className='bg-gradient-to-r from-blue-500 to-primary rounded-xl p-4 flex items-center justify-between'
            onClick={contactCustomerService}
          >
            <View>
              <Text className='text-white text-lg font-medium mb-1'>
                在线客服
              </Text>
              <Text className='text-white text-sm opacity-80'>
                工作时间: 9:00-22:00
              </Text>
            </View>
            <View className='bg-white bg-opacity-20 p-2 rounded-lg'>
              <Icon name='service-o' size={48} color='#ffffff' />
            </View>
          </View>
        </View>

        {/* 问题分类 */}
        <ScrollView
          scrollX
          enhanced
          showScrollbar={false}
          className='whitespace-nowrap px-4 py-2'
        >
          {HELP_CATEGORIES.map((category) => (
            <View
              key={category.key}
              className={`inline-block mr-2 px-4 py-2 rounded-full ${
                activeCategory === category.key
                  ? 'bg-primary text-white'
                  : 'bg-white text-gray-600'
              }`}
              onClick={() => setActiveCategory(category.key)}
            >
              <Text className='text-sm'>{category.name}</Text>
            </View>
          ))}
        </ScrollView>

        {/* 常见问题列表 */}
        <View className='p-4'>
          {loading ? (
            <View className='flex justify-center items-center p-8'>
              <Loading type='spinner' color='#1989fa' />
            </View>
          ) : (
            <Collapse
              value={activeNames}
              onChange={onCollapseChange}
              border={false}
            >
              {getFilteredFAQs().map((faq) => (
                <CollapseItem key={faq.id} title={faq.question} name={faq.id}>
                  <View className='p-2 text-secondary text-sm'>
                    <Text>{faq.answer}</Text>
                  </View>
                </CollapseItem>
              ))}
            </Collapse>
          )}
        </View>

        {/* 其他帮助入口 */}
        <View className=' mt-2'>
          <CellGroup inset>
            <Cell title='用户协议' isLink onClick={appRouter.agreement} />
            <Cell title='隐私政策' isLink onClick={appRouter.privacy} />
            <Cell title='关于我们' isLink onClick={appRouter.about} />
          </CellGroup>
        </View>

        {/* 底部联系方式 */}
        <View className='p-4 mt-4 mb-8'>
          <View className='bg-white p-4 rounded-lg flex flex-col items-center'>
            <Text className='text-sm text-secondary mb-2'>联系电话</Text>
            <Text className='text-primary font-medium'>************</Text>
            <Text className='text-xs text-disabled mt-1'>
              周一至周日 9:00-22:00
            </Text>
          </View>
        </View>
      </View>
    </PageLol>
  );
}
