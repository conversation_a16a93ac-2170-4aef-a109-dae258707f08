/**
 * 权限等级枚举。按操作来划分
 */
const PERMISSION_LEVEL = {
  PUBLIC: 0, // 公开数据，无需验证
  USER_ONLY: 1, // 登录用户，咨询师角色不能访问。如创建订单、取消订单、重新安排时间、拒绝订单、开始服务、完成服务
  THERAPIST_ONLY: 2, // 咨询师本人 如确认订单、拒绝订单、开始服务、完成服务
  THERAPIST_AND_USER_SELF: 3, // 咨询师和用户本人. 如查询订单详情、订单列表等。注意：查询操作在前端已经直接读取数据库了，数据库的安全规则已经限制了查询权限
  ADMIN_ONLY: 4, // 管理员
};

/**
 * 用户角色枚举
 */
const USER_ROLE = {
  GUEST: "guest",
  USER: "user",
  THERAPIST: "therapist",
  ADMIN: "admin",
};

module.exports = {
  PERMISSION_LEVEL,
  USER_ROLE,
};
