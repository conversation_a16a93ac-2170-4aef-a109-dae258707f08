// 数据库索引初始化脚本
const cloud = require("wx-server-sdk");

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();

// 引入模型定义
const { COLLECTIONS } = require("./db.model");

/**
 * 初始化数据库索引
 * 此脚本需要在微信云开发控制台手动调用
 */
exports.main = async () => {
  console.log("开始创建索引...");
  const result = {
    success: true,
    indexes: [],
  };

  try {
    // 创建咨询师基本信息索引
    await db.collection(COLLECTIONS.THERAPIST).createIndex({
      id: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST).createIndex({
      location: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST).createIndex({
      status: 1,
    });
    result.indexes.push("咨询师基本信息索引创建成功");

    // 创建咨询师服务索引
    await db.collection(COLLECTIONS.THERAPIST_SERVICE).createIndex({
      id: 1,
    });
    result.indexes.push("咨询师服务索引创建成功");

    // 创建咨询师扩展信息索引
    await db.collection(COLLECTIONS.THERAPIST_EXTINFO).createIndex({
      id: 1,
    });
    result.indexes.push("咨询师扩展信息索引创建成功");

    // 创建咨询师排期索引
    await db.collection(COLLECTIONS.THERAPIST_SCHEDULES).createIndex({
      id: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST_SCHEDULES).createIndex({
      therapistId: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST_SCHEDULES).createIndex({
      "schedule.date": 1,
    });
    result.indexes.push("咨询师排期索引创建成功");

    // 创建咨询师评价索引
    await db.collection(COLLECTIONS.THERAPIST_REVIEWS).createIndex({
      therapistId: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST_REVIEWS).createIndex({
      userId: 1,
    });
    await db.collection(COLLECTIONS.THERAPIST_REVIEWS).createIndex({
      score: 1,
    });
    result.indexes.push("咨询师评价索引创建成功");

    // 用户收藏表索引
    await db.collection(COLLECTIONS.USER_FAVORITES).createIndex({
      openid: 1,
      therapistId: 1,
    });
    result.indexes.push("用户收藏索引创建成功");

    // 订单相关索引
    await db.collection(COLLECTIONS.ORDER).createIndex({
      therapistId: 1,
    });
    await db.collection(COLLECTIONS.ORDER).createIndex({
      userId: 1,
    });
    await db.collection(COLLECTIONS.ORDER).createIndex({
      status: 1,
    });
    await db.collection(COLLECTIONS.ORDER).createIndex({
      startTime: 1,
    });
    result.indexes.push("订单索引创建成功");

    // 聊天会话索引
    await db.collection(COLLECTIONS.CHAT_SESSION).createIndex({
      userId: 1,
    });
    await db.collection(COLLECTIONS.CHAT_SESSION).createIndex({
      therapistId: 1,
    });
    await db.collection(COLLECTIONS.CHAT_SESSION).createIndex({
      timestamp: -1,
    });
    result.indexes.push("聊天会话索引创建成功");

    // 聊天消息索引
    await db.collection(COLLECTIONS.CHAT_MESSAGE).createIndex({
      sessionId: 1,
    });
    await db.collection(COLLECTIONS.CHAT_MESSAGE).createIndex({
      senderId: 1,
    });
    await db.collection(COLLECTIONS.CHAT_MESSAGE).createIndex({
      receiverId: 1,
    });
    await db.collection(COLLECTIONS.CHAT_MESSAGE).createIndex({
      timestamp: 1,
    });
    result.indexes.push("聊天消息索引创建成功");

    // 通知索引
    await db.collection(COLLECTIONS.NOTIFICATION_LIST).createIndex({
      read: 1,
    });
    await db.collection(COLLECTIONS.NOTIFICATION_LIST).createIndex({
      time: -1,
    });
    await db.collection(COLLECTIONS.NOTIFICATION_LIST).createIndex({
      type: 1,
    });
    result.indexes.push("通知索引创建成功");

    // 分销相关索引
    await db.collection(COLLECTIONS.DISTRIBUTION_OVERVIEW).createIndex({
      referrerId: 1,
    });

    await db.collection(COLLECTIONS.DISTRIBUTION_INVITED_USER).createIndex({
      referrerId: 1,
    });
    await db.collection(COLLECTIONS.DISTRIBUTION_INVITED_USER).createIndex({
      inviteeId: 1,
    });

    await db.collection(COLLECTIONS.DISTRIBUTION_ORDER).createIndex({
      referrerId: 1,
    });
    await db.collection(COLLECTIONS.DISTRIBUTION_ORDER).createIndex({
      inviteeId: 1,
    });
    await db.collection(COLLECTIONS.DISTRIBUTION_ORDER).createIndex({
      orderId: 1,
    });
    await db.collection(COLLECTIONS.DISTRIBUTION_ORDER).createIndex({
      status: 1,
    });

    await db.collection(COLLECTIONS.WITHDRAW).createIndex({
      userId: 1,
    });
    await db.collection(COLLECTIONS.WITHDRAW).createIndex({
      status: 1,
    });
    result.indexes.push("分销相关索引创建成功");

    console.log("所有索引创建完成");
    return result;
  } catch (error) {
    console.error("创建索引失败:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};
