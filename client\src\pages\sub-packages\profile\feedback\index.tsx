import {
  <PERSON><PERSON>,
  Picker,
  Radio,
  RadioGroup,
  Toast,
  Uploader,
} from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { feedbackActions } from '@core/actions/feedback.action';
import { Text, Textarea, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

import { FEEDBACK_TYPE_MAP, SERVICE_TYPE_MAP_SHORT } from '@constants/text';
import { orderUserActions } from '@core/actions/order.user';
import { FeedbackType } from '@model/feedback.interface';
import { Order_summary } from '@model/order.interface';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';
import { formatTime2 } from '@utils/time';
// import './index.less';

// 定义文件类型
interface FileItem {
  url: string;
  name?: string;
  path: string;
  size?: number;
}

// 扩展反馈请求类型，添加orderId字段
interface FeedbackData {
  type: FeedbackType;
  content: string;
  orderId?: string;
}

export default function FeedbackPage() {
  const [complaintType, setComplaintType] = useState<FeedbackType>(
    FeedbackType.SERVICE
  );

  const isSubmitting = useLoadingStore.use.transactionLoading();
  const [content, setContent] = useState('');
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [orderId, setOrderId] = useState('');
  const [orderList, setOrderList] = useState<Order_summary[]>([]);
  const [orderOptions, setOrderOptions] = useState<string[]>([]);

  // 获取用户订单列表
  useEffect(() => {
    if (complaintType === FeedbackType.SERVICE) {
      fetchUserOrders();
    }
  }, [complaintType]);

  // 获取用户订单列表
  const fetchUserOrders = async () => {
    try {
      await orderUserActions.fetchOrders({
        page: 1,
        pageSize: 20,
      });

      // 从store中获取订单列表数据
      const orders = useOrderStoreSelector.getState().orders;
      setOrderList(orders);

      // 生成下拉选项
      const options = orders.map(
        (order) =>
          `${order.therapistName} (${
            SERVICE_TYPE_MAP_SHORT[order.serviceType]
          }) ${formatTime2(order.startTime)}`
      );
      setOrderOptions(options);
    } catch (error) {
      console.error('获取订单列表失败:', error);
      Toast.fail('获取订单列表失败');
    }
  };

  // 选择投诉类型
  const handleTypeChange = (event: any) => {
    setComplaintType(event.detail);
  };

  // 内容变化
  const handleContentChange = (event: any) => {
    setContent(event.detail.value);
  };

  // 订单选择变化
  const handleOrderChange = (event: any) => {
    const index = event.detail;
    if (index >= 0 && index < orderList.length) {
      setOrderId(orderList[index]._id);
    }
  };

  // 上传图片后的回调
  const afterRead = async (event: any) => {
    console.log('afterRead', event.detail);
    const { file } = event.detail;
    const newFileList = [...fileList];
    newFileList.push({ ...file, url: file.path });
    setFileList(newFileList);
  };

  // 删除图片
  const handleDelete = (event: any) => {
    console.log('handleDelete', event);
    const { index } = event.detail;
    const newFileList = [...fileList];
    newFileList.splice(index, 1);
    setFileList(newFileList);
  };

  // 提交投诉
  const handleSubmit = async () => {
    useLoadingStore.getState().setTransactionLoading(true);
    // 表单验证
    if (!complaintType) {
      Toast.fail('请选择投诉类型');
      return;
    }

    if (!content.trim()) {
      Toast.fail('请填写投诉内容');
      return;
    }

    // 如果是服务投诉，检查订单号
    if (complaintType === FeedbackType.SERVICE && !orderId) {
      Toast.fail('请选择订单');
      return;
    }

    // 提交表单
    const result = await feedbackActions.submitFeedback(
      {
        type: complaintType,
        content,
        orderId: complaintType === FeedbackType.SERVICE ? orderId : undefined,
      } as FeedbackData,
      fileList.map((file) => file.path || file.url)
    );

    if (result && result.success) {
      // 重置表单
      setComplaintType(FeedbackType.SERVICE);
      setContent('');
      setFileList([]);
      setOrderId('');

      Toast.success('投诉提交成功');
    } else {
      Toast.fail(result?.message || '提交失败，请稍后再试');
    }
    useLoadingStore.getState().setTransactionLoading(false);
  };

  return (
    <PageLol
      className='feedback-page'
      navigationProps={{
        title: '投诉',
        showBackButton: true,
        buttons: [
          {
            icon: 'clock-o',
            onClick: () => {
              Taro.navigateTo({
                url: '/pages/sub-packages/profile/feedback/history',
              });
            },
          },
        ],
      }}
    >
      <View className='min-h-screen '>
        <View className='rounded-lg p-4  mb-4 mt-4'>
          <View className='mb-6'>
            <Text className='text-lg font-medium mb-3 block'>投诉类型</Text>
            <RadioGroup value={complaintType} onChange={handleTypeChange}>
              <View className='grid grid-cols-2 gap-2'>
                {Object.keys(FEEDBACK_TYPE_MAP).map((type) => (
                  <Radio key={type} name={type} checkedColor='#1989fa'>
                    {FEEDBACK_TYPE_MAP[type as FeedbackType]}
                  </Radio>
                ))}
              </View>
            </RadioGroup>
          </View>
          {complaintType === FeedbackType.SERVICE && (
            <View className='mb-6'>
              <Text className='text-lg font-medium mb-3 block'>订单号</Text>
              {orderOptions.length > 0 ? (
                <View className='h-12 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
                  <Picker
                    value={orderOptions.findIndex((option) =>
                      option.startsWith(orderId)
                    )}
                    onInput={handleOrderChange}
                    mode='content'
                    columns={orderOptions}
                    placeholder='请选择订单'
                    allowClear={false}
                    showArrowDown
                  />
                </View>
              ) : (
                <Text className='text-xs text-secondary mt-1 block'>
                  暂无订单数据，请稍后再试
                </Text>
              )}
            </View>
          )}

          <View className='mb-6'>
            <Text className='text-lg font-medium mb-3 block'>问题描述</Text>
            <View className='border border-gray-200 rounded-lg p-2'>
              <Textarea
                value={content}
                onInput={handleContentChange}
                placeholder='请详细描述您遇到的问题，以便我们更好地解决...'
                style={{ height: '120px', width: '100%' }}
                maxlength={500}
                showConfirmBar={false}
              />
            </View>
            <Text className='text-xs text-secondary mt-1 text-right block'>
              {content.length}/500
            </Text>
          </View>

          <View className='mb-6'>
            <Text className='text-lg font-medium mb-3 block'>上传图片凭证</Text>
            <Uploader
              fileList={fileList}
              maxCount={4}
              maxSize={2 * 1024 * 1024}
              deletable
              onAfterRead={afterRead}
              onDelete={handleDelete}
            />
            <Text className='text-xs text-secondary mt-1 block'>
              最多上传4张图片，每张不超过2MB
            </Text>
          </View>

          <Button
            block
            type='primary'
            round
            loading={isSubmitting}
            onClick={handleSubmit}
            disabled={
              isSubmitting ||
              !content.trim() ||
              (complaintType === FeedbackType.SERVICE && !orderId)
            }
            className='mt-6'
          >
            提交投诉
          </Button>

          {/* 温馨提示 */}
          <View className=' p-4 mt-4'>
            <Text className='text-lg font-medium mb-2 block'>温馨提示</Text>
            <View className='text-sm text-secondary'>
              <Text className='block mb-2'>
                1. 我们将在3个工作日内回复您的投诉。
              </Text>
              <Text className='block mb-2'>
                2. 请确保您的联系方式正确，以便我们及时与您沟通。
              </Text>
              <Text className='block mb-2'>
                3. 如遇紧急情况，可直接拨打客服热线：400-888-8888。
              </Text>
              <Text className='block'>
                4. 工作时间：周一至周日 9:00-22:00。
              </Text>
            </View>
          </View>
        </View>
      </View>
    </PageLol>
  );
}
