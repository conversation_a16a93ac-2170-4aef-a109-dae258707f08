import { main_pages } from '@utils/router';
import { APP, TAB_TEXTS, THERAPIST_TAB_TEXTS } from './constants/text';
import { COLORS } from './constants/theme';

export default defineAppConfig({
  pages: main_pages,
  subPackages: [
    {
      root: 'pages/sub-packages/orders',
      pages: [
        'list/index', // 订单列表页
        'detail/index', // 订单详情页
        'cancel/index', // 取消订单页
        'create/index', // 创建订单页
        'submit-info/index', // 提交咨询信息页
        'payment/index', // 支付页
        'review/index', // 评价页
        'reject/index', // 拒绝订单页
        'detail-therapist/index', // 咨询师端订单详情
        'refund/index', // 退款页
        'reject-refund/index', // 拒绝退款页
        'audit/index', // 审核退款
      ],
    },
    {
      root: 'pages/sub-packages/therapist',
      pages: [
        'detail/index', // 心理师详情页
        'favorites/index', // 收藏夹
        'register/index', // 咨询师注册/入驻页面
        'agreement/index', // 用户协议
      ],
    },
    // 咨询师端资料编辑
    {
      root: 'pages/sub-packages/therapist-updater',
      pages: [
        'index/index', // 咨询师端资料编辑入口
        'base-info/index', // 基础信息
        'detail-info/index', // 详细信息
        'service-settings/index', // 服务设置
        'work-time/index', // 工作时间设置
        'schedule-info/index', // 排期信息
        'special-agreement/index', // 特殊约定
      ],
    },
    // 心理测试
    {
      root: 'pages/sub-packages/measure',
      pages: [
        'test/index', // 心理测试介绍页面
        'test/doing', // 心理测试答题页面
        'report/index', // 心理测试结果页面
        'history/index', // 心理测试历史记录页面
      ],
    },
    // 疗愈
    {
      root: 'pages/sub-packages/treatment',
      pages: ['player/index', 'player/circleTest'], // 疗愈播放器
    },
    {
      // 功能模块 ，有些功能暂时放在这里，后面归类到主页面中
      root: 'pages/sub-packages/profile',
      pages: [
        'favorites/index', // 收藏夹
        'help/index', // 帮助中心

        'settings/index', // 设置
        'feedback/index', // 反馈
        'feedback/history', // 反馈历史
        'about/index', // 关于我们
        'privacy/index', // 隐私政策
        'agreement/index', // 用户协议

        'edit-profile/index', // 编辑个人资料
      ],
    },
    {
      root: 'pages/sub-packages/wallet',
      pages: [
        'dashboard/therapist', // 咨询师收益中心
        'withdraw/apply', // 提现
        'withdraw/records', // 提现记录
        'distribution/therapist', // 咨询师分销中心
        'distribution/user', // 用户分销中心
        'income/index', // 咨询师收入明细
      ],
    },
    {
      root: 'pages/sub-packages/functions',
      pages: [
        'chat/index', // 聊天页
        'chat/system', // 系统聊天页

        'webview/index', // 外部链接
      ],
    },
  ],
  // // 添加预加载配置
  // preloadRule: {
  //   'pages/homepage/index': {
  //     network: 'all',
  //     packages: ['im-pkg'],
  //   },
  // },
  window: {
    enablePullDownRefresh: false, // 默认开启下拉刷新
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: APP.name,
    navigationBarTextStyle: 'black',
    navigationStyle: 'custom', // 全局设置，使用自定义导航栏
  },
  tabBar: {
    custom: true,
    color: COLORS.textColor,
    selectedColor: COLORS.textColorPrimary,
    backgroundColor: COLORS.bgColor,
    list: [
      // 首页
      {
        pagePath: 'pages/homepage/index',
        iconPath: 'assets/tabbar/home-2_linear.png',
        selectedIconPath: 'assets/tabbar/home-2_bold.png',
        text: TAB_TEXTS.home,
      },
      // 测评
      {
        pagePath: 'pages/measure/index',
        iconPath: 'assets/tabbar/trend-up_linear.png',
        selectedIconPath: 'assets/tabbar/trend-up_bold.png',
        text: TAB_TEXTS.measure,
      },
      // 咨询
      {
        pagePath: 'pages/consultation/index',
        iconPath: 'assets/tabbar/trend-up_linear.png',
        selectedIconPath: 'assets/tabbar/trend-up_bold.png',
        text: TAB_TEXTS.consultation,
      },
      // 疗愈
      {
        pagePath: 'pages/treatment/index',
        iconPath: 'assets/tabbar/audio-square_linear.png',
        selectedIconPath: 'assets/tabbar/audio-square_bold.png',
        text: TAB_TEXTS.treatment,
      },
      // 我的
      {
        pagePath: 'pages/profile/index/user',
        iconPath: 'assets/tabbar/profile_linear.png',
        selectedIconPath: 'assets/tabbar/profile_bold.png',
        text: TAB_TEXTS.profile,
      },
      // 预约 咨询师端
      {
        pagePath: 'pages/therapist-orders/index',
        iconPath: 'assets/tabbar/calendar_linear.png',
        selectedIconPath: 'assets/tabbar/calendar_bold.png',
        text: THERAPIST_TAB_TEXTS.orders,
      },
      // 消息 咨询师端
      {
        pagePath: 'pages/consultation-therapist/index',
        iconPath: 'assets/tabbar/message_linear.png',
        selectedIconPath: 'assets/tabbar/message_bold.png',
        text: THERAPIST_TAB_TEXTS.consultation,
      },
      // 我的 咨询师端
      {
        pagePath: 'pages/profile/index/therapist',
        iconPath: 'assets/tabbar/profile_linear.png',
        selectedIconPath: 'assets/tabbar/profile_bold.png',
        text: TAB_TEXTS.profile,
      },
    ],
  },
  lazyCodeLoading: 'requiredComponents',
});
