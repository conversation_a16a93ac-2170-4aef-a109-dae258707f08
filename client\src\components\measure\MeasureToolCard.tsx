import { Tag } from '@antmjs/vantui';
import { Image, Text, View } from '@tarojs/components';

export interface MeasureToolItem {
  id: number;
  title: string;
  description: string;
  questions: number;
  duration: string;
  participants: number;
  image: string;
  category: string;
  isHot?: boolean;
  isProfessional?: boolean;
  progress: number;
}

interface MeasureToolCardProps {
  test: MeasureToolItem;
  onClick: (id: number) => void;
}

export default function MeasureToolCard({
  test,
  onClick,
}: MeasureToolCardProps) {
  return (
    <View
      className='bg-white rounded-lg p-4 mb-3 shadow-sm active:scale-[0.98] transition-transform'
      onClick={() => onClick(test.id)}
    >
      <View className='flex'>
        <Image
          src={test.image}
          className='w-16 h-16 rounded-md mr-3'
          mode='aspectFill'
        />
        <View className='flex-1'>
          <View className='flex items-center'>
            <Text className='font-medium'>{test.title}</Text>
            {test.isHot && (
              <Tag type='danger' size='mini' className='ml-2'>
                热门
              </Tag>
            )}
            {test.isProfessional && (
              <Tag type='primary' size='mini' className='ml-2'>
                专业
              </Tag>
            )}
          </View>
          <Text className='text-sm text-gray-500 mt-1 line-clamp-1'>
            {test.description}
          </Text>
          <View className='flex items-center mt-2 text-xs text-gray-400'>
            <Text>{test.questions}题</Text>
            <Text className='mx-2'>·</Text>
            <Text>{test.duration}</Text>
            <Text className='mx-2'>·</Text>
            <Text>{test.participants}人测过</Text>
          </View>
        </View>
      </View>

      {/* 测试进度条 */}
      {test.progress > 0 && (
        <View className='mt-3'>
          <View className='flex justify-between mb-1'>
            <Text className='text-xs'>
              {test.progress === 100 ? '已完成' : '进行中'}
            </Text>
            <Text className='text-xs'>{test.progress}%</Text>
          </View>
          <View className='h-1 bg-gray-100 rounded-sm overflow-hidden'>
            <View
              className='h-full bg-primary'
              style={{ width: `${test.progress}%` }}
            />
          </View>
        </View>
      )}
    </View>
  );
}
