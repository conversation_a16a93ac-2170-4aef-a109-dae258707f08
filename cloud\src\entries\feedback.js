// 云函数入口文件
const cloud = require("wx-server-sdk");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const feedbackCollection = db.collection("feedback");
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  try {
    switch (action) {
      case "createFeedback":
        return await createFeedback(params, openid);
      case "getFeedbackList":
        return await getFeedbackList(openid);
      case "getFeedbackDetail":
        return await getFeedbackDetail(params.feedbackId, openid);
      case "updateFeedback":
        return await updateFeedback(params.feedbackId, params.content, openid);
      default:
        return {
          success: false,
          message: "未知操作类型",
        };
    }
  } catch (error) {
    console.error("反馈操作失败:", error);
    return {
      success: false,
      message: "操作失败",
      error: error.message,
    };
  }
};

// 创建反馈
async function createFeedback(feedbackData, openid) {
  try {
    const {
      orderId,
      therapistId,
      content,
      score,
      tags = [],
      type,
    } = feedbackData;

    // 验证订单信息
    if (orderId) {
      const ordersCollection = db.collection("orders");
      const order = await ordersCollection.doc(orderId).get();

      if (!order.data || order.data._openid !== openid) {
        return {
          success: false,
          message: "订单不存在或无权访问",
        };
      }
    }

    // 创建反馈记录
    const feedback = {
      orderId,
      therapistId,
      content,
      score,
      tags,
      type,
      status: "pending", // 待处理
      createTime: Date.now(),
      updateTime: Date.now(),
      _openid: openid,
    };

    // 保存反馈
    const result = await feedbackCollection.add({
      data: feedback,
    });

    // 如果是对咨询师的评价，更新咨询师的评分
    if (therapistId && type === "review") {
      await updateTherapistReview(therapistId, score, content, tags, openid);
    }

    return {
      success: true,
      data: {
        ...feedback,
        _id: result._id,
      },
    };
  } catch (error) {
    console.error("创建反馈失败:", error);
    throw error;
  }
}

// 获取反馈列表
async function getFeedbackList(openid) {
  try {
    const { data } = await feedbackCollection
      .where({
        _openid: openid,
      })
      .orderBy("createTime", "desc")
      .get();

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("获取反馈列表失败:", error);
    throw error;
  }
}

// 获取反馈详情
async function getFeedbackDetail(feedbackId, openid) {
  try {
    const { data } = await feedbackCollection.doc(feedbackId).get();

    if (!data || data._openid !== openid) {
      return {
        success: false,
        message: "反馈不存在或无权访问",
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("获取反馈详情失败:", error);
    throw error;
  }
}

// 更新反馈
async function updateFeedback(feedbackId, content, openid) {
  try {
    const { data } = await feedbackCollection.doc(feedbackId).get();

    if (!data || data._openid !== openid) {
      return {
        success: false,
        message: "反馈不存在或无权访问",
      };
    }

    // 只能更新待处理状态的反馈
    if (data.status !== "pending") {
      return {
        success: false,
        message: "当前反馈状态不允许更新",
      };
    }

    // 更新反馈
    await feedbackCollection.doc(feedbackId).update({
      data: {
        content,
        updateTime: Date.now(),
      },
    });

    return {
      success: true,
      message: "更新反馈成功",
    };
  } catch (error) {
    console.error("更新反馈失败:", error);
    throw error;
  }
}

// 更新咨询师评价（内部使用）
async function updateTherapistReview(
  therapistId,
  score,
  content,
  tags,
  openid
) {
  try {
    const therapistsCollection = db.collection("therapists");
    const therapist = await therapistsCollection.doc(therapistId).get();

    if (!therapist.data) {
      console.error("咨询师不存在:", therapistId);
      return;
    }

    // 获取用户信息
    const usersCollection = db.collection("users");
    const { data: userList } = await usersCollection.where({ openid }).get();
    const user =
      userList.length > 0
        ? userList[0]
        : { nickName: "匿名用户", avatarUrl: "" };

    // 构建评价
    const review = {
      user: user.nickName,
      avatar: user.avatarUrl,
      content,
      date: new Date().toISOString().split("T")[0],
      score,
      tags,
    };

    // 获取当前评价
    const reviews = therapist.data.reviews || { score: 0, count: 0, list: [] };

    // 计算新的平均评分
    const newCount = reviews.count + 1;
    const newScore = (reviews.score * reviews.count + score) / newCount;

    // 更新咨询师评价
    await therapistsCollection.doc(therapistId).update({
      data: {
        "reviews.score": newScore,
        "reviews.count": newCount,
        "reviews.list": _.unshift(review),
      },
    });
  } catch (error) {
    console.error("更新咨询师评价失败:", error);
    throw error;
  }
}
