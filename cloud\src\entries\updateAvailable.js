const cloud = require("wx-server-sdk");
const { COLLECTIONS } = require("../common/db.constants");
const { _updateTherapistTodayAvailable } = require("../service/orderOperation");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

/**
 * 更新咨询师的今天可用状态/可约描述
 * 凌晨1点触发执行
 */
exports.main = async (event, context) => {
  // 获取所有咨询师ID
  const therapists = await db
    .collection(COLLECTIONS.THERAPIST)
    .field({ id: true })
    .get();

  // 并行刷新
  await Promise.all(
    therapists.data.map((t) => _updateTherapistTodayAvailable(t.id))
  );

  return { success: true, count: therapists.data.length };
};
