import { PsychologicalTestSummary } from "@/app/model/measure.model";
import { PsychologicalTest, TestCategory } from "@/app/model/test.model";
import { resourceCloudFunctions } from "@/lib/cloud-functions";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
  Pagination,
  SUCCESS_CODE,
  ToolsListParams,
  ToolsListRequest,
} from "../model/api";

/**
 * 测量工具管理Hook
 * 提供测量工具列表的获取、搜索、筛选和状态监听功能
 */
export function useAssessmentTools(initialParams?: ToolsListParams) {
  const [loading, setLoading] = useState(false);
  const [tools, setTools] = useState<PsychologicalTestSummary[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [filters, setFilters] = useState<ToolsListParams>(initialParams || {});
  const [error, setError] = useState<Error | null>(null);

  // 使用ref保存请求参数，避免依赖变化导致函数重建
  const requestParamsRef = useRef({
    filters: initialParams,
  });

  // 更新ref值
  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchTools = useCallback(
    async (request: ToolsListRequest): Promise<PsychologicalTestSummary[]> => {
      if (loading) return [];
      setLoading(true);
      setError(null);
      try {
        const res = await resourceCloudFunctions.getAssessmentTools(request);
        setTools(res.data as unknown as PsychologicalTestSummary[]);
        setPagination(res.pagination as unknown as Pagination);
        return res.data as unknown as PsychologicalTestSummary[];
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("获取测量工具失败");
        setError(err);
        console.error("获取测量工具失败:", error);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 刷新列表
  const refreshTools = useCallback(async () => {
    try {
      await fetchTools({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
      });
    } catch (error) {
      console.error("刷新测量工具失败:", error);
    }
  }, [fetchTools, pagination?.pageSize]);

  // 加载更多/翻页
  const loadMoreTools = useCallback(
    async (page: number) => {
      try {
        await fetchTools({
          page,
          pageSize: pagination?.pageSize || 10,
          params: requestParamsRef.current.filters,
        });
      } catch (error) {
        console.error("加载更多测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 搜索测量工具
  const onSearch = useCallback(
    async (query: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        query,
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("搜索测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 按分类筛选
  const onCategoryChange = useCallback(
    async (category?: TestCategory) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        category,
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按分类筛选测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 创建测量工具
  const createTool = useCallback(
    async (tool: PsychologicalTest) => {
      setLoading(true);
      setError(null);
      try {
        const res = await resourceCloudFunctions.createAssessmentTool(tool);
        if (res.success && res.code === SUCCESS_CODE) {
          toast.success("创建测量工具成功");
          // 刷新列表
          await refreshTools();
          return true;
        } else {
          console.error("创建测量工具失败:", res.message);
          throw new Error(res.message);
        }
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("创建测量工具失败");
        setError(err);
        console.error("创建测量工具失败:", error);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 更新测量工具状态
  const updateTool = useCallback(
    async (params: Partial<PsychologicalTest>) => {
      setLoading(true);
      setError(null);
      try {
        const res = await resourceCloudFunctions.updateAssessmentTool(params);
        if (res.success && res.code === SUCCESS_CODE) {
          toast.success("更新测量工具状态成功");
          // 刷新列表
          await refreshTools();
          return true;
        } else {
          console.error("更新测量工具状态失败:", res.message);
          throw new Error(res.message);
        }
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("更新测量工具状态失败");
        setError(err);
        console.error("更新测量工具状态失败:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 删除测量工具
  const deleteTool = useCallback(
    async (id: string) => {
      setLoading(true);
      setError(null);
      try {
        const res = await resourceCloudFunctions.deleteAssessmentTool(id);
        if (res.success && res.code === SUCCESS_CODE) {
          toast.success("删除测量工具成功");
          // 刷新列表
          await refreshTools();
          return true;
        } else {
          console.error("删除测量工具失败:", res.message);
          throw new Error(res.message);
        }
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("删除测量工具失败");
        setError(err);
        console.error("删除测量工具失败:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 初始加载
  useEffect(() => {
    refreshTools();
  }, [refreshTools]);

  return {
    tools,
    pagination,
    loading,
    error,
    filters,
    refreshTools,
    loadMoreTools,
    onSearch,
    onCategoryChange,

    createTool,
    updateToolStatus: updateTool,
    deleteTool,
  };
}
