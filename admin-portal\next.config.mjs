/** @type {import('next').NextConfig} */
const nextConfig = {
  // 禁用客户端暴露环境变量
  env: {
    WX_APPID: process.env.WX_APPID,
    WX_CLOUD_ENV: process.env.WX_CLOUD_ENV,
  },
  // 仅暴露必要的环境变量到客户端
  publicRuntimeConfig: {
    WX_APPID: process.env.WX_APPID,
    WX_CLOUD_ENV: process.env.WX_CLOUD_ENV,
  },
  // 服务器端安全配置
  serverRuntimeConfig: {
    WX_APPSECRET: process.env.WX_APPSECRET,
    OP_SECRET: process.env.OP_SECRET,
  },
  // 配置图片域名
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.tcb.qcloud.la", // 微信云存储的域名
        port: "",
        pathname: "/**",
      },
    ],
  },
};

export default nextConfig;
