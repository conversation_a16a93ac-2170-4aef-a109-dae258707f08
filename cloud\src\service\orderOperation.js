const cloud = require("wx-server-sdk");
const {
  COLLECTIONS,
  ORDER_STATUS,
  ACTION_TYPE,
  INITATOR_SOURCE,
  REFUND_STATUS,
} = require("../common/db.constants");
const { SUB_MCH_ID, ENV_ID } = require("../common/config");
const {
  genNonceStr,
  genRefundNo,
  formatBeijingTimeSlots,
} = require("../common/utils");
const {
  sendRefundCompleteNotification,
  sendRefundFailedNotification,
} = require("./wxSubscribe");
const { refundDistributionOrder } = require("./distribution");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 添加订单操作记录
async function _addOrderAction(
  orderId,
  action,
  initiator,
  extraData = undefined
) {
  console.log(
    "🚀🚀🚀 _addOrderAction orderId",
    orderId,
    action,
    initiator,
    extraData
  );
  try {
    await db.collection(COLLECTIONS.ORDER_ACTION).add({
      data: {
        orderId,
        action,
        initiator,
        extraData,
        actionTime: Date.now(),
      },
    });
  } catch (error) {
    console.warn("添加订单操作记录失败:", error);
  }
}

/**
 * 支付成功后更新订单状态
 * @param {string} orderId 订单ID
 * @param {string} outTradeNo 支付单号
 */
async function onPaid(orderId, outTradeNo) {
  console.log("🚀🚀🚀 onPaid orderId", orderId, outTradeNo);
  try {
    // 更新支付记录
    await db
      .collection(COLLECTIONS.PAYMENT)
      .where({ orderId, outTradeNo })
      .update({ data: { status: "paid" } });

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .where({ _id: orderId })
      .update({ data: { status: ORDER_STATUS.PENDING_CONFIRM } });

    console.log("更新支付记录成功", orderId, outTradeNo);

    // 添加订单操作记录
    await _addOrderAction(orderId, ACTION_TYPE.PAY, INITATOR_SOURCE.USER, {
      outTradeNo,
    });
  } catch (error) {
    console.error("更新支付记录失败:", error);
    throw error;
  }
}

// 处理退款成功, 微信云退款回调处理
async function onRefundSuccess(refundRecord) {
  console.log("🚀🚀🚀 onRefundSuccess refundRecord", refundRecord);

  try {
    // 之前已经校验退款记录是否存在或者状态是否正确，这里直接更新退款记录状态
    await db
      .collection(COLLECTIONS.REFUND_RECORD)
      .where({
        outRefundNo: refundRecord.outRefundNo,
        orderId: refundRecord.orderId,
      })
      .update({
        data: {
          status: REFUND_STATUS.COMPLETED,
          updatedAt: Date.now(),
        },
      })
      .then(async (res) => {
        // 这种场景不应该出现，退款回调前已经创建了退款记录
        if (res.stats.updated === 0) {
          console.error(
            "退款时未找到退款记录",
            refundRecord.orderId,
            refundRecord.outRefundNo
          );
        }
      });

    // 获取订单信息
    const orderResult = await db
      .collection(COLLECTIONS.ORDER)
      .doc(refundRecord.orderId)
      .get();

    if (!orderResult.data) {
      console.error("退款时未找到订单", refundRecord.orderId);
      throw new Error("退款时未找到订单");
    }

    const order = orderResult.data;

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(refundRecord.orderId)
      .update({
        data: {
          refundStatus: REFUND_STATUS.COMPLETED,
          updatedAt: Date.now(),
        },
      });

    // 操作记录
    await _addOrderAction(
      refundRecord.orderId,
      ACTION_TYPE.REFUND_COMPLETE,
      INITATOR_SOURCE.USER,
      {
        refundRecordId: refundRecord.outRefundNo,
      }
    );

    try {
      // 退款成功后，更新咨询师钱包和收入明细
      await _refundOrderIncome(order);
    } catch (incomeError) {
      console.error("更新咨询师钱包和收入明细失败:", incomeError);
      // 记录错误但不中断流程
    }

    try {
      // 退款成功后，更新分销关联
      await refundDistributionOrder(order._id);
    } catch (distributionError) {
      console.error("更新分销关联失败:", distributionError);
      // 记录错误但不中断流程
    }

    try {
      // 发送退款成功通知
      await sendRefundCompleteNotification(order);
    } catch (notificationError) {
      console.error("发送退款成功通知失败:", notificationError);
      // 记录错误但不中断流程
    }

    return { returnCode: 0, message: "订单退款成功" };
  } catch (error) {
    console.error("退款失败:", error);
    throw error;
  }
}

// 退款回调失败的处理。
// 目前，简单处理，直接更新退款记录状态为失败。 不处理关联订单
async function onRefundFailed(refundRecord, reason) {
  console.log("🚀🚀🚀 onRefundFailed refundRecord", refundRecord, reason);
  try {
    await db
      .collection(COLLECTIONS.REFUND_RECORD)
      .where({
        outRefundNo: refundRecord.outRefundNo,
        orderId: refundRecord.orderId,
      })
      .update({
        data: {
          status: REFUND_STATUS.FAILED,
          errDesc: reason,
          updatedAt: Date.now(),
        },
      });

    // 记录退款失败操作记录
    await _addOrderAction(
      refundRecord.orderId,
      ACTION_TYPE.REFUND_FAILED,
      INITATOR_SOURCE.USER,
      {
        refundRecordId: refundRecord.outRefundNo,
      }
    );

    // 更新订单状态
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(refundRecord.orderId)
      .update({
        data: { refundStatus: REFUND_STATUS.FAILED, updatedAt: Date.now() },
      });

    // 发送退款失败通知
    await sendRefundFailedNotification(refundRecord);

    return { returnCode: 0, message: "退款失败" };
  } catch (error) {
    console.error("退款失败:", error);
    throw error;
  }
}
async function invokePaymentRefund(payment) {
  try {
    const refundParams = {
      env: ENV_ID,
      functionName: "refundcallback",
      subMchId: SUB_MCH_ID,
      nonceStr: genNonceStr(),
      outTradeNo: payment.outTradeNo,
      outRefundNo: genRefundNo(),
      totalFee: payment.amount * 100,
      refundFee: payment.amount * 100,
    };
    console.log("🚀🚀🚀 invokePaymentRefund refundParams", refundParams);
    await cloud.cloudPay.refund(refundParams);
    return refundParams;
  } catch (error) {
    console.error("退款失败:", error);
    throw error;
  }
}

async function onRefunding(order, source, refundResult) {
  console.log("🚀🚀🚀 onRefunding order", order, source, refundResult);
  try {
    // 提取事务ID（如果有）
    const txId = refundResult._txId;

    await db
      .collection(COLLECTIONS.ORDER)
      .doc(order._id)
      .update({
        data: {
          refundStatus: REFUND_STATUS.PROCESSING,
          updatedAt: Date.now(),
          ...(txId ? { _txId: txId } : {}),
        },
      });

    // 操作记录
    await _addOrderAction(order._id, ACTION_TYPE.REFUND_REFUNDING, source, {
      refundRecordId: refundResult.outRefundNo,
      ...(txId ? { _txId: txId } : {}),
    });

    const refundRecord = {
      orderId: order._id,
      source: source,
      status: REFUND_STATUS.PROCESSING,
      outRefundNo: refundResult.outRefundNo,
      outTradeNo: refundResult.outTradeNo,
      refundFee: refundResult.refundFee,
      totalFee: refundResult.totalFee,
      ...(txId ? { _txId: txId } : {}),
    };

    await db.collection(COLLECTIONS.REFUND_RECORD).add({
      data: { ...refundRecord, createdAt: Date.now() },
    });

    return {
      success: true,
      message: "订单退款中",
      refundNo: refundResult.outRefundNo,
    };
  } catch (error) {
    console.error("退款失败:", error);
    throw error;
  }
}

// 退款时更新咨询师钱包和收入明细
async function _refundOrderIncome(order) {
  console.log("🚀🚀🚀 _refundOrderIncome order", order);

  try {
    const result = await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .where({ id: order.therapistId, orderId: order._id })
      .get();

    if (
      !result.data ||
      result.data.length === 0 ||
      result.data[0].status !== "settled"
    ) {
      console.warn("收入未入账，订单应该是未完成服务而退款", {
        orderId: order._id,
        therapistId: order.therapistId,
        status: result.data?.[0]?.status,
      });
      return; // 不存在收入明细或已退款，直接返回
    }

    const income = result.data[0].amount;

    // 更新收入明细
    await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .doc(result.data[0]._id)
      .update({
        data: {
          status: "refunded",
          updatedAt: Date.now(),
        },
      });

    // 更新咨询师钱包
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: order.therapistId })
      .update({
        data: {
          balance: _.inc(-income),
          totalIncome: _.inc(-income),
          updatedAt: Date.now(),
        },
      });

    console.log("退款更新咨询师钱包和收入明细成功", {
      orderId: order._id,
      therapistId: order.therapistId,
      income: income,
    });

    // 更新总汇总数据
    await db
      .collection(COLLECTIONS.STAT_SUMMARY)
      .where({ id: order.therapistId })
      .update({
        data: {
          "income.total": _.inc(-income),
          "income.refunded": _.inc(income),
          "orders.refunded": _.inc(1),
          updatedAt: Date.now(),
        },
      });
  } catch (error) {
    console.error("退款更新咨询师钱包和收入明细失败:", error);
    throw error;
  }
}

async function _addSchedule(startTime, duration, therapistId) {
  const { beijingDate, timeSlots } = formatBeijingTimeSlots(
    startTime,
    duration
  );

  await db
    .collection(COLLECTIONS.THERAPIST_SCHEDULES)
    .where({
      id: therapistId,
      date: beijingDate,
    })
    .update({
      data: {
        slots: _.addToSet({
          $each: timeSlots,
        }), // 自动去重添加
      },
    })
    .then((res) => {
      // 如果未匹配到文档，执行插入
      if (res.stats.updated === 0) {
        return db.collection(COLLECTIONS.THERAPIST_SCHEDULES).add({
          data: {
            id: therapistId,
            date: beijingDate,
            slots: timeSlots,
          },
        });
      }
      return res;
    });

  // 检查和更新咨询师的今天可用状态/可用状态描述
  try {
    await _updateTherapistTodayAvailable(therapistId);
  } catch (error) {
    console.error("更新咨询师今天可用状态失败:", error);
  }
}

async function _releaseSchedule(startTime, duration, therapistId) {
  console.log(
    "🚀🚀🚀 _releaseSchedule startTime",
    startTime,
    duration,
    therapistId
  );
  const { beijingDate, timeSlots } = formatBeijingTimeSlots(
    startTime,
    duration
  );
  await db
    .collection(COLLECTIONS.THERAPIST_SCHEDULES)
    .where({
      id: therapistId,
      date: beijingDate,
    })
    .update({
      data: {
        slots: _.pullAll(timeSlots),
      },
    });

  // 检查和更新咨询师的今天可用状态/可用状态描述
  try {
    await _updateTherapistTodayAvailable(therapistId);
  } catch (error) {
    console.error("更新咨询师今天可用状态失败:", error);
  }
}

async function _checkExistsOrder(orderId) {
  console.log("🚀🚀🚀 _checkExistsOrder orderId", orderId);
  const result = await db
    .collection(COLLECTIONS.ORDER)
    .where({ _id: orderId })
    .get();
  if (!result.data || result.data.length === 0) {
    throw new Error("订单不存在");
  }
  return result.data[0];
}

async function _updateTherapistTodayAvailable(therapistId) {
  // 1. 获取咨询师workTime
  const therapistServiceRes = await db
    .collection(COLLECTIONS.THERAPIST_SERVICE)
    .where({ id: therapistId })
    .get();
  if (!therapistServiceRes.data || therapistServiceRes.data.length === 0) {
    return;
  }
  const workTime = therapistServiceRes.data[0].workTime;
  if (!workTime) {
    // 没有设置工作时间，直接标记为不可约
    await db
      .collection(COLLECTIONS.THERAPIST)
      .where({ id: therapistId })
      .update({
        data: {
          todayAvailable: false,
          available: "近期不可约",
          updatedAt: Date.now(),
        },
      });
    return;
  }

  // 2. 获取未来7天的排期表
  const now = Date.now();
  const today = new Date(now + 8 * 3600 * 1000); // 北京时间
  today.setUTCHours(0, 0, 0, 0);
  const todayZero = today.getTime() - 8 * 3600 * 1000;
  const sevenDays = [];
  for (let i = 0; i < 7; i++) {
    const d = new Date(todayZero + i * 24 * 60 * 60 * 1000);
    sevenDays.push(todayZero + i * 24 * 60 * 60 * 1000);
  }
  const schedulesRes = await db
    .collection(COLLECTIONS.THERAPIST_SCHEDULES)
    .where({
      id: therapistId,
      date: _.in(sevenDays),
    })
    .get();
  const scheduleMap = {};
  for (const s of schedulesRes.data) {
    scheduleMap[s.date] = s;
  }

  // 3. 判断每天的可约性
  let todayAvailable = false;
  let availableDesc = "近期不可约";
  let foundTomorrow = false;
  let foundWeek = false;
  for (let i = 0; i < 7; i++) {
    const date = sevenDays[i];
    const d = new Date(date + 8 * 3600 * 1000);
    const dayOfWeek = d.getUTCDay(); // 0-6, 0是周日
    // workDays: 1-7, 1是周一
    const workDays = workTime.workDays || [1, 2, 3, 4, 5, 6, 7];
    const mappedDay = dayOfWeek === 0 ? 7 : dayOfWeek; // 1-7
    if (!workDays.includes(mappedDay)) continue;
    // 判断时间段
    const start = workTime.start;
    const end = workTime.end;
    const allSlots = [];
    for (let h = start; h <= end; h++) {
      allSlots.push(h);
    }
    const schedule = scheduleMap[date];
    let availableSlots = allSlots;
    if (schedule && Array.isArray(schedule.slots)) {
      // slots为已被预约的小时，排除掉
      availableSlots = allSlots.filter((h) => !schedule.slots.includes(h));
    }
    if (availableSlots.length > 0) {
      if (i === 0) todayAvailable = true;
      if (i === 1 && !todayAvailable) foundTomorrow = true;
      if (i > 1 && !todayAvailable && !foundTomorrow) foundWeek = true;
      break;
    }
  }
  if (todayAvailable) {
    availableDesc = "今天可约";
  } else if (foundTomorrow) {
    availableDesc = "明天可约";
  } else if (foundWeek) {
    availableDesc = "近期可约（7天内）";
  }
  // 4. 更新咨询师表
  await db
    .collection(COLLECTIONS.THERAPIST)
    .where({ id: therapistId })
    .update({
      data: {
        todayAvailable,
        available: availableDesc,
        updatedAt: Date.now(),
      },
    });
}

// 结算订单收入
async function _settleOrderIncome(order) {
  console.log("🚀🚀🚀 _settleOrderIncome order", order);
  try {
    // 查询收入明细
    const result = await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .where({ id: order.therapistId, orderId: order._id })
      .get();
    if (result.data && result.data.length > 0) {
      console.error("收入明细已存在, 不重复添加", result.data);
      return;
    }
    const income = order.price * ORDER_INCOME_RATE;
    // 增加收入明细
    await db.collection(COLLECTIONS.INCOME_DETAIL).add({
      data: {
        id: order.therapistId,
        orderId: order._id,
        userAvatar: order.userAvatar,
        userName: order.userName,
        userId: order.userId,
        incomeType: 1,
        serviceType: order.serviceType,
        amount: income,
        status: "settled",
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    });

    // 更新咨询师钱包
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: order.therapistId })
      .update({
        data: {
          balance: _.inc(income),
          totalIncome: _.inc(income),
          updatedAt: Date.now(),
        },
      })
      .then((res) => {
        console.log("更新咨询师钱包成功", res);
        if (res.updated === 0) {
          console.error("更新咨询师钱包失败, 咨询师钱包不存在");
          throw new Error("更新咨询师钱包失败, 咨询师钱包不存在");
        }
      });

    // 更新总汇总数据
    await db
      .collection(COLLECTIONS.STAT_SUMMARY)
      .where({ id: order.therapistId })
      .update({
        data: {
          "income.total": _.inc(income),
          "income.completed": _.inc(income),
          "orders.completed": _.inc(1),

          updatedAt: Date.now(),
        },
      });
  } catch (error) {
    console.error("结算订单收入失败:", error);
    throw error;
  }
}

async function _settleDistributionIncome(distributionOrder) {
  console.log(
    "🚀🚀🚀 _settleDistributionIncome distributionOrder",
    distributionOrder
  );
  try {
    // 查询收入明细
    const result = await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .where({
        id: distributionOrder.referrerId,
        orderId: distributionOrder.id,
      })
      .get();
    if (result.data && result.data.length > 0) {
      console.error("收入明细已存在, 不重复添加", result.data);
      return;
    }
    const income = distributionOrder.commission;
    // 增加收入明细
    await db.collection(COLLECTIONS.INCOME_DETAIL).add({
      data: {
        id: distributionOrder.referrerId,
        orderId: distributionOrder.id,
        userAvatar: distributionOrder.inviteeAvatar,
        userName: distributionOrder.inviteeName,
        userId: distributionOrder.inviteeId,
        incomeType: 2,
        serviceType: 0,
        amount: income,
        status: "settled",
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    });

    // 更新咨询师钱包
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: order.therapistId })
      .update({
        data: {
          balance: _.inc(income),
          totalIncome: _.inc(income),
          updatedAt: Date.now(),
        },
      })
      .then((res) => {
        console.log("更新咨询师钱包成功", res);
        if (res.updated === 0) {
          console.error("更新咨询师钱包失败, 咨询师钱包不存在");
          throw new Error("更新咨询师钱包失败, 咨询师钱包不存在");
        }
      });

    // 更新总汇总数据
    await db
      .collection(COLLECTIONS.STAT_SUMMARY)
      .where({ id: order.therapistId })
      .update({
        data: {
          "income.total": _.inc(income),
          "income.distribution": _.inc(income),
          "distribution.total": _.inc(1),
          updatedAt: Date.now(),
        },
      });
  } catch (error) {
    console.error("结算订单收入失败:", error);
    throw error;
  }
}

async function _refundDistributionIncome(distributionOrder) {
  console.log(
    "🚀🚀🚀 _refundDistributionIncome distributionOrder",
    distributionOrder
  );
  try {
    const result = await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .where({
        id: distributionOrder.referrerId,
        orderId: distributionOrder.id,
      })
      .get();

    if (
      !result.data ||
      result.data.length === 0 ||
      result.data[0].status !== "settled"
    ) {
      console.warn("收入未入账，订单应该是未完成服务而退款", {
        orderId: distributionOrder.id,
        therapistId: distributionOrder.referrerId,
        status: result.data?.[0]?.status,
      });
      return; // 不存在收入明细或已退款，直接返回
    }

    const income = result.data[0].amount;

    // 更新收入明细
    await db
      .collection(COLLECTIONS.INCOME_DETAIL)
      .doc(result.data[0]._id)
      .update({
        data: {
          status: "refunded",
          updatedAt: Date.now(),
        },
      });

    // 更新咨询师钱包
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: order.therapistId })
      .update({
        data: {
          balance: _.inc(-income),
          totalIncome: _.inc(-income),
          updatedAt: Date.now(),
        },
      });

    console.log("退款更新咨询师钱包和收入明细成功", {
      orderId: order._id,
      therapistId: order.therapistId,
      income: income,
    });

    // 更新总汇总数据
    await db
      .collection(COLLECTIONS.STAT_SUMMARY)
      .where({ id: order.therapistId })
      .update({
        data: {
          "income.total": _.inc(-income),
          "income.refundedDistribution": _.inc(income),
          "distribution.refunded": _.inc(1),
          updatedAt: Date.now(),
        },
      });
  } catch (error) {
    console.error("退款更新咨询师钱包和收入明细失败:", error);
    throw error;
  }
}

// 退款审核通过，由管理员发起，审核通过后，发起退款，状态变为退款中

async function refundAuditPass(orderId) {
  console.log("🚀🚀🚀 refundAuditPass orderId", orderId);
  try {
    const order = await _checkExistsOrder(orderId);
    // 验证订单状态
    if (order.refundStatus !== REFUND_STATUS.AUDITING) {
      throw new Error("当前订单状态不允许审核");
    }

    // 记录退款审核通过操作记录
    await _addOrderAction(
      orderId,
      ACTION_TYPE.REFUND_AUDIT_PASS,
      INITATOR_SOURCE.ADMIN
    );
    /** 这里不直接调用_cancelOrder，因为退款审核通过时，
     * 订单的主状态可能是PENDING_START，IN_PROGRESS，COMPLETED
     * 所以这里不更新主状态
     */
    // 执行更新排期信息
    await _releaseSchedule(order.startTime, order.duration, order.therapistId);

    const cancelResult = await _handleRefundOrder(order, INITATOR_SOURCE.ADMIN);

    if (cancelResult.warning) {
      return {
        success: true,
        message: "退款审核通过，但退款处理失败，请手动处理退款",
        warning: cancelResult.message,
        error: cancelResult.error,
      };
    }

    return { message: "退款审核通过成功" };
  } catch (error) {
    console.error("退款审核通过失败:", error);
    throw error;
  }
}
// 退款审核不通过

async function refundAuditNotPass(orderId) {
  console.log("🚀🚀🚀 refundAuditNotPass orderId", orderId);
  try {
    const order = await _checkExistsOrder(orderId);
    // 验证订单状态
    if (order.refundStatus !== REFUND_STATUS.AUDITING) {
      throw new Error("当前订单状态不允许退款");
    }

    // // 更新订单状态，根据当前时间和预约时间来确定状态
    // let status = ORDER_STATUS.PENDING;
    // const now = Date.now();
    // const appointmentTime = order.startTime;
    // const diffTime = appointmentTime - now;
    // if (diffTime <= 0) {
    //   status = ORDER_STATUS.IN_PROGRESS;
    // }
    // const endTime = order.startTime + order.duration * 60 * 1000;
    // if (endTime - now <= 0) {
    //   status = ORDER_STATUS.COMPLETED;
    // }
    await db
      .collection(COLLECTIONS.ORDER)
      .doc(orderId)
      .update({
        data: {
          refundStatus: REFUND_STATUS.REJECTED,
          updatedAt: Date.now(),
        },
      });

    await _addOrderAction(
      orderId,
      ACTION_TYPE.REFUND_AUDIT_REJECT,
      INITATOR_SOURCE.ADMIN
    );

    return { message: "退款审核不通过成功" };
  } catch (error) {
    console.error("退款审核不通过失败:", error);
    throw error;
  }
}
// 退款完成。 由管理员发起，代表手工退款后，更新相关状态。正常情况下，是审核通过后自动发起退款，不走这个接口

async function refundComplete(refundRecord) {
  console.log("🚀🚀🚀 refundComplete refundRecord", refundRecord);
  try {
    await onRefundSuccess(refundRecord);

    return { message: "退款完成成功" };
  } catch (error) {
    console.error("退款完成失败:", error);
    throw error;
  }
}
async function _handleRefundOrder(order, source, txId = null) {
  console.log("🚀🚀 _handleRefundOrder order", order, source);
  //如果已支付，则需要退款。 检查支付表，如果支付表存在，则需要退款
  try {
    const paymentResult = await db
      .collection(COLLECTIONS.PAYMENT)
      .where({ orderId: order._id, status: "paid" }) // 支付状态为已支付,注意一个订单能多次支付记录，但只有一个支付记录是已支付
      .get();
    if (!paymentResult.data || paymentResult.data.length === 0) {
      console.log(
        "🚀🚀🚀 _handleRefundOrder order 未支付，无需退款",
        order._id
      );
      // 没有支付过，直接返回
      return { success: true, message: "订单未支付，无需退款", warning: true };
    }
    const payment = paymentResult.data[0];

    // 检查订单当前的退款状态
    if (
      order.refundStatus &&
      order.refundStatus !== REFUND_STATUS.NONE &&
      order.refundStatus !== REFUND_STATUS.REJECTED &&
      order.refundStatus !== REFUND_STATUS.FAILED
    ) {
      // 如果退款状态不是"无"、"拒绝"或"失败"，说明退款正在处理中或已完成
      throw new Error(
        `订单退款${
          REFUND_STATUS.COMPLETED === order.refundStatus ? "已完成" : "处理中"
        }，不能重复退款`
      );
    }

    // 检查是否有退款记录，如果有，则需要判断退款记录状态
    const refundRecordResult = await db
      .collection(COLLECTIONS.REFUND_RECORD)
      .where({
        orderId: order._id,
      })
      .get();
    if (
      refundRecordResult.data &&
      refundRecordResult.data.length > 0 &&
      refundRecordResult.data[0].status !== REFUND_STATUS.FAILED &&
      refundRecordResult.data[0].status !== REFUND_STATUS.NONE &&
      refundRecordResult.data[0].status !== REFUND_STATUS.REJECTED
    ) {
      console.error("订单退款中或已退款，不能再次退款。");
      throw new Error("订单退款中或已退款，不能再次退款。");
    }

    try {
      // 调用退款接口，退款成功后，更新订单状态
      const refundResult = await invokePaymentRefund(payment);

      // 如果有事务ID，添加到退款结果中
      if (txId) {
        refundResult._txId = txId;
      }

      // 先预制退款中，等待退款回调
      return await onRefunding(order, source, refundResult);
    } catch (refundError) {
      console.error("调用退款接口失败:", refundError);

      // 记录退款失败信息，但不影响订单取消流程
      await _addOrderAction(order._id, ACTION_TYPE.REFUND_FAILED, source, {
        source,
        reason: "退款接口调用失败",
        detail: refundError.message,
        _txId: txId,
      });

      // 返回退款失败信息，但订单仍然取消
      return {
        success: true,
        warning: true,
        message: "订单已取消，但退款失败，请联系客服处理",
        error: refundError.message,
      };
    }
  } catch (error) {
    console.error("退款处理失败:", error);
    throw error;
  }
}

module.exports = {
  _addOrderAction,
  onPaid,
  onRefundSuccess,
  onRefundFailed,
  invokePaymentRefund,
  onRefunding,
  _refundOrderIncome,
  _addSchedule,
  _releaseSchedule,
  _checkExistsOrder,
  _settleOrderIncome,
  _settleDistributionIncome,
  _refundDistributionIncome,
  refundAuditPass,
  refundAuditNotPass,
  refundComplete,
  _handleRefundOrder,
}; // 处理退款
