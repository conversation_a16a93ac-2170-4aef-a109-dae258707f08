import { ORDER_TABS_THERAPIST } from '@constants/config';
import { ScrollView, Text, View } from '@tarojs/components';
import React from 'react';

interface OrderStatusTabsProps {
  activeKey: string;
  onChange: (key: string) => void;
  tabs: typeof ORDER_TABS_THERAPIST;
}

const OrderStatusTabs: React.FC<OrderStatusTabsProps> = ({
  activeKey,
  onChange,
  tabs,
}) => {
  return (
    <ScrollView
      className='w-full'
      scrollX
      enableFlex
      style={{ whiteSpace: 'nowrap' }}
    >
      <View className='flex flex-row px-2 py-3 space-x-2'>
        {tabs.map((tab) => (
          <View
            key={tab.key}
            className={`px-2 py-1 rounded-full text-sm cursor-pointer transition-all ${
              activeKey === tab.key
                ? 'border border-primarylight text-default font-semibold'
                : 'text-secondary'
            }`}
            onClick={() => onChange(tab.key)}
          >
            <Text>{tab.title}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

export default OrderStatusTabs;
