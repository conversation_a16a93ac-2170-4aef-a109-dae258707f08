import { USER_ROLE } from '@model/user.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  userSig: string | null;
  openid: string | null;
  role: USER_ROLE[];
  currentRole: USER_ROLE;
  needReload: boolean;

  selectedTab: number;
}

interface Action {
  setUserSig: (userSig: string | null) => void;
  setOpenid: (openid: string | null) => void;
  setRole: (role: USER_ROLE[]) => void;
  setCurrentRole: (role: USER_ROLE) => void;
  isLoggedIn: () => boolean;
  reset: () => void;
  setNeedReload: (needReload: boolean) => void;
  setSelectedTab: (selectedTab: number) => void;
}

const initialState: State = {
  userSig: null,
  openid: null,
  role: [],
  currentRole: USER_ROLE.USER,
  needReload: true, // 首次加载
  selectedTab: 0,
};

const globalStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,

        setUserSig: (userSig) => set({ userSig }),
        setOpenid: (openid) => set({ openid }),
        setRole: (role) => set({ role }),
        setCurrentRole: (role) => {
          set({ currentRole: role });
        },
        isLoggedIn: () => !!get().openid,
        reset: () => set(initialState),
        setNeedReload: (needReload) => set({ needReload }),
        setSelectedTab: (selectedTab) => set({ selectedTab }),
      }),
      {
        name: StorageSceneKey.GLOBLE,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 用户列表
        partialize: (state) => ({
          role: state.role,
          currentRole: state.currentRole,
        }),
      }
    )
  )
);

export const useGlobalStore = createSelectors(globalStore);
