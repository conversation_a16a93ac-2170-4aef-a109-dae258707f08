import {
  Button,
  Cell,
  CellGroup,
  Image,
  Loading,
  Radio,
  RadioGroup,
  Toast,
} from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import ResultPopup from '@components/common/result-popup';
import PriceDisplay from '@components/order-card/PriceDisplay';
import { ILLUSTRATION_MAP } from '@constants/assets';
import { SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_PAID } from '@constants/config';
import { paymentActions } from '@core/actions/payment.action';
import { useOrderStoreSelector } from '@stores/order.store';
import { PaymentResultStatus, usePaymentStore } from '@stores/payment.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { orderRouter } from '@utils/router';
import { useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

interface PaymentOption {
  id: string;
  name: string;
  icon: string;
}

const paymentOptions: PaymentOption[] = [
  {
    id: 'wechat',
    name: '微信支付',
    icon: '/assets/icons/payment_wechat.png',
  },
  {
    id: 'alipay',
    name: '支付宝',
    icon: '/assets/icons/payment_alipay.png',
  },
];

export default function OrderPaymentPage() {
  const router = useRouter();
  const { orderId } = router.params;

  const [selectedPayment, setSelectedPayment] = useState<string>('wechat');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showResultPopup, setShowResultPopup] = useState(false);
  const [resultPopupConfig, setResultPopupConfig] = useState({
    type: 'success' as 'success' | 'error',
    title: '',
    content: '',
    buttonText: '',
    illustration: '',
  });

  //从订单store中订阅价格
  console.log('OrderPaymentPage orderId', orderId);
  const order = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId ?? ''))
  );
  console.log('useOrderStore order', order);

  // 使用 selector 订阅支付状态
  const {
    loading: payLoading,
    error: payError,
    payStatus,
    payResult,
  } = usePaymentStore();

  const handlePay = async () => {
    console.log('🚀🚀🚀 handlePay order', order);
    if (!order || !order._id || !order.price) {
      Toast.show('订单不存在');
      return;
    }

    // 订阅通知消息
    await Taro.requestSubscribeMessage({
      tmplIds: SUBSCRIBE_MESSAGE_TEMPLATE_IDS_ON_PAID,
      entityIds: [],
      success: (res) => {
        console.log('🚀🚀🚀 requestSubscribeMessage success', res);
      },
      fail: (err) => {
        console.warn('🚀🚀🚀 requestSubscribeMessage fail', err);
      },
    });

    setIsProcessing(true);

    try {
      if (selectedPayment === 'wechat') {
        // 调用微信支付
        await paymentActions.wechatPay({
          orderId: order._id,
          amount: order.price,
          description: '订单支付',
          paymentMethod: selectedPayment,
        });
      } else if (selectedPayment === 'alipay') {
        // 支付宝支付逻辑
        Taro.showToast({
          title: '暂不支持支付宝支付',
          icon: 'none',
        });
        setIsProcessing(false);
      }
    } catch (err) {
      // 捕获到的错误直接显示弹窗
      setIsProcessing(false);
      setResultPopupConfig({
        type: 'error',
        title: '支付失败',
        content: (err as Error).message || '支付过程中出现错误，请稍后重试',
        buttonText: '重新支付',
        illustration: ILLUSTRATION_MAP.PAYMENT_FAILED,
      });
      setShowResultPopup(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // 监听支付状态变化
  useEffect(() => {
    console.log('🚀🚀🚀 payStatus', payStatus);
    setIsProcessing(false);
    setShowResultPopup(false);
    if (!orderId) {
      return;
    }

    if (payStatus === PaymentResultStatus.Success) {
      // 支付成功，显示结果弹窗
      setIsProcessing(false);
      setResultPopupConfig({
        type: 'success',
        title: '支付成功',
        content: '您已成功完成订单支付，请继续填写就诊信息',
        buttonText: '去填写就诊信息',
        illustration: ILLUSTRATION_MAP.PAYMENT_SUCCESS,
      });
      setShowResultPopup(true);
    } else if (payStatus === PaymentResultStatus.Failed) {
      // 支付失败，显示结果弹窗
      setIsProcessing(false);
      Taro.showToast({
        title: payResult || '支付失败，请稍后重试',
        icon: 'error',
      });
    }
  }, [payStatus]);

  // 处理取消
  const handleCancel = () => {
    // 提示用户需要在15分钟内完成付款
    Taro.showModal({
      title: '支付提醒',
      content: '订单需要在15分钟内完成付款，否则将自动取消',
      confirmText: '我知道了',
      success: (res) => {
        if (res.confirm) {
          // 跳转到订单详情
          orderRouter.detail(orderId!, true);
        }
      },
    });
  };

  // 处理支付结果弹窗按钮点击
  const handleResultButtonClick = () => {
    if (resultPopupConfig.type === 'success') {
      // 成功时跳转到信息填写页面
      orderRouter.submitInfo(orderId!, true, 'payment');
    } else {
      // 失败时关闭弹窗，允许用户重新支付
      setShowResultPopup(false);
    }
  };

  console.log('🚀🚀🚀 orderId', orderId);
  if (!orderId) {
    return (
      <PageLol navigationProps={{ title: '订单支付' }}>
        <View className='container  py-4'>
          <Text>订单不存在</Text>
        </View>
      </PageLol>
    );
  }
  return (
    <PageLol
      navigationProps={{
        title: '订单支付',
        showBackButton: true,
      }}
      loading={payLoading}
      error={payError || null}
    >
      <View className='container  py-4'>
        <View className='flex flex-col items-start px-4 w-full'>
          <Text className='block text-lg font-semibold'>选择支付方式</Text>
          <View className='flex flex-row items-center justify-between mt-2 w-full'>
            <Text className='text-sm text-secondary '>订单号: {orderId}</Text>
            <View className='flex flex-row items-center justify-end flex-1'>
              <PriceDisplay price={order?.price ?? 0} />
            </View>
          </View>
        </View>
        {/* 支付方式 */}
        <View className='mt-8 mb-8 w-full'>
          <RadioGroup value={selectedPayment}>
            <CellGroup inset>
              {paymentOptions.map((option) => (
                <Cell
                  key={option.id}
                  size='large'
                  title={option.name}
                  renderIcon={
                    <Image
                      src={option.icon}
                      height='32px'
                      fit='heightFix'
                      className='mr-4'
                    />
                  }
                  renderRightIcon={<Radio name={option.id} />}
                  onClick={() => setSelectedPayment(option.id)}
                />
              ))}
            </CellGroup>
          </RadioGroup>
        </View>

        <View className='notice bg-warning-light p-4 rounded-md mb-4'>
          <Text className='text-sm text-warning-dark'>
            请在15分钟内完成付款，否则订单将自动取消
          </Text>
        </View>

        {/* 底部按钮固定在页面底部 */}
        <View className='fixed-bottom p-4 flex gap-3'>
          <Button
            plain
            hairline
            round
            type='primary'
            className='flex-1'
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button
            type='primary'
            round
            className='flex-1'
            onClick={handlePay}
            disabled={isProcessing}
          >
            {isProcessing ? <Loading color='#ffffff' /> : '立即支付'}
          </Button>
        </View>

        {/* 支付结果弹窗 */}
        <ResultPopup
          show={showResultPopup}
          type={resultPopupConfig.type}
          title={resultPopupConfig.title}
          illustration={resultPopupConfig.illustration}
          content={resultPopupConfig.content}
          buttonText={resultPopupConfig.buttonText}
          onButtonClick={handleResultButtonClick}
          closeOnClickOverlay={false}
        />
      </View>
    </PageLol>
  );
}
