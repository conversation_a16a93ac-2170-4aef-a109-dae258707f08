import { Icon } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import StatisticCard from '@components/StatisticCard';
import { incomeActions } from '@core/actions/income.action';
import { TherapistMonthlyStat } from '@model/income.model';
import { ServiceType } from '@model/service.interface';
import IncomeItem from '@pages/sub-packages/wallet/components/IncomeItem';
import { useIncomeStore } from '@stores/income.store';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { appRouter } from '@utils/router';
import { formatMonth, formatMonth2, formatTime2 } from '@utils/time';
import React, { useEffect, useMemo, useState } from 'react';

// 柱状图组件
interface BarChartProps {
  data: TherapistMonthlyStat[];
  selectedIndex: number;
  setSelectedIndex: (index: number) => void;
}

interface CompositionChartProps {
  data: TherapistMonthlyStat;
}

const CompositionChart: React.FC<CompositionChartProps> = ({ data }) => {
  return (
    <View className='flex flex-col gap-4 '>
      {/* 总收入 */}
      <View className='flex flex-row items-center'>
        <Text className='text-base font-bold text-default '>
          ￥{data.income.total}
        </Text>
        <Text
          className={`text-xs   ml-1 text-default ${
            data.income.increase > 0 ? 'text-green-500' : 'text-red-500'
          }`}
        >
          {data.income.increase > 0 ? '+' : ''}
          {data.income.increase}%
        </Text>
      </View>

      {/* 收入构成进度条 */}
      <View className='flex gap-2 w-full'>
        <View
          className='bg-primary  h-1.5 rounded-full'
          style={{
            width: `${
              (data.serviceTypes[ServiceType.VIDEO].amount /
                data.income.total) *
              100
            }%`,
          }}
        />
        <View
          className='bg-secondary  h-1.5 rounded-full'
          style={{
            width: `${
              (data.serviceTypes[ServiceType.FACE_TO_FACE].amount /
                data.income.total) *
              100
            }%`,
          }}
        />
        <View
          className='bg-tertiary  h-1.5 rounded-full'
          style={{
            width: `${(data.income.distribution / data.income.total) * 100}%`,
          }}
        />
        <View
          className='bg-warning  h-1.5 rounded-full'
          style={{
            width: `${(data.income.refunded / data.income.total) * 100}%`,
          }}
        />
      </View>

      {/* 收入构成 */}
      <View className='flex w-full justify-between items-center'>
        {/* 视频问诊收入 */}
        <View className='flex flex-row items-start '>
          <View className='w-1.5 h-1.5 bg-primary rounded-full mt-1.5 mr-1'></View>
          <View className='flex flex-col'>
            <Text className='text-sm text-default'>视频问诊</Text>
            <Text className='text-sm font-bold text-secondary'>
              {data.serviceTypes[ServiceType.VIDEO].amount || 0}
            </Text>
          </View>
        </View>

        {/* 当面问诊收入 */}
        <View className='flex flex-row items-start '>
          <View className='w-1.5 h-1.5 bg-secondary rounded-full mt-1.5 mr-1'></View>
          <View className='flex flex-col'>
            <Text className='text-sm text-default'>当面问诊</Text>
            <Text className='text-sm font-bold text-secondary'>
              {data.serviceTypes[ServiceType.FACE_TO_FACE].amount || 0}
            </Text>
          </View>
        </View>

        {/* 邀请收入 */}
        <View className='flex flex-row items-start '>
          <View className='w-1.5 h-1.5 bg-tertiary rounded-full mt-1.5 mr-1'></View>
          <View className='flex flex-col'>
            <Text className='text-sm text-default'>邀请收入</Text>
            <Text className='text-sm font-bold text-secondary'>
              {data.income.distribution || 0}
            </Text>
          </View>
        </View>

        {/* 退款 */}
        <View className='flex flex-row items-start '>
          <View className='w-1.5 h-1.5 bg-warning rounded-full mt-1.5 mr-1'></View>
          <View className='flex flex-col'>
            <Text className='text-sm text-default'>退款</Text>
            <Text className='text-sm font-bold text-secondary'>
              {data.income.refunded || 0}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const BarChart: React.FC<BarChartProps> = ({
  data,
  selectedIndex,
  setSelectedIndex,
}) => {
  // 计算最大值用于比例计算，设置最小高度
  const maxIncome = Math.max(...data.map((item) => item.income.total));
  const minHeight = 20; // 最小高度百分比

  // 获取选中月份的数据
  const selectedData = data[selectedIndex];

  return (
    <View className='flex flex-col'>
      {/* 柱状图 */}
      <View className='flex flex-row items-end justify-between h-48 px-2 mb-4'>
        {data.map((item, index) => {
          const height =
            maxIncome > 0
              ? Math.max((item.income.total / maxIncome) * 80, minHeight)
              : minHeight;
          const isCurrentMonth = index === selectedIndex;
          const isSelected = selectedIndex === index;

          return (
            <View
              key={index}
              className='flex flex-col items-center justify-end flex-1 h-full'
            >
              {/* 柱状图 */}
              <View
                className={`w-4 rounded-t-sm mb-2  ${
                  isSelected ? 'ring-2 ring-blue-300' : ''
                }`}
                style={{
                  height: `${height}%`,
                  background: isCurrentMonth
                    ? 'var(--color-primary)'
                    : 'var(--color-primarylight)',
                }}
                onClick={() => setSelectedIndex(index)}
              />

              {/* 月份标签 */}
              <Text className='text-xs text-gray-500'>
                {formatMonth(item.startDate)}
              </Text>
            </View>
          );
        })}
      </View>

      {/* 选中月份的详情数据 */}
      {selectedData && <CompositionChart data={selectedData} />}
    </View>
  );
};

const MonthlyChart: React.FC<{ incomeChart: TherapistMonthlyStat[] }> = ({
  incomeChart,
}) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(
    incomeChart.length - 1
  );

  return (
    <View className='mx-4 mt-4 bg-bg rounded-xl p-4'>
      {/* 月度统计 标题+选择月份*/}
      <View className='flex flex-row justify-between items-center mb-4 w-full'>
        <Text className='text-base font-bold'>月度统计</Text>
        {/* 选择月份 */}
        {/* 月份选择下拉菜单按钮 */}
        <View
          className='flex flex-row items-center rounded-full px-2 py-1 border border-border'
          onClick={() => {
            Taro.showActionSheet({
              itemList: incomeChart.map((item) => formatMonth2(item.startDate)),
              success: async (res) => {
                const index = res.tapIndex;
                if (index >= 0 && index < incomeChart.length) {
                  setSelectedIndex(index);
                }
              },
            });
          }}
        >
          <Text className='text-sm text-default'>
            {formatMonth2(incomeChart[selectedIndex].startDate)}
          </Text>
          <Icon name='arrow-down' size='12px' />
        </View>
      </View>

      {/* 过去6个月的月度收入柱状图 */}
      <View className=' p-3 rounded-lg'>
        {/* 进度条构成图组件 */}
        <BarChart
          data={incomeChart}
          selectedIndex={selectedIndex}
          setSelectedIndex={setSelectedIndex}
        />
      </View>
    </View>
  );
};
/**
 * 咨询师收益中心页面
 */
export default function IncomePage() {
  const wallet = useIncomeStore.use.wallet();
  const incomeList = useIncomeStore.use.incomeList();
  const statSummary = useIncomeStore.use.statSummary();
  const incomeChart = useIncomeStore.use.incomeChart();

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        incomeActions.loadMyWallet(true),
        incomeActions.fetchIncomeList({ forceRefresh: true }),
        incomeActions.fetchStatSummary(true),
        incomeActions.fetchIncomeChart(true),
      ]);
    };
    loadData();
  }, []);

  const renderIncomeList = useMemo(() => {
    return (
      <View className='mx-4 mt-4 bg-bg rounded-xl overflow-hidden'>
        <View className='flex flex-row px-4 py-3 items-center justify-between'>
          <Text className='text-base font-bold'>收入明细</Text>
          <View
            className='flex flex-row items-center flex-1 justify-end ml-4'
            onClick={() => {
              appRouter.incomeListTherapist();
            }}
          >
            <Icon name='arrow' size='16px' />
          </View>
        </View>

        <View className='flex flex-col'>
          {incomeList.map((item, index) => (
            <IncomeItem
              income={item}
              key={`${item.id}-${item.createdAt}-${index}`}
            />
          ))}
        </View>
      </View>
    );
  }, [incomeList]);

  return (
    <PageLol
      navigationProps={{
        title: '收入中心',
        showBackButton: true,
        showSearch: false,
      }}
    >
      <View className='min-h-screen pb-16'>
        {/* 分享数据概览卡片 */}
        <StatisticCard
          className=''
          title={statSummary?.income.total || 0}
          subTitle='累计收入(￥)'
          renderRight={
            <Text className='text-xs text-secondary'>
              自{formatTime2(wallet?.createdAt || 0)}
            </Text>
          }
          values={[
            {
              title: '咨询收入',
              value: statSummary?.income.completed || 0,
            },
            {
              title: '邀请收入',
              value: statSummary?.income.distribution || 0,
              onClick: () => {
                appRouter.distributionDashboardTherapist();
              },
            },
            {
              title: '余额',
              value: wallet?.balance || 0,
              color: 'green',
            },
          ]}
        />

        {incomeChart && incomeChart.length > 0 && (
          <MonthlyChart incomeChart={incomeChart} />
        )}

        {/* 收入明细列表 */}
        {incomeList && incomeList.length > 0 && renderIncomeList}
      </View>
    </PageLol>
  );
}
