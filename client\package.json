{"name": "miniapp-template", "version": "1.0.0", "private": true, "description": "Taro3.6 + React + TypeScript + tailwindcss + zustand + @antmjs/vantui3.x 微信/支付宝小程序模板", "keywords": ["taro", "react", "tailwindcss", "zustand", "vant", "@amtmjs/vantui", "less", "weapp", "wx", "alipay"], "templateInfo": {"name": "Ta<PERSON>微信/支付宝小程序", "typescript": true, "css": "less"}, "scripts": {"dev:weapp": "npm run build:weapp -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "build:weapp": "taro build --type weapp", "build:alipay": "taro build --type alipay", "postinstall": "weapp-tw patch", "lint": "eslint src/**/*.js"}, "browserslist": {"production": ["ios >= 9"], "development": ["last 1 version"]}, "author": "zhu<PERSON><PERSON><PERSON> <email <EMAIL>>", "dependencies": {"@antmjs/mini-fix": "^2.4.1", "@antmjs/plugin-global-fix": "^2.4.1", "@antmjs/vantui": "^3.6.4", "@babel/runtime": "^7.27.4", "@tarojs/components": "^3.6.37", "@tarojs/helper": "^3.6.37", "@tarojs/plugin-framework-react": "^3.6.37", "@tarojs/plugin-platform-weapp": "^3.6.37", "@tarojs/react": "^3.6.37", "@tarojs/router": "^3.6.37", "@tarojs/runtime": "^3.6.37", "@tarojs/shared": "^3.6.37", "@tarojs/taro": "^3.6.37", "@vant/area-data": "^2.0.0", "dayjs": "^1.11.13", "echarts-taro3-react": "^1.0.13", "immer": "^10.1.1", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss-safe-area": "^0.6.0", "umtrack-wx": "^2.8.0", "zustand": "^5.0.5"}, "devDependencies": {"@antmjs/plugin-mini-fix": "^2.4.1", "@babel/core": "^7.27.4", "@squoosh/lib": "^0.5.3", "@tarojs/cli": "^3.6.37", "@tarojs/taro-loader": "^3.6.37", "@tarojs/webpack5-runner": "^3.6.37", "@types/lodash": "^4.17.17", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/webpack-env": "^1.18.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "babel-plugin-import": "^1.13.8", "babel-plugin-lodash": "^3.3.4", "babel-preset-taro": "^3.6.37", "eslint": "^8.57.1", "eslint-config-taro": "^3.6.37", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "image-compressor-plugin": "^1.0.4", "image-minimizer-webpack-plugin": "^4.1.3", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^8.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-optipng": "^8.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-svgo": "^11.0.1", "postcss": "^8.5.4", "stylelint": "^14.16.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "weapp-tailwindcss": "^3.7.0", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2"}}