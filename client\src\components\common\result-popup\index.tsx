import { Button, Overlay } from '@antmjs/vantui';
import { ILLUSTRATION_MAP } from '@constants/assets';
import { Image, Text, View } from '@tarojs/components';
import { ReactNode } from 'react';

export interface ResultPopupProps {
  /** 是否显示 */
  show: boolean;
  /** 标题 */
  title?: string;
  /** 结果类型 */
  type?: 'success' | 'error' | 'warning' | 'info';
  /** 插图 */
  illustration?: string;
  /** 内容 */
  content?: ReactNode;
  /** 按钮文本 */
  buttonText?: string;
  /** 按钮点击事件 */
  onButtonClick?: () => void;

  /** 按钮2文本 */
  button2Text?: string;
  /** 按钮2点击事件 */
  onButton2Click?: () => void;

  /** 点击遮罩层是否关闭 */
  closeOnClickOverlay?: boolean;
  /** 关闭事件 */
  onClose?: () => void;
}

const ResultPopup: React.FC<ResultPopupProps> = ({
  show,
  title,
  type = 'success',
  illustration,
  content,
  buttonText,
  onButtonClick,
  button2Text,
  onButton2Click,
  closeOnClickOverlay = true,
  onClose,
}) => {
  return (
    <Overlay
      show={show}
      zIndex={1000}
      onClick={() => {
        if (closeOnClickOverlay) {
          onClose?.();
        }
      }}
    >
      <View className='fixed inset-0 flex items-center justify-center'>
        <View
          className='bg-white rounded-2xl w-[80%] max-w-[600px] p-6 flex flex-col items-center'
          onClick={(e) => e.stopPropagation()}
        >
          {/* 插图, 如果未提供，根据 type 显示不同的插图 */}
          {illustration ? (
            <Image
              src={illustration}
              className='w-32 h-32 mb-4'
              mode='aspectFit'
            />
          ) : (
            <Image
              src={
                type === 'success'
                  ? ILLUSTRATION_MAP.COMMON_COMPLETE
                  : type === 'error'
                  ? ILLUSTRATION_MAP.COMMON_FAILED
                  : type === 'warning'
                  ? ILLUSTRATION_MAP.COMMON_WARNING
                  : type === 'info'
                  ? ILLUSTRATION_MAP.COMMON_INFO
                  : ILLUSTRATION_MAP.COMMON_COMPLETE
              }
              className='w-32 h-32 mb-4'
              mode='aspectFit'
            />
          )}

          {title && (
            <Text className='text-xl font-bold text-center mb-4'>{title}</Text>
          )}

          {content && (
            <View className='text-center text-gray-600 mb-6'>{content}</View>
          )}

          {buttonText && (
            <Button
              type='primary'
              block
              round
              className='w-full'
              onClick={onButtonClick}
            >
              {buttonText}
            </Button>
          )}

          {button2Text && (
            <Button
              type='primary'
              block
              plain
              round
              className='w-full mt-2'
              onClick={onButton2Click}
            >
              {button2Text}
            </Button>
          )}
        </View>
      </View>
    </Overlay>
  );
};

export default ResultPopup;
