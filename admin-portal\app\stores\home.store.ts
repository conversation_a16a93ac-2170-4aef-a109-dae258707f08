import { LearningCard } from '@model/course.interface';
import { Banner, Notice, OrderToday } from '@model/home.interface';
import { therapist_summary } from '@model/therapist.interface';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';

interface State {
  notices: Notice[] | null;
  banners: Banner[] | null;
  recommendedTherapists: therapist_summary[] | null;
  orderToday: OrderToday | null;
  recommendedCourses: LearningCard[] | null;
  loading: boolean;
}

interface Action {
  setNotices: (notices: Notice[]) => void;
  setBanners: (banners: Banner[]) => void;
  setRecommendedTherapists: (
    recommendedTherapists: therapist_summary[]
  ) => void;
  setOrderToday: (orderToday: OrderToday) => void;
  setRecommendedCourses: (recommendedCourses: LearningCard[]) => void;
  setLoading: (loading: boolean) => void;
}

const homeStore = create<State & Action>()(
  immer((set) => ({
    notices: null,
    banners: null,
    recommendedTherapists: null,
    orderToday: null,
    recommendedCourses: null,
    loading: false,
    setNotices: (notices) => set({ notices }),
    setBanners: (banners) => set({ banners }),
    setRecommendedTherapists: (recommendedTherapists) =>
      set({ recommendedTherapists }),
    setOrderToday: (orderToday) => set({ orderToday }),
    setLoading: (loading) => set({ loading }),
    setRecommendedCourses: (recommendedCourses) => set({ recommendedCourses }),
  }))
);

export const useHomeStore = createSelectors(homeStore);
