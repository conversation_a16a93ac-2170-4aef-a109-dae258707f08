import { PsychologicalTestSummary } from "@/app/model/measure.model";
import {
  therapist_extend,
  therapist_summary,
} from "@/app/model/therapist.interface";
import { callCloudFunction } from "./wxcloud";

/**
 * 获取首页推荐咨询师列表
 */
export async function getHomeTherapistList() {
  try {
    const result = await callCloudFunction(
      "admin-portal",
      "getRecommendTherapistList"
    );

    if (result.success && result.data) {
      return result.data as unknown as therapist_summary[];
    }

    throw new Error(result.message || "获取咨询师列表失败");
  } catch (error) {
    console.error("获取咨询师列表失败:", error);
    // 返回模拟数据，实际环境中应该返回空数组或者抛出异常
    return {
      list: [],
      total: 0,
    };
  }
}

export async function getPsychologicalTestSummaryList() {
  try {
    const result = await callCloudFunction(
      "admin-portal",
      "getRecommendPsychologicalTestSummaryList"
    );

    if (result.success && result.data) {
      return result.data as unknown as PsychologicalTestSummary[];
    }

    throw new Error(result.message || "获取心理测量工具列表失败");
  } catch (error) {
    console.error("获取心理测量工具列表失败:", error);
    return {
      list: [],
      total: 0,
    };
  }
}
/**
 * 获取咨询师列表
 */
export async function getTherapistList(params?: {
  page?: number;
  pageSize?: number;
  specialty?: string;
  priceRange?: string;
  experience?: string;
  keyword?: string;
}) {
  try {
    const result = await callCloudFunction(
      "therapist",
      "getTherapistList",
      params
    );

    if (result.success) {
      return result.data as unknown as therapist_summary[];
    }

    throw new Error(result.message || "获取咨询师列表失败");
  } catch (error) {
    console.error("获取咨询师列表失败:", error);
    // 返回模拟数据，实际环境中应该返回空数组或者抛出异常
    return {
      list: [],
      total: 0,
    };
  }
}

/**
 * 获取咨询师详情
 */
export async function getTherapistDetail(therapistId: string) {
  try {
    const result = await callCloudFunction("therapist", "getTherapistDetail", {
      therapistId,
    });

    if (result.success) {
      return result.data as unknown as therapist_extend;
    }

    throw new Error(result.message || "获取咨询师详情失败");
  } catch (error) {
    console.error("获取咨询师详情失败:", error);
    return null;
  }
}
