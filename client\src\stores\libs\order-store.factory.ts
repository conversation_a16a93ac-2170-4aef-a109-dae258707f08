import { OrderListParams, Pagination } from '@core/api';
import { Order_action, Order_summary } from '@model/order.interface';
import { create } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface OrderState {
  orders: Order_summary[];
  orderActions: Order_action[];
  currentOrderId: string | null;
  loading: boolean;
  loadingText: string | null;
  error: string | null;
  pagination: Pagination | null;
  filters: OrderListParams;
}

export interface OrderAction {
  setOrders: (orders: Order_summary[]) => void;
  setCurrentOrder: (orderId: string | null) => void;
  setLoading: (loading: boolean) => void;
  setLoadingText: (loadingText: string | null) => void;
  setError: (error: string | null) => void;
  setPagination: (pagination: Pagination | null) => void;
  setFilters: (filters: OrderListParams) => void;
  addOrder: (order: Order_summary) => void;
  updateOrder: (order: Order_summary) => void;
  removeOrder: (orderId: string) => void;
  getOrderById: (id: string) => Order_summary | undefined;
  setOrderActions: (actions: Order_action[]) => void;
  getOrderActions: (orderId: string) => Order_action[];
  reset: () => void;
}

const initialState: OrderState = {
  orders: [],
  orderActions: [],
  currentOrderId: null,
  loading: false,
  loadingText: null,
  error: null,
  pagination: {
    page: 0,
    pageSize: 10,
    hasNext: false,
    hasPrev: false,
  },
  filters: {
    status: undefined,
    query: undefined,
    complaint: undefined,
  },
};

export interface CreateOrderStoreOptions {
  name: string;
  storage: StateStorage;
}

export function createOrderStore({ name, storage }: CreateOrderStoreOptions) {
  const store = create<OrderState & OrderAction>()(
    immer(
      persist(
        (set, get) => ({
          ...initialState,

          setOrders: (orders) => set({ orders }),
          setCurrentOrder: (orderId) => set({ currentOrderId: orderId }),
          setLoading: (loading) => set({ loading }),
          setLoadingText: (loadingText) => set({ loadingText }),
          setError: (error) => set({ error }),
          setPagination: (pagination) => set({ pagination }),
          setFilters: (filters) => set({ filters }),

          addOrder: (order) =>
            set((state) => {
              const index = state.orders.findIndex((o) => o._id === order._id);
              if (index !== -1) {
                state.orders[index] = order;
              } else {
                state.orders.push(order);
              }
            }),

          updateOrder: (order) =>
            set((state) => {
              const index = state.orders.findIndex((o) => o._id === order._id);
              if (index !== -1) {
                state.orders[index] = order;
              } else {
                state.orders.unshift(order);
              }
            }),

          removeOrder: (orderId) =>
            set((state) => {
              state.orders = state.orders.filter((o) => o._id !== orderId);
            }),

          getOrderById: (id) => get().orders.find((o) => o._id === id),

          setOrderActions: (actions) =>
            set((state) => {
              actions.forEach((action) => {
                const index = state.orderActions.findIndex(
                  (o) => o._id === action._id
                );
                if (index !== -1) {
                  state.orderActions[index] = action;
                } else {
                  state.orderActions.push(action);
                }
              });
            }),

          getOrderActions: (orderId) =>
            get().orderActions.filter((o) => o.orderId === orderId),

          reset: () => set(initialState),
        }),
        {
          name,
          storage: createJSONStorage(() => storage),
          partialize: (state) => ({
            filters: state.filters,
          }),
        }
      )
    )
  );

  return store;
}
