import { TherapistListRequest, TherapistListResponse } from '@core/api';
import {
  therapist_extend,
  therapist_service,
  therapist_summary,
} from '@model/therapist.interface';

import { COLLECTIONS } from '@model/db.model';
import Taro from '@tarojs/taro';
/**
 * 咨询师服务层，负责与数据库通信获取咨询师数据. 这个给用户端使用
 * 遵循架构原则：
 * 1. 公开数据直接从数据库读取
 * 2. 敏感数据通过云函数获取
 * 3. 需要实时同步的数据使用watch监听
 */

/**
 * ---------------云数据库文档--------
 * https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/guide/database/security-rules.html#%E8%A7%84%E5%88%99%E5%8C%B9%E9%85%8D
 * {openid} 变量
 * 在查询时，当前用户 openid 是常用的变量，在新的安全规则体系下，
 * 要求显式传入 openid，因此为了方便开发者、让开发者无需每次先通过云函数获取用户 openid，
 * 我们规定查询条件中可使用一个字符串常量 {openid}，在后台中发现该字符串时会自动替换为小程序用户的 openid
 * ---------------云数据库文档--------
 */

export const therapistService = {
  /**
   * 获取咨询师列表（支持分页和筛选）
   * @param params 分页和筛选参数
   */
  readTherapists: async (
    params: TherapistListRequest = {}
  ): Promise<TherapistListResponse> => {
    console.log('therapistService readTherapists', params);
    try {
      // 默认参数
      const defaultParams = {
        page: 1,
        pageSize: 20,
        sort: 'rating',
      };

      const queryParams = { ...defaultParams, ...params };
      const { page, pageSize, keyword, sort, filters } = queryParams;

      // 初始化数据库
      const db = Taro.cloud.database();
      // 查询命令
      const _ = db.command;

      // 构建查询条件
      let query: any = {
        status: 'active', // 默认只查询状态为active的咨询师
      };

      // 关键词搜索(姓名或专长)
      if (keyword && keyword.trim() !== '') {
        query = _.and([
          query,
          _.or([
            {
              name: new RegExp(keyword, 'i'),
            },
            {
              specialties: new RegExp(keyword, 'i'),
            },
          ]),
        ]);
      }

      // 按咨询方向筛选
      if (filters?.directions && filters.directions.length > 0) {
        query['directions'] = _.in(filters.directions);
      }

      // 按服务类型/咨询方式筛选
      if (filters?.serviceTypes && filters.serviceTypes.length > 0) {
        query['service.services.type'] = _.in(filters.serviceTypes);
      }

      // 按地区筛选
      if (filters?.location && filters.location.trim() !== '') {
        query['location'] = filters.location;
      }

      // 按今天可约筛选
      if (filters?.todayAvailable) {
        query['todayAvailable'] = filters.todayAvailable;
      }

      // 计算分页偏移
      const skip = (page - 1) * pageSize;

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.THERAPIST)
        .where(query)
        .count();

      const total = countResult.total;

      // 构建排序条件
      let orderField = 'favoriteCount';
      let orderDirection: 'desc' | 'asc' = 'desc';

      switch (sort) {
        case 'price':
          orderField = 'price';
          orderDirection = 'asc';
          break;
        case 'rating':
          orderField = 'rating';
          orderDirection = 'desc';
          break;
        case 'favorite':
        default:
          orderField = 'favoriteCount';
          orderDirection = 'desc';
      }

      // 查询数据
      const result = await db
        .collection(COLLECTIONS.THERAPIST)
        .where(query)
        .orderBy(orderField, orderDirection)
        .skip(skip)
        .limit(pageSize)
        .get();

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      const therapists = result.data as therapist_summary[];

      return {
        success: true,
        code: 200,
        data: therapists,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取咨询师列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取咨询师基本信息
   * @param id 咨询师ID
   */
  readSummary: async (
    therapistId: string
  ): Promise<therapist_summary | null> => {
    console.log('therapistService readBaseInfo', therapistId);
    try {
      // 初始化数据库
      const db = Taro.cloud.database();

      // 查询咨询师基本信息
      const result = await db
        .collection(COLLECTIONS.THERAPIST)
        .where({ id: therapistId })
        .get();

      if (result.data && result.data.length > 0) {
        // 获取咨询师基本信息
        let therapist = result.data[0] as therapist_summary;

        return therapist;
      } else {
        throw new Error('未找到该咨询师');
      }
    } catch (error) {
      console.error('获取咨询师基本信息失败:', error);
      throw error;
    }
  },

  /**
   * 获取咨询师扩展详情(不包含排期信息)
   * @param id 咨询师ID
   */
  readExtendInfo: async (
    id: string | number
  ): Promise<therapist_extend | null> => {
    console.log('therapistService readExtendInfo', id);
    try {
      // 初始化数据库
      const db = Taro.cloud.database();

      // 查询咨询师扩展详情
      const result = await db
        .collection(COLLECTIONS.THERAPIST_EXTINFO)
        .where({ id: id.toString() })
        .get();

      console.log('therapistService readExtendInfo result', result);
      if (result.data && result.data.length > 0) {
        return result.data[0] as therapist_extend;
      } else {
        console.warn('获取咨询师扩展详情失败:', id);
        return null;
      }
    } catch (error) {
      console.error('获取咨询师详情失败:', error);
      throw error;
    }
  },

  /**
   * 获取咨询师服务
   * @param id 咨询师ID
   */
  readService: async (
    id: string | number
  ): Promise<therapist_service | null> => {
    console.log('therapistService readService', id);
    try {
      // 初始化数据库
      const db = Taro.cloud.database();

      // 查询咨询师服务
      const result = await db
        .collection(COLLECTIONS.THERAPIST_SERVICE)
        .where({ id: id.toString() })
        .get();

      console.log('therapistService readService result', result);
      return result.data[0] as therapist_service;
    } catch (error) {
      console.error('获取咨询师服务失败:', error);
      throw error;
    }
  },

  /**
   * 监听咨询师排期变化
   * @param therapistId 咨询师ID
   * @param onChange 数据变化回调
   */
  watchTherapistSchedule: (
    therapistId: string,
    onChange: (scheduleData: any) => void
  ): Taro.DB.Document.IWatcher | null => {
    try {
      // 初始化数据库
      const db = Taro.cloud.database();

      // 获取今天及以后的排期
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = today.getTime();

      // 建立数据库监听
      return db
        .collection(COLLECTIONS.THERAPIST_SCHEDULES)
        .where({
          id: therapistId,
          date: db.command.gte(todayTimestamp),
        })
        .watch({
          onChange,
          onError: (err) => {
            console.error('监听咨询师排期失败:', err);
          },
        });
    } catch (error) {
      console.error('创建咨询师排期监听失败:', error);
      throw error;
    }
  },
};
