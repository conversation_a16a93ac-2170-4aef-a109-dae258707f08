import { Circle } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import React from 'react';

interface CircularProgressProps {
  progress: number; // 0-100
  size?: number; // 组件大小
  strokeWidth?: number; // 进度条宽度
  color?: string; // 进度条颜色
  bgColor?: string; // 背景色
  children?: React.ReactNode; // 子元素
}

/**
 * 环形进度条组件 - 使用 antmjs/vantui 的 Circle 组件
 */
const CircularProgress: React.FC<CircularProgressProps> = ({
  progress,
  size = 40,
  strokeWidth = 3,
  color = '#1890ff',
  bgColor = '#eee',
  children,
}) => {
  return (
    <View className='relative flex items-center justify-center'>
      <Circle
        value={progress}
        size={size}
        strokeWidth={strokeWidth}
        color={color}
        layerColor={bgColor}
        clockwise
        speed={0}
        lineCap='round'
      />
      {/* 子元素放在中间 */}
      <View className='absolute inset-0 flex items-center justify-center'>
        {children}
      </View>
    </View>
  );
};

export default CircularProgress;
