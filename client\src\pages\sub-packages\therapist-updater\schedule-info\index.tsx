import { Button, Calendar, Icon, Radio, RadioGroup } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import DateSelector from '@components/therapist/DateSelector';
import TimeSlotSelector from '@components/therapist/TimeSlotSelector';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { useCalendar } from '@hooks/useAppointmentCalendar';
import { my_schedule } from '@model/profile.therapist';
import { useGlobalStore } from '@stores/global.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

export default function ScheduleInfoUpdater() {
  const workTime = useTherapistProfileStore(
    (state) => state.myServices?.workTime
  );
  const occupancy = useTherapistProfileStore(
    (state) => state.mySchedule?.schedule
  );

  const loading = useTherapistUpdaterStore((state) => state.loading);
  const error = useTherapistUpdaterStore((state) => state.error);

  const renderCount = useRef(0);

  // 安全的增加计数
  renderCount.current += 1;

  useEffect(() => {
    console.log('scheduleInfo', occupancy);
    const loadMyServices = async () => {
      await Promise.all([
        therapistProfileActions.loadMyServices(),
        therapistProfileActions.loadMySchedule(true),
      ]);
    };
    loadMyServices();
    return () => {
      useTherapistUpdaterStore.setState({
        loading: false,
        error: null,
      });
    };
  }, []);

  const {
    selectedDate,
    selectedTimeSlot,
    datesToShow,
    isDateAvailable,
    isTimeSlotAvailable,
    getWorkTimeSlots,
    handleDateSelect,
    handleTimeSlotSelect,
    handleCalendarDateSelect,
    formatHour,
    isAvailableDay,
    toggleDateAvailability,
  } = useCalendar({
    occupancy: occupancy,
    workTime: workTime,
  });

  const [showCalendarPicker, setShowCalendarPicker] = useState(false);

  console.log('scheduleInfo 安全渲染次数:', renderCount.current);
  console.log('showCalendarPicker', showCalendarPicker);

  // 处理日历日期选择
  const handleCalendarSelect = useCallback(
    (event: any) => {
      console.log('handleCalendarSelect 被调用', event);
      const calendarSelectedDate = new Date(event.detail.value).getTime();
      console.log('calendarSelectedDate', calendarSelectedDate);
      setShowCalendarPicker(false);
      handleCalendarDateSelect(calendarSelectedDate);
    },
    [handleCalendarDateSelect]
  );

  // 处理日历关闭
  const handleCalendarClose = useCallback(() => {
    console.log('handleCalendarClose 被调用');
    setShowCalendarPicker(false);
  }, []);

  // 处理日历打开
  const handleCalendarOpen = useCallback(() => {
    console.log('handleCalendarOpen 被调用');
    setShowCalendarPicker(true);
  }, []);

  // 计算日历的最小和最大日期
  const calendarDateRange = useMemo(() => {
    const now = new Date();
    const minDate = now.getTime();
    const maxDate = new Date(
      now.getFullYear(),
      now.getMonth() + 2,
      0
    ).getTime();
    return { minDate, maxDate };
  }, []);

  const onSubmit = async () => {
    if (!selectedDate) {
      Taro.showToast({
        title: '请选择日期',
        icon: 'none',
      });
      return;
    }

    try {
      // 创建符合 my_schedule 类型的数据
      const scheduleData: my_schedule = {
        id: useGlobalStore.getState().openid || '',
        schedule: [
          {
            id: useGlobalStore.getState().openid || '',
            date: selectedDate,
            available: isAvailableDay,
            slots: selectedTimeSlot ? [selectedTimeSlot] : [],
          },
        ],
      };

      await therapistProfileActions.updateSchedule(scheduleData);
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '排班信息编辑',
        showBackButton: true,
      }}
      loading={loading}
      error={error}
    >
      <View className='min-h-screen pb-24 px-4'>
        <View className='mt-6 flex flex-col w-full gap-6'>
          {/* 头部 */}
          <View className='flex flex-row items-center w-full'>
            <Text className='text-base font-bold'>选择日期</Text>
            <View
              className='flex flex-row items-center flex-1 justify-end'
              onClick={handleCalendarOpen}
            >
              <Text className='text-sm text-secondary '>
                {selectedDate
                  ? new Date(selectedDate).getMonth() + 1
                  : new Date().getMonth() + 1}
                月
              </Text>
              <Icon
                name='arrow'
                size='12px'
                color='var(--color-text-secondary)'
              />
            </View>
          </View>

          {/* 日期选择区 */}
          <DateSelector
            datesToShow={datesToShow}
            isDateAvailable={isDateAvailable}
            selectedDate={selectedDate}
            onDateSelect={handleDateSelect}
          />

          {/* 可用性切换 */}
          {selectedDate && (
            <View className='w-full justify-end my-2'>
              <RadioGroup
                className='justify-end'
                direction='horizontal'
                value={isAvailableDay ? 'true' : 'false'}
                onChange={(event) => {
                  toggleDateAvailability(event.detail);
                }}
              >
                <Radio name='true'>可约</Radio>
                <Radio name='false' className='ml-4'>
                  休息
                </Radio>
              </RadioGroup>
            </View>
          )}

          {/* 时间段选择区 */}
          {selectedDate && (
            <TimeSlotSelector
              isAvailableDay={isAvailableDay}
              availableTimeSlots={getWorkTimeSlots()}
              isTimeSlotAvailable={isTimeSlotAvailable}
              selectedTimeSlot={selectedTimeSlot}
              onTimeSlotSelect={handleTimeSlotSelect}
              formatHour={formatHour}
            />
          )}
        </View>

        <View className='flex w-full mt-8'>
          <Button
            type='primary'
            block
            round
            onClick={onSubmit}
            loading={loading}
            disabled={!selectedDate || loading}
          >
            提交
          </Button>
        </View>
      </View>
      <Calendar
        show={showCalendarPicker}
        minDate={calendarDateRange.minDate}
        maxDate={calendarDateRange.maxDate}
        onClose={handleCalendarClose}
        onConfirm={handleCalendarSelect}
      />
    </PageLol>
  );
}
