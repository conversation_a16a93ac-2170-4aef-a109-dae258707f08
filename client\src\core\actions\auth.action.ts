import { useUserProfileStore } from '@stores/profile.user';
import Taro from '@tarojs/taro';

import { Toast } from '@antmjs/vantui';
import { BaseCacheService } from '@core/cache/base-cache.service';
import {
  USER_LOGIN_CACHE_KEY,
  USER_SIG_CACHE_KEY,
  UserCachePolicy,
} from '@core/cache/user-cache-policy';
import { USER_ROLE } from '@model/user.interface';
import { callCloudFunction } from '@services/cloud';
import { useGlobalStore } from '@stores/global.store';
import { appRouter, tab_pages } from '@utils/router';
// import { loginIM } from './im.action';

/**
 * 定义手机号列表项类型
 */
export interface PhoneItem {
  phone: string;
  masked: string;
}

/**
 * 用户签名缓存数据结构
 */
interface UserSigCache {
  userSig: string;
  expireTime: number;
}

/**
 * 执行微信登录流程
 * @returns 登录结果
 */
export const wxLogin = async (
  cloudID: string
): Promise<{
  isNewUser: boolean;
  openid: string;
  userSig: string;
}> => {
  try {
    // 获取微信登录凭证
    const loginRes = await Taro.login();
    if (!loginRes.code) {
      console.error('wxLogin loginRes', loginRes);
      Toast.show('微信登录失败，请重试');
      throw new Error('微信登录失败，请重试');
    }
    console.log('wxLogin loginRes', loginRes);
    // 获取邀请信息
    const referralInfo = useUserProfileStore.getState().referralInfo ?? null;
    console.log('wxLogin referralInfo', referralInfo);

    // 调用云函数登录，获取用户信息
    const result = await callCloudFunction('user', 'login', {
      code: loginRes.code,
      phoneCode: cloudID,
      referralInfo,
    });

    console.log('wxLogin callCloudFunction', result);
    if (result && result.success) {
      return _onLoginSuccess(result);
    } else {
      console.log('wxLogin callCloudFunction error', result);
      Toast.show(result?.message || '登录失败，请重试');
      throw new Error(result?.message || '登录失败，请重试');
    }
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

const _onLoginSuccess = (
  result: any
): {
  isNewUser: boolean;
  openid: string;
  userSig: string;
} => {
  console.log('wxLogin _onLoginSuccess', result);
  // 保存用户信息
  const { userSig, userInfo, openid, isNewUser } = result;
  useGlobalStore.getState().setUserSig(userSig);
  useGlobalStore.getState().setOpenid(openid);
  useGlobalStore.getState().setRole(userInfo?.role || [USER_ROLE.USER]);

  const currentRole = useGlobalStore.getState().currentRole;
  if (!currentRole || currentRole === USER_ROLE.GUEST) {
    useGlobalStore.getState().setCurrentRole(USER_ROLE.USER);
  }

  // 缓存openid基本信息，有效期7天
  const userCacheData = {
    openid,
  };
  BaseCacheService.set(
    USER_LOGIN_CACHE_KEY,
    userCacheData,
    UserCachePolicy.ttl,
    UserCachePolicy.version
  );

  // // 单独缓存userSig，有效期1天
  // const userSigData: UserSigCache = {
  //   userSig,
  //   expireTime: Date.now() + 24 * 60 * 60 * 1000, // 1天后过期
  // };
  // BaseCacheService.set(
  //   USER_SIG_CACHE_KEY,
  //   userSigData,
  //   24 * 60 * 60, // 1天有效期
  //   UserCachePolicy.version
  // );

  // 初始化IM
  // loginIM(openid, userSig);

  // 清除邀请信息
  useUserProfileStore.getState().clearReferralInfo();
  return {
    isNewUser,
    openid,
    userSig,
  };
};

/**
 * 刷新用户的userSig
 * @returns 是否成功刷新
 */
// export async function refreshUserSig(): Promise<boolean> {
//   try {
//     console.log('refreshUserSig');
//     const { openid } = useGlobalStore.getState();

//     if (!openid) {
//       console.error('刷新userSig失败: 没有openid');
//       return false;
//     }

//     // 调用云函数获取新的userSig
//     const result = await callCloudFunction('user', 'refreshUserSig', {
//       openid,
//     });

//     if (result && result.success && result.userSig) {
//       const newUserSig = result.userSig;

//       // 更新store中的userSig
//       useGlobalStore.getState().setUserSig(newUserSig);

//       // 更新userSig缓存
//       const userSigData: UserSigCache = {
//         userSig: newUserSig,
//         expireTime: Date.now() + 24 * 60 * 60 * 1000, // 1天后过期
//       };
//       BaseCacheService.set(
//         USER_SIG_CACHE_KEY,
//         userSigData,
//         24 * 60 * 60, // 1天有效期
//         UserCachePolicy.version
//       );

//       // 重新初始化IM
//       // loginIM(openid, newUserSig);

//       console.log('userSig刷新成功');
//       return true;
//     } else {
//       console.error('刷新userSig失败:', result);
//       return false;
//     }
//   } catch (error) {
//     console.error('刷新userSig出错:', error);
//     return false;
//   }
// }

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function checkLogin(): boolean {
  console.log('checkLogin');
  // 先检查store中是否已登录
  const isLoggedInStore = useGlobalStore.getState().isLoggedIn();
  console.log('isLoggedInStore', isLoggedInStore);

  if (isLoggedInStore) {
    return true;
  }

  // 检查缓存中是否有有效的登录信息
  const cachedUserInfo = BaseCacheService.get(USER_LOGIN_CACHE_KEY, null);
  console.log('cachedUserInfo', cachedUserInfo);

  // // 检查userSig缓存
  // const cachedUserSig = BaseCacheService.get(
  //   USER_SIG_CACHE_KEY,
  //   null
  // ) as UserSigCache | null;
  // console.log('cachedUserSig', cachedUserSig);

  if (cachedUserInfo) {
    // 从缓存恢复用户信息到store
    const { openid } = cachedUserInfo;
    useGlobalStore.getState().setOpenid(openid);

    // // 检查userSig是否存在且未过期
    // if (!cachedUserSig) {
    //   // 没有userSig缓存，尝试刷新获取
    //   refreshUserSig().then((success) => {
    //     console.log('获取userSig结果:', success);
    //   });
    // } else {
    //   useGlobalStore.getState().setUserSig(cachedUserSig.userSig);
    // }

    return true;
  }

  return false;
}

/**
 * 确保用户已登录，如果未登录则跳转到登录页面
 * @param redirect 是否重定向回当前页面，默认为true
 * @returns 是否已登录
 */
export async function ensureLogin(redirect = true): Promise<boolean> {
  if (checkLogin()) {
    return true;
  }
  console.log('ensureLogin need login');

  // 获取当前页面路径，用于登录后重定向回来
  const pages = Taro.getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const path = currentPage ? currentPage.route : '';

  if (redirect && path && !path.includes('login')) {
    Taro.setStorageSync('redirectUrl', '/' + path);
  }

  // 跳转到登录页
  appRouter.login();

  return false;
}

/**
 * 登录成功后的重定向处理
 */
export function handleLoginRedirect(): void {
  console.log('handleLoginRedirect');
  try {
    // 检查是否有登录后的重定向URL
    const redirectUrl = Taro.getStorageSync('redirectUrl');
    console.log('redirectUrl', redirectUrl);
    if (redirectUrl) {
      // 清除重定向URL
      Taro.removeStorageSync('redirectUrl');

      // 重定向回原页面, 需要检查是否是主页面，主页面使用switchTab，其他页面使用redirectTo
      if (tab_pages.includes(redirectUrl)) {
        console.log('switchTab', redirectUrl);
        // 先关闭当前页面
        Taro.navigateBack();
        Taro.switchTab({
          url: redirectUrl,
        });
      } else {
        console.log('redirectTo', redirectUrl);
        Taro.redirectTo({
          url: redirectUrl,
        });
      }
    } else {
      // 先关闭当前页面
      Taro.navigateBack();
      // 没有重定向URL，跳转到首页
      Taro.switchTab({
        url: '/pages/homepage/index',
      });
    }
  } catch (error) {
    console.error('handleLoginRedirect error', error);
  }
}

/**
 * 重定向到登录页面
 * 如果用户未登录，跳转到登录页
 */
export function redirectToLogin() {
  // 获取当前页面路径，用于登录后重定向回来
  const pages = Taro.getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const path = currentPage ? currentPage.route : '';

  if (path) {
    Taro.setStorageSync('redirectUrl', '/' + path);
  }

  // 跳转到登录页
  appRouter.login();
}

/**
 * 检查用户是否具有咨询师角色
 * @returns Promise<{success: boolean, isTherapist: boolean, therapistId?: string, message?: string}>
 */
export async function checkTherapistRole(): Promise<boolean> {
  try {
    ensureLogin();

    const result = await callCloudFunction('user', 'checkTherapistRole');

    if (result && result.success) {
      return result.isTherapist;
    } else {
      return false;
    }
  } catch (error) {
    console.error('检查咨询师角色失败:', error);
    return false;
  }
}
/**
 * 切换用户角色
 * @param role 目标角色
 * @returns Promise<{success: boolean, message?: string}>
 */
export async function switchRole(role: USER_ROLE) {
  try {
    if (!checkLogin()) {
      redirectToLogin();
      return {
        success: false,
        message: '请先登录',
      };
    }

    // 检查当前状态是否和目标状态一致
    if (useGlobalStore.getState().currentRole === role) {
      Taro.showToast({
        title: '您已经是该角色',
        icon: 'none',
        duration: 2000,
      });
      return {
        success: true,
        message: '已经是该角色',
      };
    }

    if (role === USER_ROLE.THERAPIST) {
      const checkResult = await checkTherapistRole();
      console.log('profileActions switchRole result', checkResult);
      if (!checkResult) {
        Taro.showModal({
          title: '提示',
          content: '您还不是咨询师，是否前往申请成为咨询师？',
          confirmText: '立即前往',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 导航到咨询师入驻页面
              Taro.navigateTo({
                url: '/pages/sub-packages/therapist/register/index',
              });
            }
          },
        });
        return {
          success: false,
          message: '用户不具有咨询师角色',
        };
      }
    }

    Taro.showLoading({ title: '正在切换...', mask: true });
    console.log('switchRole', role);
    await new Promise((resolve) => setTimeout(resolve, 2000));
    // 更新本地状态
    useGlobalStore.getState().setCurrentRole(role);
    Taro.hideLoading();

    // 获取当前页面路径
    const pages = Taro.getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentPath = currentPage ? currentPage.route : '';

    // 重新加载当前页面
    if (currentPath) {
      Taro.reLaunch({
        url: `/${currentPath}`,
      });
    }

    return {
      success: true,
      message: '角色切换成功',
    };
  } catch (error) {
    console.error('切换角色失败:', error);
    Taro.showToast({
      title: '切换角色失败',
      icon: 'none',
      duration: 2000,
    });
    return {
      success: false,
      message: '角色切换失败',
    };
  } finally {
    Taro.hideLoading();
  }
}

/**
 * 登出
 */
export function logout(): void {
  // 重置用户状态
  useUserProfileStore.getState().reset();

  // 清除用户缓存
  BaseCacheService.remove(USER_LOGIN_CACHE_KEY);
  BaseCacheService.remove(USER_SIG_CACHE_KEY);

  // 跳转到登录页
  appRouter.relaunchLogin();
}

/**
 * 确保用户已登录，如果未登录则跳转到登录页并返回false
 */
export function ensureLoggedIn() {
  const isLoggedIn = checkLogin();
  if (!isLoggedIn) {
    redirectToLogin();
  }
  return isLoggedIn;
}
