import { AudioCategory } from '@model/treatment.interface';

import { treatmentService } from '@services/treatment.service';
import { useTreatmentStore } from '@stores/treatment.store';

// 缓存策略配置
const CACHE_TTL = 30 * 60 * 1000; // 30分钟缓存

/**
 * 疗愈模块业务逻辑协调层
 */
export const treatmentActions = {
  // /**
  //  * 获取分类标签数据（带缓存策略）
  //  */
  // fetchCategories: async (forceRefresh = false) => {
  //   const store = useTreatmentStore.getState();

  //   try {
  //     // 检查缓存有效性
  //     const lastUpdated = store.lastUpdated || 0;
  //     const isCacheValid =
  //       !forceRefresh &&
  //       Date.now() - lastUpdated < CACHE_TTL &&
  //       store.categories.length > 0;

  //     if (isCacheValid) {
  //       console.log('[Treatment] 使用缓存数据');
  //       return store.categories;
  //     }

  //     store.setLoading(true);
  //     store.setError(null);

  //     // 获取分类数据
  //     const categories = await treatmentService.getCategories();

  //     // 更新存储（带时间戳）
  //     store.setCategories(categories);

  //     return categories;
  //   } catch (error) {
  //     store.setError(error as Error);

  //     // 缓存回退策略
  //     if (store.categories.length > 0) {
  //       console.warn('[Treatment] 错误后使用缓存数据');
  //       return store.categories;
  //     }

  //     throw error;
  //   } finally {
  //     store.setLoading(false);
  //   }
  // },

  /**
   * 获取推荐声音
   */
  fetchFeaturedAudios: async () => {
    const store = useTreatmentStore.getState();

    try {
      store.setLoading(true);
      store.setError(null);

      const featuredAudios = await treatmentService.getFeaturedAudios();
      store.setFeaturedAudios(featuredAudios);

      return featuredAudios;
    } catch (error) {
      store.setError(error as Error);
      throw error;
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 获取音频列表
   */
  fetchAudioList: async (params: {
    category?: AudioCategory;
    forceRefresh?: boolean;
  }) => {
    console.log('fetchAudioList', params);
    const { category, forceRefresh } = params;
    const store = useTreatmentStore.getState();
    let audioList = store.getAudioListByCategory(category || 'all');

    try {
      const lastUpdated = store.lastUpdated || 0;
      const isCacheValid =
        !forceRefresh &&
        Date.now() - lastUpdated < CACHE_TTL &&
        audioList &&
        audioList.length > 0;

      if (isCacheValid) {
        console.log('[Treatment] 使用缓存数据');
        return audioList;
      }

      store.setLoading(true);
      store.setError(null);

      audioList = await treatmentService.getAudios(category);
      console.log('TreatmentActions.fetchAudioList', category, audioList);
      store.setAudios(audioList, category);

      return audioList;
    } catch (error) {
      store.setError(error as Error);
      throw error;
    } finally {
      store.setLoading(false);
    }
  },
};
