import { Icon } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { Text, View } from '@tarojs/components';
import React from 'react';

interface PrivacySection {
  title: string;
  content: string[];
}

const privacyPolicy: PrivacySection[] = [
  {
    title: '引言',
    content: [
      '感谢您使用我们的心理健康平台。我们非常重视您的个人隐私和信息安全，并致力于保护您的个人信息。',
      '本隐私政策旨在帮助您了解我们收集、使用、存储和共享您的个人信息的方式，以及您享有的相关权利。',
      '请您在使用我们的服务前仔细阅读并理解本隐私政策的全部内容。如您对本隐私政策有任何疑问，可通过本政策末尾的联系方式与我们联系。',
    ],
  },
  {
    title: '我们收集的信息',
    content: [
      '1. 您提供的信息：当您注册账号、完善个人资料、预约咨询、参与心理测评时，我们会收集您的姓名、联系方式、性别、年龄等基本信息，以及您主动提供的心理健康相关信息。',
      '2. 设备信息：我们会自动收集您使用我们服务的设备信息，包括设备型号、操作系统版本、唯一设备标识符、网络信息等。',
      '3. 服务日志信息：包括您的搜索查询内容、IP地址、浏览器类型、访问日期和时间、停留时间、访问的页面记录等。',
      '4. 位置信息：经您授权后，我们可能会收集您的位置信息，以便为您提供基于位置的服务，如推荐附近的心理咨询师。',
    ],
  },
  {
    title: '信息的使用',
    content: [
      '我们可能将收集的信息用于以下用途：',
      '1. 提供、维护和改进我们的服务，如心理咨询、心理测评、心理健康内容推荐等。',
      '2. 处理您的预约请求，并促进您与咨询师之间的沟通。',
      '3. 为您提供个性化的内容和服务推荐。',
      '4. 进行身份验证、客户服务、安全防护、诈骗监测等。',
      '5. 分析产品使用情况，进行产品开发和服务优化。',
      '6. 向您发送服务通知，如系统维护通知、更新提示等。',
      '经您同意的情况下，我们可能将信息用于其他目的。',
    ],
  },
  {
    title: '信息的共享',
    content: [
      '除以下情形外，未经您的同意，我们不会与任何第三方共享您的个人信息：',
      '1. 根据法律法规要求必须共享的情形。',
      '2. 为完成合并、收购或资产转让而必须转移的情形。',
      '3. 为向您提供服务而与合作伙伴共享的情形，如支付机构、心理咨询师等。',
      '4. 经您明确同意的其他情形。',
      '我们会要求第三方对您的个人信息保密，并且采取有效的安全措施保护您的信息。',
    ],
  },
  {
    title: '信息的存储',
    content: [
      '1. 存储地点：您的个人信息将存储在中华人民共和国境内的服务器上。',
      '2. 存储期限：我们仅在为实现本政策所述目的所必需的期限内保留您的个人信息，除非法律要求或允许在更长的时间内保留这些信息。',
      '3. 信息安全：我们采用行业标准的安全技术和程序，防止您的个人信息遭到未经授权的访问、使用或泄露。',
    ],
  },
  {
    title: '您的权利',
    content: [
      '根据适用的法律法规，您对自己的个人信息享有以下权利：',
      '1. 访问权：您有权访问我们持有的关于您的个人信息。',
      '2. 更正权：您有权要求更正或更新不准确或不完整的个人信息。',
      '3. 删除权：在特定情况下，您有权要求删除您的个人信息。',
      '4. 限制处理权：在特定情况下，您有权要求限制处理您的个人信息。',
      '5. 反对权：您有权反对我们出于特定原因处理您的个人信息。',
      '6. 数据可携权：您有权以结构化、常用和机器可读的格式接收您提供给我们的个人信息，并有权将这些信息传输给另一个控制者。',
      '如需行使上述权利，请通过本政策末尾提供的联系方式与我们联系。',
    ],
  },
  {
    title: '儿童隐私',
    content: [
      '我们的服务主要面向成人用户。对于16岁以下的未成年人，在使用我们的服务前应当事先取得其监护人的同意。',
      '如果我们发现自己收集了16岁以下儿童的个人信息，且未事先获得可证实的监护人同意，我们会设法尽快删除相关数据。',
    ],
  },
  {
    title: '隐私政策的更新',
    content: [
      '我们可能会不时更新本隐私政策，以反映我们信息实践的变化。',
      '当本政策发生重大变更时，我们会在应用程序内显著位置发布通知，并在政策顶部更新生效日期。',
      '建议您定期查阅本隐私政策，以了解我们如何保护您的信息。',
    ],
  },
  {
    title: '联系我们',
    content: [
      '如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式与我们联系：',
      '电子邮件：<EMAIL>',
      '客服电话：400-888-8888',
      '公司地址：北京市海淀区中关村创业大厦B座10层',
    ],
  },
];

const PrivacyPolicyPage: React.FC = () => {
  const lastUpdated = '2023年11月20日';

  return (
    <PageLol
      navigationProps={{
        title: '隐私政策',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen p-4 pb-8'>
        {/* 标题和更新日期 */}
        <View className='mb-6'>
          <Text className='text-2xl font-bold block text-center mb-2'>
            隐私政策
          </Text>
          <Text className='text-sm text-secondary block text-center'>
            最近更新：{lastUpdated}
          </Text>
        </View>

        {/* 隐私政策内容 */}
        <View className='space-y-6'>
          {privacyPolicy.map((section, index) => (
            <View key={index} className='bg-bg rounded-lg p-4'>
              <View className='flex items-center mb-2'>
                <Icon
                  name='shield-o'
                  size={36}
                  color='#1989fa'
                  className='mr-2'
                />
                <Text className='text-lg font-medium'>{section.title}</Text>
              </View>
              <View className='mt-2 space-y-2'>
                {section.content.map((paragraph, pIndex) => (
                  <Text
                    key={pIndex}
                    className='text-sm text-secondary leading-6'
                  >
                    {paragraph}
                  </Text>
                ))}
              </View>
            </View>
          ))}
        </View>

        {/* 底部确认 */}
        <View className='mt-8'>
          <Text className='text-sm text-center block text-secondary'>
            使用我们的服务，即表示您已阅读并同意本隐私政策
          </Text>
        </View>
      </View>
    </PageLol>
  );
};

export default PrivacyPolicyPage;
