/* eslint-disable import/no-commonjs */
/* eslint-disable @typescript-eslint/no-var-requires */
const npath = require('path');
const pkg = require('../package.json');
const miniChain = require('./webpack/mini-chain');
const fs = require('fs');

/**
 * 需要拷贝的文件
 * Taro不会把 private.config.json 文件打包，所以使用copy直接将文件拷贝到项目根目录
 * 这样开发者工具才能读取到本地私有配置项
 */
const COPY_FILES = [
  'project.private.config.json',
  {
    from: 'src/assets',
    to: `${process.env.TARO_ENV}/assets`,
  },
];

process.env.TARO_ENV = process.env.TARO_ENV ?? 'weapp';
process.env.NODE_ENV =
  process.env.NODE_ENV?.toLowerCase().trim() ?? 'development';
const generateCopyConfig = (list) => {
  const patterns = [];
  list.forEach((file) => {
    if (typeof file === 'string') {
      if (fs.existsSync(file)) {
        patterns.push({
          from: file,
          to: `${process.env.TARO_ENV}/${file}`,
        });
      }
    } else if (typeof file === 'object') {
      patterns.push(file);
    }
  });
  return { patterns };
};

const config = {
  copy: generateCopyConfig(COPY_FILES), // 拷贝文件

  projectName: pkg.name,
  date: '2024-11-11',

  //！！！ 实测：根本不可用，根本走不到分支里面去。
  // designWidth(input) {
  //   const filePath = input?.file?.replace(/\\+/g, '/') || '';
  //   if(filePath.indexOf('app.less') < 0) {
  //   console.warn('filePath', filePath)
  //   }
  //   if (filePath?.indexOf('@antmjs/vantui') > -1 || filePath?.indexOf('vantui') > -1) {
  //     console.warn('vantui 750')
  //     return 750;
  //   }
  //   console.warn('375')
  //   return 375;
  // },
  // deviceRatio: {
  //   640: 2.34 / 2,
  //   750: 1,
  //   828: 1.81 / 2,
  //   375: 2 / 1,
  // },

  //只能使用750，解决vantui的组件问题。
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
    375: 2 / 1,
  },
  sourceRoot: 'src',
  outputRoot: process.env.TARO_ENV,
  alias: {
    src: npath.resolve(process.cwd(), 'src'),
    // 配置taro项目的绝对路径
    // "@actions": npath.resolve(__dirname, "..", "src/actions"),
    '@assets': npath.resolve(__dirname, '..', 'src/assets'),
    '@components': npath.resolve(__dirname, '..', 'src/components'),
    '@constants': npath.resolve(__dirname, '..', 'src/constants'),
    '@pages': npath.resolve(__dirname, '..', 'src/pages'),
    '@styles': npath.resolve(__dirname, '..', 'src/styles'),
    '@utils': npath.resolve(__dirname, '..', 'src/utils'),
    '@services': npath.resolve(__dirname, '..', 'src/services'),
    '@hooks': npath.resolve(__dirname, '..', 'src/hooks'),
    '@stores': npath.resolve(__dirname, '..', 'src/stores'),
    '@core': npath.resolve(__dirname, '..', 'src/core'),
    '@model': npath.resolve(__dirname, '..', 'src/model'),
  },
  framework: 'react',
  compiler: {
    type: 'webpack5',
    prebundle: {
      enable: true,
      exclude: ['@tencentcloud/chat', 'trtc-wx-sdk', '@antmjs/vantui'],
    },
  },
  hmr: true,
  cache: {
    enable: true, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  mini: {
    webpackChain(chain) {
      miniChain(chain);
    },
    lessLoaderOption: {
      lessOptions: {
        modifyVars: {
          hack: `true; @import "${npath.join(
            process.cwd(),
            'src/styles/index.less'
          )}";`,
        },
      },
      // 适用于全局引入样式
      // additionalData: "@import '/src/styles/index.less';",
    },
    postcss: {
      autoprefixer: {
        enable: true,
        config: {},
      },
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: 1024, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
    optimizeMainPackage: {
      enable: true,
    },
  },
  plugins: [['@tarojs/plugin-framework-react', { reactMode: 'concurrent' }]],
};

module.exports = function (merge) {
  try {
    const envConfigPath = `./${process.env.NODE_ENV}.js`;
    const envConfigFullPath = npath.join(__dirname, envConfigPath);

    if (fs.existsSync(envConfigFullPath)) {
      return merge({}, config, require(envConfigPath));
    }

    // 如果找不到对应的环境配置文件，则使用开发环境配置
    if (process.env.NODE_ENV !== 'development') {
      console.warn(`环境配置文件 ${envConfigPath} 不存在，将使用开发环境配置`);
      return merge({}, config, require('./development'));
    }

    return merge({}, config);
  } catch (error) {
    console.error('加载配置文件出错:', error);
    return config;
  }
};
