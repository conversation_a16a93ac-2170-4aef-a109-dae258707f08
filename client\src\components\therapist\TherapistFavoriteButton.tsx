import { Loading } from '@antmjs/vantui';
import { useTherapistFavorite } from '@hooks/useTherapistFavorite';
import { View } from '@tarojs/components';
import React from 'react';

interface TherapistFavoriteButtonProps {
  therapistId: string;
  initialFavorite?: boolean;
  onFavoriteChange?: (newFavoriteState: boolean) => void;
  className?: string;
  size?: 'mini' | 'small' | 'normal' | 'large';
  showText?: boolean;
  round?: boolean;
}

/**
 * 咨询师收藏按钮组件
 * 封装了收藏状态的管理和UI展示
 */
const TherapistFavoriteButton: React.FC<TherapistFavoriteButtonProps> = ({
  therapistId,
  initialFavorite = false,
  onFavoriteChange,
  className = '',
  size = 'small',
  showText = true,
  round = true,
}) => {
  // 使用自定义hook管理收藏状态
  const { isFavorite, loading, toggleFavorite } = useTherapistFavorite(
    therapistId,
    initialFavorite
  );

  // 处理点击
  const handleClick = async () => {
    const success = await toggleFavorite();
    if (success && onFavoriteChange) {
      onFavoriteChange(!isFavorite);
    }
  };

  return (
    <View
      className={`${className} px-2 py-1 rounded-full text-xs font-bold ${
        isFavorite
          ? 'text-secondary bg-primarylight'
          : 'text-default bg-secondarylight'
      }`}
      onClick={handleClick}
    >
      {loading && <Loading size='12px' />}
      {showText ? (isFavorite ? '已收藏' : '+收藏') : ''}
    </View>
  );
};

export default TherapistFavoriteButton;
