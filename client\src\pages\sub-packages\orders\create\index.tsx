import { Steps } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import ResultPopup from '@components/common/result-popup';
import { ILLUSTRATION_MAP } from '@constants/assets';
import { paymentActions } from '@core/actions/payment.action';
import { therapistActions } from '@core/actions/therapist.action';
import { CreateOrderParams } from '@core/api';
import useRenderCount from '@hooks/useRenderCount';
import { useAppointmentStore } from '@stores/appointment.store';
import { useUserProfileStore } from '@stores/profile.user';
import { useTherapistStore } from '@stores/therapist.store';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';

import { orderUserActions } from '@core/actions/order.user';
import { useLoadingStore } from '@stores/loading.store';
import { useEffect, useState } from 'react';
import OrderConfirmation from '../components/OrderConfirmation';
import ServiceSelection from '../components/ServiceSelection';
import TimeSelection from '../components/TimeSelection';

const steps = [
  { text: '选择时间', desc: '' },
  { text: '选择服务', desc: '' },
  { text: '确认订单', desc: '' },
];

export default function CreateOrderPage() {
  // 跟踪渲染次数
  useRenderCount('CreateOrderPage');

  const router = useRouter();
  const { therapistId } = router.params;

  // 使用 selector 订阅咨询师详情
  const currentTherapistOccuppied =
    useTherapistStore.use.currentTherapistOccuppied();
  const currentTherapistService =
    useTherapistStore.use.currentTherapistService();
  const user = useUserProfileStore.use.profile();

  // // 使用 appointment store 管理步骤和选择状态
  const {
    currentStep,
    setCurrentStep,
    loading,
    error,
    resetAppointment,
    selectedTherapistId,
    selectedTimeSlot,
    selectedDate,
    selectedService,
    price,
    selectedDurationMultiple,
  } = useAppointmentStore();
  // ResultPopup 状态
  const [showResultPopup, setShowResultPopup] = useState(false);
  const [resultMessage, setResultMessage] = useState('');
  const setTransactionLoading = useLoadingStore.use.setTransactionLoading();
  //初始化async
  useEffect(() => {
    if (!therapistId) {
      return;
    }

    // 创建一个变量来存储取消订阅函数
    let unsubscribe: Taro.DB.Document.IWatcher | null = null;

    const fetchTherapistInfo = async () => {
      await therapistActions.fetchTherapistService(therapistId, true);
    };
    fetchTherapistInfo();
    unsubscribe = therapistActions.watchTherapistOccupancy(therapistId);
    return () => {
      if (unsubscribe) {
        unsubscribe.close();
        unsubscribe = null;
      }
      resetAppointment();
    };
  }, [therapistId]);

  // 提交订单
  const handleSubmitOrder = async () => {
    // 创建订单
    const orderData: CreateOrderParams = {
      therapistId: selectedTherapistId!,
      serviceType: selectedService!.type,
      duration: selectedService!.duration * selectedDurationMultiple,
      startTime: selectedDate! + selectedTimeSlot! * 3600 * 1000,
      price: price,
      location: 'online',
      // 我的信息
      consultationInfo: {
        username: user?.userName!,
        avatar: user?.avatar!,
      },
    };

    try {
      const orderId = await orderUserActions.createOrder(orderData);
      // 成功后直接导航至支付页面
      paymentActions.startPayment(orderId);
    } catch (err) {
      setResultMessage('预约失败，请稍后重试或联系客服');
      setShowResultPopup(true);
      setTransactionLoading(false);
    } finally {
      setTransactionLoading(false);
    }
  };

  // 上一步
  const handlePrevStep = () => {
    if (currentStep === 0) {
      Taro.navigateBack();
      return;
    }
    setCurrentStep(Math.max(0, currentStep - 1)); // 最小为0
  };
  // 处理时间选择 - 现在时间已经通过 store 管理，只需要进入下一步
  const handleNextStep = () => {
    // 时间已经通过 useEffect 自动更新到 orderData
    setCurrentStep(Math.min(2, currentStep + 1)); // 最大为1
  };

  // return (
  //   <PageLol
  //     navigationProps={{
  //       title: '预约咨询',
  //       showBackButton: true,
  //       backText: '取消',
  //     }}
  //   >
  //     <View className='container px-4 py-4'>
  //       <Text>123</Text>
  //     </View>
  //   </PageLol>
  // );
  return (
    <PageLol
      navigationProps={{
        title: '预约咨询',
        showBackButton: true,
        backText: '取消',
      }}
      loading={loading}
      error={!therapistId ? '缺少心理咨询师ID' : error || null}
    >
      {currentTherapistService && (
        <View className='container px-4 py-4'>
          {/* 步骤条 */}
          <Steps
            steps={steps}
            active={currentStep}
            activeIcon='success'
            activeColor='var(--color-primary)'
          />

          <View className='mt-6 pb-24'>
            {/* 步骤内容 */}
            {currentStep === 0 && (
              <TimeSelection
                onBack={handlePrevStep}
                onNext={handleNextStep}
                occupancy={currentTherapistOccuppied ?? null}
                therapistService={currentTherapistService}
              />
            )}

            {currentStep === 1 && (
              <ServiceSelection
                services={currentTherapistService.services.filter(
                  (service) => service.enabled
                )}
                onNext={handleNextStep}
                onBack={handlePrevStep}
              />
            )}

            {currentStep === 2 && (
              <OrderConfirmation
                onBack={handlePrevStep}
                onSubmit={handleSubmitOrder}
              />
            )}
          </View>

          {/* 订单创建结果弹窗 - 仅显示错误 */}
          <ResultPopup
            show={showResultPopup}
            type='error'
            title='预约失败'
            illustration={ILLUSTRATION_MAP.PAYMENT_FAILED}
            content={resultMessage}
            buttonText='返回'
            onButtonClick={() => setShowResultPopup(false)}
            onClose={() => setShowResultPopup(false)}
          />
        </View>
      )}
    </PageLol>
  );
}
