import { Pagination } from '@core/api';
import {
  DistributionOrder,
  DistributionOverview,
  InvitedUser,
} from '@model/distribution.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

interface State {
  // 分销概览数据
  overview: DistributionOverview | null;
  // 分销订单
  orders: {
    list: DistributionOrder[];
    total: number;
    loading: boolean;
    error: Error | null;
    page: number;
    pageSize: number;
  };
  // 邀请的用户
  invitedUsers: {
    list: InvitedUser[];
    total: number;
    loading: boolean;
    error: Error | null;
    page: number;
    pageSize: number;
    pagination: Pagination | null;
  };

  // 加载中状态
  isLoading: boolean;
  // 错误信息
  error: Error | null;
}

interface Action {
  // 方法
  setOverview: (overview: DistributionOverview | null) => void;
  setOrders: (data: { list: DistributionOrder[]; total: number }) => void;
  setInvitedUsers: (data: { list: InvitedUser[]; total: number }) => void;
  setInvitedUsersWithPagination: (data: {
    list: InvitedUser[];
    pagination: Pagination;
  }) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: Error | null) => void;
  resetState: () => void;
}

// 初始状态
const initialState = {
  overview: null,
  orders: {
    list: [],
    total: 0,
    loading: false,
    error: null,
    page: 1,
    pageSize: 10,
  },
  invitedUsers: {
    list: [],
    total: 0,
    loading: false,
    error: null,
    page: 1,
    pageSize: 10,
    pagination: null,
  },

  isLoading: false,
  error: null,
};

// 创建分销状态管理
export const distributionStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        // 设置分销概览
        setOverview: (overview) => set({ overview }),

        // 设置分销订单
        setOrders: (data) =>
          set((state) => ({
            orders: {
              ...state.orders,
              ...data,
            },
          })),

        // 设置邀请的用户
        setInvitedUsers: (data) =>
          set((state) => ({
            invitedUsers: {
              ...state.invitedUsers,
              ...data,
            },
          })),

        // 设置邀请的用户（带分页）
        setInvitedUsersWithPagination: (data) =>
          set((state) => ({
            invitedUsers: {
              ...state.invitedUsers,
              list: data.list,
              total: data.pagination.total,
              pagination: data.pagination,
            },
          })),

        // 设置提现记录
        // setWithdrawRecords: (data) =>
        //   set((state) => ({
        //     withdrawRecords: {
        //       ...state.withdrawRecords,
        //       ...data,
        //     },
        //   })),

        // 设置加载状态
        setLoading: (loading) => set({ isLoading: loading }),

        // 设置错误信息
        setError: (error) => set({ error }),

        // 重置状态
        resetState: () => set(initialState),
      }),

      {
        name: StorageSceneKey.DISTRIBUTION,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化 用户列表
        partialize: (state) => ({
          overview: state.overview,
          orders: state.orders,
          invitedUsers: state.invitedUsers,
          // withdrawRecords: state.withdrawRecords,
        }),
      }
    )
  )
);

export const useDistributionStore = createSelectors(distributionStore);
