import { Calendar, CheckList, Icon, Picker } from '@antmjs/vantui';
import { DEFAULT_WORK_TIME } from '@constants/config';
import { DAYS_OF_WEEK } from '@constants/text';
import { work_time } from '@model/therapist.interface';
import { Text, View } from '@tarojs/components';
import { useCallback, useMemo, useState } from 'react';

// 时间段选项
const TIME_SLOTS: string[] = [];
for (let hour = 6; hour <= 22; hour++) {
  const hourStr = hour < 10 ? `0${hour}` : `${hour}`;
  TIME_SLOTS.push(`${hourStr}:00`);
}

interface WorkTimeEditorProps {
  workTime?: work_time;
  onFormChange: (newWorkTime: work_time) => void;
}

export default function WorkTimeEditor({
  workTime = DEFAULT_WORK_TIME,
  onFormChange,
}: WorkTimeEditorProps) {
  // 例外日状态
  const [showCalendar, setShowCalendar] = useState(false);

  // 处理工作日选择变化
  const handleWorkDaysChange = useCallback(
    (e: { detail: Array<string | number> }) => {
      const selectedDays = e.detail.map((day) => Number(day));
      onFormChange({
        ...(workTime || DEFAULT_WORK_TIME),
        workDays: selectedDays,
      });
    },
    [workTime, onFormChange]
  );

  // 处理工作开始时间变化
  const handleStartTimeChange = useCallback(
    (e: any) => {
      console.log('handleStartTimeChange', e);
      const timeStr = e.detail[0] as string;
      const hour = parseInt(timeStr.split(':')[0], 10);
      onFormChange({
        ...(workTime || { workDays: [] }),
        start: hour,
      });
    },
    [workTime, onFormChange]
  );

  // 处理工作结束时间变化
  const handleEndTimeChange = useCallback(
    (e: any) => {
      console.log('handleEndTimeChange', e);
      const timeStr = e.detail[0] as string;
      const hour = parseInt(timeStr.split(':')[0], 10);
      onFormChange({
        ...(workTime || { workDays: [] }),
        end: hour,
      });
    },
    [workTime, onFormChange]
  );

  // 处理日历日期选择
  const handleCalendarSelect = useCallback(
    (event: any) => {
      console.log(event.detail);
      if (Array.isArray(event.detail.value)) {
        // 从日期中取出天数并去重并排序
        const days = event.detail.value.map((date: Date) =>
          date.getDate()
        ) as number[];
        const uniqueDays = [...new Set(days)].sort((a, b) => a - b);
        onFormChange({
          ...(workTime || { workDays: [] }),
          exceptionDays: uniqueDays,
        });
      }
      setShowCalendar(false);
    },
    [workTime, onFormChange]
  );

  // 处理日历关闭
  const handleCalendarClose = useCallback(() => {
    setShowCalendar(false);
  }, []);

  // 处理日历打开
  const handleCalendarOpen = useCallback(() => {
    setShowCalendar(true);
  }, []);

  // 计算日历的日期范围
  const calendarDateRange = useMemo(() => {
    const now = new Date();
    const minDate = now.getTime();
    const maxDate = new Date(now.getFullYear() + 1, 11, 31).getTime();
    return { minDate, maxDate };
  }, []);

  return (
    <View className='p-4'>
      {/* 工作日设置 */}
      <View className='mb-6'>
        <Text className='text-lg font-bold mb-3 block'>工作日设置</Text>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>选择工作日</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <CheckList
              data={DAYS_OF_WEEK}
              value={workTime?.workDays?.map((day) => day.toString()) || []}
              onChange={handleWorkDaysChange}
              placeholder='请选择工作日'
              showArrowRight
              checkAll
              bodyHeight='30vh'
            />
          </View>
        </View>
      </View>

      {/* 工作时间设置 */}
      <View className='mb-6'>
        <Text className='text-lg font-bold mb-3 block'>工作时间设置</Text>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>工作开始时间</Text>
          </View>
          <View className='flex items-center justify-end flex-1 h-full'>
            <Picker
              className='h-full w-full'
              mode='content'
              columns={[TIME_SLOTS]}
              value={`${String(workTime?.start ?? 9).padStart(2, '0')}:00`}
              onInput={handleStartTimeChange}
              allowClear={false}
            />
          </View>
        </View>

        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>工作结束时间</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <Picker
              mode='content'
              columns={[TIME_SLOTS]}
              value={`${String(workTime?.end ?? 18).padStart(2, '0')}:00`}
              onInput={handleEndTimeChange}
              placeholder='请选择工作结束时间'
              allowClear={false}
            />
          </View>
        </View>
      </View>

      {/* 例外日设置 */}
      <View className='mb-6'>
        <Text className='text-lg font-bold mb-3 block'>例外日设置</Text>
        <Text className='text-sm text-gray-500 mb-3 block'>
          设置每月的例外日，这些日期将不提供预约服务
        </Text>

        {/* 添加例外日按钮 */}
        <View
          className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'
          onClick={handleCalendarOpen}
        >
          <View className='flex items-center flex-1'>
            {/* 例外日列表 */}
            {workTime?.exceptionDays && workTime.exceptionDays.length > 0 ? (
              <Text className='text-md mr-2 font-bold'>
                {workTime?.exceptionDays?.map((day) => day).join(',')}
              </Text>
            ) : (
              <Text>选择例外日</Text>
            )}
          </View>
          <Icon name='arrow-down' />
        </View>

        {/* 日历选择器 */}
        <Calendar
          show={showCalendar}
          type='multiple'
          onClose={handleCalendarClose}
          onConfirm={handleCalendarSelect}
          minDate={calendarDateRange.minDate}
          maxDate={calendarDateRange.maxDate}
        />
      </View>
    </View>
  );
}
