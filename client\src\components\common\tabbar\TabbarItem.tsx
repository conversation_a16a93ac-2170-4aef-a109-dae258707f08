import { Image, View } from '@tarojs/components';
import React, { ReactNode } from 'react';

interface TabbarItemProps {
  icon?: string;
  activeIcon?: string;
  renderIcon?: ReactNode;
  renderIconActive?: ReactNode;
  active?: boolean;
  text: string;
  onClick?: () => void;
}

const TabbarItem: React.FC<TabbarItemProps> = ({
  icon,
  activeIcon,
  renderIcon,
  renderIconActive,
  active = false,
  text,
  onClick,
}) => {
  // 获取父组件的主题颜色，如果父组件有text-[#xxx]类，则使用该颜色
  const activeTextColor = active ? 'text-current' : 'text-[#646566]';

  return (
    <View
      className='flex flex-col items-center justify-center flex-1 h-[50Px]  box-border'
      onClick={onClick}
    >
      <View className='flex items-center justify-center  mb-1'>
        {active
          ? renderIconActive ||
            (activeIcon && (
              <Image className='w-[18Px] h-[18Px]' src={activeIcon} />
            ))
          : renderIcon ||
            (icon && <Image className='w-[18Px] h-[18Px]' src={icon} />)}
      </View>
      <View className={`text-xs leading-none ${activeTextColor}`}>{text}</View>
    </View>
  );
};

export default TabbarItem;
