import { Tag } from '@antmjs/vantui';
import { CONSULTATION_DIRECTIONS_MAP } from '@constants/text';
import { ConsultationDirection } from '@model/common.interface';
import { View } from '@tarojs/components';

interface DirectionTagsProps {
  directions: ConsultationDirection[];
  maxCount?: number;
  className?: string;
}

const DirectionTags: React.FC<DirectionTagsProps> = ({
  directions,
  maxCount = 3,
  className = '',
}) => {
  if (!directions || directions.length === 0) return null;

  const displayDirections = directions.slice(0, maxCount);
  // console.log('displayDirections', displayDirections);/
  return (
    <View className={`flex flex-row flex-wrap gap-2 w-full ${className}`}>
      {displayDirections.map((tag, idx) => (
        <Tag
          key={idx}
          plain
          type='default'
          color='var(--color-text-secondary)'
          size='small'
        >
          {CONSULTATION_DIRECTIONS_MAP[tag]?.label}
        </Tag>
      ))}
    </View>
  );
};

export default DirectionTags;
