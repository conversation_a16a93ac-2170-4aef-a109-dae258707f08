import { callCloudFunction } from './cloud';

export const makeCall = async (
  orderId: string,
  callerId: string,
  calleeId: string
) => {
  try {
    const result = await callCloudFunction('videoCall', 'makeCall', {
      orderId,
      callerId,
      calleeId,
    });
    console.log('makeCall result', result);
    if (result && result.success) {
      // 保存用户信息
      if (result.data) {
        const { signature, nonceStr, timeStamp, groupId } = result.data;
        return { signature, nonceStr, timeStamp, groupId };
      }
    } else {
      console.error('makeCall error', result);
      throw new Error(result.message || 'makeCall error');
    }
  } catch (error) {
    console.error('makeCall error', error);
    throw error;
  }
};
