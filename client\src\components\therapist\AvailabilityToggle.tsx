import { Text, View } from '@tarojs/components';

interface AvailabilityToggleProps {
  isAvailable: boolean;
  onChange: (available: boolean) => void;
}

export default function AvailabilityToggle({
  isAvailable,
  onChange,
}: AvailabilityToggleProps) {
  return (
    <View className='w-full py-3 border-t border-b border-gray-200 my-4'>
      <Text className='text-base font-bold mb-2'>设置日期状态</Text>
      <View className='flex flex-row items-center justify-around px-4'>
        <View
          className={`flex flex-row items-center justify-center p-3 rounded-lg ${
            isAvailable ? 'bg-primary text-white' : 'bg-gray-100'
          }`}
          onClick={() => onChange(true)}
        >
          <View
            className={`w-4 h-4 rounded-full border ${
              isAvailable ? 'bg-white border-white' : 'border-gray-400'
            } mr-2`}
          >
            {isAvailable && (
              <View className='w-2 h-2 bg-primary rounded-full m-auto' />
            )}
          </View>
          <Text className={`${isAvailable ? 'text-white' : 'text-gray-700'}`}>
            可约
          </Text>
        </View>
        <View
          className={`flex flex-row items-center justify-center p-3 rounded-lg ${
            !isAvailable ? 'bg-primary text-white' : 'bg-gray-100'
          }`}
          onClick={() => onChange(false)}
        >
          <View
            className={`w-4 h-4 rounded-full border ${
              !isAvailable ? 'bg-white border-white' : 'border-gray-400'
            } mr-2`}
          >
            {!isAvailable && (
              <View className='w-2 h-2 bg-primary rounded-full m-auto' />
            )}
          </View>
          <Text className={`${!isAvailable ? 'text-white' : 'text-gray-700'}`}>
            休息
          </Text>
        </View>
      </View>
    </View>
  );
}
