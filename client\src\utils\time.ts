export const DAY_NAMES = [
  '周日',
  '周一',
  '周二',
  '周三',
  '周四',
  '周五',
  '周六',
];

// 一天中的时间段 (7:00 - 21:00)
export const TIME_SLOTS = Array.from({ length: 15 }, (_, i) => i + 7);

/**
 * 获取精确到天的时间戳（毫秒）
 * @param date 日期对象
 * @returns 时间戳（毫秒）
 */
export const getDayInMillis = (date: Date): number => {
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate()
  ).getTime();
};

/**
 * 获取月份的时间戳（毫秒）
 * @param date 日期对象
 * @returns 时间戳（毫秒）
 */
export const getMonthInMillis = (date?: Date): number => {
  if (!date) {
    date = new Date();
  }
  return new Date(date.getFullYear(), date.getMonth() + 1, 1).getTime();
};
/**
 * 从时间戳获取精确到天的时间戳（毫秒）
 * @param dateInMillis 时间戳（毫秒）
 * @returns 精确到天的时间戳（毫秒）
 */
export const getDayInMillisFromDateInMillis = (
  dateInMillis: number
): number => {
  const date = new Date(dateInMillis);
  return getDayInMillis(date);
};

export const getHourFromDateInMillis = (dateInMillis: number) => {
  const date = new Date(dateInMillis);
  return date.getHours();
};

/**
 * 格式化日期为 YYYY-MM-DD
 * @param date 日期或时间戳
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | number): string => {
  const d = typeof date === 'number' ? new Date(date) : date;
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 格式化日期为 7月
 * @param date 日期或时间戳
 * @returns 格式化后的日期字符串
 */
export const formatMonth = (date: Date | number): string => {
  const d = typeof date === 'number' ? new Date(date) : date;
  const month = String(d.getMonth() + 1);
  return `${month}月`;
};

/**
 * 格式化日期为 2025年7月
 * @param date 日期或时间戳
 * @returns 格式化后的日期字符串
 */
export const formatMonth2 = (date: Date | number): string => {
  const d = typeof date === 'number' ? new Date(date) : date;
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1);
  return `${year}年${month}月`;
};

/**
 * 格式化日期为 MM月DD,YYYY  7月6，2025
 * @param date 日期或时间戳
 * @returns 格式化后的日期字符串
 */
export const formatDate2 = (date: Date | number): string => {
  const d = typeof date === 'number' ? new Date(date) : date;
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${month}月${day}, ${year}`;
};

// 格式化时间 2025-05-26 10:00
export const formatTime = (dateInMillis: number, timeSlot: number) => {
  if (!dateInMillis) return '未选择';

  const date = new Date(dateInMillis);
  return `${date.getFullYear()}-${
    date.getMonth() + 1
  }-${date.getDate()} ${timeSlot}:00`;
};

// 格式化时间 2025-05-26 10:00
export const formatTime2 = (dateInMillis: number) => {
  if (!dateInMillis) return '未选择';

  const date = new Date(dateInMillis);
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${pad2(
    date.getHours()
  )}:${pad2(date.getMinutes())}`;
};

// 格式化时间 如果是今天，则显示10:00，否则显示2025-05-26 10:00
export const formatTimeForToday = (dateInMillis: number) => {
  if (!dateInMillis) return '未选择';
  const date = new Date(dateInMillis);
  const today = new Date();
  if (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  ) {
    return `${pad2(date.getHours())}:${pad2(date.getMinutes())}`;
  }
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${pad2(
    date.getHours()
  )}:${pad2(date.getMinutes())}`;
};

/**
 * 格式化聊天消息时间
 * 今天: 显示时间(HH:mm)
 * 昨天: 显示"昨天"
 * 其他: 显示日期(MM-DD)
 * @param date 日期或时间戳
 */
export const formatTime6 = (date: Date | number): string => {
  const d = typeof date === 'number' ? new Date(date) : date;
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // 判断是今天
  if (
    d.getFullYear() === today.getFullYear() &&
    d.getMonth() === today.getMonth() &&
    d.getDate() === today.getDate()
  ) {
    return `${pad2(d.getHours())}:${pad2(d.getMinutes())}`;
  }

  // 判断是昨天
  if (
    d.getFullYear() === yesterday.getFullYear() &&
    d.getMonth() === yesterday.getMonth() &&
    d.getDate() === yesterday.getDate()
  ) {
    return '昨天';
  }

  // 其他日期显示月-日
  return `${d.getMonth() + 1}-${d.getDate()}`;
};

//countdown 倒计时 返回小时和分钟 4小时30分钟
export const countdown = (dateInMillis: number, timeSlot: number) => {
  const now = new Date().getTime();
  const diff = dateInMillis + timeSlot * 60 * 60 * 1000 - now;
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  return `${hours}小时${minutes}分钟`;
};

// 格式化时间 10:00 - 12:00, 2025-05-26
export const formatTime3 = (
  startTimeInMillis: number,
  endTimeInMillis: number
) => {
  const startDate = new Date(startTimeInMillis);
  const endDate = new Date(endTimeInMillis);
  // 小时和分钟都是2位数，不足补0
  return `${pad2(startDate.getHours())}:${pad2(
    startDate.getMinutes()
  )} - ${pad2(endDate.getHours())}:${pad2(
    endDate.getMinutes()
  )}, ${startDate.getFullYear()}-${
    startDate.getMonth() + 1
  }-${startDate.getDate()}`;
};

// 格式化时间 10:00 固定格式，小时和分钟都是2位，不足2位补0
export const formatTime4 = (dateInMillis: number) => {
  const date = new Date(dateInMillis);
  return `${pad2(date.getHours())}:${pad2(date.getMinutes())}`;
};

// 3分56， duration in seconds
export const formatTime5 = (duration: number) => {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60); // 整数秒 不足2位补0
  const secondsStr = seconds.toString().padStart(2, '0');
  return `${minutes}分${secondsStr}`;
};

function pad2(n: number) {
  return n.toString().padStart(2, '0');
}
