import { useEffect, useRef, useState } from 'react';
import { formatTime } from '../pages/sub-packages/services/therapist-video/components/VideoCallUI';

// 通话时长计时Hook
export const useCallDuration = (isConnected: boolean): [string, number] => {
  const [duration, setDuration] = useState<number>(0);
  const timerRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);

  useEffect(() => {
    if (isConnected && !timerRef.current) {
      // 记录开始时间
      startTimeRef.current = Date.now();

      // 启动计时器 - 分钟更新一次
      timerRef.current = setInterval(() => {
        const currentDuration = Math.floor(
          (Date.now() - startTimeRef.current) / 1000
        );
        setDuration(currentDuration);
      }, 60000) as unknown as number;
    } else if (!isConnected && timerRef.current) {
      // 停止计时器
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // 组件卸载时清理计时器
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isConnected]);

  // 返回格式化的时间字符串和原始秒数
  return [formatTime(duration), duration];
};
