const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 事务状态常量
const TX_STATUS = {
  PENDING: "pending",
  COMMITTED: "committed",
  ROLLEDBACK: "rolledback",
  FAILED: "failed",
};

// 步骤状态常量
const STEP_STATUS = {
  PENDING: "pending",
  COMPLETED: "completed",
  FAILED: "failed",
  ROLLEDBACK: "rolledback",
};

/**
 * 分布式事务协调器
 * 用于管理跨云函数、跨回调的长事务
 * 特别适用于涉及第三方支付、退款等异步流程的业务场景
 */
class TransactionCoordinator {
  constructor() {
    this.collection = "transaction_logs";
  }

  /**
   * 开始一个新事务
   * @param {string} businessId 业务ID，如订单ID
   * @param {string} businessType 业务类型，如'order_cancel'
   * @param {object} metadata 事务元数据
   * @returns {Promise<string>} 事务ID
   */
  async startTransaction(businessId, businessType, metadata = {}) {
    const txId = `tx_${Date.now()}_${businessId}_${Math.random()
      .toString(36)
      .substring(2, 10)}`;

    const transaction = {
      _id: txId,
      businessId,
      businessType,
      status: TX_STATUS.PENDING,
      steps: [],
      metadata,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      completedAt: null,
      error: null,
    };

    await db.collection(this.collection).add({
      data: transaction,
    });

    console.log(
      `[TransactionCoordinator] 事务已创建: ${txId}, 业务ID: ${businessId}, 类型: ${businessType}`
    );
    return txId;
  }

  /**
   * 提交事务
   * @param {string} txId 事务ID
   * @returns {Promise<void>}
   */
  async commitTransaction(txId) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    if (tx.status !== TX_STATUS.PENDING) {
      console.warn(
        `[TransactionCoordinator] 事务状态非pending，无法提交: ${txId}, 当前状态: ${tx.status}`
      );
      return;
    }

    await db
      .collection(this.collection)
      .doc(txId)
      .update({
        data: {
          status: TX_STATUS.COMMITTED,
          updatedAt: Date.now(),
          completedAt: Date.now(),
        },
      });

    console.log(`[TransactionCoordinator] 事务已提交: ${txId}`);
  }

  /**
   * 回滚事务
   * @param {string} txId 事务ID
   * @param {string} reason 回滚原因
   * @returns {Promise<void>}
   */
  async rollbackTransaction(txId, reason) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    if (tx.status === TX_STATUS.COMMITTED) {
      console.warn(
        `[TransactionCoordinator] 事务已提交，需要补偿操作: ${txId}`
      );
    }

    // 按照步骤的相反顺序执行回滚
    const steps = [...tx.steps].reverse();

    for (const step of steps) {
      if (step.status === STEP_STATUS.COMPLETED && step.rollbackHandler) {
        try {
          console.log(
            `[TransactionCoordinator] 回滚步骤: ${step.name}, 事务ID: ${txId}`
          );
          await this._executeRollback(step.rollbackHandler, step.data, tx);

          // 更新步骤状态
          await db
            .collection(this.collection)
            .doc(txId)
            .update({
              data: {
                ["steps." +
                tx.steps.findIndex((s) => s.name === step.name) +
                ".status"]: STEP_STATUS.ROLLEDBACK,
                ["steps." +
                tx.steps.findIndex((s) => s.name === step.name) +
                ".rolledbackAt"]: Date.now(),
              },
            });
        } catch (error) {
          console.error(
            `[TransactionCoordinator] 步骤回滚失败: ${step.name}, 事务ID: ${txId}`,
            error
          );
          // 继续尝试回滚其他步骤
        }
      }
    }

    await db
      .collection(this.collection)
      .doc(txId)
      .update({
        data: {
          status: TX_STATUS.ROLLEDBACK,
          error: reason,
          updatedAt: Date.now(),
          completedAt: Date.now(),
        },
      });

    console.log(
      `[TransactionCoordinator] 事务已回滚: ${txId}, 原因: ${reason}`
    );
  }

  /**
   * 添加事务步骤
   * @param {string} txId 事务ID
   * @param {object} step 步骤信息
   * @param {string} step.name 步骤名称
   * @param {object} step.data 步骤数据
   * @param {string} [step.rollbackHandler] 回滚处理函数名称
   * @returns {Promise<void>}
   */
  async addStep(txId, step) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    if (tx.status !== TX_STATUS.PENDING) {
      throw new Error(
        `事务状态非pending，无法添加步骤: ${txId}, 当前状态: ${tx.status}`
      );
    }

    const stepData = {
      name: step.name,
      status: STEP_STATUS.PENDING,
      data: step.data || {},
      rollbackHandler: step.rollbackHandler,
      addedAt: Date.now(),
    };

    await db
      .collection(this.collection)
      .doc(txId)
      .update({
        data: {
          steps: _.push([stepData]),
          updatedAt: Date.now(),
        },
      });

    console.log(
      `[TransactionCoordinator] 步骤已添加: ${step.name}, 事务ID: ${txId}`
    );
    return step.name;
  }

  /**
   * 完成事务步骤
   * @param {string} txId 事务ID
   * @param {string} stepName 步骤名称
   * @param {object} result 步骤结果
   * @returns {Promise<void>}
   */
  async completeStep(txId, stepName, result = {}) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    const stepIndex = tx.steps.findIndex((s) => s.name === stepName);
    if (stepIndex === -1) {
      throw new Error(`步骤不存在: ${stepName}, 事务ID: ${txId}`);
    }

    await db
      .collection(this.collection)
      .doc(txId)
      .update({
        data: {
          ["steps." + stepIndex + ".status"]: STEP_STATUS.COMPLETED,
          ["steps." + stepIndex + ".result"]: result,
          ["steps." + stepIndex + ".completedAt"]: Date.now(),
          updatedAt: Date.now(),
        },
      });

    console.log(
      `[TransactionCoordinator] 步骤已完成: ${stepName}, 事务ID: ${txId}`
    );
  }

  /**
   * 失败事务步骤
   * @param {string} txId 事务ID
   * @param {string} stepName 步骤名称
   * @param {string} error 错误信息
   * @returns {Promise<void>}
   */
  async failStep(txId, stepName, error) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    const stepIndex = tx.steps.findIndex((s) => s.name === stepName);
    if (stepIndex === -1) {
      throw new Error(`步骤不存在: ${stepName}, 事务ID: ${txId}`);
    }

    await db
      .collection(this.collection)
      .doc(txId)
      .update({
        data: {
          ["steps." + stepIndex + ".status"]: STEP_STATUS.FAILED,
          ["steps." + stepIndex + ".error"]: error.toString(),
          ["steps." + stepIndex + ".failedAt"]: Date.now(),
          updatedAt: Date.now(),
        },
      });

    console.log(
      `[TransactionCoordinator] 步骤失败: ${stepName}, 事务ID: ${txId}, 错误: ${error}`
    );
  }

  /**
   * 获取事务状态
   * @param {string} txId 事务ID
   * @returns {Promise<object>} 事务状态
   */
  async getTransactionStatus(txId) {
    const tx = await this._getTransaction(txId);
    if (!tx) {
      throw new Error(`事务不存在: ${txId}`);
    }

    return {
      id: tx._id,
      status: tx.status,
      businessId: tx.businessId,
      businessType: tx.businessType,
      steps: tx.steps.map((step) => ({
        name: step.name,
        status: step.status,
        completedAt: step.completedAt,
        failedAt: step.failedAt,
        rolledbackAt: step.rolledbackAt,
      })),
      createdAt: tx.createdAt,
      completedAt: tx.completedAt,
      error: tx.error,
    };
  }

  /**
   * 查询未完成的事务
   * @param {object} options 查询选项
   * @param {string} [options.businessType] 业务类型
   * @param {number} [options.olderThanMinutes] 创建时间早于多少分钟
   * @returns {Promise<Array>} 未完成的事务列表
   */
  async findPendingTransactions(options = {}) {
    const query = { status: TX_STATUS.PENDING };

    if (options.businessType) {
      query.businessType = options.businessType;
    }

    if (options.olderThanMinutes) {
      const timestamp = Date.now() - options.olderThanMinutes * 60 * 1000;
      query.createdAt = _.lt(timestamp);
    }

    const result = await db
      .collection(this.collection)
      .where(query)
      .orderBy("createdAt", "asc")
      .limit(100)
      .get();

    return result.data;
  }

  /**
   * 清理已完成的事务
   * @param {number} olderThanDays 完成时间早于多少天
   * @returns {Promise<number>} 清理的事务数量
   */
  async cleanupCompletedTransactions(olderThanDays = 30) {
    const timestamp = Date.now() - olderThanDays * 24 * 60 * 60 * 1000;

    const result = await db
      .collection(this.collection)
      .where({
        status: _.or(_.eq(TX_STATUS.COMMITTED), _.eq(TX_STATUS.ROLLEDBACK)),
        completedAt: _.lt(timestamp),
      })
      .remove();

    console.log(
      `[TransactionCoordinator] 已清理 ${result.deleted} 条已完成事务`
    );
    return result.deleted;
  }

  /**
   * 获取事务详情
   * @private
   * @param {string} txId 事务ID
   * @returns {Promise<object>} 事务详情
   */
  async _getTransaction(txId) {
    const result = await db.collection(this.collection).doc(txId).get();
    return result.data;
  }

  /**
   * 执行回滚处理
   * @private
   * @param {string} handlerName 处理函数名称
   * @param {object} data 步骤数据
   * @param {object} tx 事务对象
   * @returns {Promise<void>}
   */
  async _executeRollback(handlerName, data, tx) {
    // 这里可以根据handlerName动态加载对应的回滚处理函数
    // 简单实现：假设回滚处理函数已经在全局注册
    if (typeof global[handlerName] === "function") {
      await global[handlerName](data, tx);
    } else {
      console.warn(
        `[TransactionCoordinator] 回滚处理函数未找到: ${handlerName}`
      );
    }
  }
}

// 创建全局单例
const coordinator = new TransactionCoordinator();

module.exports = {
  coordinator,
  TX_STATUS,
  STEP_STATUS,
};
