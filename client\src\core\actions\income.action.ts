import { IncomeListRequest } from '@core/api';
import { Wallet } from '@model/income.model';
import { incomeService } from '@services/income.service';
import { useIncomeStore } from '@stores/income.store';
import { getMonthInMillis } from '@utils/time';
import { ensureLoggedIn } from './auth.action';

/**
 * 收入列表操作
 */
export const incomeActions = {
  loadMyWallet: async (forceRefresh = false): Promise<boolean> => {
    ensureLoggedIn();

    const store = useIncomeStore.getState();

    // 如果已有数据且不强制刷新，则直接返回
    if (store.wallet && !forceRefresh) {
      return true;
    }
    try {
      // 调用服务获取数据
      const myWallet = await incomeService.getMyWallet();

      if (myWallet) {
        store.setWallet(myWallet as Wallet);
        return true;
      }

      return false;
    } catch (error) {
      console.error('加载咨询师钱包信息失败:', error);
      return false;
    }
  },

  fetchStatSummary: async (forceRefresh = false): Promise<void> => {
    const store = useIncomeStore.getState();
    if (store.statSummary && !forceRefresh) {
      return;
    }
    try {
      const result = await incomeService.getStatSummary();
      console.log('incomeActions fetchStatSummary result', result);
      store.setStatSummary(result);
    } catch (error) {
      store.setError('获取收入统计摘要失败');
    }
  },

  fetchIncomeChart: async (forceRefresh = false): Promise<void> => {
    const store = useIncomeStore.getState();
    if (store.incomeChart.length > 0 && !forceRefresh) {
      return;
    }
    try {
      //当前月份的时间戳
      const currentMonth = getMonthInMillis();
      //前6个月的时间戳
      const sixMonthsAgo = currentMonth - 6 * 30 * 24 * 60 * 60 * 1000;
      const result = await incomeService.getIncomeChart(
        sixMonthsAgo,
        currentMonth
      );
      console.log(
        'incomeActions fetchIncomeChart result,currentMonth, sixMonthsAgo ',
        result,
        currentMonth,
        sixMonthsAgo
      );
      store.setIncomeChart(result);
    } catch (error) {
      store.setError('获取收入图表失败');
    }
  },

  /**
   * 获取收入列表
   */
  fetchIncomeList: async (params: IncomeListRequest = {}): Promise<void> => {
    const store = useIncomeStore.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);
    store.setError(null);

    try {
      const { forceRefresh } = params;

      if (!forceRefresh) {
        // 如果已有数据且不强制刷新，则直接返回
        if (store.incomeList.length > 0) {
          store.setLoading(false);
          return;
        }
      }

      const result = await incomeService.getIncomeList({
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        incomeType: params.params?.incomeType,
        status: params.params?.status,
        query: params.params?.query,
      });

      if (result.success) {
        store.setIncomeList(result.data);
        store.setPagination(result.pagination || null);
        store.setFilters(params.params || {});
        return;
      }

      throw new Error('获取收入列表失败');
    } catch (error) {
      store.setError('获取收入列表失败');
      console.error('获取收入列表失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 加载更多收入
   */
  loadMoreIncomeDetails: async (): Promise<void> => {
    const store = useIncomeStore.getState();
    if (store.loading) {
      return;
    }
    store.setLoading(true);
    const { pagination, filters } = store;

    // 没有更多数据了
    if (!pagination || !pagination.hasNext) return;

    // 构建请求参数
    const params: IncomeListRequest = {
      page: pagination.page + 1,
      pageSize: pagination.pageSize,
      params: filters,
    };

    try {
      // 从服务获取数据
      const result = await incomeService.getIncomeList({
        page: params.page,
        pageSize: params.pageSize,
        incomeType: params.params?.incomeType,
        status: params.params?.status,
        query: params.params?.query,
      });

      if (result.success) {
        const { incomeList: currentIncomeList } = store;

        // 将新数据添加到现有数据后面
        const combinedIncomeList = [
          ...(currentIncomeList || []),
          ...result.data,
        ];

        // 更新状态
        store.setIncomeList(combinedIncomeList);
        store.setPagination(result.pagination || null);
      }
    } catch (error) {
      console.error('加载更多收入失败:', error);
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 筛选收入
   */
  filterIncome: async (filters: {
    incomeType?: 1 | 2 | 3;
    status?: 'settled' | 'frozen' | 'refunded';
    query?: string;
  }): Promise<void> => {
    const store = useIncomeStore.getState();
    store.setFilters(filters);
    store.setLoading(true);

    try {
      const result = await incomeService.getIncomeList({
        page: 1,
        pageSize: store.pagination?.pageSize || 10,
        incomeType: filters.incomeType,
        status: filters.status,
        query: filters.query,
      });

      if (result.success) {
        store.setIncomeList(result.data);
        store.setPagination(result.pagination || null);
      }
    } catch (error) {
      console.error('筛选收入失败:', error);
      store.setError('筛选收入失败');
    } finally {
      store.setLoading(false);
    }
  },

  /**
   * 清除收入列表缓存
   */
  clearIncomeListCache: () => {
    const store = useIncomeStore.getState();
    store.reset();
  },
};
