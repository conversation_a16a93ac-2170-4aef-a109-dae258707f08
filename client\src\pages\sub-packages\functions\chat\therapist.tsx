import PageLol from '@components/common/page-meta';
import { AVATAR_DEFAULT } from '@constants/assets';
import { useChat } from '@hooks/useChat';
import { useUserPublic } from '@hooks/useUserPublic';
import { useGlobalStore } from '@stores/global.store';
import { View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import { ChatInput } from './components/ChatInput';
import ChatList from './components/ChatList';

/**
 * 咨询师端聊天页面
 * 用于咨询师与用户之间的聊天
 */
export default function TherapistChatPage() {
  const router = useRouter();
  const { userId } = router.params;
  // 获取当前咨询师信息
  const therapistId = useGlobalStore.use.openid();

  // 获取用户信息
  const user = useUserPublic(userId || '');
  const userName = user?.userName || '用户';

  // 使用聊天Hook
  const {
    currentMessages,
    error,
    loading,
    handleSendText,
    handleSendImage,
    handleSendAudio,
    handleSendOrder,
    handleLoadMore,
    handleResendMessage,
    handleMarkMessageAsRead,
  } = useChat(therapistId || '');

  return (
    <PageLol
      useNav
      navigationProps={{
        title: userName,
        showBackButton: true,
      }}
      loading={loading}
      error={error || null}
    >
      <View className='flex flex-col h-screen relative bg-[#f5f5f5] overflow-hidden'>
        {/* 消息列表 */}
        <ChatList
          messages={currentMessages}
          loading={loading}
          receiverAvatar={user?.avatar || AVATAR_DEFAULT}
          onLoadMore={handleLoadMore}
          onResendMessage={handleResendMessage}
          onMarkAsRead={handleMarkMessageAsRead}
        />

        {/* 输入框 */}
        <ChatInput
          onSendText={(text) => handleSendText(text)}
          onSendImage={() => handleSendImage()}
          onSendAudio={() => handleSendAudio()}
          onSendOrder={() => handleSendOrder()}
        />
      </View>
    </PageLol>
  );
}
