import { chatActions } from '@core/actions/chat.action';
import { useChatStore } from '@stores/chat.store';
import { useGlobalStore } from '@stores/global.store';
import { genSessionId } from '@utils/app';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';

export function useSession(peerId: string) {
  console.log('useSessionId', peerId);

  const myOpenId = useGlobalStore.use.openid();
  // 使用useShallow优化从store中获取session
  const session = useChatStore(
    useShallow((state) =>
      state.getSession(genSessionId(peerId, myOpenId || ''))
    )
  );

  useEffect(() => {
    console.log('useEffect', peerId, myOpenId, session);
    chatActions.fetchSession([peerId, myOpenId || '']);
  }, [peerId, myOpenId, session]);

  return session;
}
