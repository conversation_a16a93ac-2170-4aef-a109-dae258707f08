import { ChatMessage, ChatSession } from '@model/chat.interface';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';

// 空数组常量，避免重复创建
const EMPTY_MESSAGES: ChatMessage[] = [];

interface State {
  loading: boolean;
  error: string | null;
  sessions: ChatSession[];
  currentSessionId: string | null;
  // 消息存储结构: { sessionId: ChatMessage[] }
  messages: Record<string, ChatMessage[]>;
}

interface Action {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSessions: (sessions: ChatSession[]) => void;
  getSession: (sessionId: string) => ChatSession | undefined;
  addSession: (session: ChatSession) => void;
  updateSession: (session: ChatSession) => void;
  removeSession: (sessionId: string) => void;
  setCurrentSessionId: (sessionId: string | null) => void;
  setMessages: (sessionId: string, messages: ChatMessage[]) => void;
  addMessage: (sessionId: string, message: ChatMessage) => void;
  addMessages: (sessionId: string, messages: ChatMessage[]) => void;
  updateMessage: (
    sessionId: string,
    messageId: string,
    update: Partial<ChatMessage>
  ) => void;
  prependMessages: (sessionId: string, messages: ChatMessage[]) => void;
  // markSessionAsRead: (sessionId: string) => void;
  getMessageById: (
    sessionId: string,
    messageId: string
  ) => ChatMessage | undefined;
  getMessages: (sessionId: string) => ChatMessage[];
}

const initialState: State = {
  loading: false,
  error: null,
  sessions: [],
  currentSessionId: null,
  messages: {},
};

const chatStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,
        setLoading: (loading: boolean) => set({ loading }),
        setError: (error: string | null) => set({ error }),
        setSessions: (sessions: ChatSession[]) => set({ sessions }),
        getSession: (sessionId: string) =>
          get().sessions.find((s) => s.id === sessionId),
        addSession: (session: ChatSession) =>
          set((state) => {
            const exists = state.sessions.some((s) => s.id === session.id);
            if (!exists) {
              state.sessions.push(session);
            }
          }),
        updateSession: (session: ChatSession) =>
          set((state) => {
            const index = state.sessions.findIndex((s) => s.id === session.id);
            if (index !== -1) {
              state.sessions[index] = session;
            }
          }),
        removeSession: (sessionId: string) =>
          set((state) => {
            state.sessions = state.sessions.filter((s) => s.id !== sessionId);
            delete state.messages[sessionId];
          }),
        setCurrentSessionId: (sessionId: string | null) =>
          set({ currentSessionId: sessionId }),
        setMessages: (sessionId: string, messages: ChatMessage[]) =>
          set((state) => {
            console.log('setMessages', sessionId, messages);
            state.messages[sessionId] = messages;
          }),
        addMessage: (sessionId: string, message: ChatMessage) =>
          set((state) => {
            if (!state.messages[sessionId]) {
              state.messages[sessionId] = [];
            }
            state.messages[sessionId].push(message);

            // 更新会话的最后一条消息
            const sessionIndex = state.sessions.findIndex(
              (s) => s.id === sessionId
            );
            if (sessionIndex !== -1) {
              state.sessions[sessionIndex].lastMessage = message;
              state.sessions[sessionIndex].timestamp = message.timestamp;
            }
          }),
        addMessages: (sessionId: string, messages: ChatMessage[]) =>
          set((state) => {
            if (!state.messages[sessionId]) {
              state.messages[sessionId] = [];
            }
            messages.forEach((message) => {
              const index = state.messages[sessionId].findIndex(
                (m) => m.id === message.id
              );
              if (index !== -1) {
                state.messages[sessionId][index] = message;
              } else {
                state.messages[sessionId].push(message);
              }
            });
            // sort
            state.messages[sessionId].sort((a, b) => a.timestamp - b.timestamp);
          }),

        updateMessage: (
          sessionId: string,
          messageId: string,
          update: Partial<ChatMessage>
        ) =>
          set((state) => {
            const messages = state.messages[sessionId];
            if (messages) {
              const index = messages.findIndex((m) => m.id === messageId);
              if (index !== -1) {
                state.messages[sessionId][index] = {
                  ...state.messages[sessionId][index],
                  ...update,
                };
              }
            }
          }),
        prependMessages: (sessionId: string, messages: ChatMessage[]) =>
          set((state) => {
            if (!state.messages[sessionId]) {
              state.messages[sessionId] = messages;
            } else {
              state.messages[sessionId] = [
                ...messages,
                ...state.messages[sessionId],
              ];
            }
          }),
        // markSessionAsRead: (sessionId: string) =>
        //   set((state) => {
        //     const sessionIndex = state.sessions.findIndex(
        //       (s) => s.therapistId === sessionId
        //     );
        //     if (sessionIndex !== -1) {
        //       state.sessions[sessionIndex].unreadCount = 0;
        //     }
        //   }),
        getMessageById: (sessionId: string, messageId: string) => {
          // const state = get();
          const messages = get().messages[sessionId];
          if (messages) {
            return messages.find((m) => m.id === messageId);
          }
          return undefined;
        },
        getMessages: (sessionId: string): ChatMessage[] => {
          return get().messages[sessionId] || EMPTY_MESSAGES;
        },
      }),
      {
        name: StorageSceneKey.CHAT,
        storage: createJSONStorage(() => zustandStorage),
        partialize: (state) => ({
          messages: state.messages,
          sessions: state.sessions,
        }),
      }
    )
  )
);

export const useChatStore = createSelectors(chatStore);
export function useChatReset() {
  chatStore.setState(initialState);
}
