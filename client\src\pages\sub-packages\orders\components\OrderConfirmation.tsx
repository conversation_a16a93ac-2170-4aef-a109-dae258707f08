import { Button, Cell, CellGroup } from '@antmjs/vantui';
import PriceDisplay from '@components/order-card/PriceDisplay';
import TherapistSimpleCard from '@components/therapist/TherapistSimpleCard';
import { SERVICE_TYPE_MAP } from '@constants/text';
import { useAppointmentStore } from '@stores/appointment.store';
import { useLoadingStore } from '@stores/loading.store';
import { useTherapistStore } from '@stores/therapist.store';
import { View } from '@tarojs/components';
import { formatTime } from '@utils/time';

interface OrderConfirmationProps {
  // orderData: CreateOrderParams;
  // therapist: TherapistBaseInfo; // 使用实际的Therapist类型
  // onUserInfoUpdate: (name: string, category: string, desc: string) => void;
  onBack: () => void;
  onSubmit: () => void;
}

export default function OrderConfirmation({
  // orderData,
  // therapist,
  // onUserInfoUpdate,
  onBack,
  onSubmit,
}: OrderConfirmationProps) {
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  //订阅appointment store
  const {
    selectedTherapistId,
    selectedTimeSlot,
    selectedDate,
    selectedService,
    price,
    selectedDurationMultiple,
  } = useAppointmentStore();

  // 使用 selector 订阅咨询师信息
  const currentTherapist = useTherapistStore((state) =>
    state.therapists?.find((therapist) => therapist.id === selectedTherapistId)
  );

  console.log('selectedService', selectedService);
  console.log('selectedTimeSlot', selectedTimeSlot);
  console.log('selectedDate', selectedDate);
  if (!selectedService || !selectedTimeSlot || !selectedDate) {
    return <View className='text-center text-secondary'>请选择服务和时间</View>;
  }
  if (!currentTherapist) {
    return <View className='text-center text-secondary'>请选择咨询师</View>;
  }
  return (
    <View className='order-confirmation pb-24'>
      {/* 咨询师简卡 */}
      <TherapistSimpleCard therapist={currentTherapist} className='mb-6' />

      {/* 服务信息 */}
      <CellGroup className='mb-4 rounded-xl overflow-hidden'>
        <Cell
          title='预约时间'
          value={formatTime(selectedDate ?? 0, selectedTimeSlot ?? 0)}
          border={false}
        />
        <Cell
          title='服务类型'
          value={SERVICE_TYPE_MAP[selectedService?.type ?? 0]}
          border={false}
        />
        <Cell
          title='单次时长'
          value={`${selectedService?.duration}分钟`}
          border={false}
        />
      </CellGroup>

      {/* 账单信息 */}
      <CellGroup className='mb-4 rounded-xl overflow-hidden'>
        <Cell
          title='服务单价'
          value={`¥${selectedService?.price}`}
          border={false}
        />
        <Cell
          title={`服务时长(${selectedDurationMultiple}次)`}
          value={`${selectedService!.duration * selectedDurationMultiple}分钟`}
          border={false}
        />
        <Cell
          title='已优惠'
          value={`${price - selectedService?.price * selectedDurationMultiple}`}
        />

        <Cell title='总计'>
          <View className='flex justify-end w-full'>
            <PriceDisplay price={price} color='danger' />
          </View>
        </Cell>
      </CellGroup>

      {/* 底部按钮区 */}
      <View className='fixed bottom-0 left-0 w-full bg-white flex p-4 gap-3'>
        <Button type='default' block plain onClick={onBack}>
          上一步
        </Button>
        <Button
          type='primary'
          block
          round
          onClick={() => {
            setTransactionLoading(true);
            onSubmit();
          }}
          loading={transactionLoading}
          disabled={transactionLoading}
        >
          提交订单
        </Button>
      </View>
    </View>
  );
}
