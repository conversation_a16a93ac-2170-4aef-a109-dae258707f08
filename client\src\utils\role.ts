import { orderTherapistActions } from '@core/actions/order.therapist';
import { orderUserActions } from '@core/actions/order.user';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { userProfileActions } from '@core/actions/profile.user';
import { USER_ROLE } from '@model/user.interface';
import { useGlobalStore } from '@stores/global.store';
import {
  useOrderStoreSelector,
  useTherapistOrderStoreSelector,
} from '@stores/order.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useUserProfileStore } from '@stores/profile.user';

/**
 * 获取当前角色
 */
export const getCurrentRole = (): USER_ROLE => {
  return useGlobalStore.getState().currentRole;
};

/**
 * 获取订单操作的便捷方法
 */
export const getOrderActions = () => {
  const role = getCurrentRole();
  return role === USER_ROLE.THERAPIST
    ? orderTherapistActions
    : orderUserActions;
};

export const getOrderStore = () => {
  const role = getCurrentRole();
  return role === USER_ROLE.THERAPIST
    ? useTherapistOrderStoreSelector
    : useOrderStoreSelector;
};

/**
 * 获取用户信息操作的便捷方法
 */
export const getProfileActions = () => {
  const role = getCurrentRole();
  return role === USER_ROLE.THERAPIST
    ? therapistProfileActions
    : userProfileActions;
};

/**
 * 获取用户信息store
 */
export const getProfileStore = () => {
  const role = getCurrentRole();
  return role === USER_ROLE.THERAPIST
    ? useTherapistProfileStore
    : useUserProfileStore;
};
