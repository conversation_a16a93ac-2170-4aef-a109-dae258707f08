export const COLORS = {
  pageBack: '#f7f8fa', // 与 app.less 中 @pageBack 保持一致的值
  bgColor: '#ffffff',

  textColorSecondary: '#969799', // 根据实际颜色值调整
  textColor: '#323233',   // 根据实际颜色值调整
  textColorPrimary: '#2775B6', // 根据实际颜色值调整

  tabBarColor: '#646566',

  // 主色调
  primary: '#4080FF',
  primaryLight: '#E5F2FF',
  primaryDark: '#1A508B',

  // 辅助色
  secondary: '#FF976A', 
  secondaryLight: '#FFFBE8',
  secondaryDark: '#ED6A0C',

  // 警告色
  warning: '#FF7D00',
  warningLight: '#FFFBE8',
  warningDark: '#ED6A0C', 

  // 成功色
  success: '#00B42A',
  successLight: '#D1FADF',
  successDark: '#058E42', 

  // 危险色
  danger: '#F53F3F',

  // 中性色
  gray1: '#F7F8FA',
  gray2: '#F2F3F5',
  gray3: '#E5E6EB',
  gray4: '#C9CDD4',
  gray5: '#86909C',
  gray6: '#4E5969',
  gray7: '#1D2129',

  black: '#000000',
  white: '#FFFFFF',

  textColorDisabled: '#C9CDD4',
} as const; 

// 尺寸系统
export const SIZES = {
  // 字体大小
  fontXS: '20px',    // 最小字号
  fontSM: '24px',    // 小号
  fontBase: '28px',  // 基础字号
  fontLG: '32px',    // 大号
  fontXL: '36px',    // 超大号
  
  // 间距
  spacingXS: '8px',   // 最小间距
  spacingSM: '12px',  // 小间距
  spacingBase: '16px',// 基础间距
  spacingLG: '24px',  // 大间距
  spacingXL: '32px',  // 超大间距
  
  // 圆角
  radiusXS: '4px',    // 最小圆角
  radiusSM: '8px',    // 小圆角
  radiusBase: '12px', // 基础圆角
  radiusLG: '16px',   // 大圆角
  radiusXL: '24px',   // 超大圆角
} as const;

// 字体
export const FONTS = {
  base: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
} as const;

// z-index 层级管理
export const ZINDEX = {
  normal: 1,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
} as const;

// 动画时间
export const TRANSITION = {
  fast: '200ms',
  base: '300ms',
  slow: '400ms',
} as const; 