import { Button } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface ButtonAction {
  text: string;
  type?: 'default' | 'primary' | 'info' | 'warning' | 'danger';
  plain?: boolean;
  hairline?: boolean;
  round?: boolean;
  size?: 'large' | 'normal' | 'small' | 'mini';
  onClick: () => Promise<void> | void;
}

interface ButtonGroupProps {
  actions: ButtonAction[];
  gap?: number;
}

export default function ButtonGroup({ actions, gap = 4 }: ButtonGroupProps) {
  // 1. 使用数组存储每个按钮的加载状态
  const [loadingStates, setLoadingStates] = useState<boolean[]>(() =>
    actions.map(() => false)
  );

  // 2. 使用 ref 跟踪最新状态（解决闭包问题）
  const loadingStatesRef = useRef(loadingStates);
  useEffect(() => {
    loadingStatesRef.current = loadingStates;
  }, [loadingStates]);

  // 3. 计算全局禁用状态（任意按钮加载时禁用所有）
  const globalLoading = loadingStates.some(Boolean);

  // 4. 点击处理函数（核心逻辑）
  const handleClick = useCallback(
    async (index: number, action: ButtonAction) => {
      // 获取最新状态（避免闭包问题）
      const currentStates = [...loadingStatesRef.current];

      // 检查是否应阻止点击
      if (currentStates[index] || currentStates.some((state) => state)) {
        return;
      }

      try {
        // 设置当前按钮为加载状态
        setLoadingStates((prev) => {
          const newState = [...prev];
          newState[index] = true;
          return newState;
        });

        // 执行点击操作
        // const result = action.onClick();

        // // 处理异步操作
        // if (result instanceof Promise) {
        //   await result;
        // }
        // await new Promise((resolve) => setTimeout(resolve, 2000));
        await action.onClick();
      } catch (error) {
        console.error('按钮操作失败:', error);
      } finally {
        // 无论成功失败都重置状态
        setLoadingStates((prev) => {
          const newState = [...prev];
          newState[index] = false;
          return newState;
        });
      }
    },
    []
  );

  return (
    <View className={`flex flex-row justify-end w-full gap-${gap}`}>
      {actions.map((action, index) => (
        <Button
          key={index}
          type={action.type || 'default'}
          size={action.size || 'small'}
          plain={action.plain}
          hairline={action.hairline}
          round={action.round}
          loading={loadingStates[index]}
          disabled={globalLoading}
          onClick={(e) => {
            e.stopPropagation();
            handleClick(index, action);
          }}
          className='flex-1'
        >
          {action.text}
        </Button>
      ))}
    </View>
  );
}
