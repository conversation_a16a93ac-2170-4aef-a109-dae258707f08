import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { ReactNode } from "react";

interface QRCodeDialogProps {
  title?: string;
  children: ReactNode;
}

export function QRCodeDialog({
  title = "扫码进入小程序",
  children,
}: QRCodeDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">{title}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center p-6">
          <div className="bg-muted w-52 h-52 rounded-md flex items-center justify-center mb-4">
            <div className="text-xs text-muted-foreground">小程序码</div>
          </div>
          <p className="text-sm text-muted-foreground text-center">
            请使用微信扫描上方二维码进入小程序，
            <br />
            获取更完整的服务体验
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
