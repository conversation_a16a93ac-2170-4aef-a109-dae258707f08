const cloud = require("wx-server-sdk");
const { withPermission, getUserInfo } = require("../common/auth");
const { PERMISSION_LEVEL, USER_ROLE } = require("../common/permissions");
const { success, error, CODES } = require("../common/response");
const { COLLECTIONS, ORDER_STATUS } = require("../common/db.constants");
const { safeGet, generateUserSig } = require("../common/utils");

const {
  _checkExistsOrder,
  _addOrderAction,
} = require("../service/orderOperation");
const { ACTION_TYPE } = require("../common/db.constants");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, params = {} } = event;
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const appid = wxContext.APPID;

  // 请求ID，用于日志追踪
  const requestId =
    Date.now().toString(36) + Math.random().toString(36).substring(2, 10);
  console.log(`[RequestID: ${requestId}] 接收请求:`, {
    action,
    openid,
    params,
  });

  try {
    // 获取用户信息和角色
    const userInfo = await getUserInfo(openid);
    const userRole = userInfo?.role || [USER_ROLE.GUEST];
    const userId = userInfo?._id;

    // 构建上下文
    const actionContext = {
      userInfo,
      openid,
      userRole,
      userId,
      requestId,
    };

    // 调用对应的处理函数
    if (handlers[action]) {
      return await handlers[action](params, actionContext);
    } else {
      return error("未知操作类型", CODES.BAD_REQUEST);
    }
  } catch (err) {
    console.error(`[RequestID: ${requestId}] 操作失败:`, err);

    // 区分权限错误和其他错误
    if (err.type === "permission") {
      return error("权限不足", CODES.FORBIDDEN);
    }

    return error(err.message, err.code || CODES.INTERNAL_ERROR, err.stack);
  }
};

// 处理函数映射
const handlers = {
  // 评价相关
  // 更新用户信息
  comment: async (params, context) => {
    return await withPermission(
      PERMISSION_LEVEL.USER_ONLY,
      context,
      async () => {
        const result = await handleComment(params, context);
        return success(result);
      }
    );
  },
};

async function handleComment(params, context) {
  const { orderId, score, comment, tags } = params;
  const { openid } = context;

  const order = await _checkExistsOrder(orderId);

  if (order.userId !== openid) {
    throw new Error("只能评价自己的订单");
  }

  if (
    order.status !== ORDER_STATUS.COMPLETED &&
    order.status !== ORDER_STATUS.REVIEWED
  ) {
    throw new Error("订单当前状态不能评价");
  }

  try {
    await db.collection(COLLECTIONS.ORDER_REVIEWS).add({
      data: {
        userId: order.userId,
        avatar: order.userAvatar ?? "",
        userName: order.userName ?? "",
        therapistId: order.therapistId,
        service: order.serviceType,
        orderId,
        score,
        content: comment,
        tags,
        createdAt: Date.now(),
      },
    });

    // 操作日志
    await _addOrderAction(orderId, ACTION_TYPE.REVIEW, INITATOR_SOURCE.USER, {
      score,
      content: comment,
      tags,
    });
    return { message: "评价成功" };
  } catch (err) {
    throw new Error(err.message || "评价失败，请稍后重试");
  }
}
