import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import ServiceConfigEditor from '@components/therapist/ServiceConfigEditor';
import { therapistProfileActions } from '@core/actions/profile.therapist';
import { useGlobalStore } from '@stores/global.store';
import { useTherapistProfileStore } from '@stores/profile.therapist';
import { useTherapistUpdaterStore } from '@stores/updater.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useRef } from 'react';

export default function ServiceSettingsUpdater() {
  const services = useTherapistUpdaterStore(
    (state) => state.formData.serviceInfo.services
  );

  const updateFormField = useTherapistUpdaterStore(
    (state) => state.updateFormField
  );
  const loading = useTherapistUpdaterStore((state) => state.loading);
  const error = useTherapistUpdaterStore((state) => state.error);

  useEffect(() => {
    const loadMyServices = async () => {
      await therapistProfileActions.loadMyServices(true);
      const myServices = useTherapistProfileStore.getState().myServices;
      if (myServices) {
        updateFormField('serviceInfo', 'services', myServices.services || []);
      }
    };
    loadMyServices();
    return () => {
      useTherapistUpdaterStore.setState({
        loading: false,
        error: null,
      });
    };
  }, []);
  const renderCount = useRef(0);

  // 安全的增加计数
  renderCount.current += 1;
  console.log('scheduleInfo 安全渲染次数:', renderCount.current);
  // 表单验证
  const validateForm = (): boolean => {
    const currentServices = services || [];
    const enabledServices = currentServices.filter((config) => config.enabled);

    if (enabledServices.length === 0) {
      Taro.showToast({ title: '请至少启用一个服务', icon: 'none' });
      return false;
    }

    for (const service of enabledServices) {
      if (service.price <= 0) {
        Taro.showToast({ title: '请设置有效的服务价格', icon: 'none' });
        return false;
      }
      if (service.duration <= 0) {
        Taro.showToast({ title: '请设置有效的服务时长', icon: 'none' });
        return false;
      }
      if (service.finalPrice <= 0) {
        Taro.showToast({ title: '请设置有效的最终价格', icon: 'none' });
        return false;
      }
    }

    return true;
  };

  // 处理提交
  const handleSubmit = async () => {
    if (validateForm()) {
      const serviceData = {
        services: services || [],
        id: useGlobalStore.getState().openid || '',
      };
      await therapistProfileActions.updateMyServices(serviceData);
    }
  };

  return (
    <PageLol
      navigationProps={{
        title: '服务设置',
        showBackButton: true,
        showSearch: false,
      }}
      error={error}
    >
      <View className='p-4'>
        {/* 服务配置 */}
        <ServiceConfigEditor
          services={services || []}
          onServicesChange={(newServices) => {
            updateFormField('serviceInfo', 'services', newServices);
          }}
        />

        {/* 提交按钮 */}
        <View className='mt-8'>
          <Button
            type='primary'
            block
            round
            loading={loading}
            disabled={loading}
            onClick={handleSubmit}
          >
            保存设置
          </Button>
        </View>
      </View>
    </PageLol>
  );
}
