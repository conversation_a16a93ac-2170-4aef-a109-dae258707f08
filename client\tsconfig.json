{
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "baseUrl": ".",
    "rootDir": ".",
    "jsx": "react-jsx",
    "allowJs": true,
    "resolveJsonModule": true,
    "typeRoots": ["node_modules/@types"],
    "paths": {
      // 让ts识别的绝对路径

      "@actions/*": ["src/actions/*"],
      "@assets/*": ["src/assets/*"],

      "@components/*": ["src/components/*"],
      "@constants/*": ["src/constants/*"],
      "@pages/*": ["src/pages/*"],
      "@stores/*": ["src/stores/*"],

      "@styles/*": ["src/styles/*"],

      "@utils/*": ["src/utils/*"],
      "@services/*": ["src/services/*"],
      "@hooks/*": ["src/hooks/*"],
      "@core/*": ["src/core/*"],
      "@model/*": ["src/model/*"],
      "@antmjs/vantui/es/style/*": ["node_modules/@antmjs/vantui/es/style/*"]
    }
  },
  "include": ["./src", "./types", "src/pages/profile/components/.tsx"],
  "compileOnSave": false
}
