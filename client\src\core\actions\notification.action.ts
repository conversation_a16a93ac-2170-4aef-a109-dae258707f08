import { Toast } from '@antmjs/vantui';
import { notificationService } from '@services/notification.service';
import { useNotificationsStore } from '@stores/notification.store';

export const notificationActions = {
  fetchNotifications: async (lastMessageId?: string) => {
    const store = useNotificationsStore.getState();
    store.setLoading(true);
    try {
      const notifications = await notificationService.getNotifications(
        lastMessageId
      );
      store.addNotifications(notifications);
      //当前时间戳 有可能和服务端时间不一致 暂时忽略
      store.setUpdatedAt(Date.now());
    } catch (error) {
      console.error('获取消息失败:', error);
      Toast.fail('获取消息失败,请稍后重试');
    } finally {
      store.setLoading(false);
    }
  },

  //在服务端标记删除
  removeNotifications: async (notificationIds: string[]) => {
    const store = useNotificationsStore.getState();
    store.setLoading(true);
    try {
      const notifications = await notificationService.removeNotifications(
        notificationIds
      );
      store.removeNotifications(notifications);
      Toast.success('已删除消息');
    } catch (error) {
      console.error('删除消息失败:', error);
      Toast.fail('删除消息失败,请稍后重试');
    } finally {
      store.setLoading(false);
    }
  },
  //标记为已读
  markAsRead: async (notificationId: string) => {
    const store = useNotificationsStore.getState();
    try {
      await notificationService.markAsRead(notificationId);
      store.markAsRead(notificationId);
    } catch (error) {
      console.error('标记为已读失败:', error);
    }
  },
};
