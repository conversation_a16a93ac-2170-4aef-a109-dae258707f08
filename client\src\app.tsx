import { Notify } from '@antmjs/vantui';
import { ensureLogin } from '@core/actions/auth.action';
import useCheckUpdate from '@hooks/useCheckUpdate';
import { initCloud } from '@services/cloud';
import { useUserProfileStore } from '@stores/profile.user';
import Taro, { nextTick, useDidShow, useLaunch } from '@tarojs/taro';
import './app.less';
// import { initIM } from './core/actions/im.action';

// 初始化 Notify
Notify.show({
  type: 'success',
  message: '',
  duration: 0,
  onClose: () => {},
});

// const env = process.env.TARO_ENV || 'weapp';
// const track = process.env.UMENG_TRACK || false;

Taro.setEnableDebug({ enableDebug: true });
/**
 * 友盟统计初始化
 * 支付宝小程序不支持 useOpenid、autoGetOpenid参数。
 */
// console.log('track', track);
// if (track)
//   Taro.uma = uma.init(
//     env === 'weapp'
//       ? {
//           ...defaultConfig,
//           // 使用Openid进行统计，此项为false时将使用友盟+uuid进行用户统计。
//           // 使用Openid来统计微信小程序的用户，会使统计的指标更为准确，对系统准确性要求高的应用推荐使用Openid。
//           useOpenid: false,
//           // 使用openid进行统计时，是否授权友盟自动获取Openid，
//           // 如若需要，请到友盟后台"设置管理-应用信息"(https://mp.umeng.com/setting/appset)中设置appId及secret
//           autoGetOpenid: false,
//         }
//       : defaultConfig
//   );

initCloud(process.env.CLOUD_ENV);
// 用于将缓存中的登录信息同步到store中
ensureLogin(false);

// videoCallActions.enableVoIP();
// 初始化IM, 使用腾讯云IM 从env.TENCENT_IM_APP_ID获取
// initIM(Number(process.env.TENCENT_IM_APP_ID));
export default function App({ children }: React.PropsWithChildren) {
  useCheckUpdate();

  /**
   * 处理小程序启动参数
   * @param options 启动参数
   */
  async function handleStartupParams(
    options: Taro.getLaunchOptionsSync.LaunchOptions
  ) {
    try {
      console.log('处理启动参数', options);
      const { scene, query } = options;
      // 处理场景值中的分享码
      if (scene && [1047, 1048, 1049].includes(scene)) {
        // 小程序码场景会自动解析query参数
        if (query.scene) {
          const decodedScene = decodeURIComponent(query.scene);
          console.log('decodedScene', decodedScene);
          const sceneParams = decodedScene.split('=');
          const referralId = sceneParams[1];
          console.log('sceneParams', sceneParams);
          console.log('referralId', referralId);
          useUserProfileStore
            .getState()
            .setReferralInfo(referralId, 'minicode');
          return;
        }
      }

      // 处理通过URL跳转的场景
      if ([1011, 1012, 1013].includes(scene) && query.q) {
        const decodedUrl = decodeURIComponent(query.q);
        const urlParams = new URLSearchParams(decodedUrl.split('?')[1]);
        const referralId = urlParams.get('ref');

        if (referralId) {
          useUserProfileStore.getState().setReferralInfo(referralId, 'qrcode');
        }
      }
    } catch (error) {
      console.error('处理启动参数失败:', error);
    }
  }
  useLaunch(async (options) => {
    console.log('🚀🚀🚀 小程序启动', options);

    // 处理场景值和启动参数

    handleStartupParams(options);
  });

  // 对应 onShow
  useDidShow(() => {
    console.log('🚀🚀🚀 小程序useDidShow');
    nextTick(() => {
      console.log('🚀🚀🚀 小程序useDidShow nextTick');
    });
  });

  return <>{children}</>;
}
