/**
 * 场景值解析工具
 * 用于解析小程序启动时的场景值和参数
 */

/**
 * 解析场景值中的参数
 * @param scene 场景值字符串
 * @returns 解析后的参数对象
 */
export function parseSceneParam(scene: string): Record<string, string> {
  if (!scene) return {};

  try {
    // 对场景值进行解码
    const decodedScene = decodeURIComponent(scene);

    // 分割参数
    const params: Record<string, string> = {};
    const items = decodedScene.split('&');

    items.forEach((item) => {
      const [key, value] = item.split('=');
      if (key && value) {
        params[key] = value;
      }
    });

    return params;
  } catch (error) {
    console.error('解析场景值参数失败:', error);
    return {};
  }
}

/**
 * 获取分享邀请码
 * @param scene 场景值
 * @returns 邀请码
 */
export function getInviteCode(scene?: string): string | null {
  if (!scene) return null;

  const params = parseSceneParam(scene);

  // 从参数中获取邀请码，可能的键名为ref或inviteCode
  return params.ref || params.inviteCode || null;
}
