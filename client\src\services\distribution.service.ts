import { Pagination } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import {
  DistributionOrder,
  DistributionOverview,
  InvitedUser,
} from '@model/distribution.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';

/**
 * 分销服务类
 * 遵循架构要求：
 * - 用户自己的分销数据可以直接从数据库读取
 * - 提现等写操作必须通过云函数
 */
class DistributionService extends BaseService<any> {
  constructor() {
    super(COLLECTIONS.DISTRIBUTION_OVERVIEW);
  }

  /**
   * 获取用户分销概览信息 - 直接从数据库读取
   * 用户可以直接读取自己的数据
   */
  async getDistributionOverview(): Promise<DistributionOverview | null> {
    try {
      // 查询条件
      const query = {
        _openid: '{openid}',
      };

      const result = await this.directRead(query);
      console.log('getDistributionOverview result', result);
      return result[0] || null;
    } catch (error) {
      console.error('获取分销概览失败:', error);
      throw error;
    }
  }

  /**
   * 获取分销订单列表 - 直接从数据库读取
   * 用户可以直接读取自己的数据
   */
  async getDistributionOrders(params: {
    page?: number;
    pageSize?: number;
    status?: 'pending' | 'settled' | 'cancelled';
  }): Promise<{
    list: DistributionOrder[];
    pagination: Pagination;
  }> {
    try {
      const { page = 1, pageSize = 10, status } = params;
      const skip = (page - 1) * pageSize;

      // 获取当前用户openid
      const openid = '{openid}';

      // 查询条件
      let query: any = {
        referrerId: openid,
      };

      if (status) {
        query.status = status;
      }

      // 使用Taro.cloud直接读取分销订单集合
      const db = Taro.cloud.database();

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.DISTRIBUTION_ORDER)
        .where(query)
        .count();

      const total = countResult.total;

      // 获取数据
      const result = await db
        .collection(COLLECTIONS.DISTRIBUTION_ORDER)
        .where(query)
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      return {
        list: result.data as DistributionOrder[],
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取分销订单列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取邀请用户列表 - 直接从数据库读取
   * 用户可以直接读取自己的数据
   */
  async getInvitedUsers(params: { page?: number; pageSize?: number }): Promise<{
    list: InvitedUser[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    try {
      const { page = 1, pageSize = 10 } = params;
      const skip = (page - 1) * pageSize;

      // 获取当前用户openid
      const openid = '{openid}';

      // 查询条件
      const query = {
        referrerId: openid,
      };

      // 使用Taro.cloud直接读取邀请用户集合
      const db = Taro.cloud.database();

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.DISTRIBUTION_INVITED_USER)
        .where(query)
        .count();

      const total = countResult.total;

      // 获取数据
      const result = await db
        .collection(COLLECTIONS.DISTRIBUTION_INVITED_USER)
        .where(query)
        .orderBy('invitedTime', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      return {
        list: result.data as InvitedUser[],
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取邀请用户列表失败:', error);
      throw error;
    }
  }
}

// 导出服务实例
export const distributionService = new DistributionService();
