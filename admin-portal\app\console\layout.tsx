import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  FileText,
  LayoutDashboard,
  LogOut,
  Package2,
} from "lucide-react";
import Link from "next/link";
import { ReactNode } from "react";

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  isActive?: boolean;
}

function SidebarItem({ href, icon, text, isActive = false }: SidebarItemProps) {
  return (
    <Link href={href} passHref>
      <Button
        variant="ghost"
        className={cn(
          "w-full justify-start gap-2",
          isActive && "bg-accent text-accent-foreground"
        )}
      >
        {icon}
        <span>{text}</span>
      </Button>
    </Link>
  );
}

export default function ConsoleLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex min-h-screen bg-slate-50">
      {/* 侧边栏导航 */}
      <div className="fixed inset-y-0 z-10 w-64 hidden md:flex flex-col border-r bg-white">
        <div className="p-4 border-b">
          <Link href="/console" className="flex items-center gap-2">
            <span className="font-bold text-lg">心理咨询平台</span>
          </Link>
        </div>
        <div className="flex-1 overflow-auto py-2">
          <nav className="grid gap-1 px-2">
            <SidebarItem
              href="/console"
              icon={<LayoutDashboard size={18} />}
              text="仪表盘"
            />
            <div className="my-2 px-3">
              <h3 className="text-xs font-medium text-gray-500">订单管理</h3>
            </div>
            <SidebarItem
              href="/console/orders"
              icon={<FileText size={18} />}
              text="订单列表"
            />
            <SidebarItem
              href="/console/orders/refunds"
              icon={<FileText size={18} />}
              text="退款审核"
            />
            <div className="my-2 px-3">
              <h3 className="text-xs font-medium text-gray-500">资金管理</h3>
            </div>
            <SidebarItem
              href="/console/finance/transactions"
              icon={<BarChart3 size={18} />}
              text="流水管理"
            />
            <SidebarItem
              href="/console/finance/withdrawals"
              icon={<BarChart3 size={18} />}
              text="提现管理"
            />
            <div className="my-2 px-3">
              <h3 className="text-xs font-medium text-gray-500">资源管理</h3>
            </div>
            <SidebarItem
              href="/console/resources/tools"
              icon={<Package2 size={18} />}
              text="测量工具"
            />
            <SidebarItem
              href="/console/resources/music"
              icon={<Package2 size={18} />}
              text="疗愈音乐"
            />
          </nav>
        </div>
        <div className="p-4 border-t">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarFallback>管理</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium">管理员</div>
              <div className="text-xs text-gray-500 truncate">
                <EMAIL>
              </div>
            </div>
            <Button variant="ghost" size="icon">
              <LogOut size={18} />
            </Button>
          </div>
        </div>
      </div>

      {/* 移动端侧边栏触发按钮 */}
      <div className="fixed bottom-4 right-4 md:hidden z-40">
        <Button
          className="rounded-full w-12 h-12 flex items-center justify-center"
          size="icon"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="4" x2="20" y1="12" y2="12" />
            <line x1="4" x2="20" y1="6" y2="6" />
            <line x1="4" x2="20" y1="18" y2="18" />
          </svg>
          <span className="sr-only">打开菜单</span>
        </Button>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 md:ml-64">
        <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 md:px-6">
          <div className="hidden md:flex">
            <h1 className="text-lg font-semibold">管理后台</h1>
          </div>
          <div className="flex md:hidden">
            <Link href="/console" className="flex items-center gap-2">
              <span className="font-bold">心理咨询平台</span>
            </Link>
          </div>
        </header>
        <main className="p-4 md:p-6">{children}</main>
      </div>
    </div>
  );
}
