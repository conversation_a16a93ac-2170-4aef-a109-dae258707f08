import { Cell, CellGroup } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { LOGO_DEFAULT } from '@constants/assets';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React from 'react';

const AboutPage: React.FC = () => {
  // 版本信息
  const appVersion = 'v1.0.0';
  const buildNumber = '20231120001';

  // 复制微信号
  const handleCopyWechat = () => {
    Taro.setClipboardData({
      data: 'MentalHealth2023',
      success: () => {
        Taro.showToast({
          title: '微信号已复制',
          icon: 'success',
          duration: 2000,
        });
      },
    });
  };

  // 拨打电话
  const handleCallPhone = () => {
    Taro.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        Taro.showToast({
          title: '拨号已取消',
          icon: 'none',
        });
      },
    });
  };

  return (
    <PageLol
      navigationProps={{
        title: '关于我们',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen'>
        {/* 头部Logo和应用名称 */}
        <View className='flex flex-col items-center py-8 bg-bg'>
          <Image src={LOGO_DEFAULT} className='w-20 h-20 rounded-lg mb-4' />
          <Text className='text-xl font-bold mb-1'>心之安</Text>
          <Text className='text-sm text-secondary'>爱之所致,心之所安</Text>
          <Text className='text-xs text-disabled mt-2'>
            版本号：{appVersion}
          </Text>
        </View>

        {/* 公司介绍 */}
        <View className='px-4 py-6'>
          <Text className='text-lg font-medium mb-3'>公司介绍</Text>
          <View className='text-secondary text-sm leading-6 mb-6'>
            <Text className='block mb-2'>
              我们是一家专注于心理健康服务的科技公司，致力于通过互联网技术为用户提供专业、便捷的心理健康服务。
            </Text>
            <Text className='block mb-2'>
              成立于2020年，公司汇集了一批来自心理学、医学、科技等领域的优秀人才，打造了这款集心理咨询、心理测评、冥想练习于一体的综合性心理健康平台。
            </Text>
            <Text className='block'>
              我们与多家知名医院、心理健康机构建立了长期合作关系，确保为用户提供最优质的专业服务。
            </Text>
          </View>

          <Text className='text-lg font-medium mb-3'>使命与愿景</Text>
          <View className='text-secondary text-sm leading-6 mb-6'>
            <Text className='block mb-2'>
              <Text className='text-primary font-medium'>使命：</Text>
              让每个人都能便捷获取专业的心理健康服务
            </Text>
            <Text className='block mb-2'>
              <Text className='text-primary font-medium'>愿景：</Text>
              成为国内领先的心理健康服务平台，为亿万用户的心理健康保驾护航
            </Text>
            <Text className='block'>
              <Text className='text-primary font-medium'>价值观：</Text>
              专业、关爱、隐私、创新
            </Text>
          </View>

          {/* 核心团队 */}
          <Text className='text-lg font-medium mb-3'>核心团队</Text>
          <View className='text-secondary text-sm leading-6 mb-6'>
            <Text className='block mb-2'>
              <Text className='text-primary font-medium'>创始团队：</Text>
              来自北京大学、清华大学、斯坦福大学等知名高校的心理学专家和互联网精英
            </Text>
            <Text className='block mb-2'>
              <Text className='text-primary font-medium'>专家顾问团：</Text>
              由多位国内外知名心理学教授、精神科医生组成
            </Text>
            <Text className='block'>
              <Text className='text-primary font-medium'>技术团队：</Text>
              拥有丰富经验的互联网产品和技术专家
            </Text>
          </View>
        </View>

        {/* 联系方式 */}
        <View className='mt-2'>
          <CellGroup title='联系我们' inset>
            <Cell
              title='客服电话'
              value='************'
              isLink
              onClick={handleCallPhone}
            />
            <Cell
              title='客服微信'
              value='MentalHealth2023'
              isLink
              onClick={handleCopyWechat}
            />
            <Cell title='公司邮箱' value='<EMAIL>' />
            <Cell title='公司地址' value='北京市海淀区中关村创业大厦B座10层' />
          </CellGroup>
        </View>

        {/* 资质证书 */}
        <View className='mt-4 px-4 pb-8'>
          <Text className='text-lg font-medium mb-3'>资质证书</Text>
          <View className='grid grid-cols-2 gap-4'>
            <View className='bg-bg rounded-lg p-3 flex flex-col items-center'>
              <Image
                src='https://img.yzcdn.cn/vant/cat.jpeg'
                className='w-16 h-16 mb-2'
              />
              <Text className='text-xs text-center'>营业执照</Text>
            </View>
            <View className='bg-bg rounded-lg p-3 flex flex-col items-center'>
              <Image
                src='https://img.yzcdn.cn/vant/cat.jpeg'
                className='w-16 h-16 mb-2'
              />
              <Text className='text-xs text-center'>医疗机构执业许可证</Text>
            </View>
            <View className='bg-bg rounded-lg p-3 flex flex-col items-center'>
              <Image
                src='https://img.yzcdn.cn/vant/cat.jpeg'
                className='w-16 h-16 mb-2'
              />
              <Text className='text-xs text-center'>互联网医疗资质</Text>
            </View>
            <View className='bg-bg rounded-lg p-3 flex flex-col items-center'>
              <Image
                src='https://img.yzcdn.cn/vant/cat.jpeg'
                className='w-16 h-16 mb-2'
              />
              <Text className='text-xs text-center'>心理咨询机构备案</Text>
            </View>
          </View>
        </View>

        {/* 底部版权信息 */}
        <View className='pb-6 px-4'>
          <Text className='text-xs text-disabled text-center block'>
            Copyright © 2020-2023 Mental Health. All Rights Reserved.
          </Text>
          <Text className='text-xs text-disabled text-center block mt-1'>
            京ICP备XXXXXXXX号-1
          </Text>
        </View>
      </View>
    </PageLol>
  );
};

export default AboutPage;
