import PageLol from '@components/common/page-meta';
import { measureActions } from '@core/actions/measure.action';
import {
  getLevelByText,
  InterpretationLevel,
  UserTestRecord,
} from '@model/test.model';
import { useMeasureStore } from '@stores/measure.store';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { formatTime2 } from '@utils/time';
import { useEffect } from 'react';

/**
 * 测试历史记录页面
 * 展示测试历史记录
 */
export default function TestHistoryPage() {
  const history = useMeasureStore.use.recentHistory();

  console.log('TestHistoryPage history', history);
  // 加载测试记录
  useEffect(() => {
    measureActions.fetchRecentHistory();
  }, []);

  const refresh = async () => {
    await measureActions.fetchRecentHistory(true);
  };
  // 导航到报告详情页
  const navigateToReport = (recordId: string) => {
    Taro.navigateTo({
      url: `/pages/sub-packages/measure/report/index?recordId=${recordId}`,
    });
  };

  // 渲染结果卡片
  const renderResultCard = (testRecord: UserTestRecord) => {
    const { report, startTime, endTime } = testRecord;
    const { testTitle, testId, result } = report;
    const score = result.score;
    const level = result.interpretation?.level;
    const testDate = endTime ? formatTime2(endTime) : '未完成';
    const duration =
      endTime && startTime ? Math.round((endTime - startTime) / 60000) : 0;
    const dimensionScores = result.dimensionScores || [];
    const hasDimensions = dimensionScores.length > 0;

    // 根据分数确定状态颜色
    const getScoreColor = () => {
      const levelEnum = getLevelByText(level || '');
      if (levelEnum === InterpretationLevel.LOW) return 'text-success';
      if (levelEnum === InterpretationLevel.MEDIUM) return 'text-warning';
      if (levelEnum === InterpretationLevel.HIGH) return 'text-danger';
      return 'text-gray-500';
    };

    return (
      <View
        className='bg-white rounded-lg mb-4 p-4 shadow-sm'
        onClick={() => navigateToReport(testRecord.id)}
      >
        <View className='flex justify-between items-center mb-2'>
          <View>
            <Text className='text-base font-bold'>{testTitle}</Text>
            {/* <View className='mt-1'>
              <Text className='text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full'>
                测试ID: {testId}
              </Text>
            </View> */}
          </View>
          {score && (
            <Text className={`text-lg font-bold ${getScoreColor()}`}>
              {score}
            </Text>
          )}
        </View>

        {/* 维度得分 */}
        {hasDimensions && (
          <View className='mt-3 mb-3'>
            <Text className='text-xs text-secondary mb-1 block'>维度得分</Text>
            <View className='flex flex-wrap gap-2'>
              {dimensionScores.map((dim, index) => (
                <View key={index} className='bg-gray-50 px-2 py-1 rounded'>
                  <Text className='text-xs'>
                    {dim.dimension}: {dim.score}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        <View className='flex justify-between items-center mt-2 pt-2 border-t border-gray-100'>
          <View>
            <Text className='text-sm text-gray-500'>{testDate}</Text>
            {duration > 0 && (
              <Text className='text-sm text-gray-500 ml-2'>
                用时: {duration}分钟
              </Text>
            )}
          </View>

          <View className='bg-blue-50 px-3 py-1 rounded-full text-xs text-primary'>
            {result.interpretation?.level || '查看详情'}
          </View>
        </View>
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{
        title: '历史记录',
        showBackButton: true,
      }}
      isEmpty={history.length === 0}
      emptyDescription='暂无历史记录'
      onPullDownRefresh={refresh}
    >
      <View className='p-4'>
        {history.map((record) => (
          <View key={record.id}>{renderResultCard(record)}</View>
        ))}
      </View>
    </PageLol>
  );
}
