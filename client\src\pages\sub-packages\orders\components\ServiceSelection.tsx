import { Button, Image, RadioGroup } from '@antmjs/vantui';
import PriceDisplay from '@components/order-card/PriceDisplay';
import ServiceCard from '@components/order-card/ServiceCard';
import { ICONS_BOLD, ICONS_LINEAR } from '@constants/assets';
import { Service, ServiceType } from '@model/service.interface';
import { useAppointmentStore } from '@stores/appointment.store';
import { Text, View } from '@tarojs/components';
import { useState } from 'react';

interface ServiceSelectionProps {
  services: Service[];
  onNext: (service: Service, duration: number, price: number) => void;
  onBack: () => void;
}

// 服务时长选项
const DURATION_MULTIPLE = [1, 2, 3];

export default function ServiceSelection({
  services,
  onNext,
  onBack,
}: ServiceSelectionProps) {
  const {
    selectedService,
    selectedDurationMultiple,
    price,
    setSelectedService,
    setPrice,
    setSelectedDurationMultiple,
  } = useAppointmentStore();
  const [showDurationOptions, setShowDurationOptions] = useState(false);

  // 选择服务时长
  const handleDurationChange = (option) => {
    console.log('handleDurationChange', option);
    setSelectedDurationMultiple(option);
    setShowDurationOptions(false);
  };

  // 选择服务类型
  const handleServiceTypeChange = (type: ServiceType) => {
    console.log('handleServiceTypeChange', type);
    setSelectedService(services.find((item) => item.type === type)!);
  };

  // 处理下一步
  const handleNext = () => {
    if (selectedService) {
      onNext(selectedService, selectedDurationMultiple, price);
    }
  };

  if (selectedService && selectedDurationMultiple) {
    setPrice(selectedService?.finalPrice * selectedDurationMultiple);
  }

  return (
    <View className='service-selection'>
      {/* 服务时长选择 */}
      <View className='mt-4'>
        <Text className='text-lg font-semibold mb-6 block'>选择服务时长</Text>

        {/* 自定义下拉列表 */}
        <View className='relative bg-white rounded-xl px-4'>
          {/* 下拉触发器 */}
          <View
            className='flex items-center justify-between w-full h-14 rounded-xl'
            onClick={() => setShowDurationOptions(!showDurationOptions)}
          >
            <View className='flex items-center'>
              <Image className='w-4 h-4 mr-2' src={ICONS_BOLD.CLOCK32} />
              <Text>
                {selectedDurationMultiple * (selectedService?.duration ?? 50)}
                分钟
              </Text>
            </View>
            <Image className='w-4 h-4' src={ICONS_BOLD.ARROW_DOWN32} />
          </View>

          {/* 下拉选项 */}
          {showDurationOptions && (
            <View className='absolute top-full left-0 w-full bg-white rounded-xl shadow-lg mt-1 px-4 z-10'>
              {DURATION_MULTIPLE.map((option) => (
                <View
                  key={option}
                  className=' border-b border-border flex items-center h-14'
                  onClick={() => handleDurationChange(option)}
                >
                  <Text>{option * (selectedService?.duration ?? 50)}分钟</Text>
                  {selectedDurationMultiple === option && (
                    <Image
                      className='w-4 h-4 ml-auto'
                      src={ICONS_LINEAR.TICK16}
                    />
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </View>

      {/* 服务类型选择 */}
      <View className=' mb-4 mt-8'>
        <Text className='text-lg font-semibold mb-6 block'>选择服务类型</Text>

        <View className='flex flex-col gap-4'>
          <RadioGroup value={selectedService?.type}>
            {services.map((item, index) => (
              <View key={item.type} className='mb-4'>
                <ServiceCard
                  key={`${item.type}-${index}`}
                  service={item}
                  onClick={() => setSelectedService(item)}
                />
              </View>
            ))}
          </RadioGroup>
        </View>
      </View>

      {/* 价格信息 */}
      <View className='rounded-xl bg-white p-4 mb-4'>
        <View className='flex justify-between items-center'>
          <Text className='text-sm'>预计费用</Text>
          <PriceDisplay price={price} color='default' />
        </View>
      </View>

      {/* 底部按钮区 */}
      <View className='fixed bottom-0 left-0 w-full bg-white flex p-4 gap-3'>
        <Button type='default' block plain onClick={onBack}>
          上一步
        </Button>
        {/* 如果服务类型为空，则禁用下一步按钮 */}
        <Button
          type='primary'
          block
          round
          onClick={handleNext}
          disabled={!selectedService}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
