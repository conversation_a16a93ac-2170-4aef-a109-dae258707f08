"use client";

import { Toaster as SonnerToaster, toast as sonnerToast } from "sonner";

export const Toaster = SonnerToaster;

type ToastOptions = {
  duration?: number; // 持续时间
  position?:
    | "top-left"
    | "top-right"
    | "bottom-left"
    | "bottom-right"
    | "top-center"
    | "bottom-center"; // 位置
};

const defaultOptions: ToastOptions = {
  duration: 3000,
  position: "top-center",
};

export const toast = {
  // 成功提示
  success: (message: string, options?: ToastOptions) => {
    return sonnerToast.success(message, {
      ...defaultOptions,
      ...options,
    });
  },

  // 错误提示
  error: (message: string, options?: ToastOptions) => {
    return sonnerToast.error(message, {
      ...defaultOptions,
      ...options,
    });
  },

  // 警告提示
  warning: (message: string, options?: ToastOptions) => {
    return sonnerToast(message, {
      ...defaultOptions,
      ...options,
      style: { backgroundColor: "#FEF9C3", color: "#854D0E" },
    });
  },

  // 普通提示
  info: (message: string, options?: ToastOptions) => {
    return sonnerToast(message, {
      ...defaultOptions,
      ...options,
    });
  },
};
