import { AVATAR_DEFAULT, DEFAULT_USER_NAME } from '@constants/assets';
import { SERVICE_TYPE_MAP } from '@constants/text';
import { IncomeDetail } from '@model/income.model';
import { Image, Text, View } from '@tarojs/components';
import { formatTime2 } from '@utils/time';

export default function IncomeItem({ income }: { income: IncomeDetail }) {
  return (
    <View className='flex flex-row items-center px-4 py-3 bg-bg rounded-xl'>
      <View className='flex flex-row items-center flex-1'>
        <Image
          src={income.userAvatar || AVATAR_DEFAULT}
          className='w-10 h-10 rounded-full'
        />
        {/* 用户信息 昵称 服务类型 时间 */}
        <View className='flex flex-col flex-1 gap-1.5 ml-2.5'>
          <View className='flex flex-row items-start justify-between '>
            <View className='flex flex-row items-center gap-2'>
              <Text className='text-sm font-bold'>
                {income.userName || DEFAULT_USER_NAME}
              </Text>
              <Text className='text-xs text-secondary'>
                {income.incomeType === 2
                  ? '分销'
                  : SERVICE_TYPE_MAP[income.serviceType]}
              </Text>
            </View>
            <View
              className={`text-xs px-1 py-0.5 rounded-sm ${
                income.status === 'settled'
                  ? 'bg-green-50'
                  : income.status === 'frozen'
                  ? 'bg-yellow-50 '
                  : 'bg-red-50 '
              }`}
            >
              {income.status === 'settled'
                ? '已入账'
                : income.status === 'frozen'
                ? '冻结中'
                : '已退款'}
            </View>
          </View>
          <View className='flex flex-row items-end justify-between '>
            <Text className='text-xs text-secondary'>
              {formatTime2(income.createdAt)}
            </Text>
            <Text
              className={`text-sm font-bold ${
                income.status === 'settled'
                  ? 'text-green-500'
                  : income.status === 'frozen'
                  ? 'text-yellow-500'
                  : 'text-red-500'
              }`}
            >
              {income.status === 'settled'
                ? `+${income.amount.toFixed(2)}`
                : income.status === 'frozen'
                ? `-${income.amount.toFixed(2)}`
                : `-${income.amount.toFixed(2)}`}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
