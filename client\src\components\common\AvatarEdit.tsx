import { Button } from '@antmjs/vantui';
import { AVATAR_DEFAULT } from '@constants/assets';
import { Image, View } from '@tarojs/components';

export function AvatarUploader({
  handleChooseAvatar,
  avatar,
}: {
  handleChooseAvatar: (e: any) => Promise<void>;
  avatar: string;
}) {
  return (
    <View className='flex flex-col w-full'>
      <View className='flex flex-col items-center space-y-4'>
        <Button
          className='text-sm w-16 h-16 bg-transparent'
          size='large'
          openType='chooseAvatar'
          onChooseAvatar={handleChooseAvatar}
        >
          <Image
            className='w-14 h-14 rounded-full bg-gray-200'
            src={avatar || AVATAR_DEFAULT}
            mode='aspectFill'
          />
        </Button>
      </View>
    </View>
  );
}
