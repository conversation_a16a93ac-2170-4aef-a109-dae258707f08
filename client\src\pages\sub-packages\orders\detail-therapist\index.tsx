// client/src/pages/therapist-orders/detail/index.tsx
import { Button } from '@antmjs/vantui';
import ActionBar from '@components/common/ActionBar';
import PageLol from '@components/common/page-meta';
import { SERVICE_TYPE_ICON_DIVERSED } from '@constants/assets';
import { ORDER_STATUS_MAP, REFUND_STATUS_MAP } from '@constants/text';
import { orderTherapistActions } from '@core/actions/order.therapist';
import { videoCallActions } from '@core/actions/videoCall.action';
import { useOrderDetailForTherapist } from '@hooks/useOrderDetailForTherapist';
import { ORDER_STATUS, REFUND_STATUS } from '@model/order.interface';
import { ServiceType } from '@model/service.interface';
import { OrderDetailShared } from '@pages/sub-packages/orders/components/OrderDetailShared';
import { useLoadingStore } from '@stores/loading.store';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { appRouter, orderRouter } from '@utils/router';
import { useRef, useState } from 'react';
import UserSimpleCard from '../components/UserSimpleCard';

export default function TherapistOrderDetailPage() {
  const router = useRouter();
  const orderId = router.params.orderId as string;
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  const [isConfirming, setIsConfirming] = useState(false);
  // 使用自定义hook获取订单详情
  const { order, orderActions, user, orderReview, refresh, error } =
    useOrderDetailForTherapist(orderId);
  const [localError, setLocalError] = useState<string | null>(null);
  const renderCount = useRef(0);

  // 安全的增加计数
  renderCount.current += 1;

  console.log('orderDetailPage 安全渲染次数:', renderCount.current);

  console.log('orderDetailPage orderActions', orderActions);

  const completeSeviceButton = () => {
    if (!order || order.status !== ORDER_STATUS.IN_PROGRESS) {
      return null;
    }
    return (
      <Button
        type='primary'
        round
        loading={transactionLoading}
        disabled={transactionLoading}
        className='flex-1'
        onClick={async () => {
          Taro.showModal({
            title: '完成服务',
            content:
              order.startTime + order.duration * 60 * 1000 > Date.now()
                ? '还未到预约结束时间，是否继续？'
                : '请确认服务已完成',
            showCancel: true,
            success: async (result) => {
              if (result.confirm) {
                try {
                  await orderTherapistActions.completeService(orderId);
                } catch (err) {
                  console.error(err);
                  setLocalError(err.message || '完成服务失败');
                }
              }
            },
          });
        }}
      >
        完成服务
      </Button>
    );
  };

  const startServiceButton = () => {
    if (
      !order ||
      order.status !== ORDER_STATUS.PENDING_START ||
      order.serviceType !== ServiceType.VIDEO
    ) {
      return null;
    }
    return (
      <Button
        type='primary'
        round
        loading={transactionLoading}
        disabled={transactionLoading}
        className='flex-1'
        onClick={() => {
          setTransactionLoading(true);
          Taro.showModal({
            title: '开始服务',
            content:
              order.startTime > Date.now()
                ? '还未到预约开始时间，是否继续？'
                : '您将开始服务',
            showCancel: true,
            success: async (result) => {
              if (result.confirm) {
                try {
                  await orderTherapistActions.startService(
                    orderId,
                    order.serviceType
                  );
                } catch (err) {
                  console.error(err);
                  setLocalError(err.message || '开始服务失败');
                }
              }
            },
          });
        }}
      >
        开始服务
      </Button>
    );
  };

  const renderOrderDetailActions = () => {
    if (!order || !user || !orderId) {
      return <View className='h-10' />;
    }
    console.log('renderOrderDetailActions', order.status, order.serviceType);

    const leftButtons = [
      {
        text: '消息',
        icon: 'message-o',
        onClick: async () => {
          // 跳转到消息页面
          appRouter.chat(order.therapistId);
        },
      },
    ];
    if (order.status >= ORDER_STATUS.PENDING_START) {
      leftButtons.push({
        text: '退款',
        icon: 'close-o',
        onClick: async () => {
          // 导航到退款页面
          Taro.navigateTo({
            url: `/pages/sub-packages/orders/refund/index?orderId=${orderId}`,
            complete: () => {
              setTransactionLoading(false);
            },
          });
        },
      });
    }

    if (order.status === ORDER_STATUS.PENDING_CONFIRM) {
      return (
        <ActionBar>
          {/* 拒绝订单  */}
          <Button
            type='primary'
            round
            className='flex-1'
            loading={transactionLoading}
            disabled={transactionLoading || isConfirming}
            onClick={() => {
              setTransactionLoading(true);
              orderRouter.reject(orderId, true);
            }}
          >
            拒绝订单
          </Button>
          <Button
            type='primary'
            round
            disabled={transactionLoading || isConfirming}
            className='flex-1'
            onClick={async () => {
              Taro.showModal({
                title: '确认订单',
                content: '您将接受该订单',
                success: async (result) => {
                  if (result.confirm) {
                    try {
                      setIsConfirming(true);
                      await orderTherapistActions.confirmOrder(orderId);
                    } catch (err) {
                      console.error(err);
                      setLocalError(err.message || '确认订单失败');
                    } finally {
                      setIsConfirming(false);
                    }
                  }
                },
              });
            }}
          >
            确认订单
          </Button>
        </ActionBar>
      );
    }
    if (order.status === ORDER_STATUS.PENDING_START) {
      return (
        <ActionBar leftButtons={leftButtons}>
          {/* 视频服务，开始服务 */}
          {startServiceButton()}
          {completeSeviceButton()}
        </ActionBar>
      );
    }
    if (order.status === ORDER_STATUS.IN_PROGRESS) {
      return (
        <ActionBar>
          {/* 视频通话 */}
          {order.serviceType === ServiceType.VIDEO && (
            <Button
              type='primary'
              round
              loading={transactionLoading}
              disabled={transactionLoading}
              className='flex-1'
              icon={SERVICE_TYPE_ICON_DIVERSED[order.serviceType]}
              onClick={() => {
                //开始视频通话
                setTransactionLoading(true);
                try {
                  videoCallActions.call(orderId);
                } catch (err) {
                  console.error(err);
                  setLocalError(err.message || '视频通话失败');
                }
              }}
            >
              视频通话
            </Button>
          )}
          {/* 完成服务 */}
          {completeSeviceButton()}
        </ActionBar>
      );
    }
  };

  if (!order || !user) {
    return null;
  }

  // 获取显示的状态文本
  const getStatusText = () => {
    // 如果有退款状态且不是无退款，优先显示退款状态
    if (order.refundStatus && order.refundStatus !== REFUND_STATUS.NONE) {
      return REFUND_STATUS_MAP[order.refundStatus];
    }
    // 否则显示订单主状态
    return ORDER_STATUS_MAP[order.status] || '订单详情';
  };

  return (
    <PageLol
      useNav
      navigationProps={{
        title: getStatusText(),
        showBackButton: true,
      }}
      onRetry={refresh}
      error={localError || error}
      onPullDownRefresh={refresh}
    >
      <OrderDetailShared
        order={order}
        orderActions={orderActions}
        orderReview={orderReview}
        personCard={<UserSimpleCard user={user} className='mb-6' />}
        renderOrderDetailActions={renderOrderDetailActions}
      />
    </PageLol>
  );
}
