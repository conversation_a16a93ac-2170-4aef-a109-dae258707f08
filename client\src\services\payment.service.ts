import { COLLECTIONS } from '@model/db.model';
import {
  PaymentParams,
  WechatPayPayment,
  WithdrawParams,
  WithdrawRecord,
} from '@model/payment.interface';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

/**
 * 支付服务类
 * 遵循架构要求：
 * - 支付相关操作属于敏感数据和写操作，必须通过云函数
 */
class PaymentService extends BaseService<any> {
  constructor() {
    super(COLLECTIONS.ORDER);
  }

  /**
   * 创建支付 - 通过云函数
   * 敏感操作，必须通过云函数
   */
  async createPayment(params: PaymentParams): Promise<WechatPayPayment> {
    try {
      console.log('🚀🚀🚀 createPayment params', params);
      const result = await callCloudFunction(
        'payment',
        'createPayment',
        params
      );

      if (result.paymentData) {
        return result.paymentData;
      }

      throw new Error(result.message || '创建支付失败');
    } catch (error) {
      console.error('创建支付失败:', error);
      throw error;
    }
  }

  /**
   * 查询支付状态 - 通过云函数
   * 敏感数据，通过云函数
   */
  async validatePaymentSuccess(orderId: string): Promise<boolean> {
    try {
      const result = await callCloudFunction(
        'payment',
        'validatePaymentSuccess',
        {
          orderId,
        }
      );

      if (result.success && result.status === 'paid') {
        return true;
      }

      return false;
    } catch (error) {
      console.error('查询支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取提现配置 - 直接读取
   * 公开配置数据，可以直接读取
   */
  async getWithdrawConfig(): Promise<any> {
    try {
      // 由于BaseService不支持直接切换集合，这里使用Taro.cloud直接读取
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.SYSTEM_CONFIG)
        .limit(1)
        .get();

      return result.data[0] || null;
    } catch (error) {
      console.error('获取提现配置失败:', error);
      throw error;
    }
  }

  /**
   * 提交提现申请 - 通过云函数
   * 写操作必须通过云函数
   */
  async submitWithdrawApplication(params: WithdrawParams): Promise<{
    id: string;
    status: string;
  }> {
    try {
      const result = await callCloudFunction(
        'distribution',
        'submitWithdraw',
        params
      );

      if (result.id && result.status) {
        return { id: result.id, status: result.status };
      }

      throw new Error(result.message || '提交提现申请失败');
    } catch (error) {
      console.error('提交提现申请失败:', error);
      throw error;
    }
  }

  /**
   * 获取提现记录列表 - 直接从数据库读取
   * 用户可以直接读取自己的数据
   */
  async getWithdrawRecords(
    params: {
      page?: number;
      pageSize?: number;
    } = {}
  ): Promise<{
    success: boolean;
    code: number;
    data: WithdrawRecord[];
    pagination?: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    try {
      const { page = 1, pageSize = 10 } = params;
      const skip = (page - 1) * pageSize;

      // 获取当前用户openid
      const openid = '{openid}';

      // 查询条件
      let query: any = {
        userId: openid,
      };

      // 使用Taro.cloud直接读取提现记录集合
      const db = Taro.cloud.database();

      // 获取总数
      const countResult = await db
        .collection(COLLECTIONS.WITHDRAW)
        .where(query)
        .count();

      const total = countResult.total;

      // 获取数据
      const result = await db
        .collection(COLLECTIONS.WITHDRAW)
        .where(query)
        .orderBy('createdAt', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSize);

      return {
        success: true,
        code: 200,
        data: result.data as WithdrawRecord[],
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error('获取提现记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 取消提现申请 - 通过云函数
   * 写操作必须通过云函数
   */
  async cancelWithdrawApplication(id: string): Promise<boolean> {
    try {
      const result = await callCloudFunction('distribution', 'cancelWithdraw', {
        id,
      });
      return result.success;
    } catch (error) {
      console.error('取消提现申请失败:', error);
      throw error;
    }
  }
}

// 导出服务实例
export const paymentService = new PaymentService();
