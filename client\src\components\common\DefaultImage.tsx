import { Text, View } from '@tarojs/components';
import { FC } from 'react';

interface DefaultImageProps {
  size?: number;
  className?: string;
}

const DefaultImage: FC<DefaultImageProps> = ({
  size = 100,
  className = '',
}) => {
  return (
    <View
      className={`flex items-center justify-center bg-gray-200 rounded-lg ${className}`}
      style={{
        width: `${size}px`,
        height: `${size}px`,
      }}
    >
      <View className='flex flex-col items-center'>
        <Text className='text-gray-400 text-2xl mb-1'>🖼️</Text>
        <Text className='text-gray-400 text-xs'>暂无图片</Text>
      </View>
    </View>
  );
};

export default DefaultImage;
