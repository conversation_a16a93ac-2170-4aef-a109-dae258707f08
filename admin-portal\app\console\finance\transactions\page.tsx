"use client";

import { useTransactionList } from "@/app/hooks/useTransactionList";
import { Payment } from "@/app/model/payment.interface";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DotLoading } from "@/components/ui/dot-loading";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { useState } from "react";

// 支付状态对应的样式
const statusStyles = {
  pending: "bg-yellow-100 text-yellow-800",
  paid: "bg-green-100 text-green-800",
  refunded: "bg-blue-100 text-blue-800",
  failed: "bg-red-100 text-red-800",
};

// 格式化时间戳为可读日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return "-";
  return format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss");
};

// 将金额分转为元
const formatAmount = (amount: number) => {
  return (amount / 100).toFixed(2);
};

// 时间范围选项
const TIME_RANGES = {
  "7d": { label: "最近7天", days: 7 },
  "30d": { label: "最近30天", days: 30 },
  "90d": { label: "最近90天", days: 90 },
  all: { label: "所有时间", days: 0 },
};

// 获取支付状态显示
const getPaymentStatus = (transaction: Payment) => {
  switch (transaction.status) {
    case "pending":
      return { status: "待支付", style: statusStyles.pending };
    case "paid":
      return { status: "已支付", style: statusStyles.paid };
    case "refunded":
      return { status: "已退款", style: statusStyles.refunded };
    case "failed":
      return { status: "支付失败", style: statusStyles.failed };
    default:
      return { status: "未知", style: "" };
  }
};

export default function TransactionsPage() {
  const [searchInput, setSearchInput] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [timeRange, setTimeRange] = useState("7d");

  const {
    transactions,
    pagination,
    summary,
    loading,
    refreshTransactions,
    loadMoreTransactions,
    onStatusChange,
    onSearch,
    onDateRangeChange,
  } = useTransactionList();

  // 处理搜索
  const handleSearch = () => {
    onSearch(searchInput);
  };

  // 处理搜索框按回车
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // 处理状态过滤变化
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    onStatusChange(value === "all" ? "" : value);
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);

    if (value === "all") {
      onDateRangeChange(undefined);
    } else {
      const days = TIME_RANGES[value as keyof typeof TIME_RANGES].days;
      const endDate = Date.now();
      const startDate = endDate - days * 24 * 60 * 60 * 1000;
      onDateRangeChange([startDate, endDate]);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">流水管理</h1>
        <p className="text-muted-foreground">查看平台资金流水记录</p>
      </div>

      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Input
            placeholder="搜索交易描述/订单号..."
            className="max-w-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <div className="flex flex-wrap gap-2">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="支付状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="pending">待支付</SelectItem>
                <SelectItem value="paid">已支付</SelectItem>
                <SelectItem value="refunded">已退款</SelectItem>
                <SelectItem value="failed">支付失败</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
                <SelectItem value="all">所有时间</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>搜索</Button>
            <Button variant="outline" onClick={refreshTransactions}>
              刷新
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <DotLoading size={8} color="#1e40af" className="mx-auto" />
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易编号</TableHead>
                    <TableHead>支付商交易号</TableHead>
                    <TableHead>订单号</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead className="text-right">金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions && transactions.length > 0 ? (
                    transactions.map((transaction) => (
                      <TableRow key={transaction.orderId}>
                        <TableCell className="font-medium">
                          {transaction.outTradeNo || "-"}
                        </TableCell>

                        <TableCell>{transaction.transactionId}</TableCell>
                        <TableCell>{transaction.orderId}</TableCell>
                        <TableCell>{transaction.paymentMethod}</TableCell>
                        <TableCell
                          className={`text-right ${
                            transaction.amount > 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {transaction.amount > 0 ? "+" : ""}
                          {formatAmount(transaction.amount)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={getPaymentStatus(transaction).style}
                          >
                            {getPaymentStatus(transaction).status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatDate(transaction.createdAt)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        暂无交易记录
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                共 {pagination?.total} 条记录，第 {pagination?.page}/
                {pagination?.totalPages} 页
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasPrev}
                  onClick={() => loadMoreTransactions(pagination?.page - 1)}
                >
                  上一页
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasNext}
                  onClick={() => loadMoreTransactions(pagination?.page + 1)}
                >
                  下一页
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
}
