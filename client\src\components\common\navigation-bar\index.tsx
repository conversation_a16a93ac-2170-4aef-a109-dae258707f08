import { Icon, Search } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ReactNode, useEffect, useState } from 'react';
import './index.less';

export interface ButtonProps {
  /** 按钮文本 */
  text?: string;
  /** 按钮图标 */
  icon?: string;
  /** 点击事件 */
  onClick?: () => void;
}

export interface NavigationBarProps {
  /** 左侧按钮组 */
  buttons?: ButtonProps[];
  /** 默认返回按钮 */
  showBackButton?: boolean;
  /** 返回按钮文本 */
  backText?: string;
  /** 自定义返回事件 */
  onBack?: () => void;
  /** 标题 */
  title?: string;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 搜索框占位文本 */
  searchPlaceholder?: string;
  /** 搜索事件 */
  onSearch?: (value: string) => void;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 自定义内容（插槽），改为children */
  children?: ReactNode;
}

/**
 * 定制化导航栏组件
 * 包含按钮组、搜索框、标题，支持自定义插槽
 */
const NavigationBar: React.FC<NavigationBarProps> = ({
  buttons = [],
  showBackButton = true,
  backText,
  onBack,
  title,
  showSearch = false,
  searchPlaceholder = '搜索',
  onSearch,
  backgroundColor = 'transparent',
  children,
}) => {
  const [statusBarHeight, setStatusBarHeight] = useState(0);
  const [menuButtonInfo, setMenuButtonInfo] =
    useState<Taro.getMenuButtonBoundingClientRect.Rect | null>(null);
  const [navBarHeight, setNavBarHeight] = useState(44);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    // 获取状态栏高度和胶囊信息
    const sys = Taro.getSystemInfoSync();
    setStatusBarHeight(sys.statusBarHeight || 0);

    const menuButton = Taro.getMenuButtonBoundingClientRect();
    setMenuButtonInfo(menuButton);

    // 根据胶囊位置计算导航栏高度
    if (menuButton) {
      // 导航栏高度 = 胶囊的height + 胶囊顶部距离状态栏的距离(胶囊top - 状态栏高度) * 2
      const navContentHeight =
        menuButton.height +
        (menuButton.top - (sys.statusBarHeight || 0)) * 2 +
        2;
      setNavBarHeight(navContentHeight);

      // console.log('statusBarHeight', statusBarHeight);
      // console.log('menuButton', menuButton);
      // console.log('navContentHeight', navContentHeight);
      // console.log('navBarHeight', navBarHeight);
    }
  }, []);

  // 返回上一页
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      // const pages = Taro.getCurrentPages();
      // if (pages.length > 1) {
      Taro.navigateBack();
      // } else {
      //   Taro.reLaunch({ url: '/pages/homepage/index' });
      // }
    }
  };

  // 计算总高度: 状态栏高度 + 导航栏高度
  const totalNavHeight = statusBarHeight + navBarHeight;

  // 计算右侧安全距离，确保与胶囊按钮保持至少24px的距离
  const rightSafeDistance = menuButtonInfo ? menuButtonInfo.width + 24 : 120;

  // 判断是否有左侧按钮
  const hasLeftButtons = showBackButton || buttons.length > 0;

  return (
    <View
      className='navigation-bar fixed top-0 left-0 w-full z-50 flex flex-col'
      style={{ height: `${totalNavHeight}px` }}
    >
      {/* 状态栏 */}
      <View className='w-full' style={{ height: `${statusBarHeight}px` }} />

      {/* 导航内容区 */}
      <View
        className='w-full flex items-center relative box-border pl-4'
        style={{
          height: `${navBarHeight}px`,
          paddingRight: `${rightSafeDistance}px`,
        }}
      >
        {/* 如果有自定义内容（children），则显示自定义内容 */}
        {children ? (
          <View className='w-full h-full flex items-center'>{children}</View>
        ) : (
          <>
            {/* 左侧按钮组 */}
            {hasLeftButtons && (
              <View className='flex items-center h-full z-10'>
                {showBackButton && (
                  <View
                    className='flex items-center h-full pr-2'
                    onClick={handleBack}
                  >
                    <Icon name='arrow-left' size='20px' />
                    {backText && (
                      <Text className='text-md text-primary ml-1'>
                        {backText}
                      </Text>
                    )}
                  </View>
                )}
              </View>
            )}
            {/* 按钮组 */}
            {buttons.length > 0 && (
              <View className='flex items-center h-full gap-4'>
                {buttons.map((button, index) => (
                  <View
                    key={index}
                    className='flex items-center h-full'
                    onClick={button.onClick}
                  >
                    {button.icon && <Icon name={button.icon} size='20px' />}
                    {button.text && (
                      <Text className='text-md ml-1'>{button.text}</Text>
                    )}
                  </View>
                ))}
              </View>
            )}
            {showSearch && (
              <Search
                value={searchValue}
                placeholder={searchPlaceholder}
                onChange={(e) => setSearchValue(e.detail)}
                onSearch={() => onSearch && onSearch(searchValue)}
                className='w-full h-16 pointer-events-auto'
                shape='round'
                background={backgroundColor}
              />
            )}
            {/* 标题 居中显示*/}
            {title && (
              <View
                className='absolute left-0 right-0 h-full flex items-center justify-center box-border pointer-events-none'
                style={{
                  width: '100vw',
                  left: '50%',
                }}
              >
                <Text className='text-base font-medium text-center'>
                  {title}
                </Text>
              </View>
            )}
          </>
        )}
      </View>
    </View>
  );
};

export default NavigationBar;
