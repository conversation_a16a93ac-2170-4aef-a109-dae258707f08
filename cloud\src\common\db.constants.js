/**
 * 数据库集合名称常量
 * 与前端 client/src/model/db.model.ts 中的定义保持一致
 */
const COLLECTIONS = {
  USER_SENSITIVE: "user_sensitive",

  USER_PUBLIC: "user_public",
  THERAPIST: "therapists",
  THERAPIST_SERVICE: "therapist_service",
  WALLET: "wallet",
  INCOME_DETAIL: "income_detail",
  STAT_SUMMARY: "stat_summary",
  THERAPIST_EXTINFO: "therapist_extinfo",
  THERAPIST_SCHEDULES: "therapist_schedules",
  ORDER_REVIEWS: "order_reviews",
  USER_FAVORITES: "user_favorites",
  DISTRIBUTION_OVERVIEW: "distribution_overview",
  DISTRIBUTION_INVITED_USER: "distribution_invited_user",
  DISTRIBUTION_ORDER: "distribution_order",
  SYSTEM_CONFIG: "system_config",
  NOTIFICATION_LIST: "notification_list",
  CHAT_SESSION: "chat_session",
  CHAT_MESSAGE: "chat_message",
  ORDER: "order",
  ORDER_ACTION: "order_action",
  ORDER_EXTINFO: "order_extinfo",
  PAYMENT: "payment",
  REFUND_RECORD: "refund_record",
  WITHDRAW: "withdraw_record",

  THERAPIST_MONTHLY_STATS: "therapist_monthly_stats",

  // 测量测试相关集合
  MEASURES_TEST: "measures_test",
  MEASURES_FEATURED: "measures_featured",
  MEASURES_TEST_RECORD: "measures_test_record",
  MEASURES_TEST_REPORT: "measures_test_report",
  // 疗愈音频相关集合
  TREATMENT_CATEGORY: "treatment_category",
  TREATMENT_AUDIO: "treatment_audio",
  TREATMENT_FEATURED: "treatment_featured",
  TREATMENT_LISTENING_RECORD: "treatment_listening_record",
  TREATMENT_FAVORITES: "treatment_favorites",

  // 课程相关集合
  COURSES: "courses",
  COURSE_FEATURED: "course_featured",
  COURSE_LISTENING_RECORD: "course_listening_record",
  COURSE_FAVORITES: "course_favorites",
};

/**
 * todo: 如何让前端和后端保持一致
 * 订单状态
 * 与前端 client/src/model/order.interface.ts 中的定义保持一致
 */

/** 主订单状态 */
const ORDER_STATUS = {
  // 待处理状态
  PENDING_PAYMENT: 10, // 待支付
  PENDING_CONFIRM: 11, // 待确认
  // 活跃状态
  PENDING_START: 20, // 待开始
  IN_PROGRESS: 21, // 进行中
  // 结束状态
  COMPLETED: 30, // 已完成
  REVIEWED: 31, // 已评价
  REJECTED: 40, // 已拒绝
  CANCELLED: 41, // 已取消
};

/** 退款状态 */
const REFUND_STATUS = {
  NONE: 0, // 无退款
  AUDITING: 1, // 退款审核中
  REJECTED: 2, // 退款拒绝
  PROCESSING: 3, // 退款中
  COMPLETED: 4, // 已退款
  FAILED: 5, // 退款失败
};

const INITATOR_SOURCE = {
  USER: 0,
  THERAPIST: 1,
  ADMIN: 2,
  SYSTEM: 3,
};
// 操作类型枚举
const ACTION_TYPE = {
  CREATE: 0, // 创建订单
  PAY: 1, // 支付
  SUBMIT_USER_INFO: 2, // 提交用户信息
  ACCEPT: 3, // 接受
  REJECT: 4, // 拒绝
  CANCEL: 5, // 取消
  START: 6, // 开始服务
  COMPLETE: 7, // 完成服务
  REVIEW: 8, // 评价
  REFUND_REQUEST: 9, // 退款申请
  REFUND_AUDIT_PASS: 10, // 退款审核通过
  REFUND_AUDIT_REJECT: 11, // 退款审核拒绝
  REFUND_REFUNDING: 12, // 退款中
  REFUND_COMPLETE: 13, // 退款完成
  REFUND_FAILED: 14, // 退款失败
  REFUND_ROLLBACK: 15, // 退款回滚
};

/** 服务类型 */
const ServiceType = {
  VIDEO: 0,
  VOICE: 1,
  TEXT: 2,
  FACE_TO_FACE: 3,
};

module.exports = {
  COLLECTIONS,
  ORDER_STATUS,
  REFUND_STATUS,
  INITATOR_SOURCE,
  ACTION_TYPE,
  ServiceType,
};
