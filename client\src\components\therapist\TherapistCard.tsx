import { Icon, Image, Tag } from '@antmjs/vantui';
import PriceDisplay from '@components/order-card/PriceDisplay';
import TherapistFavoriteButton from '@components/therapist/TherapistFavoriteButton';
import { ICONS_LINEAR } from '@constants/assets';
import { SERVICE_TYPE_MAP_SHORT } from '@constants/text';
import { therapist_summary } from '@model/therapist.interface';
import { Text, View } from '@tarojs/components';
import DirectionTags from './DirectionTags';
import ExperienceTags from './ExperienceTags';

export interface TherapistCardProps {
  therapist: therapist_summary;
  // 组件配置
  backgroundColor?: string;
  showDivider?: boolean;
  onClick?: (id: string | number) => void;
  isFavorite?: boolean; // 是否收藏，默认从therapist中获取
  showFavoriteButton?: boolean; // 是否显示收藏按钮
  onFavoriteClick?: () => void; // 收藏按钮点击事件
}

const TherapistCard: React.FC<TherapistCardProps> = ({
  therapist,
  backgroundColor = 'var(--color-primary-bg-color)',
  showDivider = false,
  onClick,
  isFavorite,
  showFavoriteButton = false,
  onFavoriteClick,
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(therapist.id);
    }
  };

  // 默认使用therapist中的isFavorite值，如果传入了isFavorite则优先使用传入的
  const isTherapistFavorite =
    isFavorite !== undefined ? isFavorite : (therapist as any).isFavorite;

  // 处理收藏状态变化
  const handleFavoriteChange = (newFavoriteState: boolean) => {
    // 调用外部传入的收藏点击事件
    if (onFavoriteClick) {
      onFavoriteClick();
    }
  };

  // 服务类型
  const serviceTypes =
    therapist.service && Array.isArray(therapist.service)
      ? therapist.service.map((s) => SERVICE_TYPE_MAP_SHORT[s]).join(' / ')
      : '';

  return (
    <View
      className='flex flex-col items-center w-full px-4'
      style={`backgroundColor:${backgroundColor}`}
      onClick={handleClick}
    >
      {/* content */}
      <View className='flex flex-row gap-4 py-4 w-full'>
        {/* 左侧头像 */}
        <View className='flex flex-col justify-start items-center gap-2'>
          <Image
            src={therapist.avatar}
            width={76 * 2}
            height={76 * 2}
            radius={12 * 2}
          />
          {/* 收藏按钮 */}
          {showFavoriteButton && (
            <View
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <TherapistFavoriteButton
                therapistId={therapist.id.toString()}
                initialFavorite={isTherapistFavorite}
                onFavoriteChange={handleFavoriteChange}
                size='small'
                showText
                // className='p-1'
              />
            </View>
          )}
        </View>

        {/* 右侧内容区 */}
        <View className='flex-1 overflow-hidden flex flex-col gap-2'>
          <View className='flex flex-col items-start gap-1 w-full'>
            {/* 姓名和可约状态 */}
            <View className='flex flex-row items-center justify-between w-full'>
              <Text className='font-bold text-base mr-2'>{therapist.name}</Text>
              <View className='flex flex-row items-center justify-end gap-2'>
                {therapist.available && (
                  <View className='bg-primarylight text-xs text-primary font-medium px-1 py-0.5 rounded-sm'>
                    {therapist.available}
                  </View>
                )}
              </View>
            </View>
            {/* 服务 */}
            <View className='flex flex-row items-center justify-between w-full'>
              <Text className='text-xs text-default font-regular'>
                {serviceTypes}
              </Text>
              {/* 地理位置 icon+text */}
              <View className='flex flex-row items-center'>
                <Icon
                  name={ICONS_LINEAR.LOCATION24}
                  size='12px'
                  color='var(--color-secondary)'
                />
                <Text className='text-xs text-default font-regular'>
                  {therapist.location}
                </Text>
              </View>
            </View>
            {/* 专业/title */}
            {therapist.specialties?.[0] && therapist.titles?.[0] && (
              <Text className='text-sm text-default font-medium'>
                {`${therapist.specialties[0]} | ${therapist.titles[0]}`}
              </Text>
            )}

            {/* 经验 */}
            <ExperienceTags tags={therapist.tags} maxCount={2} />
          </View>
          {/* 咨询方向 */}
          <DirectionTags directions={therapist.directions} maxCount={2} />
          {/* 价格区域 */}
          <View className='flex flex-row items-center justify-between w-full'>
            {/* 优惠 */}
            {therapist.promo && (
              <Tag type='warning' size='small' plain>
                {therapist.promo}
              </Tag>
            )}

            {/* 价格 */}
            {therapist.price > 0 && (
              <View className='flex flex-1 items-center justify-end '>
                <PriceDisplay price={therapist.price} color='danger' />
              </View>
            )}
          </View>
        </View>
      </View>
      {showDivider && <View className='w-full h-px bg-gray-200' />}
    </View>
  );
};

export default TherapistCard;
