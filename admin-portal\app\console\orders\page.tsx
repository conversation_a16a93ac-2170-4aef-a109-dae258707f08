"use client";

import { SERVICE_TYPE_MAP } from "@/app/constants/text";
import { useOrderList } from "@/app/hooks/useOrderList";
import {
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from "@/app/model/order.interface";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DotLoading } from "@/components/ui/dot-loading";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { useState } from "react";

// 订单状态对应的样式
const statusStyles = {
  [ORDER_STATUS.PENDING_PAYMENT]: "bg-yellow-100 text-yellow-800",
  [ORDER_STATUS.PENDING_CONFIRM]: "bg-yellow-100 text-yellow-800",
  [ORDER_STATUS.PENDING_START]: "bg-yellow-100 text-yellow-800",
  [ORDER_STATUS.COMPLETED]: "bg-green-100 text-green-800",
  [ORDER_STATUS.IN_PROGRESS]: "bg-blue-100 text-blue-800",
  [ORDER_STATUS.PENDING_PAYMENT]: "bg-yellow-100 text-yellow-800",
  [ORDER_STATUS.CANCELLED]: "bg-gray-100 text-gray-800",
};

const getOrderStatus = (order: Order_summary) => {
  const status = order.status;
  switch (status) {
    case ORDER_STATUS.PENDING_PAYMENT:
      return { status: "待支付", style: statusStyles[status] || "" };
    case ORDER_STATUS.PENDING_CONFIRM:
      return { status: "待确认", style: statusStyles[status] || "" };
    case ORDER_STATUS.PENDING_START:
      return { status: "待开始", style: statusStyles[status] || "" };
    case ORDER_STATUS.IN_PROGRESS:
      return { status: "进行中", style: statusStyles[status] || "" };
    case ORDER_STATUS.COMPLETED:
      return { status: "已完成", style: statusStyles[status] || "" };
    case ORDER_STATUS.REJECTED:
      return { status: "已拒绝", style: statusStyles[status] || "" };
    case ORDER_STATUS.CANCELLED:
      return { status: "已取消", style: statusStyles[status] || "" };
    default:
      return { status: "", style: statusStyles[status] || "" };
  }
};

const getRefundStatus = (order: Order_summary) => {
  const refundStatus = order.refundStatus;
  switch (refundStatus) {
    case REFUND_STATUS.AUDITING:
      return { status: "退款审核中", style: "bg-yellow-100 text-yellow-800" };
    case REFUND_STATUS.REJECTED:
      return { status: "退款拒绝", style: "bg-red-100 text-red-800" };
    case REFUND_STATUS.PROCESSING:
      return { status: "退款中", style: "bg-blue-100 text-blue-800" };
    case REFUND_STATUS.COMPLETED:
      return { status: "已退款", style: "bg-green-100 text-green-800" };
    case REFUND_STATUS.FAILED:
      return { status: "退款失败", style: "bg-red-100 text-red-800" };
    default:
      return { status: "无退款", style: "bg-gray-100 text-gray-800" };
  }
};
// 格式化时间戳为可读日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return "-";
  return format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss");
};

// 时间范围选项
const TIME_RANGES = {
  "7d": { label: "最近7天", days: 7 },
  "30d": { label: "最近30天", days: 30 },
  "90d": { label: "最近90天", days: 90 },
  all: { label: "所有时间", days: 0 },
};

export default function OrdersPage() {
  const [searchInput, setSearchInput] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [timeRange, setTimeRange] = useState("7d");
  const [refundStatusFilter, setRefundStatusFilter] = useState(
    REFUND_STATUS.NONE.toString()
  );
  const {
    orders,
    pagination,
    loading,
    refreshOrders,
    loadMoreOrders,
    onStatusChange,
    onSearch,
    onDateRangeChange,
  } = useOrderList();

  // 处理状态过滤变化
  const handleStatusChange = (value: string) => {
    console.log("handleStatusChange", value);
    setStatusFilter(value);

    if (value === "all") {
      onStatusChange([], false, []);
    } else if (value === ORDER_STATUS.PENDING_PAYMENT.toString()) {
      onStatusChange([ORDER_STATUS.PENDING_PAYMENT], false, [
        parseInt(refundStatusFilter),
      ]);
    } else if (value === ORDER_STATUS.PENDING_CONFIRM.toString()) {
      onStatusChange([ORDER_STATUS.PENDING_CONFIRM], false, [
        parseInt(refundStatusFilter),
      ]);
    } else if (value === ORDER_STATUS.PENDING_START.toString()) {
      onStatusChange([ORDER_STATUS.PENDING_START], false, [
        parseInt(refundStatusFilter),
      ]);
    } else if (value === ORDER_STATUS.IN_PROGRESS.toString()) {
      onStatusChange([ORDER_STATUS.IN_PROGRESS], false, [
        parseInt(refundStatusFilter),
      ]);
    } else if (value === ORDER_STATUS.CANCELLED.toString()) {
      onStatusChange([ORDER_STATUS.CANCELLED, ORDER_STATUS.REJECTED], false, [
        parseInt(refundStatusFilter),
      ]);
    } else if (value === ORDER_STATUS.COMPLETED.toString()) {
      onStatusChange([ORDER_STATUS.COMPLETED, ORDER_STATUS.REVIEWED], false, [
        parseInt(refundStatusFilter),
      ]);
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);

    if (value === "all") {
      onDateRangeChange(undefined);
    } else {
      const days = TIME_RANGES[value as keyof typeof TIME_RANGES].days;
      const endDate = Date.now();
      const startDate = endDate - days * 24 * 60 * 60 * 1000;
      onDateRangeChange([startDate, endDate]);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    onSearch(searchInput);
  };

  // 处理搜索框按回车
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleRefundStatusChange = (value: string) => {
    setRefundStatusFilter(value);
    if (value === REFUND_STATUS.NONE.toString()) {
      onStatusChange(
        statusFilter === "all" ? [] : [parseInt(statusFilter)],
        false,
        []
      );
    } else {
      onStatusChange(
        statusFilter === "all" ? [] : [parseInt(statusFilter)],
        false,
        [parseInt(value)]
      );
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">订单管理</h1>
        <p className="text-muted-foreground">查看和管理所有订单信息</p>
      </div>

      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Input
            placeholder="搜索订单号/用户/咨询师..."
            className="max-w-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <div className="flex flex-wrap gap-2">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="订单状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value={ORDER_STATUS.COMPLETED.toString()}>
                  已完成
                </SelectItem>
                <SelectItem value={ORDER_STATUS.IN_PROGRESS.toString()}>
                  进行中
                </SelectItem>
                <SelectItem value={ORDER_STATUS.PENDING_PAYMENT.toString()}>
                  待支付
                </SelectItem>
                <SelectItem value={ORDER_STATUS.PENDING_CONFIRM.toString()}>
                  待确认
                </SelectItem>
                <SelectItem value={ORDER_STATUS.PENDING_START.toString()}>
                  待开始
                </SelectItem>
                <SelectItem value={ORDER_STATUS.CANCELLED.toString()}>
                  已取消
                </SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={refundStatusFilter.toString()}
              onValueChange={handleRefundStatusChange}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="退款状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={REFUND_STATUS.NONE.toString()}>
                  无退款
                </SelectItem>
                <SelectItem value={REFUND_STATUS.AUDITING.toString()}>
                  退款审核中
                </SelectItem>
                <SelectItem value={REFUND_STATUS.REJECTED.toString()}>
                  退款拒绝
                </SelectItem>
                <SelectItem value={REFUND_STATUS.PROCESSING.toString()}>
                  退款中
                </SelectItem>
                <SelectItem value={REFUND_STATUS.COMPLETED.toString()}>
                  已退款
                </SelectItem>
                <SelectItem value={REFUND_STATUS.FAILED.toString()}>
                  退款失败
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
                <SelectItem value="all">所有时间</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch}>搜索</Button>
            <Button variant="outline" onClick={refreshOrders}>
              刷新
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <DotLoading size={8} color="#1e40af" className="mx-auto" />
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>订单编号</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>咨询师</TableHead>
                    <TableHead>服务类型</TableHead>
                    <TableHead className="text-right">金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>退款状态</TableHead>
                    <TableHead>下单时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders && orders.length > 0 ? (
                    orders.map((order) => (
                      <TableRow key={order._id}>
                        <TableCell className="font-medium">
                          {order._id}
                        </TableCell>
                        <TableCell>{order.userName}</TableCell>
                        <TableCell>{order.therapistName}</TableCell>
                        <TableCell>
                          {SERVICE_TYPE_MAP[order.serviceType]}
                        </TableCell>
                        <TableCell className="text-right">
                          ¥{order.price}
                        </TableCell>
                        <TableCell>
                          <Badge className={getOrderStatus(order).style}>
                            {getOrderStatus(order).status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRefundStatus(order).style}>
                            {getRefundStatus(order).status}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(order.createdAt)}</TableCell>
                        <TableCell>
                          <Button variant="link" className="h-auto p-0">
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        暂无订单数据
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                共 {pagination?.total} 条记录，第 {pagination?.page}/
                {pagination?.totalPages} 页
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasPrev}
                  onClick={() => loadMoreOrders(pagination?.page - 1)}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination?.hasNext}
                  onClick={() => loadMoreOrders(pagination?.page + 1)}
                >
                  下一页
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
}
