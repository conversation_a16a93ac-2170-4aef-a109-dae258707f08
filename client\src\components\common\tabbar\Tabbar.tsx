import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { cloneElement, isValidElement, ReactNode } from 'react';

const sys = Taro.getSystemInfoSync();
interface TabbarProps {
  children: ReactNode;
  active: number;
  onChange: (event: { index: number }) => void;
  className?: string;
  fixed?: boolean;
}

const Tabbar: React.FC<TabbarProps> = ({
  children,
  active,
  onChange,
  className = '',
  fixed = false,
}) => {
  const childrenArray = Array.isArray(children) ? children : [children];

  const handleItemClick = (index: number) => {
    onChange({ index: index });
  };

  return (
    <View
      className={`flex flex-row items-start w-full  bg-white pb-[${
        sys.safeArea ? sys.screenHeight - sys.safeArea.bottom : 0
      }Px] ${
        fixed ? 'fixed left-0 right-0 bottom-0 z-[100]' : ''
      } ${className}`}
    >
      {React.Children.map(childrenArray, (child, index) => {
        if (!isValidElement(child)) return null;

        return cloneElement(child, {
          active: index === active,
          onClick: () => handleItemClick(index),
        } as any);
      })}
    </View>
  );
};

export default Tabbar;
