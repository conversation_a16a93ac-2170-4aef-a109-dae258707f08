import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Activity, CreditCard, Package, Users } from "lucide-react";
import Link from "next/link";

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">仪表盘</h1>
        <p className="text-muted-foreground">管理心理咨询平台的数据和资源</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日订单数</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">15</div>
            <p className="text-xs text-muted-foreground">较昨日 +2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日交易额</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥2,350</div>
            <p className="text-xs text-muted-foreground">较昨日 +18%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">咨询师总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42</div>
            <p className="text-xs text-muted-foreground">本月新增 +4</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">资源总数</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">56</div>
            <p className="text-xs text-muted-foreground">本月新增 +8</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>待处理事项</CardTitle>
            <CardDescription>系统中需要您处理的事项</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
                <div className="flex-1">
                  <div className="font-medium">待审核退款申请</div>
                  <div className="text-sm text-muted-foreground">
                    共有 3 笔退款申请待处理
                  </div>
                </div>
                <Link
                  href="/console/orders/refunds"
                  className="text-sm text-blue-600 hover:underline"
                >
                  查看
                </Link>
              </div>
              <div className="flex items-center gap-4">
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
                <div className="flex-1">
                  <div className="font-medium">待审核提现申请</div>
                  <div className="text-sm text-muted-foreground">
                    共有 5 笔提现申请待处理
                  </div>
                </div>
                <Link
                  href="/console/finance/withdrawals"
                  className="text-sm text-blue-600 hover:underline"
                >
                  查看
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>系统公告</CardTitle>
            <CardDescription>平台系统通知</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-baseline">
                  <div className="font-medium">
                    心理咨询平台管理系统上线通知
                  </div>
                  <div className="text-xs text-muted-foreground">
                    2023-06-01
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  心理咨询平台管理系统正式上线，欢迎使用！
                </div>
              </div>
              <div>
                <div className="flex justify-between items-baseline">
                  <div className="font-medium">新增疗愈音乐资源</div>
                  <div className="text-xs text-muted-foreground">
                    2023-05-28
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  系统新增10首疗愈音乐资源，请在资源管理中查看。
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
