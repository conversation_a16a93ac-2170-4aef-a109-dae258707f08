import { Toast } from '@antmjs/vantui';
import { useAppointmentStore } from '@stores/appointment.store';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';
import { useTherapistStore } from '@stores/therapist.store';
import { getOrderActions } from '@utils/role';
import { orderRouter } from '@utils/router';
import {
  getDayInMillisFromDateInMillis,
  getHourFromDateInMillis,
} from '@utils/time';
import { therapistActions } from './therapist.action';

export const appointmentActions = {
  // 创建预约
  startAppointment: async ({
    from,
    orderId,
    therapistId,
  }: {
    from: string;
    orderId?: string;
    therapistId?: string;
  }) => {
    console.log('startAppointment', from, orderId, therapistId);
    useLoadingStore.getState().setTransactionLoading(true);
    let _therapistId = therapistId;

    //从咨询师详情页面过来的
    if (from === 'therapist') {
      // 跳转到咨询师详情页
    } else if (from === 'order' && orderId) {
      // 从订单中过来的 '再来一单'

      //先重置下状态
      useAppointmentStore.getState().resetAppointment();

      // 需要将原订单信息参数设置到appointmentStore中

      //order从store取，没有就拉取
      let order = useOrderStoreSelector.getState().getOrderById(orderId);
      if (!order) {
        order = (await getOrderActions().fetchOrderById(orderId)) ?? undefined;
      }
      if (!order) {
        // 订单不存在 仍然继续
        console.warn('订单不存在', orderId);
        return;
      } else {
        const _serviceType = order.serviceType;

        //从store取，没有就拉取
        let services = useTherapistStore.getState().currentTherapistService;
        if (!services) {
          services =
            (await therapistActions.fetchTherapistService(order.therapistId)) ??
            null;
        }
        useAppointmentStore
          .getState()
          .setSelectedService(
            services?.services.find(
              (service) => service.type === _serviceType
            ) ?? null
          );
        useAppointmentStore
          .getState()
          .setSelectedDurationMultiple(order!.duration);

        // 如果therapistId为空，则从order中获取
        if (!_therapistId) {
          _therapistId = order.therapistId;
        }
      }
    }

    console.log('therapistId', _therapistId);
    if (_therapistId) {
      useAppointmentStore.getState().setCurrentTherapistId(_therapistId);
      orderRouter.create(_therapistId);
    } else {
      Toast.show('咨询师不存在');
      useLoadingStore.getState().setTransactionLoading(false);
    }
  },

  rescheduleOrder: async (orderId: string) => {
    useAppointmentStore.getState().resetAppointment();
    //从store中获取订单
    let order = useOrderStoreSelector.getState().getOrderById(orderId);
    if (!order) {
      order = (await getOrderActions().fetchOrderById(orderId)) ?? undefined;
    }
    if (!order) {
      return;
    }
    // 从订单中取出已有的预约时间
    const date = getDayInMillisFromDateInMillis(order.startTime);
    const timeSlot = getHourFromDateInMillis(order.startTime);
    useAppointmentStore.getState().setSelectedDate(date);
    useAppointmentStore.getState().setSelectedTimeSlot(timeSlot);
    orderRouter.reschedule(order.therapistId);
  },
};
