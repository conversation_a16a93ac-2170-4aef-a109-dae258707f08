import { Button, Field, Image, Rate, Tag } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import ResultPopup from '@components/common/result-popup';
import { AVATAR_DEFAULT } from '@constants/assets';
import { reviewActions } from '@core/actions/review.action';
import { useOrderTherapist } from '@hooks/useOrderTherapist';
import { useLoadingStore } from '@stores/loading.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useState } from 'react';

// 预设印象标签
const IMPRESSION_TAGS = [
  '专业负责',
  '耐心倾听',
  '思路清晰',
  '温暖治愈',
  '有同理心',
  '建议实用',
  '幽默风趣',
  '善于引导',
];

export default function ReviewPage() {
  const router = useRouter();
  const { orderId } = router.params;
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  // 使用自定义Hook获取咨询师信息
  const { therapistAvatar, therapistName } = useOrderTherapist(orderId);

  // 评分和评价内容状态
  const [score, setScore] = useState(0);
  const [comment, setComment] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // 结果弹窗状态
  const [showResultPopup, setShowResultPopup] = useState(false);
  const [resultType, setResultType] = useState<'success' | 'error'>('success');
  const [resultMsg, setResultMsg] = useState('');

  // 选择/取消选择标签
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((item) => item !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // 提交评价
  const handleSubmit = async () => {
    try {
      setTransactionLoading(true);
      const result = await reviewActions.reviewOrder(
        orderId!,
        score,
        comment,
        selectedTags
      );

      // 模拟提交成功
      setResultType('success');
      setResultMsg('评价提交成功，感谢您的反馈！');
      setShowResultPopup(true);
    } catch (error) {
      setResultType('error');
      setResultMsg(error.message || '评价提交失败，请稍后重试');
      setShowResultPopup(true);
    } finally {
      setTransactionLoading(false);
    }
  };

  // 处理结果弹窗确认按钮点击
  const handleResultConfirm = () => {
    // 关闭弹窗并返回订单详情页
    setShowResultPopup(false);
    Taro.navigateBack();
  };

  return (
    <PageLol
      navigationProps={{
        title: '服务评价',
        showBackButton: true,
      }}
      error={!orderId ? '订单ID不存在' : null}
    >
      <View className='p-4 flex flex-col items-center'>
        {/* 咨询师头像 */}
        <Image
          src={therapistAvatar || AVATAR_DEFAULT}
          width='180px'
          height='180px'
          radius='9999px'
          className='mb-2'
        />

        {/* 评分区域 */}
        <Text className='text-base font-bold mb-4'>
          请您为 {therapistName} 的服务打分
        </Text>
        <View className='mb-8'>
          <Rate
            value={score}
            onChange={(e) => setScore(Number(e.detail))}
            size='32px'
            gutter='32px'
            color='var(--color-star)'
            voidColor='var(--color-text-disabled)'
            className='flex flex-row items-center justify-center'
          />
        </View>

        {/* 印象标签区域 */}
        <View className='w-full mb-6'>
          <Text className='text-base font-bold mb-4 block'>您的印象是</Text>
          <View className='flex flex-row flex-wrap gap-3'>
            {IMPRESSION_TAGS.map((tag) => (
              <Tag
                key={tag}
                size='large'
                plain={!selectedTags.includes(tag)}
                type={selectedTags.includes(tag) ? 'primary' : 'default'}
                onClick={() => toggleTag(tag)}
                className={
                  selectedTags.includes(tag)
                    ? 'border-primary text-white'
                    : 'border-secondary text-secondary'
                }
              >
                {tag}
              </Tag>
            ))}
          </View>
        </View>

        {/* 评价内容区域 */}
        <View className='w-full mb-6'>
          <Text className='text-base font-bold mb-4 block'>请写下您的评价</Text>
          <View className='rounded-xl bg-gray-50'>
            <Field
              placeholder='请描述您对本次服务的评价和建议'
              value={comment}
              onChange={(e) => setComment(e.detail)}
              type='textarea'
              required={false}
              border={false}
              autosize={{ minHeight: '120px' }}
              style='background-color: var(--color-primary-bg) !important; border-radius: 12px !important;'
            />
          </View>
        </View>

        {/* 提交按钮 */}
        <Button
          type='primary'
          round
          block
          loading={transactionLoading}
          disabled={score === 0 || transactionLoading}
          onClick={handleSubmit}
        >
          提交评价
        </Button>
      </View>

      {/* 评价结果弹窗 */}
      <ResultPopup
        show={showResultPopup}
        type={resultType}
        title={resultType === 'success' ? '评价成功' : '评价失败'}
        illustration={
          resultType === 'success'
            ? '/assets/images/illustration_review.png'
            : '/assets/images/illustration_fail.png'
        }
        content={resultMsg}
        buttonText='确定'
        onButtonClick={handleResultConfirm}
        closeOnClickOverlay={false}
      />
    </PageLol>
  );
}
