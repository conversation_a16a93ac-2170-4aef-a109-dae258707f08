# online_consultation_mini

[![Taro](https://img.shields.io/badge/Taro-3.x-brightgreen)](https://taro-docs.jd.com/) [![Node.js](https://img.shields.io/badge/Node.js-%3E%3D18.17.0-blue)](https://nodejs.org/) [![pnpm](https://img.shields.io/badge/pnpm-%3E%3D8.0.0-orange)](https://pnpm.io/)

> 一款基于 Taro 3.x 开发的在线心理咨询平台小程序客户端。
>
> 本项目集成了@antmj/vanui, Tailwind CSS 和 Zustand 等现代化技术栈，旨在提供稳定、高效且用户友好的在线咨询服务。
>
> 当前主要适配微信和支付宝小程序。若需适配其他平台，可能需要根据 Taro 文档进行额外配置和依赖安装。不建议将此模板直接用于 RN 应用开发。

---

## 🚀 快速开始

### 环境要求

- Node.js >= 18.17.0
- pnpm >= 8.0.0
- 微信开发者工具或支付宝开发者工具（根据目标平台选择）

### 开发流程

#### 1. 安装依赖

```shell
pnpm install
```

#### 2. 启动本地开发服务

```shell
# 微信小程序
pnpm run dev:weapp

# 支付宝小程序 (假设已有相应 npm script)
# pnpm run dev:alipay
```

#### 3. 预览与调试

打开相应平台的开发者工具，导入项目根目录下的 `weapp` (或对应平台的产物目录) 并运行。

---

## 📦 生产构建

运行以下命令生成相应平台的生产包：

```shell
# 微信小程序
pnpm run build:weapp

# 支付宝小程序 (假设已有相应 npm script)
# pnpm run build:alipay
```

---

## 🛠 技术栈

### 核心框架

- **[Taro 3.x](https://docs.taro.zone/docs/3.x/)**：多端统一开发框架
- **React 18**：声明式、组件化的前端开发库
- **TypeScript**：为 JavaScript 添加静态类型检查

### 状态管理

- **[Zustand](https://zustand.docs.pmnd.rs/getting-started/introduction)**：轻量、灵活的状态管理方案

### 样式方案

- **[Tailwind CSS 3.x](https://v3.tailwindcss.com/docs/installation)**：实用优先的原子化 CSS 框架
- **[weapp-tailwindcss](https://weapp-tw.icebreaker.top/docs/quick-start/frameworks/taro)**：Tailwind CSS 在小程序端的适配器

### 组件库

- **[@antmjs/vantui](https://antmjs.github.io/vantui/#/home)**：基于 VantWeapp 封装，同时支持 Taro 和 React 的 UI 组件库

### 工具库

- **Lodash**：实用的 JavaScript 工具函数集合
- **Dayjs**：轻量级的日期时间处理库

---

### Taro 和@antmjs/vantui、tailwindcss、weapp-tailwindcss 插件、微信小程序

微信小程序使用 rpx 单位，默认以 750rpx 为设计基础稿，750rpx=375px(iphone6 物理尺寸)
Taro 默认也以 750 为设计稿尺寸，并 1:1 转化成 rpx。
https://docs.taro.zone/docs/size

tailwindcss 中
https://v3.tailwindcss.com/docs/customizing-spacing#default-spacing-scale

Name Size Pixels Preview
0 0px 0px
px 1px 1px
0.5 0.125rem 2px
1 0.25rem 4px

figma 中设计稿默认以逻辑像素，例如 iphone8/6(375\*667)。而 figma 中的 vant 插件也相应地根据 figma 做了尺寸减半：例如一个 button 组件，在 figma 中,h=40, text=14，而在@antmjs/vantui 代码中, h=80,text=28。
于是，如果将 designwidth 设置为 750，那么 1px 转化成 1rpx=0.5px(微信小程序),vantui 的组件自然就和设计稿效果一致。但是如果设计稿自定义的尺寸，如我使用 20 号字体或者 20 的间隔，那么转化成 rpx 后效果减半。反之将 designwidth 设置为 375，则效果颠倒，vantui 的显示效果增倍。

同样，对于 tailwindcss，也有 2 个转化，使用 gap-1 这样的写法(0.25rem)和使用 gap-[4px]，则存在转化问题。

各种解决的方法：
方法一： 但这个只能解决 vantui 的问题，tailwindcss 还需要配置.
**‼️ 实测：根本不可用，因为 input 传入的是个数字，永远走不到分支里面去。**
https://docs.taro.zone/docs/config-detail#designwidth
https://antmjs.github.io/vantui/main/#/quickstart

config = {
designWidth(input) {
if (input.file.replace(/\+/g, '/').indexOf('@antmjs/vantui') > -1) {
return 750
}
return 375
},
deviceRatio: {
640: 2.34 / 2,
750: 1,
828: 1.81 / 2,
375: 2 / 1,
},
}

方法二：使用 weapp-tailwind 介绍的插件
**‼️ 实测：根本不可用，问题同上，根本走不到分支里面去。**
https://weapp-tw.icebreaker.top/docs/quick-start/rem2rpx
https://weapp-tw.icebreaker.top/docs/quick-start/css-unit-transform

只能这样：
开启 rem2rpx。解决 tailwindcss 的 gap-1 问题。
designWidth: 750.解决 vantui 的组件问题。

style 中设置 px 时不会被转化，除非显示使用 pxTransform。
class 中的 px 会被转化。 在使用时要么\*2 后被转化成 rpx，要么使用使用大小的 Px,避开转化。

## 使用 vantui 的组件时，使用 px 单位，不要使用 rpx 单位。

<Image
src='/assets/images/illustration_notfound.png'
width='268px' //实际得到的是 268rpx
height='268px' //实际得到的是 268rpx
/>
<Image
src='/assets/images/illustration_notfound.png'
width='268' //实际得到的是 138px
height='268' //实际得到的是 138px
/>

## 在 tailwindcss 中,使用 rem,会正确转化，使用 px，因为 pxTransform，会转化成 1:1 rpx，导致图片减半。

## 📁 项目结构

```text
├── config/             # Taro 编译配置
├── src/
│   ├── actions/        # 业务逻辑协调层
│   ├── assets/         # 静态资源 (如图片、字体) (新增或调整)
│   ├── components/     # 通用业务组件
│   ├── hooks/          # 自定义 React Hooks
│   ├── pages/          # 页面级组件
│   ├── services/       # 服务层 (如用户服务、咨询服务等) (新增或调整)
│   ├── store/          # Zustand 状态管理模块
│   ├── styles/         # 全局及主题样式
│   ├── types/          # TypeScript 类型定义
│   ├── utils/          # 通用工具函数
│   │   ├── index.ts    # 工具函数主入口
│   │   └── request.ts  # HTTP 请求封装
│   ├── app.config.ts  # 小程序全局配置
│   ├── app.less        # 全局 Less 样式
│   ├── app.tsx         # 应用入口文件
│   └── index.html      # H5 端 HTML 模板 (若支持)
├── types/              # 全局 TypeScript 类型定义
│   └── global.d.ts
├── .editorconfig       # 编辑器编码规范配置
├── .eslintrc           # ESLint 代码检查配置
├── .gitignore          # Git 版本控制忽略文件
├── babel.config.js     # Babel 转译配置
├── postcss.config.js   # PostCSS 配置
├── tailwind.config.js  # Tailwind CSS 配置文件
├── tsconfig.json       # TypeScript 编译器配置
└── pnpm-lock.yaml      # pnpm 依赖版本锁定文件
```

---

## 架构设计

理想的数据流应该是：
页面调用 action
action 调用 service 获取数据
action 将数据更新到 store
页面从 store 消费数据
这样的好处是职责分离清晰：
service：负责 API 调用，数据获取，处理网络请求
store：负责状态管理和存储
action：负责业务逻辑和协调

### 1. 状态管理 在 src/stores 目录下

使用 Zustand 管理状态，每个 store 一个文件，使用 immer 管理状态变化。

### 2. 接口请求

在 src/services 目录下，

### 3. 组件

工作组件 在 src/components 目录下，每个组件一个文件，使用 react 组件。

业务组件 在 src/pages/\*/\*\* 目录下，该业务专属组件。

### 4. 页面

在 src/pages 目录下，每个页面一个文件，使用 react 组件。

### 5. 核心

在 src/core 目录下，定义核心业务接口、逻辑。

### 6. 常量

在 src/constants 目录下，定义常量。

### 7. 工具

在 src/utils 目录下，工具。 如统一路由。

## ⚡ 性能优化

### 1. 延迟渲染

对于非首屏或次要内容，可使用 `Taro.nextTick()` 或自定义组件实现延迟渲染，优化初始加载性能，减少白屏时间。

```tsx
// 示例：NextTickComponent.tsx (位于 src/components/NextTickComponent 或类似路径)
import Taro, { useEffect, useState } from '@tarojs/taro';
import React from 'react';

interface NextTickComponentProps {
  children: React.ReactNode;
}

const NextTickComponent: React.FC<NextTickComponentProps> = ({ children }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    Taro.nextTick(() => {
      setIsMounted(true);
    });
  }, []);

  return isMounted ? <>{children}</> : null;
};

export default NextTickComponent;

// 使用示例
// import NextTickComponent from '@/components/NextTickComponent';
// <NextTickComponent>
//   <ExpensiveContent />
// </NextTickComponent>
```

### 2. 跳转预加载

在页面跳转前，可通过 `Taro.preload` 提前请求目标页面所需数据，从而缩短用户等待时间。

```tsx
// 页面 A (发起跳转和预加载)
// import { getDataForPageB } from '@/services/yourService'; // 假设的服务请求

Taro.preload({
  pageBData: getDataForPageB(), // 假设 getDataForPageB 返回一个 Promise
});
Taro.navigateTo({ url: '/pages/PageB/index' }); // 跳转到页面 B

// 页面 B (消费预加载数据)
useEffect(() => {
  const preloadData = Taro.getCurrentInstance().preloadData;
  if (preloadData?.pageBData) {
    preloadData.pageBData
      .then((res) => {
        // 使用 res 更新页面状态
        console.log('Preloaded data received:', res);
      })
      .catch((err) => {
        console.error('Failed to process preloaded data:', err);
      });
  }
}, []);
```

### 3. 图片资源优化

- **图片压缩**：使用工具（如 TinyPNG）或构建插件压缩图片体积。
- **适当格式**：根据场景选择合适的图片格式（如 WebP）。
- **图片懒加载**：对非首屏图片开启 `lazy-load` 属性（注意其生效范围，通常是视窗三屏外的图片）。
- **雪碧图/Iconfont**：对于小图标，考虑使用雪碧图或 Iconfont 减少 HTTP 请求。

### 4. 合理的缓存策略

对于不经常变动或实时性要求不高的数据（例如：心理科普文章、咨询师列表），应设计合理的缓存机制，优先从缓存读取，减少不必要的网络请求。

### 5. 使用性能优化组件

对于包含大量节点或复杂逻辑的组件，可以考虑使用 Taro 官方提供的 `CustomWrapper` 进行包裹，以改善渲染性能。

```jsx
import { CustomWrapper } from '@tarojs/components'; // 确保已安装 @tarojs/components

// <CustomWrapper>
//   <ComplexListComponent data={list} />
// </CustomWrapper>
```

---

## 📊 数据统计与分析

本项目支持集成主流的数据统计平台（如友盟、阿拉丁等），以便于跟踪用户行为、分析产品数据，并为产品迭代提供数据支持。

具体的集成方式请参考各统计平台的官方文档，并根据业务需求进行初始化和自定义事件埋点。

### 参考文档 (示例)

- [微信小程序数据助手](https://developers.weixin.qq.com/miniprogram/dev/framework/operating/analysis.html)
- 友盟 U-MiniProgram SDK

### 集成要点

- 在 `app.tsx` 中初始化 SDK。
- 在页面生命周期或关键用户操作时，调用相应 API 上报数据。
- 保护用户隐私，遵守相关法律法规。

---

## 🔒 API 地址动态配置

`src/utils/request.ts` 文件中提供了 `getApiUrl` 函数，该函数能够根据当前运行环境（开发、测试、生产）动态获取对应的 API 服务端地址。

> **重要提示**：请务必谨慎管理各环境的 API 地址配置，避免将测试或开发环境的敏感信息泄露到生产环境，造成安全风险。

---

## ⚠️ 开发注意事项

1.  **缓存问题**
    - 代码更新后，若开发者工具未能及时同步，可尝试清除 Taro 编译缓存（通常在 `node_modules/.cache/taro`）或禁用开发者工具的热重载功能后重试。
    - 微信开发者工具中，可以尝试点击"清除缓存" -> "编译缓存"。
2.  **Tailwind CSS 与 UI 组件库兼容性**
    - 在使用 `@antmjs/vantui` 或其他 UI 组件库时，部分组件可能无法直接应用 Tailwind CSS 的原子类。此时，需要检查样式优先级，或通过 `:global` 选择器、增加样式权重等方式确保样式正确应用。
3.  **真机调试差异**
    - 部分样式或 API 在开发者工具和真机上可能存在表现差异。务必以真机测试结果为准。
    - 遇到真机调试问题时，可以尝试微信开发者工具的"真机调试 2.0"或检查开发者工具版本。
4.  **环境切换**
    - 在 `dev`（开发）和 `build`（构建）命令间切换后，建议在开发者工具中执行"清除缓存"并重新编译项目，以确保加载最新的代码和配置。
5.  **JS 兼容性**
    - 为提升小程序在不同版本客户端上的兼容性，建议在生产构建时，将 JavaScript 代码编译转换为 ES5 标准。Taro 默认配置通常已包含此项。
6.  **原生组件使用**
    - 如需使用小程序原生组件（如 `Swiper`、`Map` 等），推荐优先使用 Taro 封装后的对应组件，以保证多端兼容性和 React 开发体验。若直接使用原生组件，需注意其在 Taro 中的引入和事件处理方式。
7.  **交互反馈**
    - 对于用户操作的反馈，如加载提示、成功或失败信息，推荐使用 `Taro.showToast`、`Taro.showLoading` 等 API，以保持小程序体验的一致性。
8.  **分包优化**
    - 当项目体积较大时，应合理规划并使用 Taro 的分包加载功能，优化小程序启动速度和加载性能。
9.  **代码规范与质量**
    - 遵循项目配置的 ESLint 和 Prettier 规范，保持代码风格一致性和可读性。
    - 定期进行代码审查，及时重构和优化。

# 心之安心理咨询应用

## 组件结构

### 咨询师相关组件

1. **TherapistListItem** - 咨询师列表项组件

   - 用途：在首页和咨询页面展示咨询师基本信息卡片
   - 属性：接收完整的 therapist 对象、背景色、分割线和可约状态等配置

2. **ExperienceTags** - 经验标签组件

   - 用途：显示咨询师的经验、资质等标签信息
   - 属性：支持标签数据组、最大显示数量控制

3. **DirectionTags** - 专业方向标签组件

   - 用途：显示咨询师擅长的方向标签
   - 属性：支持字符串数组或对象数组，自动适配数据类型

4. **TherapistDirections** - 咨询师专业方向详情组件

   - 用途：在咨询师详情页展示擅长方向的详细描述
   - 属性：接收方向数据数组，每个包含标题和描述

5. **TherapistReviewItem** - 单条评论组件

   - 用途：展示单条用户评价
   - 属性：头像、用户名、日期、服务类型和评价内容

6. **TherapistReviews** - 评论列表组件
   - 用途：展示评分、评价数量和评价列表
   - 属性：完整的 reviews 对象，支持最大显示数量限制

## 项目架构

本项目采用三层架构：

1. **Service 层** - 负责 API 调用和数据获取
2. **Store 层** - 负责状态管理，提供 setter 方法
3. **Action 层** - 处理业务逻辑，协调 Service 和 Store

## 使用技术

- Taro3 - 多端统一开发框架
- React - 前端 UI 库
- Zustand - 状态管理
- VantUI - UI 组件库

## 开发指南

### 组件开发规范

1. 每个组件应当只关注一个功能点
2. 善用组合而非继承
3. 首选函数式组件和 React Hooks
4. 提取可复用逻辑到自定义 Hooks

# Taro 微信云开发集成方案

本项目是一个基于 Taro 框架的微信小程序，集成了微信云开发功能，实现前端与云端一体化开发。

## 项目架构

```
├── client                             // 小程序客户端
│   ├── config                         // Taro配置目录
│   ├── dist                           // 编译结果目录
│   ├── src                            // 源码目录
│   │   ├── api                        // 云函数API封装
│   │   │   ├── user.js                // 用户相关API
│   │   │   ├── common.js              // 通用API
│   │   │   └── cloud.js               // 云函数统一调用封装
│   │   ├── app.config.ts              // 应用配置
│   │   ├── app.scss                   // 应用样式
│   │   ├── app.tsx                    // 应用入口文件（云初始化）
│   │   ├── components                 // 组件文件目录
│   │   ├── constants                  // 常量定义
│   │   ├── hooks                      // 自定义hooks
│   │   ├── pages                      // 页面目录
│   │   ├── services                   // 服务层
│   │   │   └── cloud-service.ts       // 云服务封装
│   │   ├── store                      // 状态管理
│   │   ├── types                      // 类型定义
│   │   └── utils                      // 工具函数
├── cloud                              // 云函数目录
│   ├── functions                      // 云函数定义
│   │   ├── login                      // 登录云函数
│   │   │   ├── index.js               // 函数入口
│   │   │   └── package.json
│   │   ├── getUser                    // 获取用户信息
│   │   ├── updateUser                 // 更新用户信息
│   │   ├── addData                    // 添加数据
│   │   ├── getData                    // 获取数据
│   │   └── common                     // 通用函数库
│   └── database                       // 数据库设计文档
│       └── collections.md             // 集合设计说明
├── project.config.json                // 小程序项目配置
├── package.json
└── README.md
```

## 快速开始

### 1. 环境配置

确保已安装 Node.js 和 Taro CLI:

```bash
# 全局安装Taro CLI
npm install -g @tarojs/cli
# 或使用yarn
yarn global add @tarojs/cli
```

### 2. 开发步骤

1. 安装依赖

```bash
# 安装项目依赖
npm install
# 或使用yarn
yarn
```

2. 启动开发服务

```bash
# 微信小程序
npm run dev:weapp
# 或
yarn dev:weapp
```

3. 使用微信开发者工具打开项目根目录进行预览和调试

### 3. 云开发配置

1. 在微信开发者工具中开通云开发
2. 创建云环境并获取环境 ID
3. 在`src/app.tsx`中配置云环境 ID
4. 部署云函数

## 云开发使用说明

### 1. 初始化云环境

在小程序入口文件`src/app.tsx`中初始化云环境：

```typescript
import { Component } from 'react';
import Taro from '@tarojs/taro';
import './app.scss';

class App extends Component {
  componentDidMount() {
    if (process.env.TARO_ENV === 'weapp') {
      Taro.cloud.init({
        env: 'your-env-id', // 替换为你的云环境ID
        traceUser: true,
      });
    }
  }

  // 其他代码...
}

export default App;
```

### 2. 云函数封装与调用

在`src/services/cloud-service.ts`中封装云函数调用：

### 3. 在页面中使用云服务

在页面或组件中调用云服务：

```typescript
import { Component } from 'react';
import { View, Text, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import CloudService from '../../services/cloud-service';

export default class IndexPage extends Component {
  state = {
    userInfo: null,
    loading: false,
  };

  async componentDidMount() {
    await this.getUserInfo();
  }

  getUserInfo = async () => {
    try {
      this.setState({ loading: true });
      // 调用云函数获取用户信息
      const result = await CloudService.callFunction('getUser', {});
      this.setState({
        userInfo: result.data,
        loading: false,
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
      Taro.showToast({
        title: '获取用户信息失败',
        icon: 'none',
      });
      this.setState({ loading: false });
    }
  };

  render() {
    const { userInfo, loading } = this.state;

    return (
      <View className='index-page'>
        <Text>云开发示例</Text>
        <View>
          {loading ? (
            <Text>加载中...</Text>
          ) : userInfo ? (
            <View>
              <Text>用户名: {userInfo.nickname}</Text>
              {/* 其他用户信息 */}
            </View>
          ) : (
            <Text>暂无用户信息</Text>
          )}
        </View>
        <Button onClick={this.getUserInfo}>刷新用户信息</Button>
      </View>
    );
  }
}
```

### 4. 云函数开发

在`cloud/functions/`目录下创建云函数：

以`getUser`云函数为例，在`cloud/profile/getUser/`目录下创建`index.js`和`package.json`：

```javascript
// cloud/functions/getUser/index.js
const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  try {
    // 从数据库获取用户信息
    const userResult = await db
      .collection('users')
      .where({
        _openid: openid,
      })
      .get();

    if (userResult.data.length > 0) {
      return {
        code: 0,
        data: userResult.data[0],
        message: '获取成功',
      };
    } else {
      return {
        code: 1,
        data: null,
        message: '用户不存在',
      };
    }
  } catch (error) {
    return {
      code: -1,
      data: null,
      message: error.message || '服务器错误',
    };
  }
};
```

package.json:

```json
{
  "name": "getUser",
  "version": "1.0.0",
  "description": "获取用户信息",
  "main": "index.js",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

### 5. 数据库操作

在云函数中操作数据库示例：

```javascript
// 插入数据
async function addUser(userInfo) {
  try {
    return await db.collection('users').add({
      data: {
        ...userInfo,
        createTime: db.serverDate(),
      },
    });
  } catch (error) {
    console.error('添加用户失败:', error);
    throw error;
  }
}

// 更新数据
async function updateUser(userId, userInfo) {
  try {
    return await db
      .collection('users')
      .doc(userId)
      .update({
        data: {
          ...userInfo,
          updateTime: db.serverDate(),
        },
      });
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
}

// 查询数据
async function getUserById(userId) {
  try {
    return await db.collection('users').doc(userId).get();
  } catch (error) {
    console.error('查询用户失败:', error);
    throw error;
  }
}

// 条件查询
async function getUsersByCondition(condition, page = 1, size = 10) {
  try {
    return await db
      .collection('users')
      .where(condition)
      .skip((page - 1) * size)
      .limit(size)
      .get();
  } catch (error) {
    console.error('条件查询失败:', error);
    throw error;
  }
}

// 删除数据
async function deleteUser(userId) {
  try {
    return await db.collection('users').doc(userId).remove();
  } catch (error) {
    console.error('删除用户失败:', error);
    throw error;
  }
}
```

## 数据库集合设计

推荐的基本集合设计：

1. users - 用户信息集合
2. posts - 帖子/文章集合
3. comments - 评论集合
4. files - 文件信息集合
5. settings - 系统设置集合

## 注意事项

1. 小程序端调用云函数会自动包含用户的 openid，不需要额外传递
2. 云函数的运行环境是 Node.js，可以使用 npm 安装第三方依赖
3. 云函数本地调试需要在微信开发者工具中进行
4. 云函数每次修改后需要重新部署
5. 云数据库建议在云函数中操作，避免权限问题
6. 较大文件建议使用云存储功能进行管理

## 参考资源

- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [Taro 文档](https://docs.taro.zone/)
- [Taro 小程序云开发模板](https://docs.taro.zone/docs/wxcloudbase)
