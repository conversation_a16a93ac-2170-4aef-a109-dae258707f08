import { Icon } from '@antmjs/vantui';
import { incomeActions } from '@core/actions/income.action';
import { useIncomeStore } from '@stores/income.store';
import { useEffect } from 'react';
import StatisticCard from './StatisticCard';

export default function IncomeStatisticCard({
  className,
}: {
  className?: string;
}) {
  const wallet = useIncomeStore.use.wallet();
  const statSummary = useIncomeStore.use.statSummary();

  useEffect(() => {
    console.log('IncomeStatisticCard statSummary', statSummary);
    const loadMyInfo = async () => {
      console.log('loadMyInfo');
      await Promise.all([
        incomeActions.loadMyWallet(),
        incomeActions.fetchStatSummary(),
      ]);
    };
    loadMyInfo();
  }, []);
  return (
    <StatisticCard
      className={className}
      onClick={() => {
        Taro.navigateTo({
          url: '/pages/sub-packages/wallet/dashboard/therapist',
        });
      }}
      title={statSummary?.income.total || 0}
      subTitle='累计收入(￥)'
      renderRight={<Icon name='arrow' size='16px' />}
      values={[
        {
          title: '咨询收入',
          value: statSummary?.income.completed || 0,
        },
        {
          title: '邀请收入',
          value: statSummary?.income.distribution || 0,
          color: 'success',
          onClick: () => {
            Taro.navigateTo({
              url: '/pages/sub-packages/wallet/distribution/therapist',
            });
          },
        },
        {
          title: '余额',
          value: wallet?.balance || 0,
        },
      ]}
    />
  );
}
