// 云函数：updateMonthlyStats
const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;
const moment = require("moment");
const {
  COLLECTIONS,
  ORDER_STATUS,
  REFUND_STATUS,
} = require("../common/db.constants");

exports.main = async (event, context) => {
  console.log("更新治疗师月度统计:", event);
  // 1. 获取所有治疗师ID
  const therapists = await db
    .collection(COLLECTIONS.THERAPIST)
    .field({ id: true })
    .get();

  // 2. 计算统计周期 (上月)
  const today = moment().startOf("day");
  const startDate = today.clone().subtract(1, "month").startOf("month");
  const endDate = startDate.clone().endOf("month");

  // 3. 并行处理每个治疗师
  await Promise.all(
    therapists.data.map((t) =>
      calculateTherapistStats(t.id, startDate, endDate)
    )
  );

  return { success: true, count: therapists.data.length };
};

async function calculateTherapistStats(therapistId, startDate, endDate) {
  const statId = `${therapistId}_${startDate.year()}_${startDate.month() + 1}`;

  // 聚合查询订单数据
  const res = await db
    .collection(COLLECTIONS.ORDER)
    .aggregate()
    .match({
      therapistId,
      createTime: _.gte(startDate.toDate()).and(_.lte(endDate.toDate())),
    })
    .group({
      _id: null,
      totalIncome: $.sum("$amount"),
      completedIncome: $.sum({
        $cond: [
          { $in: ["$status", [ORDER_STATUS.COMPLETED, ORDER_STATUS.REVIEWED]] },
          "$amount",
          0,
        ],
      }),
      refundedAmount: $.sum({
        $cond: [
          {
            $in: [
              "$refundStatus",
              [REFUND_STATUS.PROCESSING, REFUND_STATUS.COMPLETED],
            ],
          },
          "$amount",
          0,
        ],
      }),
      totalOrders: $.sum(1),
      completedOrders: $.sum({
        $cond: [
          {
            $in: ["$status", [ORDER_STATUS.COMPLETED, ORDER_STATUS.REVIEWED]],
          },
          1,
          0,
        ],
      }),
      refundedOrders: $.sum({
        $cond: [
          {
            $in: [
              "$refundStatus",
              [REFUND_STATUS.PROCESSING, REFUND_STATUS.COMPLETED],
            ],
          },
          1,
          0,
        ],
      }),
    })
    .end();

  const stats = res.data[0] || {
    totalIncome: 0,
    completedIncome: 0,
    refundedAmount: 0,
    totalOrders: 0,
    completedOrders: 0,
    refundedOrders: 0,
  };

  // 构建统计数据
  const monthlyStat = {
    _id: statId,
    therapistId,
    year: startDate.year(),
    month: startDate.month() + 1,
    income: {
      total: stats.totalIncome,
      completed: stats.completedIncome,
      refunded: stats.refundedAmount,
      pending: stats.totalIncome - stats.completedIncome - stats.refundedAmount,
    },
    orders: {
      total: stats.totalOrders,
      completed: stats.completedOrders,
      refunded: stats.refundedOrders,
      cancelled:
        stats.totalOrders - stats.completedOrders - stats.refundedOrders,
    },
    updateTime: new Date(),
    startDate: startDate.toDate(),
    endDate: endDate.toDate(),
  };

  // 更新统计集合
  return db.collection(COLLECTIONS.THERAPIST_MONTHLY_STATS).doc(statId).set(
    {
      data: monthlyStat,
    },
    { merge: true }
  ); // 存在则更新，不存在则创建
}
