import { Button } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';

interface ReportCardProps {
  count: number;
  onViewReports: () => void;
}

export default function ReportCard({ count, onViewReports }: ReportCardProps) {
  return (
    <View className='bg-primary-light rounded-xl p-4 flex justify-between items-center'>
      <View>
        <Text className='text-lg font-bold text-primary'>我的测评报告</Text>
        <Text className='text-sm text-gray-600 block mt-1'>
          您有 {count} 份报告待查看
        </Text>
      </View>
      <Button size='small' round type='primary' onClick={onViewReports}>
        查看报告
      </Button>
    </View>
  );
}
