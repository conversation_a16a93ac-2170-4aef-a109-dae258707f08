import PageLol from '@components/common/page-meta';
import { AVATAR_DEFAULT } from '@constants/assets';
import { useChat } from '@hooks/useChat';
import useRenderCount from '@hooks/useRenderCount';
import { useGlobalStore } from '@stores/global.store';
import { View } from '@tarojs/components';
import Taro, { useReachBottom, useRouter } from '@tarojs/taro';
import { useEffect, useRef } from 'react';
import { ChatInput } from './components/ChatInput';
import ChatList from './components/ChatList';

/**
 * 用户端聊天页面
 * 用于用户与咨询师之间的聊天
 */
export default function ChatPage() {
  const router = useRouter();
  const { peerId } = router.params;

  // 使用聊天Hook
  const {
    session,
    messages,
    error,
    loading,
    handleSendText,
    handleSendImage,
    handleSendAudio,
    handleSendOrder,
    handleLoadMore,
    handleResendMessage,
    handleMarkMessageAsRead,
    refreshMessages,
  } = useChat(peerId || '');

  // 获取当前用户信息
  const myUserId = useGlobalStore.use.openid();

  // 跟踪渲染次数并输出到控制台
  const renderCount = useRenderCount('UserChatPage');
  console.log(
    `UserChatPage 渲染 #${renderCount} - peerId: ${peerId}, myUserId: ${myUserId}`
  );

  const peerName =
    session?.AId === myUserId ? session?.BName : session?.AName || '';
  const peerAvatar =
    session?.AId === myUserId
      ? session?.BAvatar || AVATAR_DEFAULT
      : session?.AAvatar || AVATAR_DEFAULT;
  // 使用useCallback优化处理函数，避免不必要的重新创建
  // const onSendText = useCallback(
  //   (text: string) => {
  //     handleSendText(text);
  //   },
  //   [handleSendText]
  // );

  // const onSendImage = useCallback(() => {
  //   handleSendImage();
  // }, [handleSendImage]);

  // const onSendAudio = useCallback(() => {
  //   handleSendAudio();
  // }, [handleSendAudio]);

  // const onSendOrder = useCallback(() => {
  //   handleSendOrder();
  // }, [handleSendOrder]);

  // const onLoadMore = useCallback(() => {
  //   handleLoadMore();
  // }, [handleLoadMore]);

  // const onResendMessage = useCallback(
  //   (messageId: string) => {
  //     handleResendMessage(messageId);
  //   },
  //   [handleResendMessage]
  // );

  // const onMarkAsRead = useCallback(
  //   (messageId: string) => {
  //     handleMarkMessageAsRead(messageId);
  //   },
  //   [handleMarkMessageAsRead]
  // );

  const loadingRef = useRef(false);
  const prevMessagesLengthRef = useRef(0);
  // 添加一个标志位，用于标记是程序自动滚动还是用户滚动
  const isProgrammaticScrollRef = useRef(false);

  useRenderCount('ChatList');
  console.log('ChatList messages', messages, loading);
  // 监听触顶事件，加载更多消息
  useReachBottom(() => {
    console.log('ChatList useReachBottom');
    // 如果是程序自动滚动，则不触发加载更多
    if (isProgrammaticScrollRef.current) {
      isProgrammaticScrollRef.current = false;
      return;
    }

    if (messages.length > 0 && !loading && !loadingRef.current) {
      loadingRef.current = true;
      refreshMessages();
      setTimeout(() => {
        loadingRef.current = false;
      }, 1000); // 防抖，避免频繁触发
    }
  });

  // 滚动到底部
  const scrollToBottom = () => {
    console.log('ChatList scrollToBottom');
    // 设置标志位，标记为程序自动滚动
    isProgrammaticScrollRef.current = true;

    Taro.pageScrollTo({
      scrollTop: 9999,
      duration: 300,
      success: () => {
        console.log('ChatList scrollToBottom success');
        // 滚动完成后一段时间再重置标志位
        // 确保useReachBottom的检测发生在标志位被重置前
        setTimeout(() => {
          isProgrammaticScrollRef.current = false;
        }, 200); // 给足够时间让滚动完成并触发检测
      },
      fail: () => {
        console.log('ChatList scrollToBottom fail');
      },
    });
  };

  // 监听消息变化，滚动到底部
  useEffect(() => {
    // 只有在消息增加且不是加载更多的情况下才滚动到底部
    if (
      messages.length > prevMessagesLengthRef.current &&
      !loadingRef.current
    ) {
      scrollToBottom();
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages.length]);

  console.log('UserChatPage messages', messages, loading);

  return (
    <PageLol
      useNav
      navigationProps={{
        title: peerName,
        showBackButton: true,
      }}
      onPullDownRefresh={handleLoadMore}
      error={error || null}
    >
      <View className='flex flex-col relative'>
        {/* 消息列表 */}
        <ChatList
          messages={messages}
          loading={loading}
          receiverAvatar={peerAvatar}
          onResendMessage={handleResendMessage}
          onMarkAsRead={handleMarkMessageAsRead}
        />

        {/* 输入框 */}
        <ChatInput
          onSendText={handleSendText}
          onSendImage={handleSendImage}
          onSendAudio={handleSendAudio}
          onSendOrder={handleSendOrder}
        />
      </View>
    </PageLol>
  );
}
