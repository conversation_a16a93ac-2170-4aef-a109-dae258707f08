/**
 * 模拟数据生成云函数
 *
 * 此云函数用于调用模拟数据生成脚本，插入咨询师相关数据到数据库
 */

const cloud = require("wx-server-sdk");
const { insertMockData, deleteMockData } = require("./mockData");
const { createIndexes } = require("./createIndexes");

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

// 提供创建索引、插入模拟数据、删除模拟数据等功能

exports.main = async (event, context) => {
  try {
    console.log("数据库管理云函数入口...");

    const { action, data } = event;

    switch (action) {
      case "createIndexes":
        await createIndexes();
        break;
      case "insertMockData":
        await insertMockData();
        break;
      case "deleteMockData":
        await deleteMockData();
        break;
    }

    return {
      success: true,
      message: "success",
    };
  } catch (error) {
    console.error("操作失败:", error);
    return {
      success: false,
      message: "error",
      error: error.message,
    };
  }
};
