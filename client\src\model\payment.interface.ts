/**
 * 微信支付文档
 * https://pay.weixin.qq.com/doc/v2/merchant/4011939566
 */
/**
 * 微信支付订单
 */

import { INITATOR_SOURCE } from './common.interface';
import { REFUND_STATUS } from './order.interface';

export interface WechatPayPayment {
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
  notifyUrl?: string;
  attach?: string;
  timeExpire?: string;
}

/**
 * 后端创建支付订单参数
 */
export interface PaymentParams {
  orderId: string;
  amount: number;
  description?: string;
  paymentMethod?: string;
}

/**
 * 后端创建支付订单返回结果
 */
export interface PaymentResult {
  success: boolean;
  orderId: string;
  paymentId?: string;
  paymentData?: WechatPayPayment;
  message?: string;
}

/**
 * 支付信息表结构
 */
export interface Payment {
  orderId: string;
  userId: string;
  amount: number;
  description: string;
  status: 'pending' | 'paid' | 'refunded' | 'failed';
  paymentMethod: string;
  outTradeNo: string;
  transactionId?: string;
  createdAt: number;
  updatedAt: number;
  _openid: string;
}

/**
 * 提现参数
 */
export interface WithdrawParams {
  type: 'wechat' | 'alipay' | 'bank';
  amount: number;
  bankAccount?: string;
  bankName?: string;
  name?: string;
}

/**
 * 提现结果
 */
export interface WithdrawResult {
  success: boolean;
  withdrawId?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message?: string;
}

/**
 * 提现记录
 */
export interface WithdrawRecord {
  id: string; // 提现记录ID
  userId: string; // 用户ID
  amount: number; // 提现金额
  fee: number; // 手续费
  actualAmount: number; // 实际到账金额
  paymentMethod: 'bank'; // 目前只支持银行转账
  bankAccount?: string; // 银行账号
  bankName?: string; // 银行名称
  name?: string; // 收款人姓名
  createdAt: number; // 创建时间
  updatedAt: number; // 更新时间
  completedAt?: number; // 完成时间
  remark?: string; // 备注
  status: 'pending' | 'processing' | 'completed' | 'failed'; // 状态
}

/**
 * 提现配置
 */
export interface WithdrawConfig {
  minAmount: number;
  maxAmount: number;
  fee: number;
  feeRate: number;
  withdrawDays: string[];
  withdrawTime: string;
  instructions: string;
}

/** 退款信息 */

export interface RefundRecord {
  /** 订单ID */
  orderId: string;
  /** 来源 */
  source: INITATOR_SOURCE;
  /** 退款状态 */
  status: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS];
  /** 退款失败描述 */
  errDesc?: string;
  /** 退款单号 */
  outRefundNo: string;
  /** 交易单号 */
  outTradeNo: string;
  /** 退款金额 */
  refundFee: number;
  /** 总金额 */
  totalFee: number;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}
