/** @type {import('tailwindcss').Config} */
module.exports = {
  important: true,
  // 不在 content 包括的文件内，你编写的 class，是不会生成对应的css工具类的
  content: ['./public/index.html', './src/**/*.{html,js,ts,jsx,tsx}'],
  // 其他配置项
  // ...
  corePlugins: {
    // 小程序不需要 preflight，因为这主要是给 h5 的，如果你要同时开发小程序和 h5 端，你应该使用环境变量来控制它
    preflight: false,
  },
  theme: {
    fontSize: {
      // //       @font-size-xs: 20px;
      // // @font-size-sm: 24px;
      // // @font-size-md: 28px;
      // // @font-size-lg: 32px;
      // // @font-weight-bold: 500;
      // // @line-height-xs: 28px;
      // // @line-height-sm: 36px;
      // // @line-height-md: 40px;
      // // @line-height-lg: 44px;
      xs: ['20px', '28px'],
      sm: ['24px', '36px'],
      md: ['28px', '40px'],
      base: ['32px', '44px'],
      lg: ['36px', '48px'],
      xl: ['40px', '52px'],
      '2xl': ['48px', '64px'],
    },
    extend: {
      colors: {
        primary: 'var(--color-primary)',
        primarylight: 'var(--color-primarylight)',
        secondary: 'var(--color-secondary)',
        secondarylight: 'var(--color-secondarylight)',
        tertiary: 'var(--color-tertiary)',
        tertiarylight: 'var(--color-tertiarylight)',
        success: 'var(--color-success)',
        danger: 'var(--color-danger)',
        warning: 'var(--color-warning)',
        border: 'var(--color-border)',
        placeholder: 'var(--color-text-placeholder)',
        bg: 'var(--color-primary-bg)',
      },
      textColor: {
        default: 'var(--color-text-default)',
        primary: 'var(--color-text-primary)',
        secondary: 'var(--color-text-secondary)',
        disabled: 'var(--color-text-disabled)',
      },
    },
  },
};
