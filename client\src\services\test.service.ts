import { SUCCESS_CODE } from '@core/api';
import { COLLECTIONS } from '@model/db.model';
import { PsychologicalTest, UserTestRecord } from '@model/test.model';
import { useGlobalStore } from '@stores/global.store';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

class TestServiceImpl extends BaseService<PsychologicalTest> {
  constructor() {
    super(COLLECTIONS.MEASURES_TEST);
  }

  /**
   * 获取测试详情
   * @param testId 测试ID
   */
  async getTest(testId: string): Promise<PsychologicalTest> {
    try {
      const result = await this.directRead({ id: testId });
      if (!result || result.length === 0) {
        throw new Error(`测试不存在: ${testId}`);
      }
      return result[0];
    } catch (error) {
      console.error('获取测试详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取测试记录
   * @param recordId 记录ID
   */
  async getTestRecord(recordId: string): Promise<UserTestRecord | null> {
    try {
      const openid = useGlobalStore.getState().openid;

      // 直接从数据库读取用户自己的测试记录
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.MEASURES_TEST_RECORD)
        .where({
          id: recordId,
          userId: openid,
        })
        .get();

      return (result.data[0] as UserTestRecord) || null;
    } catch (error) {
      console.error('获取测试记录失败:', error);
      return null;
    }
  }

  /**
   * 提交测试
   * @param record 测试记录
   */
  async submitTest(record: UserTestRecord): Promise<string> {
    try {
      // 调用云函数提交测试
      const result = await callCloudFunction('measure', 'submitTest', {
        record,
      });
      console.log('submitTest result', result);
      if (result.success && result.code === SUCCESS_CODE && result.id) {
        return result.id;
      }
      throw new Error(result.message || '提交测试失败');
    } catch (error) {
      console.error('提交测试失败:', error);
      throw error;
    }
  }
}

export const testService = new TestServiceImpl();
