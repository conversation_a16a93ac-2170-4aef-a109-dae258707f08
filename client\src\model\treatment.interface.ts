/**
 * 疗愈音频类别
 */
export enum AudioCategory {
  ALL = 'all', // 全部
  EMOTION = 'emotion', // 情绪调节
  ENERGY = 'energy', // 活力唤醒
  BODY = 'body', // 疗愈身心
  LOVE = 'love', // 为爱而生
  RELAX = 'relax', // 休憩空间
}

/**
 * 疗愈音频类型
 */
export enum AudioType {
  MUSIC = 'music', // 音乐
  NATURE = 'nature', // 自然声音
  MEDITATION = 'meditation', // 引导冥想
  STORY = 'story', // 治愈故事
}

/**
 * 疗愈音频数据
 */
export interface TreatmentAudio {
  id: string;
  title: string;
  category: AudioCategory; // 分类
  description: string;
  author: string;
  thumbnail: string; // 缩略图
  coverImage: string; // 封面图
  audioUrl: string; // 音频地址
  duration: number; // 音频时长（秒）
  plays: number; // 播放次数
  rating: number; // 评分
  type: AudioType; // 类型
  tags: string[]; // 标签
  isPremium: boolean; // 是否会员专属
  createdAt: number; // 创建时间
  updatedAt: number; // 更新时间
}

/**
 * 音乐分类信息
 */
export interface CategoryTab {
  title: string; // 分类名称
  // icon: string;
  // description: string;
  category: AudioCategory; // 分类
  count?: number; // 该分类下的音频数量
}

/**
 * 用户收听记录
 */
export interface ListeningRecord {
  id: string;
  userId: string;
  audioId: string;
  audioTitle: string;
  coverImage: string;
  progress: number; // 进度百分比 0-100
  duration: number; // 总时长（秒）
  lastPosition: number; // 上次播放位置（秒）
  lastListenTime: number; // 上次收听时间
}

/**
 * 用户收藏记录
 */
export interface FavoriteAudio {
  id: string;
  userId: string;
  audioId: string;
  createdAt: number;
}

/**
 * 播放状态
 */
export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progress: number; // 0-100
}
export const AUDIO_CATEGORY_MAP = {
  [AudioCategory.ALL]: '全部',
  [AudioCategory.EMOTION]: '情绪调节',
  [AudioCategory.ENERGY]: '活力唤醒',
  [AudioCategory.BODY]: '疗愈身心',
  [AudioCategory.LOVE]: '为爱而生',
  [AudioCategory.RELAX]: '休憩空间',
};
