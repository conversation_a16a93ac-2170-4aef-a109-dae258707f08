import {
  Button,
  Cell,
  CellGroup,
  Checkbox,
  CheckboxGroup,
  Field,
  Progress,
  Radio,
  RadioGroup,
} from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { testAction } from '@core/actions/test.action';
import { DEFAULT_LIKERT_OPTIONS, QuestionType } from '@model/test.model';
import { useLoadingStore } from '@stores/loading.store';
import { useTestStore } from '@stores/test.store';
import { Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

/**
 * 正式答题页（TestDoingPage）
 */
const DEFAULT_ANSWER = null;
export default function TestDoingPage() {
  const router = useRouter();
  const { testId } = router.params;

  const currentTest = useTestStore.use.currentTest();
  const currentQuestionIndex = useTestStore.use.currentQuestionIndex();
  const userAnswers = useTestStore.use.activeRecord()?.report.answers;
  const error = useTestStore.use.error();

  const [currentAnswer, setCurrentAnswer] = useState<
    number | string | number[] | null
  >(DEFAULT_ANSWER);
  const transicationLoading = useLoadingStore.use.transactionLoading();

  // 初始化测试
  useEffect(() => {
    if (!testId) {
      Taro.showToast({
        title: '测试ID不能为空',
        icon: 'error',
      });
      return;
    }
    // 开始测试
    testAction.startTest(testId);
    return () => {
      useTestStore.getState().clearCurrentTest();
      useTestStore.getState().setLoading(false);
      useTestStore.getState().setError(null);
    };
  }, [testId]);

  // 当前问题
  const currentQuestion = currentTest?.questions[currentQuestionIndex];

  // 总进度
  const progress = currentTest
    ? Math.floor(
        ((currentQuestionIndex + 1) / currentTest.questions.length) * 100
      )
    : 0;

  // 处理答案变更
  const handleAnswerChange = (value: number | string | number[]) => {
    setCurrentAnswer(value);
  };

  // 保存当前答案
  const saveCurrentAnswer = () => {
    if (!currentQuestion) return;
    let answer = currentAnswer;
    // 针对开放题，若答案为 undefined/null，提交空字符串
    if (
      currentQuestion.type === QuestionType.OPEN_ENDED &&
      (answer === undefined || answer === null)
    ) {
      answer = '';
    }
    testAction.answerQuestion(currentQuestion.id, answer!);
  };

  // 下一题
  const handleNext = () => {
    saveCurrentAnswer();
    if (!testAction.nextQuestion()) {
      // 已经是最后一题，显示提交按钮
      Taro.showToast({
        title: '已经是最后一题',
        icon: 'none',
      });
    }
  };

  // 上一题
  const handlePrev = () => {
    saveCurrentAnswer();
    if (!testAction.prevQuestion()) {
      Taro.showToast({
        title: '已经是第一题',
        icon: 'none',
      });
    }
  };

  // 提交测试
  const handleSubmit = async () => {
    saveCurrentAnswer();

    await testAction.submitTest();
  };

  // 取消测试
  const handleCancel = () => {
    Taro.showModal({
      title: '确认取消',
      content: '取消测试将丢失当前进度，确认取消吗？',
      success: (res) => {
        if (res.confirm) {
          testAction.clearCurrentTest();
          Taro.navigateBack();
        }
      },
    });
  };

  // 加载当前问题的已有答案
  useEffect(() => {
    if (currentQuestion) {
      const existingAnswer = userAnswers?.find(
        (a) => a.questionId === currentQuestion.id
      );
      if (existingAnswer) {
        setCurrentAnswer(existingAnswer.answer);
      } else {
        // 重置答案 - 根据题目类型设置默认值
        if (currentQuestion.type === QuestionType.MULTIPLE_CHOICE) {
          setCurrentAnswer([] as number[]);
        } else if (currentQuestion.type === QuestionType.OPEN_ENDED) {
          setCurrentAnswer(DEFAULT_ANSWER);
        } else {
          setCurrentAnswer(DEFAULT_ANSWER);
        }
      }
    }
  }, [currentQuestion, userAnswers]);

  // 渲染问题内容
  const renderQuestion = () => {
    if (!currentQuestion) return null;
    // 题型标识
    let typeLabel = '';
    if (currentQuestion.type === QuestionType.MULTIPLE_CHOICE) {
      typeLabel = '（可多选）';
    } else if (
      currentQuestion.type === QuestionType.LIKERT ||
      currentQuestion.type === QuestionType.BINARY
    ) {
      typeLabel = '（单选）';
    }
    return (
      <View className='p-4'>
        <Text className='text-lg font-medium block mb-6'>
          {currentQuestionIndex + 1}. {currentQuestion.text}
          {typeLabel && <Text className='text-gray-400 ml-2'>{typeLabel}</Text>}
          {currentQuestion.required && (
            <Text className='text-red-500 ml-1'>*</Text>
          )}
        </Text>
        {renderAnswerOptions()}
      </View>
    );
  };

  // 渲染答题选项
  const renderAnswerOptions = () => {
    if (!currentQuestion) return null;
    switch (currentQuestion.type) {
      case QuestionType.LIKERT:
        return renderLikertScale(
          currentQuestion.options ?? DEFAULT_LIKERT_OPTIONS
        );
      case QuestionType.MULTIPLE_CHOICE:
        return renderMultipleChoice(currentQuestion.options ?? []);
      case QuestionType.BINARY:
        return renderBinaryChoice(currentQuestion.options ?? ['否', '是']);
      case QuestionType.OPEN_ENDED:
        return renderOpenEnded();
      default:
        return <Text className='text-red-500'>不支持的问题类型</Text>;
    }
  };

  // 渲染李克特量表
  const renderLikertScale = (options: string[]) => {
    return (
      <View className='flex flex-col space-y-4'>
        <RadioGroup
          value={currentAnswer}
          onChange={(e) => handleAnswerChange(e.detail)}
        >
          {options.map((value, index) => (
            <Radio key={index} name={index} className='mb-6' iconSize={40}>
              {value}
            </Radio>
          ))}
        </RadioGroup>
      </View>
    );
  };

  // 处理多选题答案变更
  const onCheckboxChange = (index: number) => {
    console.log('onCheckboxChange', index, currentAnswer);
    const currentValues = Array.isArray(currentAnswer)
      ? (currentAnswer as number[])
      : [];
    const values = [...currentValues];

    if (values.includes(index)) {
      values.splice(values.indexOf(index), 1);
    } else {
      values.push(index);
    }
    setCurrentAnswer(values);
    console.log('onCheckboxChange after', values);
  };

  // 渲染多选题
  const renderMultipleChoice = (options: string[]) => {
    console.log('renderMultipleChoice', currentAnswer);
    return (
      <View className='flex flex-col space-y-4'>
        <CheckboxGroup
          value={currentAnswer ? (currentAnswer as number[]).map(String) : []}
        >
          <CellGroup inset>
            {options.map((item, index) => {
              return (
                <Cell
                  key={index}
                  title={item}
                  clickable
                  onClick={() => onCheckboxChange(index)}
                >
                  <Checkbox
                    style={{ justifyContent: 'flex-end' }}
                    name={`${index}`}
                  />
                </Cell>
              );
            })}
          </CellGroup>
        </CheckboxGroup>
      </View>
    );
  };

  // 渲染是非题
  const renderBinaryChoice = (options: string[]) => {
    return (
      <View className='flex justify-center space-x-8'>
        <View
          className={`py-3 px-8 rounded-full ${
            currentAnswer === 1 ? 'bg-blue-500 text-white' : 'bg-gray-100'
          }`}
          onClick={() => handleAnswerChange(1)}
        >
          <Text>{options[1] || '是'}</Text>
        </View>
        <View
          className={`py-3 px-8 rounded-full ${
            currentAnswer === 0 ? 'bg-blue-500 text-white' : 'bg-gray-100'
          }`}
          onClick={() => handleAnswerChange(0)}
        >
          <Text>{options[0] || '否'}</Text>
        </View>
      </View>
    );
  };

  // 渲染开放题
  const renderOpenEnded = () => {
    return (
      <View className='mt-4'>
        <Field
          className='w-full p-3 border border-gray-300 rounded-lg h-32'
          value={currentAnswer?.toString() || ''}
          onInput={(e) => handleAnswerChange(e.detail)}
          placeholder='请输入您的回答...'
        />
      </View>
    );
  };

  const canGoNext = () => {
    return (
      currentQuestion?.required &&
      (currentAnswer === null ||
        currentAnswer === undefined ||
        currentAnswer === '' ||
        (Array.isArray(currentAnswer) && currentAnswer.length === 0))
    );
  };

  // 渲染底部导航按钮
  const renderNavButtons = () => {
    const isLastQuestion =
      currentTest && currentQuestionIndex === currentTest.questions.length - 1;
    return (
      <View className='flex justify-between p-4 border-t border-gray-200'>
        <Button
          type='primary'
          round
          plain
          hairline
          onClick={handlePrev}
          disabled={currentQuestionIndex === 0}
        >
          上一题
        </Button>
        {isLastQuestion ? (
          <Button
            type='primary'
            round
            onClick={handleSubmit}
            loading={transicationLoading}
            disabled={transicationLoading || canGoNext()}
          >
            提交测试
          </Button>
        ) : (
          // 比选题必须选择一个答案
          <Button
            type='primary'
            round
            onClick={handleNext}
            disabled={canGoNext()}
          >
            下一题
          </Button>
        )}
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{
        title: currentTest?.title || '心理测试',
        showBackButton: true,
        onBack: handleCancel,
      }}
      error={error?.message || null}
    >
      <View className='flex flex-col h-full'>
        {/* 进度条 */}
        <View className='p-8 '>
          <Progress
            percentage={progress}
            strokeWidth={4}
            trackColor='var(--color-primarylight)'
            showPivot={false}
          />
          <View className='flex justify-between mt-1'>
            <Text className='text-xs text-secondary'>
              题目 {currentQuestionIndex + 1}/
              {currentTest?.questions.length || 0}
            </Text>
            <Text className='text-xs text-secondary'>{progress}%</Text>
          </View>
        </View>
        {/* 问题内容 */}
        <View className='flex-1 overflow-y-auto'>{renderQuestion()}</View>
        {/* 导航按钮 */}
        {renderNavButtons()}
      </View>
    </PageLol>
  );
}
