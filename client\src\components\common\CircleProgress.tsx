import { Canvas, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';

interface CircleProgressProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  trackColor?: string;
  progressColor?: string;
  textColor?: string;
  animate?: boolean;
  duration?: number;
  showPercentage?: boolean;
  canvasId?: string;
  children?: React.ReactNode;
  type?: '2d' | 'normal';
  clockwise?: boolean; // 新增，是否顺时针
}

const CircleProgress: React.FC<CircleProgressProps> = ({
  progress = 75,
  size = 200,
  strokeWidth = 12,
  trackColor = '#f3f4f6',
  progressColor = '#3b82f6',
  textColor = '#4b5563',
  animate = true,
  duration = 800,
  showPercentage = true,
  canvasId = 'circle-progress-canvas',
  children,
  type = '2d',
  clockwise = true, // 默认顺时针
}) => {
  // 生成唯一canvasId，防止冲突
  const uniqueCanvasId = useRef(
    `${canvasId}-${Math.random().toString(36).substr(2, 9)}`
  ).current;
  const [displayProgress, setDisplayProgress] = useState(progress);
  const ctxRef = useRef<any>(null);
  const animationIdRef = useRef<number | null>(null);
  const canvasNodeRef = useRef<any>(null);

  // 初始化context
  useEffect(() => {
    if (type === '2d') {
      const query = Taro.createSelectorQuery();
      query
        .select(`#${uniqueCanvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            canvasNodeRef.current = res[0].node;
            ctxRef.current = canvasNodeRef.current.getContext('2d');
            // 适配高清屏
            const dpr = Taro.getSystemInfoSync().pixelRatio || 1;
            canvasNodeRef.current.width = size * dpr;
            canvasNodeRef.current.height = size * dpr;
            ctxRef.current.scale(dpr, dpr);
            drawProgress(displayProgress);
          }
        });
    } else {
      // normal模式
      ctxRef.current = Taro.createCanvasContext(uniqueCanvasId);
      drawProgress(displayProgress);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uniqueCanvasId, size, type]);

  // 动画进度处理
  useEffect(() => {
    let stopped = false;
    if (!animate) {
      setDisplayProgress(progress);
      drawProgress(progress);
      return;
    }

    if (animationIdRef.current !== null) {
      cancelAnimationFrame(animationIdRef.current);
      animationIdRef.current = null;
    }

    const startValue = displayProgress;
    const endValue = progress;
    const startTime = Date.now();

    const animateProgress = () => {
      if (stopped) return;
      const elapsed = Date.now() - startTime;
      const progressRatio = Math.min(elapsed / duration, 1);
      const easedProgress = easeOutQuad(progressRatio);
      const currentValue = startValue + (endValue - startValue) * easedProgress;

      setDisplayProgress(currentValue);
      drawProgress(currentValue);

      if (progressRatio < 1) {
        animationIdRef.current = requestAnimationFrame(animateProgress);
      } else {
        animationIdRef.current = null;
      }
    };

    animationIdRef.current = requestAnimationFrame(animateProgress);

    return () => {
      stopped = true;
      if (animationIdRef.current !== null) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [progress, animate, duration]);

  // 缓动函数
  const easeOutQuad = (t: number): number => {
    return t * (2 - t);
  };

  // 绘制圆环
  const drawProgress = (currentProgress: number) => {
    if (!ctxRef.current) return;
    const ctx = ctxRef.current;
    if (type === '2d' && canvasNodeRef.current) {
      ctx.clearRect(0, 0, size, size);
    } else if (type === 'normal') {
      ctx.clearRect(0, 0, size, size);
    }
    const center = size / 2;
    const radius = (size - strokeWidth) / 2;
    const startAngle = -Math.PI / 2;
    // 顺时针/逆时针
    const endAngle = clockwise
      ? startAngle + (currentProgress / 100) * Math.PI * 2
      : startAngle - (currentProgress / 100) * Math.PI * 2;

    // 绘制背景轨道
    ctx.beginPath();
    ctx.arc(center, center, radius, 0, Math.PI * 2);
    if (type === '2d') {
      ctx.strokeStyle = trackColor;
      ctx.lineWidth = strokeWidth;
      ctx.lineCap = 'round'; // 圆头
    } else {
      ctx.setStrokeStyle(trackColor);
      ctx.setLineWidth(strokeWidth);
      ctx.setLineCap('round'); // 圆头
    }
    ctx.stroke();

    // 绘制进度条
    ctx.beginPath();
    ctx.arc(center, center, radius, startAngle, endAngle, !clockwise);
    if (type === '2d') {
      ctx.strokeStyle = progressColor;
      ctx.lineWidth = strokeWidth;
      ctx.lineCap = 'round'; // 圆头
    } else {
      ctx.setStrokeStyle(progressColor);
      ctx.setLineWidth(strokeWidth);
      ctx.setLineCap('round'); // 圆头
    }
    ctx.stroke();

    // 绘制文本
    if (showPercentage && !children) {
      if (type === '2d') {
        ctx.fillStyle = textColor;
        ctx.font = `${size * 0.2}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${Math.round(currentProgress)}%`, center, center);
      } else {
        ctx.setFillStyle(textColor);
        ctx.setFontSize(size * 0.2);
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(`${Math.round(currentProgress)}%`, center, center);
      }
    }
    // normal模式需要draw()
    if (type === 'normal') {
      ctx.draw();
    }
  };

  const canvasStyle = {
    width: `${size}px`,
    height: `${size}px`,
  };
  const innerSize = size - strokeWidth * 2;
  return (
    <View className='relative' style={canvasStyle}>
      <Canvas
        id={uniqueCanvasId}
        type={type === '2d' ? '2d' : undefined}
        style={canvasStyle}
        className='absolute top-0 left-0'
        canvasId={type === 'normal' ? uniqueCanvasId : undefined}
      />
      {/* 中心内容（自定义） */}
      {children && (
        <View
          className='absolute flex items-center justify-center'
          style={{
            left: strokeWidth,
            top: strokeWidth,
            width: `${innerSize}px`,
            height: `${innerSize}px`,
          }}
        >
          {children}
        </View>
      )}
    </View>
  );
};

export default CircleProgress;
