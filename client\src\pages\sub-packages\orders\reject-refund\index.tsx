import PageLol from '@components/common/page-meta';
import ReasonForm, { ReasonOption } from '@components/common/reason-form';
import { orderAdminActions } from '@core/actions/order.admin';
import { REFUND_STATUS, REJECT_REASON } from '@model/order.interface';
import { useLoadingStore } from '@stores/loading.store';
import { useOrderStoreSelector } from '@stores/order.store';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

/**
 * 拒绝退款申请页面
 */
export default function RejectRefundPage() {
  const router = useRouter();
  const { orderId } = router.params;
  const { transactionLoading } = useLoadingStore();
  const [reason, setReason] = useState<REJECT_REASON | null>(null);
  const [detail, setDetail] = useState<string | null>(null);

  // 订阅订单的状态
  const order = useOrderStoreSelector(
    useShallow((state) => state.getOrderById(orderId ?? ''))
  );

  const [localError, setLocalError] = useState<string | null>(null);

  if (!orderId) {
    console.error('订单ID不存在', orderId);
    return;
  }

  if (order?.refundStatus && order.refundStatus !== REFUND_STATUS.AUDITING) {
    setLocalError('订单状态不正确，无法拒绝退款');
  }

  // 拒绝退款理由选项
  const refundRejectReasons: ReasonOption[] = [
    { value: REJECT_REASON.TIME_CONFLICT, label: '时间冲突' },
    { value: REJECT_REASON.FOUND_UNSUITABLE, label: '不符合退款条件' },
    { value: REJECT_REASON.PRICE_CONCERN, label: '价格原因' },
    { value: REJECT_REASON.FORCE_MAJEURE, label: '不可抗力' },
    { value: REJECT_REASON.OTHER, label: '其他原因' },
  ];

  // 提交拒绝退款
  const handleSubmit = async () => {
    if (!orderId || !reason) return;

    try {
      await orderAdminActions.rejectRefund(orderId);

      Taro.showToast({
        title: '已拒绝退款',
        icon: 'success',
      });

      // 返回到审核列表页
      setTimeout(() => {
        Taro.navigateBack();
      }, 1500);
    } catch (err: any) {
      console.error('拒绝退款失败:', err);
      Taro.showToast({
        title: '操作失败',
        icon: 'error',
      });
    }
  };

  return (
    <PageLol
      navigationProps={{ title: '拒绝退款', showBackButton: true }}
      error={localError || null}
    >
      <View className='reject-refund-page'>
        <ReasonForm
          title='请选择拒绝退款原因'
          reasonOptions={refundRejectReasons}
          selectedReason={reason}
          detail={detail}
          onReasonChange={(value) => setReason(value as REJECT_REASON)}
          onDetailChange={(value) => setDetail(value)}
          onSubmit={handleSubmit}
          loading={transactionLoading}
          buttonText='确认拒绝'
          placeholder='请详细描述拒绝退款原因'
        />
      </View>
    </PageLol>
  );
}
