import { Cell, CellGroup, Grid, GridItem, Icon } from '@antmjs/vantui';
import { ICONS_BOLD } from '@constants/assets';
import { distributionActions } from '@core/actions/distribution.action';
import { incomeActions } from '@core/actions/income.action';
import { userProfileActions } from '@core/actions/profile.user';
import useRenderCount from '@hooks/useRenderCount';
import { USER_ROLE } from '@model/user.interface';
import { useDistributionStore } from '@stores/distribution.store';
import { useIncomeStore } from '@stores/income.store';
import { useUserProfileStore } from '@stores/profile.user';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { orderRouter } from '@utils/router';
import { useEffect } from 'react';

export default function UserPage({ onSwitchRole }) {
  const profile = useUserProfileStore.use.profile();
  useRenderCount('UserPage');
  useEffect(() => {
    const loadMyInfo = async () => {
      await Promise.all([
        userProfileActions.fetchMyPublicProfile(),
        incomeActions.loadMyWallet(),
        distributionActions.fetchDistributionOverview(),
      ]);
    };
    loadMyInfo();
  }, []);

  /**
   * 个人资料卡片组件
   */
  const ProfileCard = () => {
    const wallet = useIncomeStore.use.wallet();
    const distribution = useDistributionStore.use.overview();

    return (
      profile && (
        <View className='mx-4 bg-white rounded-xl p-4 flex flex-col mb-4 shadow-sm'>
          {/* 头部行 */}
          <View className='flex flex-row items-center'>
            {/* 头像 */}
            <Image
              src={profile.avatar}
              className='w-10 h-10 rounded-full mr-4'
            />
            <View className='flex-1 flex flex-col py-2.5 justify-center'>
              <View className='flex flex-row items-center mb-1'>
                <Text className='text-lg font-semibold mr-2 text-default'>
                  {profile.userName}
                </Text>
                <View className='px-1 py-0.5 mr-2 bg-tertiary rounded-sm text-xs text-white'>
                  用户
                </View>
                <View
                  className='px-1 py-0.5 bg-primary rounded-sm text-xs text-white'
                  onClick={() => {
                    Taro.navigateTo({
                      url: '/pages/sub-packages/profile/edit-profile/index',
                    });
                  }}
                >
                  <Icon name='edit' size={12} />
                  编辑资料
                </View>
              </View>
              {/* <Text className='text-xs text-secondary'>id:{profile.id}</Text> */}
            </View>
            {/* 二维码 */}
            <View className='flex flex-col items-center ml-2'>
              <Icon
                name='qr'
                size={96}
                className='mb-1'
                onClick={() =>
                  Taro.previewImage({
                    urls: [distribution?.qrCodeUrl || ''],
                  })
                }
              />
            </View>
          </View>
          {/* 统计卡片 */}
          <View
            className='flex flex-row mt-2 justify-between items-center'
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/sub-packages/wallet/distribution/user',
              });
            }}
          >
            {/* 邀请 */}
            <View className='flex flex-col items-start'>
              <View className='flex flex-row items-center'>
                <Icon name={ICONS_BOLD.SEND24} size='12px' />
                <Text className='text-xs text-secondary ml-1'>邀请</Text>
              </View>
              <Text className='text-base font-bold text-default'>
                {distribution?.inviteCount || 0}
                <Text className='text-xs text-secondary'>人</Text>
              </Text>
            </View>
            {/* 分割线 */}
            <View className='w-px h-6 bg-border' />
            {/* 累计收入 */}
            <View className='flex flex-col items-start'>
              <View className='flex flex-row items-center'>
                <Icon name={ICONS_BOLD.DOLLAR24} size='12px' />
                <Text className='text-xs text-secondary ml-1'>收入</Text>
              </View>
              <Text className='text-base font-bold text-default'>
                {wallet?.totalIncome || 0}
                <Text className='text-xs text-secondary'>￥</Text>
              </Text>
            </View>
            {/* 分割线 */}
            <View className='w-px h-6 bg-border' />
            {/* 余额 */}
            <View className='flex flex-col items-start'>
              <View className='flex flex-row items-center'>
                <Icon name={ICONS_BOLD.WALLET24} size='12px' />
                <Text className='text-xs text-secondary ml-1'>余额</Text>
              </View>
              <View className='flex flex-row items-center'>
                <Text className='text-base font-bold text-default'>
                  {wallet?.balance || 0}
                  <Text className='text-xs text-secondary'>￥</Text>
                </Text>
                <View
                  className='ml-4 bg-success rounded-sm px-1 py-0.5 text-xs text-white'
                  onClick={(e) => {
                    e.stopPropagation();
                    Taro.navigateTo({
                      url: '/pages/sub-packages/wallet/withdraw/apply',
                    });
                  }}
                >
                  +提现
                </View>
              </View>
            </View>
          </View>
        </View>
      )
    );
  };

  /**
   * 订单卡片组件
   */
  const OrdersCard = () => {
    return (
      <View className='mt-4 mx-4 rounded-xl bg-bg'>
        <Cell
          title='我的订单'
          isLink
          border={false}
          titleStyle={{ fontSize: 48, fontWeight: 'medium' }}
          onClick={() => {
            orderRouter.list();
          }}
        />
        <Grid columnNum={4} iconSize={56} border={false}>
          <GridItem
            icon='clock-o'
            text='进行中'
            onClick={() => {
              orderRouter.list({ tab: 'pending' });
            }}
          />
          <GridItem
            icon='passed'
            text='已完成'
            onClick={() => {
              orderRouter.list({ tab: 'completed' });
            }}
          />
          <GridItem
            icon='comment-o'
            text='已取消'
            onClick={() => {
              orderRouter.list({ tab: 'cancelled' });
            }}
          />
          <GridItem
            icon='refund-o'
            text='售后退款'
            onClick={() => {
              orderRouter.list({ tab: 'refund' });
            }}
          />
        </Grid>
      </View>
    );
  };

  /**
   * 用户角色功能卡片组件
   */
  const UserFunctionsCard = () => {
    const navigateToFunction = (url: string) => {
      Taro.navigateTo({ url });
    };

    return (
      <View className='mt-4'>
        <CellGroup border={false} inset>
          <Cell
            title='收藏的咨询师'
            icon='star-o'
            isLink
            border={false}
            onClick={() =>
              navigateToFunction(
                '/pages/sub-packages/therapist/favorites/index'
              )
            }
          />
          <Cell
            title='分享中心'
            icon='friends-o'
            isLink
            border={false}
            onClick={() =>
              navigateToFunction('/pages/sub-packages/wallet/distribution/user')
            }
          />

          <Cell
            title='设置'
            icon='setting-o'
            isLink
            border={false}
            onClick={() =>
              navigateToFunction('/pages/sub-packages/profile/settings/index')
            }
          />
          <Cell
            title='帮助中心'
            icon='question-o'
            isLink
            border={false}
            onClick={() =>
              Taro.navigateTo({
                url: '/pages/sub-packages/profile/help/index',
              })
            }
          />
          <Cell
            title='我要投诉'
            icon='warning-o'
            isLink
            border={false}
            onClick={() =>
              Taro.navigateTo({
                url: '/pages/sub-packages/profile/feedback/index',
              })
            }
          />
          {profile?.role?.includes(USER_ROLE.THERAPIST) && (
            <Cell
              title='切换到咨询师'
              icon='user-o'
              isLink
              border={false}
              onClick={() => onSwitchRole(USER_ROLE.THERAPIST)}
            />
          )}
          {!profile?.role?.includes(USER_ROLE.THERAPIST) && (
            <Cell
              title='注册为咨询师'
              icon='user-o'
              isLink
              border={false}
              onClick={() =>
                navigateToFunction(
                  '/pages/sub-packages/therapist/register/index'
                )
              }
            />
          )}
        </CellGroup>
      </View>
    );
  };

  return (
    <View className='pt-4 pb-2'>
      {/* 个人信息卡片 */}
      {ProfileCard()}
      {OrdersCard()}
      {UserFunctionsCard()}
    </View>
  );
}
