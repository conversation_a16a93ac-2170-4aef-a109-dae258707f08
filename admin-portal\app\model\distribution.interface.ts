/**
 * 分销数据类型定义
 */

// 分销用户信息
export interface DistributionOverview {
  referrerId: string;
  qrCodeUrl: string;
  totalIncome: number;
  inviteCount: number;
  totalOrderCount: number;
  createdAt: number; // 时间戳 秒
  updatedAt: number; // 时间戳 秒
}

// 分销邀请记录
export interface InvitedUser {
  /** 邀请人ID */
  referrerId: string;
  /** 被邀请人ID */
  inviteeId: string;
  /** 被邀请人头像 冗余字段*/
  inviteeAvatar?: string;
  /** 被邀请人名称 冗余字段*/
  inviteeName?: string;
  /** 邀请时间 */
  invitedTime: number; // 时间戳 毫秒
  /** 渠道 */
  channel?: string;
  date?: string; // 注册时间 格式：2025-01-01
}

// 分销订单, 只有存在推荐人时才创建记录，记录收入分配
// 只有服务完成时，才创建订单
export interface DistributionOrder {
  /** 分销订单ID */
  id: string;
  /** 邀请人ID */
  referrerId: string;
  /** 被邀请人ID */
  inviteeId: string;
  /** 被邀请人头像 冗余字段*/
  inviteeAvatar?: string;
  /** 被邀请人名称 冗余字段*/
  inviteeName?: string;

  /** 关联咨询订单ID */
  orderId: string;
  /** 订单金额 冗余字段*/
  orderAmount: number;
  /** 佣金 */
  commission: number;
  /** 状态 */
  status: 'settled' | 'refunded';

  /** 退款时间 */
  refundedAt?: number;
  createdAt: number;
}
