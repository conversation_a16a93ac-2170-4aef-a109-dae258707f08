// 尺寸变量
@size-mini: 24px;
@size-small: 32px;
@size-normal: 40px;
@size-big: 56px;

@icon-mini: 24px;
@icon-small: 32px;
@icon-normal: 48px;
@icon-big: 64px;

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  transition: all 0.2s;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  // 尺寸变体
  &.mini {
    width: @size-mini;
    height: @size-mini;
  }
  
  &.small {
    width: @size-small;
    height: @size-small;
  }
  
  &.normal {
    width: @size-normal;
    height: @size-normal;
  }
  
  &.big {
    width: @size-big;
    height: @size-big;
  }

  // 类型变体  
  &.standard {
    background-color: transparent;
    
    &:hover, &:active {
      background-color: rgba(0, 0, 0, 0.08);
    }
  }
  
  &.filled {
    background-color: var(--color-primary);
    
    &:hover, &:active {
      background-color: var(--color-primary-dark);
    }
    
    .icon-button-icon {
      color: var(--color-primary-bg);
    }
  }
  
  &.outlined {
    background-color: transparent;
    border: 1px solid var(--color-primary);
    
    &:hover, &:active {
      background-color: rgba(var(--color-primary-rgb), 0.08);
    }
    
    .icon-button-icon {
      color: var(--color-primary);
    }
  }

  // 禁用状态
  &.disabled {
    opacity: 0.38;
    cursor: not-allowed;
    pointer-events: none;
    background-color: var(--color-disabled);
    
    .icon-button-icon {
      color: var(--text-disabled);
    }
  }
} 