const cloud = require("wx-server-sdk");
const { COLLECTIONS } = require("../common/db.constants");
const { generateWithdrawId } = require("../common/utils");

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

/**
 * 提交提现申请
 * @param {Object} withdrawData 提现数据
 * @param {string} openid 用户openid
 */
async function submitWithdraw(withdrawData, openid) {
  try {
    const {
      amount,
      paymentMethod,
      account: accountInfo,
      name,
      bankName,
    } = withdrawData;

    // 验证参数
    if (!amount || amount <= 0) {
      throw new Error("提现金额必须大于0");
    }
    if (!paymentMethod || paymentMethod !== "bank") {
      throw new Error("提现方式必须为银行转账");
    }
    if (!name || !bankName || !accountInfo) {
      throw new Error("请填写完整的收款信息");
    }

    // 验证提现金额
    if (!amount || amount <= 0) {
      throw new Error("提现金额必须大于0");
    }

    // 获取提现配置
    const configResult = await db
      .collection(COLLECTIONS.SYSTEM_CONFIG)
      .limit(1)
      .get();

    const config = configResult.data[0] || {
      minAmount: 1,
      maxAmount: 10000,
      fee: 0,
      feeRate: 0,
    };

    // 验证提现金额是否符合要求
    if (amount < config.minAmount) {
      throw new Error(`提现金额不能小于${config.minAmount}元`);
    }

    if (amount > config.maxAmount) {
      throw new Error(`提现金额不能大于${config.maxAmount}元`);
    }

    // 获取用户钱包
    const accountResult = await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: openid })
      .get();

    if (!accountResult.data || accountResult.data.length === 0) {
      throw new Error("钱包不存在");
    }

    const userWallet = accountResult.data[0];

    // 验证余额是否足够
    if (userWallet.balance < amount) {
      throw new Error("账户余额不足");
    }

    // 计算手续费
    const fee = Math.max(config.fee, amount * config.feeRate);
    const actualAmount = amount - fee;

    // 生成提现记录ID
    const withdrawId = generateWithdrawId();

    // 创建提现记录
    const withdraw = {
      _id: withdrawId,
      id: withdrawId,
      userId: openid,
      amount,
      fee,
      actualAmount,
      paymentMethod: paymentMethod || "bank",
      name: name,
      bankName: bankName,
      bankAccount: accountInfo,
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
      _openid: openid,
    };

    await db.collection(COLLECTIONS.WITHDRAW).add({
      data: withdraw,
    });

    // 更新账户余额
    await db
      .collection(COLLECTIONS.WALLET)
      .doc(userWallet._id)
      .update({
        data: {
          balance: _.inc(-amount),
          frozenAmount: _.inc(amount),
          updatedAt: Date.now(),
        },
      });

    return {
      id: withdrawId,
      status: "pending",
    };
  } catch (error) {
    console.error("提交提现申请失败:", error);
    throw error;
  }
}

// 完成提现
async function completeWithdraw(withdrawId) {
  try {
    const withdrawResult = await db
      .collection(COLLECTIONS.WITHDRAW)
      .where({ id: withdrawId })
      .get();
    if (!withdrawResult.data || withdrawResult.data.length === 0) {
      throw new Error("提现记录不存在");
    }
    const withdraw = withdrawResult.data[0];
    if (withdraw.status !== "pending") {
      throw new Error("只能完成待处理的提现申请");
    }
    // 更新提现记录状态
    await db
      .collection(COLLECTIONS.WITHDRAW)
      .where({ id: withdrawId })
      .update({
        data: {
          status: "completed",
          completedAt: Date.now(),
          updatedAt: Date.now(),
        },
      });

    // 更新钱包冻结金额
    await db
      .collection(COLLECTIONS.WALLET)
      .where({ id: withdraw.userId })
      .update({
        data: {
          frozenAmount: _.inc(-withdraw.amount),
          updatedAt: Date.now(),
        },
      });
  } catch (error) {
    console.error("完成提现失败:", error);
    throw error;
  }
}

module.exports = {
  submitWithdraw,
  completeWithdraw,
};
