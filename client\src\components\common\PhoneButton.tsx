import { Button } from '@antmjs/vantui';
import { callCloudFunction } from '@services/cloud';
import { useLoadingStore } from '@stores/loading.store';
import Taro from '@tarojs/taro';

export const PhoneButton = ({
  orderId,
  personId,
}: {
  orderId: string;
  personId: string;
}) => {
  const { transactionLoading, setTransactionLoading } = useLoadingStore();
  return (
    <Button
      type='primary'
      round
      loading={transactionLoading}
      disabled={transactionLoading}
      className='flex-1'
      onClick={async () => {
        //云函数获取咨询师电话号码
        setTransactionLoading(true);
        const result = await callCloudFunction('user', 'getUserPhoneNumber', {
          userId: personId,
          orderId: orderId,
        });
        console.log('result', result);
        setTransactionLoading(false);
        if (result.success && result.phone) {
          Taro.makePhoneCall({
            phoneNumber: result.phone,
          });
        }
      }}
    >
      拨打电话
    </Button>
  );
};
