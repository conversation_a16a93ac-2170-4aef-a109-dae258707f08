/**
 * 微信支付文档
 * https://pay.weixin.qq.com/doc/v2/merchant/4011939566
 */
/**
 * 微信支付订单
 */

export interface WechatPayPayment {
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
  notifyUrl?: string;
  attach?: string;
  timeExpire?: string;
}

/**
 * 后端创建支付订单参数
 */
export interface PaymentParams {
  orderId: string;
  amount: number;
  description?: string;
  paymentMethod?: string;
}

/**
 * 后端创建支付订单返回结果
 */
export interface PaymentResult {
  success: boolean;
  orderId: string;
  paymentId?: string;
  paymentData?: WechatPayPayment;
  message?: string;
}

/**
 * 支付信息表结构
 */
export interface Payment {
  // 订单ID
  orderId: string;
  // 用户ID 冗余存储
  userId: string;
  // 支付金额 单位：分
  amount: number;
  // 支付描述
  description: string;
  // 支付状态 pending: 待支付 paid: 已支付 refunded: 已退款 failed: 支付失败
  status: "pending" | "paid" | "refunded" | "failed";
  // 支付方式
  paymentMethod: string;
  // 商户订单号
  outTradeNo: string;
  // 微信支付订单号
  transactionId?: string;

  // 创建时间
  createdAt: number;
  // 更新时间
  updatedAt: number;
  // 用户openid
  _openid: string;
}

/**
 * 提现参数
 */
export interface WithdrawParams {
  type: "wechat" | "alipay" | "bank";
  amount: number;
  bankAccount?: string;
  bankName?: string;
  name?: string;
}

/**
 * 提现结果
 */
export interface WithdrawResult {
  success: boolean;
  withdrawId?: string;
  status: "pending" | "processing" | "completed" | "failed";
  message?: string;
}

/**
 * 提现记录
 */
export interface WithdrawRecord {
  // 提现记录ID
  id: string;
  // 用户ID
  userId: string;
  // 提现金额 单位：分
  amount: number;
  // 提现状态 pending: 待处理 processing: 处理中 completed: 已完成 failed: 失败
  status: "pending" | "processing" | "completed" | "failed";
  // 银行账户
  bankAccount?: string;
  // 银行名称
  bankName?: string;
  // 姓名
  name?: string;
  // 创建时间
  createdAt: number;
  // 更新时间
  updatedAt: number;
  // 完成时间
  completedAt?: number;
  // 备注
  remark?: string;
}

/**
 * 提现配置
 */
export interface WithdrawConfig {
  minAmount: number;
  maxAmount: number;
  fee: number;
  feeRate: number;
  withdrawDays: string[];
  withdrawTime: string;
  instructions: string;
}
