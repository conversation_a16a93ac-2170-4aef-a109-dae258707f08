import { <PERSON><PERSON>, Cell, Field, Picker, Uploader } from '@antmjs/vantui';
import { EDUCATION_LEVELS } from '@constants/text';
import { my_extend } from '@model/profile.therapist';
import {
  TherapistCertification,
  TherapistEducation,
  TherapistTraining,
} from '@model/therapist.interface';
import { uploadService } from '@services/upload.service';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import './DetailInfoEditer.less';

interface FileItem {
  url: string;
  name?: string;
  size?: number;
  type?: string;
  deletable?: boolean;
}

interface DetailInfoEditerProps {
  detailInfo: Partial<my_extend>;
  onFormChange: (field: string, value: any) => void;
}

export default function DetailInfoEditer({
  detailInfo,
  onFormChange,
}: DetailInfoEditerProps) {
  // 照片上传状态
  const [photoFiles, setPhotoFiles] = useState<FileItem[]>(
    (detailInfo.photos || []).map((url) => ({ url }))
  );
  const [isUploading, setIsUploading] = useState(false);

  console.log('DetailInfoEditer detailInfo', detailInfo);
  console.log('DetailInfoEditer photoFiles', photoFiles);
  // 证书和培训临时状态
  const [newCertification, setNewCertification] = useState<
    Partial<TherapistCertification>
  >({
    title: '',
    when: '',
    organization: '',
    description: '',
    imageUrl: '',
  });

  const [newTraining, setNewTraining] = useState<Partial<TherapistTraining>>({
    name: '',
    organization: '',
    start: '',
    end: '',
    duration: '',
    items: [],
  });

  // 新增培训项目状态
  const [newTrainingItem, setNewTrainingItem] = useState({
    title: '',
    start: '',
    end: '',
    description: '',
  });

  const [newEducation, setNewEducation] = useState<
    Partial<TherapistEducation['schools'][0]>
  >({
    name: '',
    degree: '',
    major: '',
    start: '',
    end: '',
  });

  const [newSocialMedia, setNewSocialMedia] = useState({
    platform: '',
    link: '',
  });

  const [certificationFile, setCertificationFile] = useState<FileItem[]>([]);

  const handlePhotoUpload = async (event) => {
    try {
      const { file } = event.detail;
      // 更新本地UI状态，显示上传中
      setIsUploading(true);
      // 将新文件添加到本地显示
      const newLocalFiles = [...photoFiles, file as FileItem];
      setPhotoFiles(newLocalFiles);

      // 上传到云服务器
      const result = await uploadService.uploadAvatar(file.url);
      // 更新表单数据
      const updatedPhotos = [...(detailInfo.photos || []), result];
      onFormChange('photos', updatedPhotos);
      Taro.showToast({ title: '照片上传成功', icon: 'success' });
    } catch (error) {
      console.error('照片上传失败:', error);
      Taro.showToast({ title: '照片上传失败', icon: 'none' });
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // 上传到云服务器
  const handleCertFileUpload = async (event) => {
    const { file } = event.detail;
    try {
      setIsUploading(true);
      setCertificationFile([file]);
      const result = await uploadService.uploadAvatar(file.url);
      // 更新表单数据
      setNewCertification({
        ...newCertification,
        imageUrl: result,
      });
      Taro.showToast({ title: '证书上传成功', icon: 'success' });
    } catch (error) {
      console.error('证书上传失败:', error);
      setCertificationFile([]);
      Taro.showToast({ title: '证书上传失败', icon: 'none' });
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // 添加证书
  const addCertification = () => {
    if (!newCertification.title) {
      Taro.showToast({ title: '请输入证书名称', icon: 'none' });
      return;
    }

    if (!newCertification.when) {
      Taro.showToast({ title: '请输入颁发机构', icon: 'none' });
      return;
    }

    if (!newCertification.imageUrl) {
      Taro.showToast({ title: '请上传证书文件', icon: 'none' });
      return;
    }

    // 添加新证书
    const newCerts = [
      ...(detailInfo.certifications || []),
      {
        title: newCertification.title,
        when: newCertification.when,
        organization: newCertification.organization,
        description: newCertification.description,
        imageUrl: newCertification.imageUrl,
      },
    ];

    onFormChange('certifications', newCerts);

    // 重置表单
    setNewCertification({
      title: '',
      organization: '',
      when: '',
      description: '',
      imageUrl: '',
    });

    setCertificationFile([]);
  };

  // 添加培训项目
  const addTrainingItem = () => {
    if (!newTrainingItem.title) {
      Taro.showToast({ title: '请输入项目名称', icon: 'none' });
      return;
    }

    if (!newTrainingItem.start) {
      Taro.showToast({ title: '请输入项目开始时间', icon: 'none' });
      return;
    }

    if (!newTrainingItem.end) {
      Taro.showToast({ title: '请输入项目结束时间', icon: 'none' });
      return;
    }

    const newItems = [
      ...(newTraining.items || []),
      {
        title: newTrainingItem.title,
        start: newTrainingItem.start,
        end: newTrainingItem.end,
        description: newTrainingItem.description,
      },
    ];

    setNewTraining({ ...newTraining, items: newItems });

    // 重置培训项目表单
    setNewTrainingItem({
      title: '',
      start: '',
      end: '',
      description: '',
    });
  };

  // 删除培训项目
  const removeTrainingItem = (index: number) => {
    const newItems = [...(newTraining.items || [])];
    newItems.splice(index, 1);
    setNewTraining({ ...newTraining, items: newItems });
  };

  // 添加培训经历
  const addTraining = () => {
    if (!newTraining.name) {
      Taro.showToast({ title: '请输入培训名称', icon: 'none' });
      return;
    }

    if (!newTraining.organization) {
      Taro.showToast({ title: '请输入培训机构', icon: 'none' });
      return;
    }

    if (!newTraining.start) {
      Taro.showToast({ title: '请输入培训开始时间', icon: 'none' });
      return;
    }

    if (!newTraining.end) {
      Taro.showToast({ title: '请输入培训结束时间', icon: 'none' });
      return;
    }

    if (!newTraining.duration) {
      Taro.showToast({ title: '请输入培训时长', icon: 'none' });
      return;
    }

    if (!newTraining.items || newTraining.items.length === 0) {
      Taro.showToast({ title: '请至少添加一个培训项目', icon: 'none' });
      return;
    }

    // 添加新培训
    const newTrainings = [
      ...(detailInfo.trainings || []),
      {
        name: newTraining.name,
        organization: newTraining.organization,
        start: newTraining.start,
        end: newTraining.end,
        duration: newTraining.duration,
        items: newTraining.items,
      },
    ];

    onFormChange('trainings', newTrainings);

    // 重置表单
    setNewTraining({
      name: '',
      organization: '',
      start: '',
      end: '',
      duration: '',
      items: [],
    });
  };

  // 添加教育经历
  const addEducation = () => {
    if (!newEducation.name) {
      Taro.showToast({ title: '请输入学校名称', icon: 'none' });
      return;
    }

    if (!newEducation.degree) {
      Taro.showToast({ title: '请输入学位', icon: 'none' });
      return;
    }

    if (!newEducation.major) {
      Taro.showToast({ title: '请输入专业', icon: 'none' });
      return;
    }

    // 添加新教育经历
    const newSchools = [
      ...(detailInfo.education?.schools || []),
      {
        name: newEducation.name,
        degree: newEducation.degree,
        major: newEducation.major,
        start: newEducation.start,
        end: newEducation.end,
      },
    ];

    onFormChange('education', {
      ...detailInfo.education,
      education: detailInfo.education?.education || '硕士',
      schools: newSchools,
    });

    // 重置表单
    setNewEducation({
      name: '',
      degree: '',
      major: '',
      start: '',
      end: '',
    });
  };

  // 添加社交媒体
  const addSocialMedia = () => {
    if (!newSocialMedia.platform) {
      Taro.showToast({ title: '请输入平台名称', icon: 'none' });
      return;
    }

    if (!newSocialMedia.link) {
      Taro.showToast({ title: '请输入链接', icon: 'none' });
      return;
    }

    // 添加新社交媒体
    const newSocialMedias = [
      ...(detailInfo.socialMedia || []),
      {
        platform: newSocialMedia.platform,
        link: newSocialMedia.link,
      },
    ];

    onFormChange('socialMedia', newSocialMedias);

    // 重置表单
    setNewSocialMedia({
      platform: '',
      link: '',
    });
  };

  return (
    <View>
      <View className='mx-4'>
        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <Field
            label='个人简介'
            type='textarea'
            placeholder='请详细介绍您的专业背景、治疗理念、擅长领域等'
            value={detailInfo.introduction}
            onChange={(e) => onFormChange('introduction', e.detail)}
            required
            autosize={{ minHeight: 150 }}
            border={false}
          />
        </View>
      </View>

      <Text className='text-lg font-bold my-4 block px-4'>个人照片</Text>
      <Text className='text-sm text-secondary mb-2 block px-4'>
        上传清晰的形象照，建议上传正面照和工作照
      </Text>

      <View className='mx-4'>
        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <View className='items-center justify-start flex flex-row gap-2'>
            <Uploader
              fileList={photoFiles}
              onAfterRead={handlePhotoUpload}
              onDelete={(event) => {
                const { index } = event.detail;
                const newFiles = [...photoFiles];

                // 仅从缓存中删除，不删除云端文件
                newFiles.splice(index, 1);
                setPhotoFiles(newFiles);

                // 更新表单数据，从detailInfo.photos中移除对应的URL
                if (detailInfo.photos && detailInfo.photos.length > index) {
                  const newPhotos = [...detailInfo.photos];
                  newPhotos.splice(index, 1);
                  onFormChange('photos', newPhotos);
                }
              }}
              maxCount={6}
              disabled={isUploading}
            />
          </View>
        </View>
      </View>

      <Text className='text-lg font-bold my-4 block px-4'>教育背景</Text>

      <View className='mx-4'>
        <View className='h-12 mb-3 bg-white border border-border rounded-lg px-4 flex justify-between items-center'>
          <View className='flex items-center'>
            <Text>最高学历</Text>
            <Text className='text-red-500'>*</Text>
          </View>
          <View className='flex items-center justify-end flex-1'>
            <Picker
              columns={EDUCATION_LEVELS}
              mode='content'
              value={detailInfo.education?.education}
              onInput={(e) =>
                onFormChange('education', {
                  ...detailInfo.education,
                  education: e.detail,
                })
              }
              placeholder='请选择最高学历'
              allowClear={false}
              showArrowRight
            />
          </View>
        </View>

        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <View className='items-start justify-between flex flex-col gap-2 w-full'>
            <Field
              label='学校名称'
              type='text'
              placeholder='请输入学校名称'
              value={newEducation.name}
              border={false}
              onChange={(e) =>
                setNewEducation({ ...newEducation, name: e.detail })
              }
            />

            <Cell
              title='学位'
              border={false}
              titleStyle={{ maxWidth: '6.2em !important', marginRight: '12px' }}
            >
              <View className='flex items-center flex-1'>
                <Picker
                  columns={EDUCATION_LEVELS}
                  mode='content'
                  value={newEducation.degree}
                  onInput={(e) =>
                    setNewEducation({
                      ...newEducation,
                      degree: e.detail as string,
                    })
                  }
                  placeholder='请选择学位'
                  allowClear={false}
                  showArrowRight
                />
              </View>
            </Cell>
            <Field
              label='专业'
              type='text'
              placeholder='请输入专业'
              value={newEducation.major}
              border={false}
              onChange={(e) =>
                setNewEducation({ ...newEducation, major: e.detail })
              }
            />

            <View className='flex education-start-input'>
              <Field
                label='入学年份'
                type='digit'
                // renderTitle={
                //   <View
                //     className='flex items-center mr-2 text-md'
                //     style={{
                //       minWidth: 'auto !important',
                //       maxWidth: 'none !important',
                //       width: 'auto !important',
                //       flex: 'none !important',
                //       display: 'inline-block',
                //     }}
                //   >
                //     <Text>入学年份</Text>
                //   </View>
                // }
                placeholder='如2015'
                value={newEducation.start}
                border={false}
                onChange={(e) =>
                  setNewEducation({ ...newEducation, start: e.detail })
                }
                className='flex-1 mr-2'
              />

              <Field
                label='毕业年份'
                type='digit'
                placeholder='如2019'
                value={newEducation.end}
                border={false}
                onChange={(e) =>
                  setNewEducation({ ...newEducation, end: e.detail })
                }
                className='flex-1 ml-2'
              />
            </View>

            <Button
              type='primary'
              size='small'
              className='mt-2 w-full'
              onClick={addEducation}
            >
              添加新教育经历
            </Button>
          </View>
        </View>
      </View>

      {/* 已添加教育经历列表 */}
      {detailInfo.education?.schools &&
        detailInfo.education.schools.length > 0 && (
          <View className='mt-4 mx-4'>
            <Text className='font-medium mb-2 block'>已添加教育经历</Text>

            {detailInfo.education.schools.map((school, index) => (
              <View key={index} className='bg-gray-50 p-3 rounded mb-2'>
                <View className='flex justify-between items-center'>
                  <Text className='font-medium'>{school.name}</Text>
                  <Text
                    className='text-red-500 text-sm'
                    onClick={() => {
                      const newSchools = [
                        ...(detailInfo.education?.schools || []),
                      ];
                      newSchools.splice(index, 1);
                      onFormChange('education', {
                        ...detailInfo.education,
                        schools: newSchools,
                      });
                    }}
                  >
                    删除
                  </Text>
                </View>
                <Text className='text-sm block mt-1'>
                  {school.degree} · {school.major}
                </Text>
                <Text className='text-sm text-gray-500 block mt-1'>
                  {school.start} - {school.end}
                </Text>
              </View>
            ))}
          </View>
        )}

      <Text className='text-lg font-bold my-4 block px-4'>资质证书</Text>

      <View className='mx-4'>
        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <View className='items-start justify-between flex flex-col gap-2 w-full'>
            <Field
              label='证书名称'
              type='text'
              placeholder='请输入证书名称'
              value={newCertification.title}
              border={false}
              onChange={(e) =>
                setNewCertification({ ...newCertification, title: e.detail })
              }
            />

            <Field
              label='颁发机构'
              type='text'
              placeholder='请输入颁发机构'
              value={newCertification.organization}
              border={false}
              onChange={(e) =>
                setNewCertification({
                  ...newCertification,
                  organization: e.detail,
                })
              }
            />

            <Field
              label='颁发日期'
              type='text'
              placeholder='如2020-01-01'
              value={newCertification.when}
              border={false}
              onChange={(e) =>
                setNewCertification({
                  ...newCertification,
                  when: e.detail,
                })
              }
            />

            <View className='mt-2'>
              <Text className='text-sm mb-1 block'>证书文件</Text>
              <Uploader
                fileList={certificationFile}
                onAfterRead={handleCertFileUpload}
                onDelete={() => {
                  // 仅从缓存中删除，不删除云端文件
                  setCertificationFile([]);
                  setNewCertification({ ...newCertification, imageUrl: '' });
                }}
                maxCount={1}
                disabled={isUploading}
              />
            </View>

            <Button
              type='primary'
              size='small'
              className='mt-2 w-full'
              onClick={addCertification}
              disabled={!newCertification.imageUrl}
            >
              添加新证书
            </Button>
          </View>
        </View>
      </View>

      {/* 已添加证书列表 */}
      {detailInfo.certifications && detailInfo.certifications.length > 0 && (
        <View className='mt-4 mx-4'>
          <Text className='font-medium mb-2 block'>已添加证书</Text>

          {detailInfo.certifications.map((cert, index) => (
            <View key={index} className='bg-gray-50 p-3 rounded mb-2'>
              <View className='flex justify-between items-center'>
                <Text className='font-medium'>{cert.title}</Text>
                <Text
                  className='text-red-500 text-sm'
                  onClick={() => {
                    const newCerts = [...(detailInfo.certifications || [])];
                    newCerts.splice(index, 1);
                    onFormChange('certifications', newCerts);
                  }}
                >
                  删除
                </Text>
              </View>
              <Text className='text-sm block mt-1'>{cert.organization}</Text>
              {cert.when && (
                <Text className='text-sm text-gray-500 block mt-1'>
                  颁发日期: {cert.when}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}

      <Text className='text-lg font-bold my-4 block px-4'>培训经历</Text>

      <View className='mx-4'>
        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <View className='items-start justify-between flex flex-col gap-2 w-full'>
            <Field
              label='培训名称'
              type='text'
              placeholder='请输入培训名称'
              value={newTraining.name}
              border={false}
              onChange={(e) =>
                setNewTraining({ ...newTraining, name: e.detail })
              }
            />

            <Field
              label='培训机构'
              type='text'
              placeholder='请输入培训机构'
              value={newTraining.organization}
              border={false}
              onChange={(e) =>
                setNewTraining({ ...newTraining, organization: e.detail })
              }
            />

            <View className='flex training-start-input'>
              <Field
                label='开始日期'
                type='text'
                placeholder='如2020-01'
                value={newTraining.start}
                border={false}
                onChange={(e) =>
                  setNewTraining({ ...newTraining, start: e.detail })
                }
                className='flex-1 mr-2'
              />

              <Field
                label='结束日期'
                type='text'
                placeholder='如2020-06'
                value={newTraining.end}
                border={false}
                onChange={(e) =>
                  setNewTraining({ ...newTraining, end: e.detail })
                }
                className='flex-1 ml-2'
              />
            </View>

            <Field
              label='培训时长'
              type='text'
              placeholder='如3个月、120小时等'
              value={newTraining.duration}
              border={false}
              onChange={(e) =>
                setNewTraining({ ...newTraining, duration: e.detail })
              }
            />

            {/* 培训项目添加区域 */}
            <View className='mt-4'>
              <Text className='text-sm font-medium mb-2 block'>培训项目</Text>

              {/* 已添加的培训项目列表 */}
              {newTraining.items && newTraining.items.length > 0 && (
                <View className='mb-3'>
                  {newTraining.items.map((item, index) => (
                    <View key={index} className='bg-gray-50 p-2 rounded mb-2'>
                      <View className='flex justify-between items-center'>
                        <Text className='font-medium text-sm'>
                          {item.title}
                        </Text>
                        <Text
                          className='text-red-500 text-xs'
                          onClick={() => removeTrainingItem(index)}
                        >
                          删除
                        </Text>
                      </View>
                      <Text className='text-xs text-gray-500 block mt-1'>
                        {item.start} - {item.end}
                      </Text>
                      {item.description && (
                        <Text className='text-xs text-gray-600 block mt-1'>
                          {item.description}
                        </Text>
                      )}
                    </View>
                  ))}
                </View>
              )}

              {/* 添加新培训项目的表单 */}
              <View className='bg-gray-50 p-3 rounded'>
                <Field
                  label='项目名称'
                  type='text'
                  placeholder='请输入培训项目名称'
                  value={newTrainingItem.title}
                  border={false}
                  onChange={(e) =>
                    setNewTrainingItem({ ...newTrainingItem, title: e.detail })
                  }
                />

                <View className='flex training-item-time-input'>
                  <Field
                    label='开始时间'
                    type='text'
                    placeholder='如2020-01-01'
                    value={newTrainingItem.start}
                    border={false}
                    onChange={(e) =>
                      setNewTrainingItem({
                        ...newTrainingItem,
                        start: e.detail,
                      })
                    }
                    className='flex-1 mr-2'
                  />

                  <Field
                    label='结束时间'
                    type='text'
                    placeholder='如2020-01-31'
                    value={newTrainingItem.end}
                    border={false}
                    onChange={(e) =>
                      setNewTrainingItem({ ...newTrainingItem, end: e.detail })
                    }
                    className='flex-1 ml-2'
                  />
                </View>

                <Field
                  label='项目描述'
                  type='textarea'
                  placeholder='请简述该项目的内容（可选）'
                  value={newTrainingItem.description}
                  border={false}
                  onChange={(e) =>
                    setNewTrainingItem({
                      ...newTrainingItem,
                      description: e.detail,
                    })
                  }
                  autosize={{ minHeight: 40 }}
                />

                <Button
                  type='primary'
                  size='small'
                  className='mt-2 w-full'
                  onClick={addTrainingItem}
                >
                  添加培训项目
                </Button>
              </View>
            </View>

            <Button
              type='primary'
              size='small'
              className='mt-2 w-full'
              onClick={addTraining}
            >
              添加新培训经历
            </Button>
          </View>
        </View>
      </View>

      {/* 已添加培训列表 */}
      {detailInfo.trainings && detailInfo.trainings.length > 0 && (
        <View className='mt-4 mx-4'>
          <Text className='font-medium mb-2 block'>已添加培训</Text>

          {detailInfo.trainings.map((training, index) => (
            <View key={index} className='bg-gray-50 p-3 rounded mb-2'>
              <View className='flex justify-between items-center'>
                <Text className='font-medium'>{training.name}</Text>
                <Text
                  className='text-red-500 text-sm'
                  onClick={() => {
                    const newTrainings = [...(detailInfo.trainings || [])];
                    newTrainings.splice(index, 1);
                    onFormChange('trainings', newTrainings);
                  }}
                >
                  删除
                </Text>
              </View>
              <Text className='text-sm block mt-1'>
                {training.organization}
              </Text>
              <Text className='text-sm text-gray-500 block mt-1'>
                {training.start} - {training.end} · {training.duration}
              </Text>
              {training.items && training.items.length > 0 && (
                <View className='mt-2'>
                  <Text className='text-sm font-medium block mb-1'>
                    培训项目：
                  </Text>
                  {training.items.map((item, itemIndex) => (
                    <View key={itemIndex} className='ml-2 mb-1'>
                      <Text className='text-sm block'>
                        • {item.title} ({item.start} - {item.end})
                      </Text>
                      {item.description && (
                        <Text className='text-xs text-gray-600 block ml-2'>
                          {item.description}
                        </Text>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>
      )}

      <Text className='text-lg font-bold my-4 block px-4'>社交媒体</Text>

      <View className='mx-4'>
        <View className='mb-3 bg-white border border-border rounded-lg px-4 py-3'>
          <View className='items-start justify-between flex flex-col gap-2 w-full'>
            <Field
              label='平台名称'
              type='text'
              placeholder='如微博、知乎、公众号等'
              value={newSocialMedia.platform}
              border={false}
              onChange={(e) =>
                setNewSocialMedia({ ...newSocialMedia, platform: e.detail })
              }
            />

            <Field
              label='链接'
              type='text'
              placeholder='请输入链接'
              value={newSocialMedia.link}
              border={false}
              onChange={(e) =>
                setNewSocialMedia({ ...newSocialMedia, link: e.detail })
              }
            />

            <Button
              type='primary'
              size='small'
              className='mt-2 w-full'
              onClick={addSocialMedia}
            >
              添加新社交媒体
            </Button>
          </View>
        </View>
      </View>

      {/* 已添加社交媒体列表 */}
      {detailInfo.socialMedia && detailInfo.socialMedia.length > 0 && (
        <View className='mt-4 mx-4'>
          <Text className='font-medium mb-2 block'>已添加社交媒体</Text>

          {detailInfo.socialMedia.map((media, index) => (
            <View key={index} className='bg-gray-50 p-3 rounded mb-2'>
              <View className='flex justify-between items-center'>
                <Text className='font-medium'>{media.platform}</Text>
                <Text
                  className='text-red-500 text-sm'
                  onClick={() => {
                    const newSocialMedias = [...(detailInfo.socialMedia || [])];
                    newSocialMedias.splice(index, 1);
                    onFormChange('socialMedia', newSocialMedias);
                  }}
                >
                  删除
                </Text>
              </View>
              <Text className='text-sm block mt-1 text-blue-500'>
                {media.link}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}
