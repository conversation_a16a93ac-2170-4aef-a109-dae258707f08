export enum NotificationType {
  SYSTEM = "system",
  CONSULTATION = "consultation",
  REMINDER = "reminder",
}

export enum ReminderEvent {
  ORDER_CREATED = "order_created",
  ORDER_CANCELLED = "order_cancelled",
  ORDER_COMPLETED = "order_completed",
  ORDER_REFUNDED = "order_refunded",
}

export interface Notification {
  id: string;
  title: string;
  content: string;
  time: string;
  read: boolean;
  type: NotificationType;
  event?: ReminderEvent;
  sender?: {
    id: string;
    name: string;
    avatar: string;
  };
  orderId?: string;
}
