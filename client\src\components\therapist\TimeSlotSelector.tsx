import { Text, View } from '@tarojs/components';
import { useCallback } from 'react';

interface TimeSlotSelectorProps {
  isAvailableDay: boolean;
  availableTimeSlots: number[];
  isTimeSlotAvailable: (hour: number) => boolean;
  selectedTimeSlot: number | null;
  onTimeSlotSelect: (hour: number) => void;
  formatHour: (hour: number) => string;
}

export default function TimeSlotSelector({
  isAvailableDay,
  availableTimeSlots,
  isTimeSlotAvailable,
  selectedTimeSlot,
  onTimeSlotSelect,
  formatHour,
}: TimeSlotSelectorProps) {
  // 渲染时间段项
  const renderTimeSlotItem = useCallback(
    (hour: number) => {
      const available = isAvailableDay && isTimeSlotAvailable(hour);
      const isSelected = selectedTimeSlot === hour;

      return (
        <View
          key={hour}
          className={`min-w-[4.25rem] h-10 flex items-center justify-center  rounded-lg ${
            !available
              ? 'border border-placeholder'
              : isSelected
              ? 'bg-primary'
              : 'border border-gray-600'
          } ${available ? 'cursor-pointer' : 'cursor-not-allowed'}`}
          onClick={() => available && onTimeSlotSelect(hour)}
        >
          <Text
            className={`text-md font-bold ${
              !available
                ? 'text-placeholder'
                : isSelected
                ? 'text-white'
                : 'text-default'
            }`}
          >
            {formatHour(hour)}
          </Text>
        </View>
      );
    },
    [
      isAvailableDay,
      isTimeSlotAvailable,
      selectedTimeSlot,
      onTimeSlotSelect,
      formatHour,
    ]
  );

  return (
    <View className='w-full mt-4'>
      <Text className='text-base font-bold'>选择时间</Text>

      <View className='grid grid-cols-4 gap-x-2 gap-y-2 mt-6 md:grid-cols-6'>
        {availableTimeSlots.map((hour) => renderTimeSlotItem(hour))}
      </View>
    </View>
  );
}
