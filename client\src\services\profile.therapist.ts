import { COLLECTIONS } from '@model/db.model';

import { CloudFunctionResult } from '@core/api';
import {
  my_extend,
  my_schedule,
  my_service,
  my_summary,
} from '@model/profile.therapist';
import { useGlobalStore } from '@stores/global.store';
import Taro from '@tarojs/taro';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';
import { therapistService } from './therapist.service';

/**
 * 咨询师个人profile服务实现类
 * 从咨询师角度提供个人资料管理和业务功能
 * 遵循架构原则：
 * 1. 公开数据直接从数据库读取
 * 2. 敏感数据通过云函数获取
 * 3. 所有写操作通过云函数
 */
class ProfileServiceImpl extends BaseService<my_summary> {
  constructor() {
    super(COLLECTIONS.THERAPIST);
  }

  /**
   * 更新咨询师基本信息
   */
  async updateMyProfile(
    profile: Partial<my_summary>
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('therapist', 'updateProfile', {
        profile,
      });
      return result;
    } catch (error) {
      console.error('更新咨询师基本信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新咨询师详细信息
   */
  async updateMyExtendInfo(
    extendInfo: Partial<my_extend>
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('therapist', 'updateExtendInfo', {
        extend: extendInfo,
      });
      return result;
    } catch (error) {
      console.error('更新咨询师详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新咨询师服务信息
   */
  async updateMyServices(
    services: Partial<my_service>
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('therapist', 'updateServices', {
        services,
      });
      return result;
    } catch (error) {
      console.error('更新咨询师服务信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新咨询师排期信息
   */
  async updateMySchedule(
    schedule: Partial<my_schedule>
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('therapist', 'updateSchedule', {
        schedule,
      });
      return result;
    } catch (error) {
      console.error('更新咨询师排期信息失败:', error);
      throw error;
    }
  }

  /**
   * 注册成为咨询师
   */
  async register(registrationData: {
    summary: Partial<my_summary>;
    detailInfo: Partial<my_extend>;
    serviceInfo: Partial<my_service>;
  }): Promise<CloudFunctionResult> {
    try {
      console.log('registerTherapist', registrationData);
      const result = await callCloudFunction(
        'therapist',
        'register',
        registrationData
      );

      return result;
      // // 检查返回值
      // if (result && result.success && result.id) {
      //   return {
      //     result: true,
      //     id: result.id,
      //     code: result.code,
      //     message: result.message,
      //   };
      // } else {
      //   return {
      //     result: false,
      //     code: result.code,
      //     message: result.message,
      //   };
      // }
    } catch (error) {
      console.error('提交咨询师注册信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取我的个人信息
   * 公开数据，直接从数据库读取
   */
  async getMyProfile() {
    try {
      const result = await this.directRead({
        id: useGlobalStore.getState().openid,
      });
      return result[0] || null;
    } catch (error) {
      console.error('获取个人信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取我的详细信息
   * 公开数据，直接从数据库读取
   */
  async getMyExtendInfo() {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.THERAPIST_EXTINFO)
        .where({ id: useGlobalStore.getState().openid })
        .get();
      return result.data[0] || null;
    } catch (error) {
      console.error('获取详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取我的服务信息
   * 公开数据，直接从数据库读取
   */
  async getMyServices() {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(COLLECTIONS.THERAPIST_SERVICE)
        .where({ id: useGlobalStore.getState().openid })
        .get();
      console.log('therapistProfileService getMyServices result', result);
      return result.data[0] || null;
    } catch (error) {
      console.error('获取个人信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取我的排期信息
   * 公开数据，直接从数据库读取
   */
  async getMySchedule(startDate?: number, endDate?: number) {
    try {
      const db = Taro.cloud.database();
      const _ = db.command;
      const where: any = {
        id: useGlobalStore.getState().openid,
      };
      if (!startDate) {
        // 获取今天及以后的排期
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        startDate = today.getTime();
      }

      if (startDate && endDate) {
        where.date = _.gte(startDate).and(_.lte(endDate));
      } else if (startDate) {
        where.date = _.gte(startDate);
      } else if (endDate) {
        where.date = _.lte(endDate);
      }

      const result = await db
        .collection(COLLECTIONS.THERAPIST_SCHEDULES)
        .where(where)
        .get();
      return result.data || [];
    } catch (error) {
      console.error('获取排期信息失败:', error);
      throw error;
    }
  }

  /**
   * 监听我的排期信息
   */
  watchMySchedule(onChange: (schedule: my_schedule) => void) {
    console.log('therapistProfileService watchMySchedule');
    const result = therapistService.watchTherapistSchedule(
      '{openid}',
      onChange
    );
    return result;
  }

  async updateAvatarAndNickname(
    avatar: string,
    nickname: string
  ): Promise<CloudFunctionResult> {
    try {
      const result = await callCloudFunction('therapist', 'updateProfile', {
        profile: {
          id: useGlobalStore.getState().openid,
          avatar,
          name: nickname,
        },
      });
      return result;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }
}

export const ProfileService = new ProfileServiceImpl();
