import { Image, Text, View } from '@tarojs/components';

export interface MeasurePackage {
  id: number;
  title: string;
  description: string;
  tests: number;
  duration: string;
  image: string;
}

interface MeasurePackageCardProps {
  package: MeasurePackage;
  onClick: (id: number) => void;
}

export default function TestPackageCard({
  package: pkg,
  onClick,
}: MeasurePackageCardProps) {
  return (
    <View
      className='bg-white rounded-lg overflow-hidden mb-3 shadow-sm active:scale-[0.98] transition-transform'
      onClick={() => onClick(pkg.id)}
    >
      <Image src={pkg.image} className='w-full h-32' mode='aspectFill' />
      <View className='p-4'>
        <Text className='font-medium'>{pkg.title}</Text>
        <Text className='text-sm text-gray-500 mt-1 block'>
          {pkg.description}
        </Text>
        <View className='flex items-center mt-2 text-xs text-gray-400'>
          <Text>{pkg.tests}个测评</Text>
          <Text className='mx-2'>·</Text>
          <Text>{pkg.duration}</Text>
        </View>
      </View>
    </View>
  );
}
