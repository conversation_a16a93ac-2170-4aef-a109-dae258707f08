import Taro from '@tarojs/taro';

/**
 * 服务层基类
 * 统一封装数据库操作和云函数调用
 */
/**
 * ---------------云数据库文档--------
 * https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/guide/database/security-rules.html#%E8%A7%84%E5%88%99%E5%8C%B9%E9%85%8D
 * {openid} 变量
 * 在查询时，当前用户 openid 是常用的变量，在新的安全规则体系下，
 * 要求显式传入 openid，因此为了方便开发者、让开发者无需每次先通过云函数获取用户 openid，
 * 我们规定查询条件中可使用一个字符串常量 {openid}，在后台中发现该字符串时会自动替换为小程序用户的 openid
 * ---------------云数据库文档--------
 */

export abstract class BaseService<T> {
  protected collectionName: string;

  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }

  /**
   * 直接读取数据库
   * 适用于：
   * 1. 公开配置数据
   * 2. 低频变更数据
   * 3. 用户自己的数据
   * @param query 查询条件
   * @param options 查询选项
   */
  protected async directRead(query: any = {}, options: any = {}): Promise<T[]> {
    try {
      const db = Taro.cloud.database();
      const { orderField, orderDirection, skip, limit } = options;

      let dbQuery = db.collection(this.collectionName).where(query);

      // 添加排序
      if (orderField) {
        dbQuery = dbQuery.orderBy(orderField, orderDirection || 'desc');
      }

      // 添加分页
      if (typeof skip === 'number') {
        dbQuery = dbQuery.skip(skip);
      }

      if (typeof limit === 'number') {
        dbQuery = dbQuery.limit(limit);
      }

      const result = await dbQuery.get();
      return result.data as T[];
    } catch (error) {
      console.error(`直接读取${this.collectionName}失败:`, error);
      throw error;
    }
  }

  /**
   * 获取数据总数
   * @param query 查询条件
   */
  protected async getCount(query: any = {}): Promise<number> {
    try {
      const db = Taro.cloud.database();
      const result = await db
        .collection(this.collectionName)
        .where(query)
        .count();
      return result.total;
    } catch (error) {
      console.error(`获取${this.collectionName}总数失败:`, error);
      throw error;
    }
  }

  /**
   * 监听数据变化
   * 用于需要实时同步的数据
   * @param query 查询条件
   * @param onChange 数据变化回调
   * @returns 取消监听的函数
   */
  protected watchData(query: any = {}, onChange: (snapshot: any) => void): any {
    try {
      const db = Taro.cloud.database();
      const watcher = db
        .collection(this.collectionName)
        .where(query)
        .watch({
          onChange,
          onError: (err) => {
            console.error(`监听${this.collectionName}失败:`, err);
          },
        });

      // 返回取消监听的函数
      return watcher;
    } catch (error) {
      console.error(`设置监听${this.collectionName}失败:`, error);
      throw error;
    }
  }
}
