import { Button } from '@antmjs/vantui';
import PageLol from '@components/common/page-meta';
import { Text, View } from '@tarojs/components';
import { appRouter } from '@utils/router';
import React from 'react';

interface AgreementSection {
  title: string;
  content: string[];
}

const userAgreement: AgreementSection[] = [
  {
    title: '总则',
    content: [
      '欢迎您使用心理健康平台！',
      '本用户协议（以下简称"协议"）是您与心理健康平台（以下简称"我们"或"平台"）之间关于您使用我们提供的产品和服务的法律协议。',
      '请您在使用我们的服务前，仔细阅读并充分理解本协议的全部内容。一旦您开始使用我们的服务，即表示您已经同意遵守本协议的所有条款。如您对本协议有任何疑问，应向我们咨询。',
    ],
  },
  {
    title: '服务说明',
    content: [
      '我们的服务包括但不限于：心理咨询预约、心理测评、心理健康内容推荐、冥想练习等功能。',
      '我们保留随时变更、中断或终止部分或全部服务的权利。',
      '我们保留根据实际情况随时调整服务种类、形式等的权利。',
      '我们有权对服务中的内容进行审核，有权对违反法律法规或本协议的内容予以删除。',
    ],
  },
  {
    title: '用户账号',
    content: [
      '您在使用我们的服务前需要注册账号。您应当提供真实、准确、完整的个人资料，并在资料发生变更时及时更新。',
      '您应当妥善保管账号和密码，对您账号下的所有行为负责。如发现任何未经授权使用您账号的情况，应立即通知我们。',
      '您的账号仅限您本人使用，不得将账号出借、转让或售卖给他人使用。',
      '我们有权根据实际情况，对违反法律法规、本协议或平台规则的账号采取警告、限制功能、暂停使用、终止服务等措施。',
    ],
  },
  {
    title: '用户行为规范',
    content: [
      '您应当遵守中华人民共和国法律法规，不得利用我们的服务从事违法违规活动。',
      '您不得利用我们的服务实施以下行为：',
      '1. 发布、传播违反国家法律法规的信息；',
      '2. 侵犯他人知识产权、商业秘密等合法权益的行为；',
      '3. 发布、传播骚扰、侮辱、诽谤、恐吓或其他侵害他人合法权益的信息；',
      '4. 从事任何可能对互联网或移动网络的正常运行造成不利影响的行为；',
      '5. 以任何方式危害未成年人的行为；',
      '6. 其他违反法律法规、政策及公序良俗、社会公德或干扰平台正常运营的行为。',
    ],
  },
  {
    title: '知识产权',
    content: [
      '我们提供的服务中包含的所有内容，包括但不限于文本、图片、音频、视频、图表、标识、版面设计、电子文档等，均受著作权、商标权及其他法律法规的保护。',
      '未经我们或相关权利人书面许可，您不得以任何方式擅自复制、修改、转载、出版、发行、传播、展示或以其他方式使用前述内容。',
      '您在使用我们的服务时发布的内容，您保证对该等内容享有合法权利。您同意授予我们非独家的、全球性的、免费的、可转授权的、可再许可的权利，以使用、复制、修改、改编、出版、翻译、创建衍生作品、传播、表演和展示此等内容。',
    ],
  },
  {
    title: '隐私保护',
    content: [
      '我们重视您的隐私保护，我们将按照《隐私政策》收集、使用、存储和共享您的个人信息。',
      '请您详细阅读我们的《隐私政策》以了解我们收集、使用您个人信息的情况，以及您享有的权利。',
    ],
  },
  {
    title: '支付与退款',
    content: [
      '您可以根据自身需求选择购买我们提供的付费服务。',
      '服务费用将在您购买前予以明确告知，您应在购买前确认价格和内容，并按照我们指定的方式支付相应费用。',
      '除法律法规另有规定或双方另有约定外，您已支付的费用一般不予退还。具体退款规则请参见相关服务页面的说明。',
      '未成年人购买付费服务需要取得其监护人的同意。',
    ],
  },
  {
    title: '免责声明',
    content: [
      '我们的服务按"现状"和"可得到"的状态提供，不保证服务一定能满足您的要求，也不保证服务不会中断，对服务的及时性、安全性、准确性也不作担保。',
      '我们提供的心理健康服务不能替代专业医疗机构的诊断和治疗。如您存在严重的心理健康问题，请及时就医。',
      '您理解并同意，在使用我们的服务过程中因网络环境、通讯线路、第三方服务等原因造成的服务中断或不能满足您要求的风险。',
      '因不可抗力、计算机病毒或黑客攻击、系统不稳定、您所在位置、您关闭页面等原因造成的服务中断或服务质量下降的风险。',
    ],
  },
  {
    title: '协议变更',
    content: [
      '我们保留在必要时变更本协议条款的权利。',
      '协议变更后，我们会在应用内显著位置发布通知，并将更新后的协议发布在应用内。',
      '如您继续使用我们的服务，即视为您已同意接受变更后的协议。如您不同意变更内容，应停止使用我们的服务。',
    ],
  },
  {
    title: '适用法律与争议解决',
    content: [
      '本协议的订立、执行和解释及争议的解决均适用中华人民共和国法律。',
      '如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向我们所在地有管辖权的人民法院提起诉讼。',
    ],
  },
  {
    title: '联系我们',
    content: [
      '如您对本协议或我们的服务有任何疑问、建议或投诉，请通过以下方式与我们联系：',
      '电子邮件：<EMAIL>',
      '客服电话：400-888-8888',
      '公司地址：北京市海淀区中关村创业大厦B座10层',
    ],
  },
];

const UserAgreementPage: React.FC = () => {
  const lastUpdated = '2023年11月20日';


  return (
    <PageLol
      navigationProps={{
        title: '用户协议',
        showBackButton: true,
      }}
    >
      <View className='min-h-screen p-4 pb-8'>
        {/* 标题和更新日期 */}
        <View className='mb-6'>
          <Text className='text-2xl font-bold block text-center mb-2'>
            用户协议
          </Text>
          <Text className='text-sm text-secondary block text-center'>
            最近更新：{lastUpdated}
          </Text>
        </View>

        {/* 协议内容 */}
        <View className='space-y-6'>
          {userAgreement.map((section, index) => (
            <View key={index} className='mb-6'>
              <View className='bg-primary bg-opacity-10 p-2 rounded-lg mb-2'>
                <Text className='text-lg font-medium text-primary'>
                  {index + 1}. {section.title}
                </Text>
              </View>
              <View className='px-2 space-y-2'>
                {section.content.map((paragraph, pIndex) => (
                  <Text
                    key={pIndex}
                    className='text-sm text-secondary leading-6 block'
                  >
                    {paragraph}
                  </Text>
                ))}
              </View>
            </View>
          ))}
        </View>

        {/* 隐私政策链接 */}
        <View className='mt-6 mb-4'>
          <Text className='text-sm text-secondary mb-2 block'>相关协议：</Text>
          <Button type='primary' plain size='small' onClick={appRouter.privacy}>
            查看《隐私政策》
          </Button>
        </View>

        {/* 底部确认 */}
        <View className='mt-8'>
          <Text className='text-xs text-center block text-secondary'>
            使用我们的服务，即表示您已阅读并同意本用户协议及相关政策
          </Text>
        </View>
      </View>
    </PageLol>
  );
};

export default UserAgreementPage;
