import { Button } from '@antmjs/vantui';
import BaseInfoEditer from '@components/therapist/BaseInfoEditer';
import { my_summary } from '@model/profile.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

interface BasicInfoStepProps {
  summary: Partial<my_summary>;
  onFormChange: (section: string, field: string, value: any) => void;
  onNextStep: () => void;
}

export default function BasicInfoStep({
  summary,
  onFormChange,
  onNextStep,
}: BasicInfoStepProps) {
  // 表单验证
  const validateForm = (): boolean => {
    if (!summary.name) {
      Taro.showToast({ title: '请输入姓名', icon: 'none' });
      return false;
    }

    if (!summary.location) {
      Taro.showToast({ title: '请选择所在城市', icon: 'none' });
      return false;
    }
    if (summary.titles?.length === 0) {
      Taro.showToast({ title: '请选择职称', icon: 'none' });
      return false;
    }
    if (summary.specialties?.length === 0) {
      Taro.showToast({ title: '请选择专长领域', icon: 'none' });
      return false;
    }
    if (summary.directions?.length === 0) {
      Taro.showToast({ title: '请选择咨询方向', icon: 'none' });
      return false;
    }
    return true;
  };

  // 处理下一步按钮点击
  const handleNextStep = () => {
    if (validateForm()) {
      onNextStep();
    }
  };

  return (
    <View>
      {/* <Text className='text-lg font-bold mb-4 block'>基本信息</Text> */}

      <BaseInfoEditer summary={summary} onFormChange={onFormChange} />
      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='primary'
          block
          round
          className='flex-1 ml-2'
          onClick={handleNextStep}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
