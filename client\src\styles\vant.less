/* 所有 vant 组件 */
#root .van-theme-light,
#root .van-theme-light body {
  background: transparent !important;
  background-color: transparent !important;
}
/* src/styles/global.css 或类似的全局样式文件 */
.van-cell,
.van-grid,
.van-grid-item 
.van-grid-item__content{
  background: transparent !important;
  background-color: transparent !important;
}
// /* 以 vantui 组件常用类名为例，实际以你项目中生成的类名为准 */
// .vant-cell,
// .vant-cell-group,
// .vant-grid,
// .vant-grid-item,
// .vant-tabs,
// .vant-tab,
// .vant-nav-bar,
// .vant-popup,
// .vant-dialog,
// .vant-action-sheet,
// .vant-list,
// .vant-search,
// .vant-field,
// .vant-button,
// .vant-tag,
// .vant-card,
// .vant-panel,
// .vant-panel__content,
// .vant-panel__footer,
// .vant-panel__header {
//   background: transparent !important;
//   background-color: transparent !important;
// }
// .van-cell::after {
//   border: none !important;
// }

// 可以用的类
// .van-hairline,
// .van-hairline--top,
// .van-hairline--left,
// .van-hairline--right,
// .van-hairline--bottom,
// .van-hairline--top-bottom,
// .van-hairline--surround
// .van-ellipsis
// .van-multi-ellipsis--l2
// .van-multi-ellipsis--l3
