import { TherapistListRequest } from '@core/api';
import { BaseCacheService } from '@core/cache/base-cache.service';
import {
  therapistExtinfoCacheOptions,
  therapistListCacheOptions,
  therapistPreferRatingCacheOptions,
  therapistServiceCacheOptions,
  therapistSummaryCacheOptions,
} from '@core/cache/therapist-cache-policy';
import {
  TherapistFilters,
  schedule_item,
  therapist_service,
  therapist_summary,
} from '@model/therapist.interface';
import { reviewService } from '@services/review.service';
import { therapistService } from '@services/therapist.service';
import { useTherapistStore } from '@stores/therapist.store';
import Taro from '@tarojs/taro';

/**
 * 咨询师模块Actions层
 * 负责协调服务层和状态管理层之间的数据流转
 */
export const therapistActions = {
  /**
   * 获取咨询师列表（支持分页和筛选）
   */
  fetchTherapists: async (params: TherapistListRequest = {}) => {
    console.log('therapistActions fetchTherapists', params);
    // 如果params为空，使用默认参数
    if (!params) {
      params = {
        page: 1,
        pageSize: 20,
      };
    }
    try {
      // 生成缓存键
      const cacheKey = therapistListCacheOptions.getCacheKey(params);
      console.log('therapistActions fetchTherapists cacheKey', cacheKey);

      if (!params.forceRefresh) {
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          console.log(
            'therapistActions fetchTherapists cachedData',
            cachedData
          );
          // 缓存的是id列表，需要转换为therapist_summary[]
          const therapists = await Promise.all(
            cachedData.map((id) => therapistActions.fetchTherapistSummary(id))
          );
          // 跟新状态
          useTherapistStore.setState({
            therapists: therapists,
            loading: false,
          });
          return therapists;
        }
      }
      const therapistStore = useTherapistStore.getState();
      const { loading } = therapistStore;

      // 已经在加载中，防止重复请求
      if (loading) return;

      // 更新加载状态
      useTherapistStore.setState({ loading: true, error: null });

      // 调用服务层API
      const result = await therapistService.readTherapists(params);

      if (result.success) {
        // 缓存数据
        if (result.data && result.data.length > 0) {
          BaseCacheService.set(
            cacheKey,
            result.data.map((item) => item.id),
            therapistListCacheOptions.ttl,
            therapistListCacheOptions.getVersion()
          );
          // 缓存到summary
          await Promise.all(
            result.data.map(async (item) => {
              const cacheKey_s = therapistSummaryCacheOptions.getCacheKey(
                item.id.toString()
              );
              BaseCacheService.set(
                cacheKey_s,
                item,
                therapistSummaryCacheOptions.ttl,
                therapistSummaryCacheOptions.getVersion()
              );
            })
          );
        }
        // 更新状态
        useTherapistStore.setState({
          therapists: result.data,
          pagination: result.pagination,
          loading: false,
        });

        return result.data;
      } else {
        throw new Error('获取咨询师列表失败');
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error);
      useTherapistStore.setState({
        loading: false,
        error: '获取咨询师列表失败，请稍后重试',
      });
      return [];
    } finally {
      useTherapistStore.setState({ loading: false });
    }
  },

  /**
   * 获取咨询师基础信息
   * @param id 咨询师ID
   * @param forceRefresh 是否强制刷新，不使用缓存
   */
  fetchTherapistSummary: async (
    therapistId: string,
    forceRefresh = false
  ): Promise<therapist_summary | null> => {
    try {
      if (!forceRefresh) {
        // 尝试从store获取数据
        const therapistStore = useTherapistStore.getState();
        const { therapists } = therapistStore;
        if (therapists && therapists.length > 0) {
          const therapist = therapists.find((t) => t.id === therapistId);
          if (therapist) {
            return therapist;
          }
        }
      } else {
        // 生成缓存键
        const cacheKey = therapistSummaryCacheOptions.getCacheKey(therapistId);

        // 尝试从缓存获取数据
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          // 更新状态
          useTherapistStore.getState().updateSummary(cachedData);
          return cachedData;
        }
      }

      // 缓存未命中或强制刷新，从服务获取数据
      const therapistInfo = await therapistService.readSummary(therapistId);

      // 缓存数据
      if (therapistInfo) {
        // 生成缓存键
        const cacheKey = therapistSummaryCacheOptions.getCacheKey(therapistId);
        BaseCacheService.set(
          cacheKey,
          therapistInfo,
          therapistSummaryCacheOptions.ttl,
          therapistSummaryCacheOptions.getVersion()
        );
        //更新状态
        useTherapistStore.getState().updateSummary(therapistInfo);
      }

      return therapistInfo;
    } catch (error) {
      console.error('获取咨询师基础信息失败:', error);
      return null;
    }
  },

  /**
   * 获取咨询师详情,不包含敏感信息
   */
  fetchTherapistFullInfo: async (therapistId: string) => {
    console.log('fetchTherapistFullInfo', therapistId);
    try {
      const therapistStore = useTherapistStore.getState();
      const {
        currentTherapistExtinfo,
        currentTherapistService,
        currentTherapistPreferRating,
        loadingDetail,
      } = therapistStore;

      // 已经在加载中，防止重复请求
      if (loadingDetail) return;

      // 读summary
      await therapistActions.fetchTherapistSummary(therapistId);

      // 读extend
      if (
        !currentTherapistExtinfo ||
        currentTherapistExtinfo.id !== therapistId
      ) {
        await therapistActions.fetchTherapistExtendInfo(therapistId);
      }

      // 读service
      if (
        !currentTherapistService ||
        currentTherapistService.id !== therapistId
      ) {
        await therapistActions.fetchTherapistService(therapistId, true);
      }

      // 读评价
      if (!currentTherapistPreferRating) {
        await therapistActions.fetchTherapistPreferRating(therapistId);
      }
    } catch (error) {
      console.error('获取咨询师详情失败:', error);
      useTherapistStore.setState({
        loadingDetail: false,
        detailError: '获取咨询师详情失败，请稍后重试',
      });
    }
  },

  /**
   * 获取咨询师扩展信息
   */
  fetchTherapistExtendInfo: async (
    therapistId: string,
    forceRefresh = false
  ) => {
    try {
      // 生成缓存键
      const cacheKey = therapistExtinfoCacheOptions.getCacheKey(therapistId);

      if (!forceRefresh) {
        // 尝试从缓存获取数据
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          //更新状态
          useTherapistStore.setState({
            currentTherapistExtinfo: cachedData,
          });
          return cachedData;
        }
      }

      // 缓存未命中或强制刷新，从服务获取数据
      const therapistExtInfo = await therapistService.readExtendInfo(
        therapistId
      );

      console.log('therapistExtInfo', therapistExtInfo);
      // 缓存数据
      if (therapistExtInfo) {
        BaseCacheService.set(
          cacheKey,
          therapistExtInfo,
          therapistExtinfoCacheOptions.ttl,
          therapistExtinfoCacheOptions.getVersion()
        );
      }

      //更新状态
      useTherapistStore.setState({
        currentTherapistExtinfo: therapistExtInfo,
      });

      return therapistExtInfo;
    } catch (error) {
      console.error('获取咨询师扩展信息失败:', error);
      return null;
    }
  },

  /**
   * 获取咨询师服务
   */
  fetchTherapistService: async (
    therapistId: string,
    forceRefresh = false
  ): Promise<therapist_service | null> => {
    try {
      // 生成缓存键
      const cacheKey = therapistServiceCacheOptions.getCacheKey(therapistId);

      if (!forceRefresh) {
        // 尝试从缓存获取数据
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          //更新状态
          useTherapistStore.setState({
            currentTherapistService: cachedData,
          });
          return cachedData;
        }
      }

      // 缓存未命中或强制刷新，从服务获取数据
      const therapistServices = await therapistService.readService(therapistId);

      // 缓存数据
      if (therapistServices) {
        BaseCacheService.set(
          cacheKey,
          therapistServices,
          therapistServiceCacheOptions.ttl,
          therapistServiceCacheOptions.getVersion()
        );
      }

      //更新状态
      useTherapistStore.setState({
        currentTherapistService: therapistServices,
      });

      return therapistServices;
    } catch (error) {
      console.error('获取咨询师服务失败:', error);
      return null;
    }
  },

  /**
   * 获取咨询师优选评级
   */
  fetchTherapistPreferRating: async (
    therapistId: string,
    forceRefresh = false
  ) => {
    console.log('fetchTherapistPreferRating', therapistId, forceRefresh);
    try {
      const cacheKey =
        therapistPreferRatingCacheOptions.getCacheKey(therapistId);

      if (!forceRefresh) {
        // 尝试从缓存获取数据
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData && cachedData.length > 0) {
          console.log('fetchTherapistPreferRating cachedData', cachedData);
          useTherapistStore.setState({
            currentTherapistPreferRating: cachedData,
          });
          return cachedData;
        }
      }

      // 缓存未命中或强制刷新，从服务获取数据
      const result = await reviewService.readFewReviews(therapistId);

      // 缓存数据
      if (result) {
        console.log('fetchTherapistPreferRating result', result);
        BaseCacheService.set(
          cacheKey,
          result,
          therapistPreferRatingCacheOptions.ttl,
          therapistPreferRatingCacheOptions.getVersion()
        );
        useTherapistStore.setState({
          currentTherapistPreferRating: result,
        });
      }

      return result;
    } catch (error) {
      console.error('获取咨询师优选评级失败:', error);
      return null;
    }
  },

  /**
   * 应用筛选条件
   */
  applyFilters: async (filterOptions: TherapistFilters) => {
    console.log('applyFilters', filterOptions);
    try {
      // 获取当前分页状态
      const { pagination } = useTherapistStore.getState();

      // 重置页码，保持其他分页参数
      const params: TherapistListRequest = {
        page: 1,
        pageSize: pagination?.pageSize || 20,
        // 添加筛选条件
        filters: filterOptions,
      };

      // 保存当前筛选条件到状态管理
      useTherapistStore.setState({
        filters: filterOptions,
      });

      // 请求筛选后的数据
      await therapistActions.fetchTherapists(params);
    } catch (error) {
      console.error('应用筛选条件失败:', error);
    }
  },

  /**
   * 加载更多咨询师（用于分页）
   */
  loadMore: async () => {
    try {
      const { pagination, filters } = useTherapistStore.getState();

      // 没有更多数据了
      if (!pagination || !pagination.hasNext) return [];

      // 构建请求参数
      const params: TherapistListRequest = {
        page: pagination.page + 1,
        pageSize: pagination.pageSize,
        // 保持当前的筛选条件
        filters: filters ?? undefined,
      };

      // 调用服务层API
      const result = await therapistService.readTherapists(params);

      if (result.success) {
        const { therapists: currentTherapists } = useTherapistStore.getState();

        // 将新数据添加到现有数据后面
        const combinedTherapists = [
          ...(currentTherapists || []),
          ...result.data,
        ];

        // 更新状态
        useTherapistStore.setState({
          therapists: combinedTherapists,
          pagination: result.pagination,
        });

        return result.data;
      }
      return [];
    } catch (error) {
      console.error('加载更多咨询师失败:', error);
      return [];
    }
  },

  /**watch 咨询师排期  */
  watchTherapistOccupancy: (
    therapistId: string
  ): Taro.DB.Document.IWatcher | null => {
    try {
      const unsubscribe = therapistService.watchTherapistSchedule(
        therapistId,
        (snapshot) => {
          console.log(snapshot);
          if (snapshot.type === 'init') {
            //初始化咨询师排期
            therapistActions._initSchedule(therapistId, snapshot.docs);
          } else {
            //更新咨询师排期, 重新初始化排期
            therapistActions._initSchedule(therapistId, snapshot.docs);
          }
        }
      );
      return unsubscribe;
    } catch (error) {
      console.error('watch 咨询师排期失败:', error);
      throw error;
    }
  },

  /**初始化咨询师排期 */
  _initSchedule: (therapistId: string, docs: any) => {
    console.log('initSchedule', docs);
    try {
      const schedule: schedule_item[] = [];
      for (const doc of docs) {
        const scheduleItem = doc as schedule_item;
        schedule.push(scheduleItem);
      }
      useTherapistStore.setState({
        currentTherapistOccuppied: {
          id: therapistId,
          schedule: schedule,
        },
      });
    } catch (error) {
      console.error('watch 咨询师排期失败:', error);
      throw error;
    }
  },

  /**更新咨询师排期 */
  _updateSchedule: (therapistId: string, docChanges: any) => {
    console.log('updateSchedule', docChanges);
    try {
      const schedule = useTherapistStore.getState()
        .currentTherapistOccuppied ?? {
        id: therapistId,
        schedule: [],
      };
      // 数据变更
      docChanges.forEach((change) => {
        if (change.type === 'add') {
          const item = change.doc as schedule_item;
          schedule.schedule.push(item);
        } else if (
          change.dataType === 'update' ||
          change.dataType === 'replace'
        ) {
          const item = change.doc as schedule_item;
          const index = schedule.schedule.findIndex(
            (s) => s.date === item.date
          );
          if (index !== -1) {
            schedule.schedule[index] = item;
          } else {
            schedule.schedule.push(item);
          }
        }
      });
      useTherapistStore.setState({
        currentTherapistOccuppied: schedule,
      });
    } catch (error) {
      console.error('更新咨询师排期失败:', error);
      throw error;
    }
  },

  /**
   * 刷新咨询师列表（强制刷新）
   */
  refreshTherapists: async () => {
    try {
      const { pagination, filters } = useTherapistStore.getState();

      // 构建请求参数，保持当前页码和筛选条件
      const params: TherapistListRequest = {
        page: pagination?.page || 1,
        pageSize: pagination?.pageSize || 20,
        forceRefresh: true, // 强制刷新，不使用缓存
        // 保持当前的筛选条件
        filters: filters ?? undefined,
      };

      return await therapistActions.fetchTherapists(params);
    } catch (error) {
      console.error('刷新咨询师列表失败:', error);
      return [];
    }
  },

  /**
   * 清空状态
   */
  resetStore: () => {
    useTherapistStore.setState({
      therapists: null,
      currentTherapistExtinfo: null,
      currentTherapistPreferRating: null,
      currentTherapistOccuppied: null,
      pagination: null,
      loading: false,
      error: null,
      filters: null,
    });
  },

  /**
   * 清空当前咨询师
   */
  resetCurrentTherapist: () => {
    useTherapistStore.setState({
      currentTherapistExtinfo: null,
      currentTherapistPreferRating: null,
      currentTherapistOccuppied: null,
      loadingDetail: false,
      detailError: null,
    });
  },
};
