import { Icon, Result } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import React, { useState } from 'react';
import OrderTimelineItem from './OrderTimelineItem';

interface OrderTimelineProps {
  data: Record<string, any[]>; // { '2024-06-20': [order, ...] }
}

const OrderTimeline: React.FC<OrderTimelineProps> = ({ data }) => {
  const [collapsedDates, setCollapsedDates] = useState<string[]>([]);

  // 处理日期展开/收起
  const handleDateToggle = (date: string) => {
    if (collapsedDates.includes(date)) {
      setCollapsedDates((prev) => prev.filter((d) => d !== date));
    } else {
      setCollapsedDates((prev) => [...prev, date]);
    }
  };
  const renderDateGroup = (date: string, orders: any[]) => {
    const collapsed = collapsedDates.includes(date);
    return (
      <View key={date} className='mb-6'>
        {/* 日期标题 */}
        <View className='mb-2  pt-2.5 flex flex-row items-center '>
          <Text className='text-md text-secondary font-medium mr-2'>
            {date}
          </Text>
          <View
            className='w-5 h-5 flex items-center justify-center rounded-full  border border-gray-300'
            onClick={() => handleDateToggle(date)}
          >
            <Icon
              name={collapsed ? 'arrow-down' : 'arrow-up'}
              size={12}
              color='#999'
            />
          </View>
        </View>
        {/* 时间线内容 */}
        {!collapsed && (
          <View className='relative space-y-4'>
            {orders.map((order, idx) => (
              <OrderTimelineItem key={order.id} order={order} />
            ))}
          </View>
        )}
      </View>
    );
  };

  if (!data || Object.keys(data).length === 0) {
    return <Result type='error' title='暂无订单' message='暂无订单' />;
  }

  return (
    <View className='px-4'>
      {Object.entries(data).map(([date, orders]) =>
        renderDateGroup(date, orders)
      )}
    </View>
  );
};

export default OrderTimeline;
