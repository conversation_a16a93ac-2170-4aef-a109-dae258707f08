{"version": "2.0", "region": "ap-shanghai", "envId": "{{env.CLOUD_ENV_ID}}", "framework": {"plugins": {"function": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./cloud/functions", "publishIncludeList": "user", "functionDefaultConfig": {"timeout": 5, "runtime": "Nodejs16.13", "envVariables": {}, "memorySize": 256}, "functions": [{"name": "user", "envVariables": {}}, {"name": "therapist", "envVariables": {}}, {"name": "paymentcallback", "envVariables": {}}, {"name": "payment", "envVariables": {}}, {"name": "order", "envVariables": {}}, {"name": "feedback", "envVariables": {}}, {"name": "distribution", "envVariables": {}}, {"name": "config", "envVariables": {}}, {"name": "chat", "envVariables": {}}, {"name": "test2", "envVariables": {}}]}}}, "name": "mental-AI"}, "functionRoot": "./cloud/functions", "functions": [{"name": "user", "nodeVersion": "16.13", "envVariables": {}}, {"name": "therapist", "envVariables": {}}, {"name": "paymentcallback", "envVariables": {}}, {"name": "payment", "envVariables": {}}, {"name": "order", "envVariables": {}}, {"name": "feedback", "envVariables": {}}, {"name": "distribution", "envVariables": {}}, {"name": "config", "envVariables": {}}, {"name": "chat", "envVariables": {}}, {"name": "test2", "envVariables": {}}]}