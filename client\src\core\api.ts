import { IncomeDetail } from '@model/income.model';
import {
  ORDER_STATUS,
  Order_summary,
  OrderCancelInfo,
  REFUND_STATUS,
} from '@model/order.interface';
import {
  therapist_extend,
  therapist_schedule,
  therapist_summary,
  TherapistFilters,
} from '@model/therapist.interface';
import { CACHE_ROLE } from './cache/order-cache-policy';

export interface Pagination {
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 基础响应接口
 * 所有API响应的标准格式
 */
export interface BaseResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 响应数据 */
  data: T;
}
/**
 * 分页请求参数
 */

export interface PageRequest {
  /** 页码，从1开始 */
  page?: number;
  /** 每页数量，默认20 */
  pageSize?: number;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向: asc-升序, desc-降序 */
  sortOrder?: 'asc' | 'desc';
}
/**
 * 分页响应结果
 */

export interface PageResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 数据列表 */
  data: T[];
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
    /** 总记录数 */
    total: number;
    /** 总页数 */
    totalPages: number;
    /** 是否有下一页 */
    hasNext: boolean;
    /** 是否有上一页 */
    hasPrev: boolean;
  };
}
/**
 * 咨询师列表请求参数
 */

export interface TherapistListRequest extends PageRequest {
  /** 搜索关键词 */
  keyword?: string;

  /** 排序方式: favorite-按收藏, price-按价格, rating-按评分 默认按照评分*/
  sort?: 'favorite' | 'price' | 'rating';

  /** 筛选条件 */
  filters?: TherapistFilters;

  /** 是否强制刷新，不使用缓存 */
  forceRefresh?: boolean;
}
/**
 * 咨询师列表响应
 */

export interface TherapistListResponse
  extends PageResponse<therapist_summary> {}
/**
 * 咨询师基本信息响应
 */

export type TherapistSummaryResponse = BaseResponse<therapist_summary>;
/**
 * 咨询师扩展信息响应
 */

export type TherapistExtendInfoResponse = BaseResponse<therapist_extend>;
/**
 * 咨询师收藏请求
 */

export interface TherapistFavoriteRequest {
  /** 咨询师ID */
  therapistId: string;
  /** 是否收藏 */
  favorite: boolean;
}
/**
 * 咨询师收藏响应
 */

export interface TherapistFavoriteResponse
  extends BaseResponse<{
    /** 最新收藏数 */
    favoriteCount: number;
    /** 是否已收藏 */
    isFavorite: boolean;
  }> {}
/**
 * 咨询师排期请求
 */

export interface TherapistScheduleRequest {
  /** 咨询师ID */
  therapistId: string;
  /** 开始日期 (date in milliseconds 精确到天) */
  startDate?: number;
  /** 结束日期 (date in milliseconds 精确到天) */
  endDate?: number;
}
/**
 * 咨询师排期响应
 */

export interface TherapistScheduleResponse
  extends BaseResponse<therapist_schedule[]> {}
/**
 * 咨询师评价请求
 */

export interface TherapistReviewRequest {
  /** 咨询师ID */
  therapistId: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 最低评分 */
  minRating?: number;
  /** 只看有内容的评价 */
  hasContent?: boolean;
}

export interface OrderListParams {
  status?: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][];
  refundStatus?: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS][];
  query?: string;
  complaint?: boolean;
}
/**
 * 订单列表请求参数
 */
export interface OrderListRequest {
  page?: number;
  pageSize?: number;
  params?: OrderListParams;
  forceRefresh?: boolean;
  /** 角色：user-用户端，therapist-咨询师端 */
  role?: CACHE_ROLE;
}

/**
 * 订单列表响应
 */
export interface OrderListResponse {
  success: boolean;
  code: number;
  data: Order_summary[];
  pagination?: Pagination;
}

/**
 * 云函数返回结果
 */
export const SUCCESS_CODE = 200;
export interface CloudFunctionResult {
  success: boolean;
  code: number;
  message: string;
  [key: string]: any; // 云函数返回的数据
}
// 创建订单参数 based on Order and omit id and createdAt and status
export interface CreateOrderParams
  extends Omit<
    Order_summary,
    | 'userId'
    | '_id'
    | 'createdAt'
    | 'status'
    | 'endTime'
    | 'hasComplaint'
    | 'updatedAt'
  > {}

export interface CancelOrderParams {
  /** 订单ID */
  _id: string;
  /** 取消信息 */
  cancel_info: OrderCancelInfo;
}

export interface ReviewOrderParams {
  score: number;
  comment?: string;
  tags?: string[];
}

/**
 * 收入列表请求参数
 */
export interface IncomeListRequest {
  page?: number;
  pageSize?: number;
  params?: IncomeListParams;
  forceRefresh?: boolean;
}

/**
 * 收入列表筛选参数
 */
export interface IncomeListParams {
  /** 收入类型：1-咨询 2-分销 3-退款 */
  incomeType?: 1 | 2 | 3;
  /** 状态：settled-已入账 frozen-冻结中 refunded-已退款 */
  status?: 'settled' | 'frozen' | 'refunded';
  /** 关键词搜索 */
  query?: string;
}

/**
 * 收入列表响应
 */
export interface IncomeListResponse {
  success: boolean;
  code: number;
  data: IncomeDetail[];
  pagination?: Pagination;
}
