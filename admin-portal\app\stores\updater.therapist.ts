import { my_extend, my_service, my_summary } from '@model/profile.therapist';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import createSelectors from './libs/selector';
import { StorageSceneKey, zustandStorage } from './libs/storage';
import { useTherapistProfileStore } from './profile.therapist';

// 注册表单数据类型
export interface TherapistUpdaterFormData {
  summary: Partial<my_summary>;
  extend: Partial<my_extend>;
  serviceInfo: Partial<my_service>;
}

/**
 * 咨询师注册状态
 */
interface State {
  // 表单状态
  formData: TherapistUpdaterFormData;
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string | null;
}

/**
 * 咨询师注册状态操作
 */
interface Action {
  // 初始化表单数据
  setFormData: (formData: TherapistUpdaterFormData) => void;
  // 重置表单数据
  resetFormData: (keepUserInfo?: boolean) => void;
  // 更新表单字段
  updateFormField: (
    section: keyof TherapistUpdaterFormData,
    field: string,
    value: any
  ) => void;

  // 获取表单数据
  getFormData: () => TherapistUpdaterFormData;
  // 设置加载状态
  setLoading: (loading: boolean) => void;
  // 设置错误信息
  setError: (error: string | null) => void;
}

// 初始状态
const initialState: State = {
  loading: false,
  error: null,
  formData: {
    // 基础信息
    summary:
      (useTherapistProfileStore.getState().myProfile as Partial<my_summary>) ||
      {},
    // 详细信息
    extend:
      (useTherapistProfileStore.getState()
        .myExtendInfo as Partial<my_extend>) || {},
    // 服务信息
    serviceInfo:
      (useTherapistProfileStore.getState().myServices as Partial<my_service>) ||
      {},
  },
};

// 创建 store
const therapistUpdaterStore = create<State & Action>()(
  immer(
    persist(
      (set, get) => ({
        ...initialState,

        // 初始化表单数据
        setFormData: (formData) => {
          set((draft) => {
            draft.formData = formData;
          });
        },

        // 重置表单数据
        resetFormData: (keepUserInfo = true) => {
          const currentState = get();
          const userName = keepUserInfo
            ? currentState.formData.summary.name
            : '';
          const avatar = keepUserInfo
            ? currentState.formData.summary.avatar
            : '';

          set((draftState) => {
            // 创建新的表单数据对象而不是修改现有对象
            draftState.formData = {
              summary: {
                ...initialState.formData.summary,
                name: userName,
                avatar: avatar,
              },
              extend: { ...initialState.formData.extend },
              serviceInfo: { ...initialState.formData.serviceInfo },
            };
          });
        },

        // 更新表单字段
        updateFormField: (section, field, value) => {
          set((draftState) => {
            // 使用类型断言确保类型兼容
            draftState.formData[section] = {
              ...(draftState.formData[section] || {}),
              [field]: value,
            } as any;
          });
        },

        // 获取表单数据
        getFormData: () => {
          return get().formData;
        },

        // 设置加载状态
        setLoading: (loading) => {
          set((draftState) => {
            draftState.loading = loading;
          });
        },

        // 设置错误信息
        setError: (error) => {
          set((draftState) => {
            draftState.error = error;
          });
        },
      }),
      {
        name: StorageSceneKey.THERAPIST_UPDATER,
        storage: createJSONStorage(() => zustandStorage),
        // 只持久化关键状态
        partialize: (state) => ({
          formData: state.formData,
        }),
      }
    )
  )
);

// 导出带选择器的 store
export const useTherapistUpdaterStore = createSelectors(therapistUpdaterStore);
