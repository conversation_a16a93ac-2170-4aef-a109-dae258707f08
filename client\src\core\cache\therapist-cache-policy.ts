import { TherapistListRequest } from '@core/api';
import { CacheOptions, CachePolicy } from './cache-types';
/**
 * 咨询师模块缓存策略配置
 *
 * 缓存策略设计原则:
 * 1. 公开配置数据: 长期缓存，定期后台刷新
 * 2. 用户私有数据: 短期缓存，自动过期或强制更新
 * 3. 高频变更数据: 短期缓存或不缓存，每次使用前校验
 * 4. 敏感数据: 不缓存，每次从云函数获取
 */

// 缓存键前缀
export const CACHE_PREFIX = {
  // 咨询师列表
  THERAPIST_LIST: 'therapist:list',
  // 咨询师基本信息
  THERAPIST_BASE_INFO: 'therapist:base:',
  // 咨询师详细信息
  THERAPIST_EXTRA_INFO: 'therapist:extra:',
  // 咨询师服务
  THERAPIST_SERVICE: 'therapist:service:',
  // 咨询师优选评级
  THERAPIST_PREFER_RATING: 'therapist:prefer:',
  // 推荐咨询师
  RECOMMENDED_THERAPISTS: 'therapist:recommended',
  // 咨询师评价
  THERAPIST_REVIEWS: 'therapist:reviews:',
  // 收藏的咨询师
  FAVORITE_THERAPISTS: 'therapist:favorite:',
  // 推荐课程
  RECOMMENDED_COURSES: 'therapist:recommended_courses',
};

/**
 * 缓存时间配置 (毫秒)
 */
export const CACHE_TTL = {
  // 咨询师列表缓存5分钟
  THERAPIST_LIST: 5 * 60 * 1000,
  // 咨询师基本信息缓存30分钟
  THERAPIST_BASE_INFO: 30 * 60 * 1000,
  // 咨询师详细信息缓存10分钟
  THERAPIST_EXTRA_INFO: 10 * 60 * 1000,
  // 咨询师服务缓存10分钟
  THERAPIST_SERVICE: 10 * 60 * 1000,
  // 咨询师优选评级缓存2小时
  THERAPIST_PREFER_RATING: 2 * 60 * 60 * 1000,
  // 推荐咨询师列表缓存1小时
  RECOMMENDED_THERAPISTS: 60 * 60 * 1000,
  // 咨询师评价缓存15分钟
  THERAPIST_REVIEWS: 15 * 60 * 1000,
  //收藏的咨询师缓存1小时
  FAVORITE_THERAPISTS: 60 * 60 * 1000,
  // 推荐课程缓存1小时
  RECOMMENDED_COURSES: 60 * 60 * 1000,
};

/**
 * 咨询师列表缓存策略
 */
export const therapistListCacheOptions: CachePolicy<TherapistListRequest> = {
  keyPrefix: 'therapist_list',
  ttl: 5 * 60 * 1000, // 5分钟
  version: '1.0.0',

  /**
   * 根据查询参数生成缓存键
   * @param params 查询参数
   */
  getCacheKey: (params: TherapistListRequest): string => {
    // 将查询参数转换为字符串，用于生成缓存键
    const { page, pageSize, keyword, sort, filters } = params;

    // 构建参数字符串
    const paramsStr = [
      `page=${page || 1}`,
      `pageSize=${pageSize || 20}`,
      keyword ? `keyword=${keyword}` : '',
      sort ? `sort=${sort}` : 'sort=favorite',
      filters ? `filters=${encodeURIComponent(JSON.stringify(filters))}` : '',
    ]
      .filter(Boolean)
      .join('&');

    return `${therapistListCacheOptions.keyPrefix}_${paramsStr}`;
  },

  /**
   * 获取当前缓存版本
   */
  getVersion: (): string => {
    return therapistListCacheOptions.version;
  },
};

/**
 * 咨询师基本信息缓存策略
 */
export const therapistSummaryCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.THERAPIST_BASE_INFO,
  backgroundRefresh: true,
  getCacheKey: (id: string) => {
    return `${CACHE_PREFIX.THERAPIST_BASE_INFO}${id}`;
  },
  // 每小时版本更新，确保定期刷新数据
  getVersion: () => {
    const now = new Date();
    return `${now.toISOString().split('T')[0]}:${now.getHours()}`;
  },
  ignoreErrors: true,
};

/**
 * 咨询师详细信息缓存策略
 */
export const therapistExtinfoCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.THERAPIST_EXTRA_INFO,
  backgroundRefresh: false, // 需要获取最新排期，不在后台刷新
  getCacheKey: (id: string) => {
    return `${CACHE_PREFIX.THERAPIST_EXTRA_INFO}${id}`;
  },
  // 每小时更新，需要较新数据
  getVersion: () => {
    const now = new Date();
    return `${now.toISOString().split('T')[0]}:${now.getHours()}`;
  },
  ignoreErrors: false, // 不忽略错误，确保数据准确
};

/**
 * 咨询师服务缓存策略
 */
export const therapistServiceCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.THERAPIST_SERVICE,
  backgroundRefresh: true,
  getCacheKey: (id: string) => {
    return `${CACHE_PREFIX.THERAPIST_SERVICE}${id}`;
  },
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};

/**
 * 咨询师优选评级缓存策略
 */
export const therapistPreferRatingCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.THERAPIST_PREFER_RATING,
  backgroundRefresh: true,
  getCacheKey: (id: string) => {
    return `${CACHE_PREFIX.THERAPIST_PREFER_RATING}${id}`;
  },
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};

/**
 * 推荐咨询师缓存策略
 */
export const recommendedTherapistsCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.RECOMMENDED_THERAPISTS,
  backgroundRefresh: true,
  getCacheKey: (direction?: string) => {
    return `${CACHE_PREFIX.RECOMMENDED_THERAPISTS}${
      direction ? `:${direction}` : ''
    }`;
  },
  // 每天更新一次
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};

/**
 * 咨询师评价缓存策略
 */
export const therapistReviewsCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.THERAPIST_REVIEWS,
  backgroundRefresh: true,
  getCacheKey: (id: string, page = 1) => {
    return `${CACHE_PREFIX.THERAPIST_REVIEWS}${id}:${page}`;
  },
  // 每天更新一次
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};

//收藏的咨询师缓存策略
export const favoriteTherapistsCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.FAVORITE_THERAPISTS,
  backgroundRefresh: true,
  getCacheKey: (id: string) => {
    return `${CACHE_PREFIX.FAVORITE_THERAPISTS}${id}`;
  },
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};

// 推荐课程缓存策略
export const recommendedCoursesCacheOptions: CacheOptions = {
  ttl: CACHE_TTL.RECOMMENDED_COURSES,
  backgroundRefresh: true,
  getCacheKey: () => {
    return `${CACHE_PREFIX.RECOMMENDED_COURSES}`;
  },
  getVersion: () => {
    return new Date().toISOString().split('T')[0];
  },
  ignoreErrors: true,
};
/**
 * 缓存校验最长时间
 * 当缓存数据超过此时间时，即使TTL未到期也强制刷新
 */
export const CACHE_MAX_AGE = {
  // 咨询师列表最长缓存1小时
  THERAPIST_LIST: 60 * 60 * 1000,
  // 咨询师基本信息最长缓存4小时
  THERAPIST_BASE_INFO: 4 * 60 * 60 * 1000,
  // 咨询师详细信息最长缓存30分钟
  THERAPIST_EXTRA_INFO: 30 * 60 * 1000,
  // 推荐咨询师列表最长缓存4小时
  RECOMMENDED_THERAPISTS: 4 * 60 * 60 * 1000,
  // 咨询师评价最长缓存2小时
  THERAPIST_REVIEWS: 2 * 60 * 60 * 1000,
};
