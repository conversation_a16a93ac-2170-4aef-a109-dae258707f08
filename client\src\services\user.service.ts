import { COLLECTIONS } from '@model/db.model';
import { user_public } from '@model/user.interface';
import { BaseService } from './base.service';
import { callCloudFunction } from './cloud';

/**
 * 用户服务实现类。 这是操作第三人数据，操作自己数据使用profileService
 * 遵循架构原则：
 * 1. 公开数据直接从数据库读取
 * 2. 敏感数据通过云函数获取
 * 3. 所有写操作通过云函数
 */
class UserServiceImpl extends BaseService<user_public> {
  constructor() {
    super(COLLECTIONS.USER_PUBLIC);
  }

  /**
   * 获取用户信息
   * 用户自己的数据，直接读取
   */
  async getUserInfo(id: string): Promise<user_public | null> {
    try {
      const result = await this.directRead({ id });
      return result[0] || null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户电话号码
   */
  async getPhoneNumber(userId: string, orderId: string): Promise<string> {
    try {
      const result = await callCloudFunction('user', 'getUserPhoneNumber', {
        userId,
        orderId,
      });
      return result.phone as string;
    } catch (error) {
      console.error('获取用户电话号码失败:', error);
      throw error;
    }
  }
}

export const userService = new UserServiceImpl();
