import PageLol from '@components/common/page-meta';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';

/**
 * 咨询师注册页面
 */
export default function TherapistUpdaterPage() {
  return (
    <PageLol
      navigationProps={{
        title: '资料编辑',
        showBackButton: true,
        showSearch: false,
      }}
    >
      <View className='min-h-screen pb-24 px-4'>
        {/* 基础信息 */}
        <View className='mt-6 flex flex-row bg-white border border-primary rounded-lg p-4'>
          <View className='flex flex-col flex-1'>
            <Text className='text-base font-bold'>基础信息</Text>

            <View className='flex flex-row items-center  mt-3'>
              <View className='w-1.5 h-1.5 bg-gray-500 rounded-full mr-2' />
              <Text className='text-md font-medium '>
                请使用真实资料，不要频繁修改，平台保护您的隐私信息
              </Text>
            </View>
            <View className='flex flex-row items-center  mt-3'>
              <View className='w-1.5 h-1.5 bg-gray-500 rounded-full mr-2' />
              <Text className='text-md font-medium '>
                将职称/头衔按重要性排序
              </Text>
            </View>
          </View>

          <Text
            className='text-sm text-primary'
            onClick={() => {
              Taro.redirectTo({
                url: '/pages/sub-packages/therapist-updater/base-info/index',
              });
            }}
          >
            编辑
          </Text>
        </View>

        {/* 详细信息 */}

        <View className='mt-6 flex flex-row bg-white border border-primary rounded-lg p-4'>
          <View className='flex flex-col flex-1'>
            <Text className='text-base font-bold'>详细信息</Text>

            <View className='flex flex-row items-center  mt-3'>
              <View className='w-1.5 h-1.5 bg-green-500 rounded-full mr-2' />
              <Text className='text-md font-medium '>
                个人相册显著增加用户信任感
              </Text>
            </View>
            <View className='flex flex-row items-center  mt-3'>
              <View className='w-1.5 h-1.5 bg-green-500 rounded-full mr-2' />
              <Text className='text-md font-medium '>
                详细的教育/培训记录增加专业度
              </Text>
            </View>
            <View className='flex flex-row items-center  mt-3'>
              <View className='w-1.5 h-1.5 bg-green-500 rounded-full mr-2' />
              <Text className='text-md font-medium '>
                发表Blog能为您的资料增色加分
              </Text>
            </View>
          </View>

          <Text
            className='text-sm text-primary'
            onClick={() => {
              Taro.redirectTo({
                url: '/pages/sub-packages/therapist-updater/detail-info/index',
              });
            }}
          >
            编辑
          </Text>
        </View>

        {/* 服务信息 */}
        <View className='mt-6 flex flex-row bg-white border border-primary rounded-lg p-4'>
          <View className='flex flex-col flex-1'>
            <Text className='text-base font-bold'>服务设置</Text>

            <View className='flex flex-row items-center  mt-3'>
              <Text className='text-md font-medium '>
                设置您提供的服务类型、价格、时长和最终价格
              </Text>
            </View>
            <View className='flex flex-row items-center  mt-3'>
              <Text className='text-md font-medium '>
                后续支持灵活的折扣/优惠等
              </Text>
            </View>
          </View>

          <Text
            className='text-sm text-primary'
            onClick={() => {
              Taro.redirectTo({
                url: '/pages/sub-packages/therapist-updater/service-settings/index',
              });
            }}
          >
            编辑
          </Text>
        </View>

        {/* 工作时间 */}
        <View className='mt-6 flex flex-row bg-white border border-primary rounded-lg p-4'>
          <View className='flex flex-col flex-1'>
            <Text className='text-base font-bold'>工作时间</Text>

            <View className='flex flex-row items-center  mt-3'>
              <Text className='text-md font-medium '>
                设置工作日、工作时间段和例外日
              </Text>
            </View>
            {/* <View className='flex flex-row items-center  mt-3'>
              <Text className='text-md font-medium '>
                后续支持灵活的折扣/优惠等
              </Text>
            </View> */}
          </View>

          <Text
            className='text-sm text-primary'
            onClick={() => {
              Taro.redirectTo({
                url: '/pages/sub-packages/therapist-updater/work-time/index',
              });
            }}
          >
            编辑
          </Text>
        </View>
        {/* footer */}
        <View className='flex flex-row items-center justify-center mt-3'>
          <Text className='text-sm font-medium text-secondary'>
            未提交的资料会自动保存为草稿，您可以使用 &apos;我的 - 设置 -
            清除缓存&apos; 清理保存的草稿
          </Text>
        </View>
      </View>
    </PageLol>
  );
}
