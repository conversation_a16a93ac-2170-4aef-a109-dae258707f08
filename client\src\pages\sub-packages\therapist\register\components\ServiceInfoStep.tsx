import { Button } from '@antmjs/vantui';
import ServiceConfigEditor from '@components/therapist/ServiceConfigEditor';
import { my_service } from '@model/profile.therapist';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';

// 扩展表单数据类型，包含额外的UI状态字段
interface ExtendedFormData extends Partial<my_service> {
  agreeTerms?: boolean;
}

interface ServiceInfoStepProps {
  formData: ExtendedFormData;
  onFormChange: (field: string, value: any) => void;
  onPrevStep: () => void;
  onNextStep: () => void;
}

export default function ServiceInfoStep({
  formData,
  onFormChange,
  onPrevStep,
  onNextStep,
}: ServiceInfoStepProps) {
  // 表单验证
  const validateForm = (): boolean => {
    // 验证服务列表
    const serviceList = formData.services || [];
    if (serviceList.length === 0) {
      Taro.showToast({ title: '请至少添加一个服务', icon: 'none' });
      return false;
    }

    // 验证工作时间
    if (
      !formData.workTime?.workDays ||
      formData.workTime.workDays.length === 0
    ) {
      Taro.showToast({ title: '请选择工作日', icon: 'none' });
      return false;
    }

    return true;
  };

  return (
    <View>
      <ServiceConfigEditor
        services={formData.services || []}
        onServicesChange={(newServices) => {
          onFormChange('services', newServices);
        }}
      />
      {/* 底部按钮 */}
      <View className='mt-8 flex'>
        <Button
          type='default'
          block
          round
          plain
          hairline
          className='flex-1 mr-2'
          onClick={onPrevStep}
        >
          上一步
        </Button>

        <Button
          type='primary'
          block
          round
          className='flex-1 ml-2'
          onClick={() => {
            if (validateForm()) {
              onNextStep();
            }
          }}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
