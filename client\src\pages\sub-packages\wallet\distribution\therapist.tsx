import PageLol from '@components/common/page-meta';
import StatisticCard from '@components/StatisticCard';
import { distributionActions } from '@core/actions/distribution.action';
import { useDistributionStore } from '@stores/distribution.store';
import { Text, View } from '@tarojs/components';
import { formatTime5 } from '@utils/time';
import { useEffect } from 'react';
import SharePromotion from './SharePromotion';
import ShareTabs from './ShareTabs';

/**
 * 分享中心页面
 */
export default function DistributionPage() {
  const distributionData = useDistributionStore.use.overview();

  useEffect(() => {
    console.log('distributionData', distributionData);
    const loadData = async () => {
      await Promise.all([
        distributionActions.fetchDistributionOverview(),
        // 移除重复的数据加载，让ShareTabs组件自己处理
      ]);
    };
    loadData();
  }, []);

  return (
    <PageLol
      navigationProps={{
        title: '分享中心',
        showBackButton: true,
        showSearch: false,
      }}
    >
      <View className='min-h-screen pb-16'>
        {/* 分享数据概览卡片 */}
        <StatisticCard
          className='mb-4 bg-gradient-to-r from-[#E0EAFC] to-[#CFDEF3]'
          title='分享概览'
          renderRight={
            <Text className='text-xs text-secondary'>
              自{formatTime5(distributionData?.createdAt || 0)}
            </Text>
          }
          values={[
            {
              title: '累计收益',
              value: distributionData?.totalIncome || 0,
            },
            {
              title: '累计订单数',
              value: distributionData?.totalOrderCount || 0,
            },
            {
              title: '邀请用户数',
              value: distributionData?.inviteCount || 0,
            },
          ]}
        ></StatisticCard>

        {/* 分享推广 */}
        <SharePromotion qrCodeUrl={distributionData?.qrCodeUrl || ''} />

        {/* 分享数据Tab */}
        <ShareTabs />
      </View>
    </PageLol>
  );
}
