import CircleProgress from '@components/common/CircleProgress';
import { ScrollView, Text, View } from '@tarojs/components';
import { useState } from 'react';

export default function ProgressDemo() {
  const [progress, setProgress] = useState(0);

  return (
    <View className='min-h-screen flex flex-col items-center justify-center bg-gray-100 p-4'>
      <Text className='text-xl font-bold mb-8'>高性能圆环进度组件</Text>

      <View className='grid grid-cols-2 gap-6'>
        <ScrollView
          className='whitespace-nowrap'
          scrollX
          enhanced
          showScrollbar={false}
        >
          <View className='flex flex-row items-center justify-between'>
            <CircleProgress
              progress={progress}
              size={160}
              strokeWidth={10}
              progressColor='#3b82f6'
            />
            <Text className='mt-3 text-gray-700'>默认样式</Text>
            <View className='flex w-[600px] h-20 bg-blue-400 items-center' />
          </View>
        </ScrollView>

        {/* <View className='flex flex-col items-center'>
          <CircleProgress
            progress={85}
            size={160}
            strokeWidth={14}
            trackColor='#fef3c7'
            progressColor='#f59e0b'
          />
          <Text className='mt-3 text-gray-700'>橙色主题</Text>
        </View>

        <View className='flex flex-col items-center'>
          <CircleProgress
            progress={65}
            size={160}
            strokeWidth={12}
            trackColor='#f0fdf4'
            progressColor='#10b981'
          >
            <View className='flex flex-col items-center'>
              <Text
                className='font-bold'
                style={{ color: '#10b981', fontSize: '24px' }}
              >
                65%
              </Text>
              <Text className='text-gray-500 text-sm'>完成率</Text>
            </View>
          </CircleProgress>
          <Text className='mt-3 text-gray-700'>自定义内容</Text>
        </View>

        <View className='flex flex-col items-center'>
          <CircleProgress
            progress={progress}
            size={160}
            strokeWidth={16}
            trackColor='#f5f3ff'
            progressColor='#8b5cf6'
          />
          <Text className='mt-3 text-gray-700'>动态进度</Text>
        </View> */}
      </View>

      <View className='mt-12 w-full max-w-md'>
        <Text className='block mb-3 text-gray-700'>当前进度: {progress}%</Text>
        <View className='relative h-4 bg-gray-200 rounded-full overflow-hidden'>
          <View
            className='absolute top-0 left-0 h-full bg-blue-500'
            style={{ width: `${progress}%` }}
          />
        </View>
        <View className='mt-4 flex justify-between'>
          {[0, 25, 50, 75, 100].map((value) => (
            <Text
              key={value}
              className='text-blue-600 font-medium'
              onClick={() => setProgress(value)}
            >
              {value}%
            </Text>
          ))}
        </View>
      </View>

      <View className='mt-8 p-4 bg-white rounded-lg shadow'>
        <Text className='text-gray-700'>
          此组件使用Canvas实现，解决了在微信小程序中滑动页面时进度条卡顿的问题。
          相比SVG或CSS实现，Canvas提供了更好的滚动性能和动画流畅度。
        </Text>
      </View>
    </View>
  );
}
