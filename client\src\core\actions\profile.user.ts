import { BaseCacheService } from '@core/cache/base-cache.service';
import { favoriteTherapistsCacheOptions } from '@core/cache/therapist-cache-policy';
import { therapist_summary } from '@model/therapist.interface';
import { UserProfileService } from '@services/profile.user';
import { useGlobalStore } from '@stores/global.store';
import { useUserProfileStore } from '@stores/profile.user';
import { useTherapistStore } from '@stores/therapist.store';
import { appRouter } from '@utils/router';
import { ensureLoggedIn } from './auth.action';
import { therapistActions } from './therapist.action';

/**
 * 用户个人资料管理操作
 * 处理用户端/咨询师端的个人信息相关业务逻辑
 */
export const userProfileActions = {
  fetchMyPublicProfile: async () => {
    // 检查是否登录
    ensureLoggedIn();

    const result = await UserProfileService.fetchMyPublicProfile();
    if (result) {
      useUserProfileStore.setState({
        profile: {
          userName: result.userName,
          avatar: result.avatar,
          role: result.role,
          id: result.id,
          status: result.status,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
        },
      });
      useGlobalStore.getState().setNeedReload(false);
    }
    return result;
  },

  /**
   * 处理扫码操作
   */
  // handleScan: () => {
  //   ensureLoggedIn();
  //   Taro.scanCode({
  //     success: (res) => {
  //       // 处理扫码结果
  //       const scanResult = res.result;

  //       // 根据不同的二维码内容进行不同的处理
  //       if (scanResult.startsWith('therapist:')) {
  //         // 扫描心理咨询师名片
  //         const therapistId = scanResult.split(':')[1];
  //         Taro.navigateTo({
  //           url: `/pages/sub-packages/therapist/detail/index?id=${therapistId}`,
  //         });
  //       } else if (scanResult.startsWith('course:')) {
  //         // 扫描课程
  //         const courseId = scanResult.split(':')[1];
  //         Taro.navigateTo({
  //           url: `/pages/sub-packages/course/detail/index?id=${courseId}`,
  //         });
  //       } else if (scanResult.startsWith('invite:')) {
  //         // 扫描邀请码
  //         const inviteCode = scanResult.split(':')[1];
  //         Taro.navigateTo({
  //           url: `/pages/sub-packages/invite/join/index?code=${inviteCode}`,
  //         });
  //       } else {
  //         // 未知二维码
  //         Taro.showToast({
  //           title: '无法识别的二维码',
  //           icon: 'none',
  //         });
  //       }
  //     },
  //     fail: () => {
  //       Taro.showToast({
  //         title: '扫码失败',
  //         icon: 'none',
  //       });
  //     },
  //   });
  // },

  /**
   * 处理消息按钮点击
   */
  handleMessage: () => {
    ensureLoggedIn();
    appRouter.userMessages();
  },

  /**
   * 收藏/取消收藏咨询师
   */
  toggleFavorite: async (therapistId: string, favorite: boolean) => {
    try {
      // 乐观更新UI
      const { therapists, currentTherapistExtinfo: currentTherapist } =
        useTherapistStore.getState();

      // 更新列表
      if (therapists) {
        const updatedTherapists = therapists.map((t) =>
          t.id === therapistId
            ? {
                ...t,
                isFavorite: favorite,
                favoriteCount: favorite
                  ? (t.favoriteCount || 0) + 1
                  : Math.max((t.favoriteCount || 0) - 1, 0),
              }
            : t
        );
        useTherapistStore.setState({ therapists: updatedTherapists });
      }

      // 更新咨询师基础信息
      if (currentTherapist && currentTherapist.id === therapistId) {
        const updatedTherapist = {
          ...currentTherapist,
          isFavorite: favorite,
          favoriteCount: favorite
            ? ((currentTherapist as any).favoriteCount || 0) + 1
            : Math.max(((currentTherapist as any).favoriteCount || 0) - 1, 0),
        };
        useTherapistStore.setState({
          currentTherapistExtinfo: updatedTherapist,
        });
      }

      // 调用服务
      const result = await UserProfileService.favorite(therapistId, favorite);
      console.log('profileActions toggleFavorite result', result);

      if (!result) {
        // 如果失败，回滚UI变更
        if (therapists) {
          const rollbackTherapists = therapists.map((t) =>
            t.id === therapistId
              ? {
                  ...t,
                  isFavorite: !favorite,
                  favoriteCount: !favorite
                    ? (t.favoriteCount || 0) + 1
                    : Math.max((t.favoriteCount || 0) - 1, 0),
                }
              : t
          );
          useTherapistStore.setState({ therapists: rollbackTherapists });
        }

        if (currentTherapist && currentTherapist.id === therapistId) {
          const rollbackTherapist = {
            ...currentTherapist,
            isFavorite: !favorite,
            favoriteCount: !favorite
              ? ((currentTherapist as any).favoriteCount || 0) + 1
              : Math.max(((currentTherapist as any).favoriteCount || 0) - 1, 0),
          };
          useTherapistStore.setState({
            currentTherapistExtinfo: rollbackTherapist,
          });
        }

        throw new Error('收藏操作失败');
      }

      //更新收藏咨询师列表状态
      useUserProfileStore.setState({
        // 如果是需要收藏，则添加到收藏列表，否则从收藏列表中移除
        favoriteTherapists: favorite
          ? [
              ...(useUserProfileStore.getState().favoriteTherapists || []),
              therapistId,
            ]
          : (useUserProfileStore.getState().favoriteTherapists || []).filter(
              (id) => id !== therapistId
            ),
      });

      return result;
    } catch (error) {
      console.error('收藏操作失败:', error);
      throw error;
    }
  },

  /**
   * 获取收藏的咨询师列表
   */
  fetchFavoriteTherapists: async (
    forceRefresh: boolean = false
  ): Promise<therapist_summary[]> => {
    try {
      // 生成缓存键
      const cacheKey = favoriteTherapistsCacheOptions.getCacheKey('favorite');

      if (!forceRefresh) {
        // 尝试从缓存获取数据 ,因为缓存的是id列表，所以需要转换为therapist_summary[]
        const cachedData = BaseCacheService.get(cacheKey, null);
        if (cachedData) {
          const therapistAsync = cachedData.map(async (id) => {
            //先从store中查找，找不到查找缓存，找不到则从服务获取
            const therapist = useTherapistStore
              .getState()
              .therapists?.find((t) => t.id === id);
            if (therapist) {
              return therapist;
            }
            // 从服务获取
            const therapistResult =
              await therapistActions.fetchTherapistSummary(id.toString());
            return therapistResult;
          });
          // 等待所有请求完成
          const therapists = await Promise.all(therapistAsync);
          // 过滤掉null
          therapists.filter((t) => t !== null);
          // 更新store
          useUserProfileStore.setState({
            favoriteTherapists: therapists.map((t) => t.id),
          });
          return therapists;
        }
      }

      // 调用服务层API
      const result = await UserProfileService.readFavoriteTherapists();
      // 缓存数据 只缓存id列表
      if (result) {
        // 提取咨询师ID列表
        const therapistIds = result;
        BaseCacheService.set(
          cacheKey,
          therapistIds,
          favoriteTherapistsCacheOptions.ttl
        );
        // 转化成therapist_summary[]
        const therapists = await Promise.all(
          therapistIds.map(async (id) => {
            return await therapistActions.fetchTherapistSummary(id.toString());
          })
        );
        // 更新store
        useUserProfileStore.setState({
          favoriteTherapists: therapistIds,
        });
        return therapists.filter((t) => t !== null) as therapist_summary[];
      }
      return [];
    } catch (error) {
      console.error('获取收藏的咨询师列表失败:', error);
      return [];
    }
  },

  updateAvatarAndNickname: async (avatar: string, nickname: string) => {
    console.log('profileActions updateAvatarAndNickname', avatar, nickname);
    const result = await UserProfileService.updateAvatarAndNickname(
      avatar,
      nickname
    );
    if (result.success) {
      userProfileActions.fetchMyPublicProfile();
    } else {
      console.error('更新用户信息失败:', result.message);
      throw new Error(result.message || '更新用户信息失败');
    }
    return result;
  },
};
