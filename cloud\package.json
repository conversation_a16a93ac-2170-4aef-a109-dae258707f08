{"name": "cloud", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "node bundle-modules.js --no-sourcemap", "deploy": "cloudbase framework deploy --verbose", "build:db_manage": "node bundle-modules.js -f src/entries/db_manage.js --no-sourcemap", "deploy:db_manage": "cloudbase framework deploy db_manage", "build:user": "node bundle-modules.js -f src/entries/user.js --no-sourcemap", "deploy:user": "cloudbase framework deploy user --verbose --force", "build:therapist": "node bundle-modules.js -f src/entries/therapist.js --no-sourcemap", "deploy:therapist": "cloudbase framework deploy therapist", "build:paymentcallback": "node bundle-modules.js -f src/entries/paymentcallback.js --no-sourcemap", "deploy:paymentcallback": "cloudbase framework deploy paymentcallback", "build:payment": "node bundle-modules.js -f src/entries/payment.js --no-sourcemap", "deploy:payment": "cloudbase framework deploy payment", "build:order": "node bundle-modules.js -f src/entries/order.js --no-sourcemap", "deploy:order": "cloudbase framework deploy order", "build:feedback": "node bundle-modules.js -f src/entries/feedback.js --no-sourcemap", "deploy:feedback": "cloudbase framework deploy feedback", "build:distribution": "node bundle-modules.js -f src/entries/distribution.js --no-sourcemap", "deploy:distribution": "cloudbase framework deploy distribution", "build:config": "node bundle-modules.js -f src/entries/config.js --no-sourcemap", "deploy:config": "cloudbase framework deploy config", "build:chat": "node bundle-modules.js -f src/entries/chat.js --no-sourcemap", "deploy:chat": "cloudbase framework deploy chat", "build:test2": "node bundle-modules.js -f src/entries/test2.js --no-sourcemap", "deploy:test2": "cloudbase framework deploy test2", "build:review": "node bundle-modules.js -f src/entries/review.js --no-sourcemap", "deploy:review": "cloudbase framework deploy review", "build:measure": "node bundle-modules.js -f src/entries/measure.js --no-sourcemap", "deploy:measure": "cloudbase framework deploy measure", "build:treatment": "node bundle-modules.js -f src/entries/treatment.js --no-sourcemap", "deploy:treatment": "cloudbase framework deploy treatment", "build:admin-portal": "node bundle-modules.js -f src/entries/admin-portal.js --no-sourcemap", "deploy:admin-portal": "cloudbase framework deploy admin-portal", "lint": "eslint src/**/*.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "devDependencies": {"esbuild": "^0.25.5", "esbuild-node-externals": "^1.18.0"}, "dependencies": {"crypto": "^1.0.1", "moment": "^2.30.1", "tls-sig-api-v2": "^1.0.2", "wx-server-sdk": "^3.0.1"}}