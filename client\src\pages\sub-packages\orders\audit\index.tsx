import { Empty } from '@antmjs/vantui';
import ButtonGroup, { ButtonAction } from '@components/common/ButtonGroup';
import PageLol from '@components/common/page-meta';
import OrderCardForAdmin from '@components/order-card/OrderCardForAdmin';
import { orderAdminActions } from '@core/actions/order.admin';
import { useLoadingStore } from '@stores/loading.store';
import { Text, View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { useState } from 'react';

/**
 * 退款审核页面 - 管理员专用
 */
export default function RefundAuditPage() {
  const [orders, setOrders] = useState<any[]>([]);
  const { setTransactionLoading, transactionLoading } = useLoadingStore();
  const [error, setError] = useState<string | null>(null);

  // 加载所有退款审核中的订单
  const loadRefundAuditingOrders = async () => {
    setTransactionLoading(true);
    try {
      const data = await orderAdminActions.fetchRefundAuditingOrders();
      setOrders(data);
      console.log('退款审核订单列表:', data);
    } catch (err: any) {
      console.error('获取退款审核订单失败:', err);
      setError(err.message || '获取退款审核订单失败');
    } finally {
      setTransactionLoading(false);
    }
  };

  // 页面显示时加载数据
  useDidShow(() => {
    loadRefundAuditingOrders();
  });

  // 通过退款申请
  const handleApprove = async (orderId: string) => {
    try {
      await orderAdminActions.approveRefund(orderId);
      // 刷新订单列表
      await loadRefundAuditingOrders();
      return Promise.resolve();
    } catch (err: any) {
      console.error('退款审核通过失败:', err);
      setError(err.message || '退款审核通过失败');
      return Promise.reject(err);
    }
  };

  // 拒绝退款申请，跳转到拒绝理由页面
  const handleReject = (orderId: string) => {
    orderAdminActions.navigateToRejectRefund(orderId);
  };

  // 渲染订单卡片和操作按钮
  const renderOrderCard = (order: any) => {
    const actions: ButtonAction[] = [
      {
        text: '拒绝退款',
        type: 'danger',
        onClick: () => handleReject(order._id),
      },
      {
        text: '通过退款',
        type: 'primary',
        onClick: () => handleApprove(order._id),
      },
    ];

    return (
      <View key={order._id} className='mb-4'>
        <OrderCardForAdmin order={order} />
        <View className='px-4 mt-2'>
          <ButtonGroup actions={actions} />
        </View>
      </View>
    );
  };

  return (
    <PageLol
      navigationProps={{ title: '退款审核', showBackButton: true }}
      error={error || null}
    >
      <View className='p-4'>
        <Text className='text-lg font-bold mb-4'>待审核退款申请</Text>

        {orders.length > 0 ? (
          <View className='mt-4'>
            {orders.map((order) => renderOrderCard(order))}
          </View>
        ) : (
          <Empty description='暂无待审核的退款申请' />
        )}
      </View>
    </PageLol>
  );
}
